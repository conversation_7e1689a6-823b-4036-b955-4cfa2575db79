/* jshint node: true *//* eslint-env node */

"use strict";

const loadTasksRelative = require('grunt-niagara/lib/loadTasksRelative');

const SRC_FILES = [
  'src/rc/**/*.js',
  'Gruntfile.js',
  '!**/*.built.js',
  '!**/*.built.min.js'
];
const TEST_FILES = [
  'srcTest/rc/**/*.js'
];
const JS_FILES = SRC_FILES.concat(TEST_FILES);
const ALL_FILES = JS_FILES.concat('src/rc/**/*.css');

const RJS_MAP = {
    '*': {
        'React': 'react',
        'ReactDOM': 'react-dom'
    }
};

module.exports = function runGrunt(grunt) {

  grunt.initConfig({
    pkg: grunt.file.readJSON('package.json'),

    jsdoc: {
      src: SRC_FILES.concat([ 'README.md' ])
    },
    eslint: {
      src: JS_FILES
    },
    babel: {
      options: {
        presets: [ '@babel/preset-env', '@babel/preset-react' ],
        plugins: [ '@babel/plugin-transform-react-jsx', '@babel/plugin-transform-class-properties']
      },
      coverage: {
        options: {
          presets: [ '@babel/preset-env', '@babel/preset-react' ],
          plugins: [ '@babel/plugin-transform-react-jsx', '@babel/plugin-transform-class-properties', 'istanbul' ]
        }
      }
    },
    watch: {
      src: ALL_FILES
    },
    karma: {},
    requirejs: {
        options: {
            paths: {
                'react': 'node_modules/react/umd/react.development',
                'react-dom': 'node_modules/react-dom/umd/react-dom.development',
                'semantic-ui-react': 'node_modules/semantic-ui-react/dist/umd/semantic-ui-react.min',
                'resize-observer-polyfill': 'node_modules/resize-observer-polyfill/dist/ResizeObserver',
                'html-react-parser': 'node_modules/html-react-parser/dist/html-react-parser.min'
            },
            map: RJS_MAP
        }
    },
    niagara: {}
  });

  grunt.registerTask('appendJs', 'Append JS code to .min.js files in build folder', function() {
    const fs = require('fs');
    const path = require('path');

    // Define the directory and the code to append
    const buildDir = 'build/src/rc';
    const appendCode = `require.config({map: ${JSON.stringify(RJS_MAP)}});`;

    const filePath = path.join(buildDir, "honApplicationHandler.built.min.js");
    const fileContent = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(filePath, appendCode + fileContent, 'utf8');
    grunt.log.writeln(`Appended code`);
  });

  loadTasksRelative(grunt, 'grunt-niagara');
};
