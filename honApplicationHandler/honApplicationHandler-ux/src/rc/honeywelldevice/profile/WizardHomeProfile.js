define([
    'baja!',
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ModalDialog',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/Scheduling',
    'nmodule/honApplicationHandler/rc/honeywelldevice/pages/TerminalAssignment',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/CommonVisibilityRules',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/TerminalAssignmentValueRules',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/HoneywellLogo',
    'nmodule/honApplicationHandler/rc/honeywelldevice/pages/DynamicPage',
    'nmodule/honApplicationHandler/rc/factory/HoneywellDeviceWizardRPC',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard'
],
(
    baja,
    React,
    SemanticReact,
    ModalDialog,
    DropdownFloatLabel,
    lexicons,
    Scheduling,
    TerminalAssignment,
    CommonVisibilityRules,
    TerminalAssignmentValueRules,
    HoneywellLogo,
    DynamicPage,
    HoneywellDeviceWizardRPC
) => {
    'use strict';

    var honApplicationHandlerLex = lexicons[0];

    const {
        Menu,
        MenuItem,
        Segment,
        Tab,
        Button,
        Dimmer,
        Loader,
        Image,
        Modal,
        ModalHeader,
        ModalContent,
        ModalActions,
    } = SemanticReact;
    const TERMINAL_ASSIGNMENT = "Terminal$20Assignment";
    const SCHEDULING = 'Scheduling';

    class WizardHomeProfile extends React.Component {

        constructor(props) {
            super(props);
            this.state = {
                activeIndex: 0,
                saving: false,
                loading: false,
                dynamicStores: [],
                changedDynamicStores: [],
                scheduling: [{
                    events: [],
                    defaultLocalScheduleType: 0,
                    label: '',
                    Holidays: [],
                }],                
                impactedTabs: [],
                impactedSubTabs: [],
                terminalRuleErrors:[],
                disableSaveButton: false,
                disableSaveButtonMsg: '',
                unitComponentInTab: '',
                unitComponentKey: '',
                airflowUnitComponentInTab: '',
                airflowUnitComponentKey: '',
                wizardValidationMessage: "",
                errorModal: false,
                errorInfo: '',
                draggedTabIndex: null,
                mainTabIndexesChanged: false,
                wizardSaveOption: '',
                wizardSaveOptionModal: false,
            };
            this.isReadOnly = false;
        }

        componentDidMount() {
            this.loadPage();
            this.checkForDuplicateHolidaysExist(); 
            const unloadCallback = (event) => {
                event.preventDefault();
                event.returnValue = "";
                return "";
            };

            const keydownEvent = async (event) => {
                const allFocusableElementsUnsorted = document.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([disabled])'); 
                const allFocusableElements = Array.from(allFocusableElementsUnsorted).sort((a, b) => {
                    const tabindexA = parseInt(a.getAttribute('tabindex'), 10) || 0; // Handle missing or invalid tabindex
                    const tabindexB = parseInt(b.getAttribute('tabindex'), 10) || 0;
                    return tabindexA - tabindexB;
                });
                if (event.key === 'Tab' && 
                    allFocusableElements[allFocusableElements.length - 1].tabIndex === document.activeElement.tabIndex && 
                    allFocusableElements[allFocusableElements.length - 1].innerText === document.activeElement.innerText) {       
                    setTimeout(() => {
                        const firstElement = document.querySelector(`[tabindex="${allFocusableElements[0].tabIndex}"]`);
                        firstElement && firstElement.focus(); 
                    }, 0);
                } else if (event.ctrlKey && event.key === 'ArrowUp' && this.state.activeIndex > 0) {
                    let activeIndex = this.state.activeIndex-1;
                    this.handleTabChange({},{ activeIndex });
                    const subTabNames = this.getSubTabNames(this.state.dynamicStores[this.state.activeIndex]); 
                    subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[this.state.activeIndex]}-${subTabNames[0].tabInPage}`);
                    await this.removeFromImpactedTabs(this.state.dynamicStores[this.state.activeIndex]);
                } else if (event.ctrlKey && event.key === 'ArrowDown' && this.state.activeIndex < this.state.dynamicStores.length) {
                    let activeIndex = this.state.activeIndex+1;
                    this.handleTabChange({},{ activeIndex });
                    const subTabNames = this.getSubTabNames(this.state.dynamicStores[this.state.activeIndex]); 
                    subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[this.state.activeIndex]}-${subTabNames[0].tabInPage}`);
                    await this.removeFromImpactedTabs(this.state.dynamicStores[this.state.activeIndex]);
                } else if(event.ctrlKey && event.altKey && event.keyCode === 83) {
                    if((this.state.changedDynamicStores.length !== 0 || this.state.mainTabIndexesChanged) && !this.state.disableSaveButton && this.state.impactedTabs.length === 0) {
                        this.handleClickSave(event);
                    }
                } else if (event.altKey && /\d/.test(event.key)) { 
                    event.preventDefault();
                    if(event.key !== '0') {
                        let activeIndex = this.state.activeIndex;
                        this.state.dynamicStores[event.key-1] && this.setState({ activeIndex: event.key-1 });
                        const subTabNames = this.getSubTabNames(this.state.dynamicStores[activeIndex]); 
                        subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[activeIndex]}-${subTabNames[0].tabInPage}`);
                        await this.removeFromImpactedTabs(this.state.dynamicStores[activeIndex]);
                    }
                }
            }

            document.addEventListener('keydown', keydownEvent);
            
            window.addEventListener("beforeunload", unloadCallback);

            return () => {
                window.removeEventListener("beforeunload", unloadCallback);
                document.removeEventListener("keydown", keydownEvent);
            }
        }

        componentDidUpdate(prevProps, prevState) {
            const {refresh} = this.props;
            if(refresh !== prevProps.refresh) {
                this.loadPage();
            }
        }
		savePage() {
			let jsonObj = {
				deviceHandle: this.props.deviceHandle
			};
			const params = [{ myJSON: jsonObj }];
			return HoneywellDeviceWizardRPC.invokeRPC('closeWizard', params).then(() => {
				const { globalStore } = this.props;
			});
		}
        componentWillUnmount(){
			this.savePage();
        }

        loadPage() {
            this.setState({
                loading: true
            }, () => {
                this.getDynamicStoresAndRules().then(() => {
                    const {globalStore} = this.props;
                    let sortedKeys = []; 
                    Object.entries(globalStore.dynamicStores).forEach(([key, value]) => {
                        if (Array.isArray(value)) {
                            // Push each array item with its index
                            value.forEach(item => {
                                sortedKeys.push({ key: `Scheduling_${item.label}`, index: item.index });
                            });
                        } else {
                            // Push object item with its own index
                            sortedKeys.push({ key, index: value.index });
                        }
                    });
                    sortedKeys.sort((a, b) => a.index - b.index)
                    sortedKeys = sortedKeys.map(entry => entry.key);

                    CommonVisibilityRules.applyVisibleRules(globalStore);
                    const {terminalRuleErrors, changedTerminals} = TerminalAssignmentValueRules.applyValueRules(globalStore);
                    if(changedTerminals && changedTerminals.length){
                        this.setState({
                            changedDynamicStores: [...this.state.changedDynamicStores, TERMINAL_ASSIGNMENT],
                        });
                    }
                    let scheduling = globalStore.dynamicStores.Scheduling ? 
                    globalStore.dynamicStores.Scheduling.map(item => { return {
                        events: item.items.Schedules.Events || [],
                        defaultLocalScheduleType: item.items.Schedules.defaultLocalScheduleType || 0,
                        label: item.label,
                        Holidays: [...item.items.Holidays.recurringDaysEveryYear, 
                            ...item.items.Holidays.specificDateEveryYear, 
                            ...item.items.Holidays.dateRangeEveryYear] || [],
                    }}) : [{
                        events: [],
                        defaultLocalScheduleType: 0,
                        label: '',
                        Holidays: [],
                    }];

                    (this.state.unitComponentInTab === '' || this.state.airflowUnitComponentInTab === '') && Object.keys(globalStore.dynamicStores).forEach(store => {
                        if (store !== "Scheduling" && store !== "Terminal$20Assignment") {
                            const storeItems = globalStore.dynamicStores[store] && globalStore.dynamicStores[store].items;
                            if (storeItems) {
                                Object.keys(storeItems).forEach(item => {
                                    if (storeItems[item].componentType === "MeasurementType") {
                                        this.setState({ unitComponentInTab: store, unitComponentKey: storeItems[item].name });
                                    } else if (storeItems[item].componentType === "AirflowUnit") {
                                        this.setState({ airflowUnitComponentInTab: store, airflowUnitComponentKey: storeItems[item].name });
                                    }
                                });
                            }
                        }
                    });
                    this.setState({ dynamicStores: sortedKeys, loading: false, scheduling, terminalRuleErrors });
                }, () => { });
            });
        }

        getDynamicStoresAndRules() {
            let jsonObj = {
                deviceHandle: this.props.deviceHandle
            };
            const params = [{ myJSON: jsonObj }];
            return HoneywellDeviceWizardRPC.invokeRPC("getPermissionsForLoginUser", params).then(permission => {
                console.log(permission);
                this.isReadOnly = permission.isReadOnly;
                return HoneywellDeviceWizardRPC.invokeRPC('getDynamicStores', params).then(resultStore=>{
                    if(resultStore.wizardValidationMessage) {
                        this.setState({wizardValidationMessage: resultStore.wizardValidationMessage, loading: false});
                        return Promise.reject();
                    }

                    const {globalStore} = this.props;
                    console.log(resultStore);
                    globalStore.dynamicStores = resultStore;
                    globalStore.convertDynamicStoreValues(this.isReadOnly);
                    this.buildTerminalStore(this.isReadOnly);

                    return HoneywellDeviceWizardRPC.invokeRPC('getWizardRules', params).then(resultRules => {
                        const {globalStore} = this.props;
                        console.log(resultRules);
                        globalStore.wizardRules = resultRules;
                    });
                });
            })
        }

        sortRulesWithDependencyHandling = (rules) => {
            const extractDeps = (obj) => {
                const deps = new Set();

                function recurse(o) {
                    if (o && typeof o === 'object') {
                        if (o.targetItem && o.targetStore) {
                            deps.add(`${o.targetItem} IN ${o.targetStore}`);
                        }
                        if (Array.isArray(o.rules)) {
                            o.rules.forEach(recurse);
                        }
                        Object.values(o).forEach(recurse);
                    }
                }

                if (obj.valueRules) recurse(obj.valueRules.if);
                if (obj.terminalAssignmentValueRules) recurse(obj.terminalAssignmentValueRules.if);
                if (obj.visibilityRule) recurse(obj.visibilityRule);

                return deps;
            };

            const extractOutputs = (obj) => {
                const outputs = new Set();

                function recurse(o) {
                    if (o && typeof o === 'object') {
                        if (o.item && o.itemStore) {
                            outputs.add(`${o.item} IN ${o.itemStore}`);
                        }
                        if (Array.isArray(o)) {
                            o.forEach(recurse);
                        } else {
                            Object.values(o).forEach(recurse);
                        }
                    }
                }

                if (obj.valueRules) {
                    recurse(obj.valueRules.then);
                    recurse(obj.valueRules.else);
                }
                if (obj.terminalAssignmentValueRules) {
                    recurse(obj.terminalAssignmentValueRules.then);
                    recurse(obj.terminalAssignmentValueRules.else);
                }
                if (obj.trueVisibleItems) {
                    recurse(obj.trueVisibleItems.map(str => {
                        const [item, store] = str.split(' IN ');
                        return { item, itemStore: store };
                    }));
                }
                if (obj.falseVisibleItems) {
                    recurse(obj.falseVisibleItems.map(str => {
                        const [item, store] = str.split(' IN ');
                        return { item, itemStore: store };
                    }));
                }

                return outputs;
            };

            // Step 1: Build dependency graph
            const ruleMeta = rules.map((rule, index) => ({
                index,
                deps: extractDeps(rule),
                outputs: extractOutputs(rule)
            }));

            const outputMap = new Map();
            ruleMeta.forEach((meta, i) => {
                meta.outputs.forEach(out => outputMap.set(out, i));
            });

            const graph = new Map(); // index -> Set of dependencies (indices)
            const inDegree = Array(rules.length).fill(0);

            ruleMeta.forEach(({ index, deps }) => {
                const depIndices = new Set();
                deps.forEach(dep => {
                    const depIdx = outputMap.get(dep);
                    if (depIdx !== undefined && depIdx !== index) {
                        depIndices.add(depIdx);
                        inDegree[index]++;
                    }
                });
                graph.set(index, depIndices);
            });

            // Step 2: Topological sort with cycle handling
            const queue = [];
            inDegree.forEach((deg, idx) => {
                if (deg === 0) queue.push(idx);
            });

            const sorted = [];
            const visited = new Set();

            while (queue.length > 0) {
                const current = queue.shift();
                sorted.push(current);
                visited.add(current);

                for (const [node, deps] of graph.entries()) {
                    if (deps.has(current)) {
                        deps.delete(current);
                        inDegree[node]--;
                        if (inDegree[node] === 0 && !visited.has(node)) {
                            queue.push(node);
                        }
                    }
                }
            }

            // Step 3: Handle cycles (add unvisited nodes at the end)
            for (let i = 0; i < rules.length; i++) {
                if (!visited.has(i)) {
                    console.warn(`Cycle detected involving rule index ${i}`);
                    sorted.push(i);
                }
            }

            return sorted.map(i => rules[i]);
        }

        handleTabChange=async (e, {activeIndex})=>{
            if (!this.state.dynamicStores[activeIndex].startsWith('Scheduling_')) {
                const subTabNames = this.getSubTabNames(this.state.dynamicStores[activeIndex]); 
                let impactedSubTabs = JSON.parse(JSON.stringify(this.state.impactedSubTabs));
                impactedSubTabs.indexOf(`${this.state.dynamicStores[activeIndex]}-${subTabNames[0].tabInPage}`) !== -1 && impactedSubTabs.splice(impactedSubTabs.indexOf(`${this.state.dynamicStores[activeIndex]}-${subTabNames[0].tabInPage}`),1);
                if (impactedSubTabs.filter(item => item.includes(`${this.state.dynamicStores[activeIndex]}-`)).length === 0) {
                    await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[activeIndex]}-${subTabNames[0].tabInPage}`);
                }
            }
            if (this.state.dynamicStores[this.state.activeIndex].startsWith('Scheduling_') && this.state.dynamicStores[activeIndex].startsWith('Scheduling_')) {
                await this.setState({ activeIndex: 0 }, () => this.setState({activeIndex}));
            } else {
                this.setState({ activeIndex });
            }
        }

        getSubTabNames(storeLabel) {
            if (!storeLabel.startsWith('Scheduling_')) {
                const storeValues = Object.values(this.props.globalStore.dynamicStores[storeLabel].items);
                if(storeValues.length === 0) {
                    return [];
                }

                storeValues.sort((a, b) => a.index - b.index);

                const result = [];
                storeValues.forEach(obj => {
                    const objTabName = obj.tabInPage ? obj.tabInPage : storeLabel;
                    let objectsInTab = result.findLast(x => objTabName === x.tabInPage);

                    if(objectsInTab) {
                        objectsInTab.objects.push(obj);
                    }
                    else {
                        objectsInTab = {tabInPage: objTabName, objects: [obj]};
                        result.push(objectsInTab);
                    }
                });

                return result;
            }
        }

        handleKeyDown = async (e, impactedTabsFlag, storeName, activeIndex) => {
            if(e.key === 'Enter') {
                e.preventDefault();
                const subTabNames = this.getSubTabNames(this.state.dynamicStores[this.state.activeIndex]); 
                subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[this.state.activeIndex]}-${subTabNames[0].tabInPage}`);
                impactedTabsFlag && await this.removeFromImpactedTabs(storeName);
                this.handleTabChange({},{ activeIndex });
            }
        } 

        removeFromImpactedTabs = async (tabName) => {            
            let impactedTabsArray = [];
            this.state.impactedSubTabs.map(item => {
                let splittedItem = item.split('-');
                impactedTabsArray.indexOf(splittedItem[0]) === -1 && impactedTabsArray.push(splittedItem[0]);
            });
            if(impactedTabsArray.indexOf(tabName) === -1 || tabName === TERMINAL_ASSIGNMENT) {
                let impactedTabs = [...this.state.impactedTabs];
                let impactedSubTabs = [...this.state.impactedSubTabs];
                if(tabName === TERMINAL_ASSIGNMENT) {
                    impactedSubTabs.indexOf(TERMINAL_ASSIGNMENT) !== -1 && impactedSubTabs.splice(impactedSubTabs.indexOf(TERMINAL_ASSIGNMENT),1);
                    TerminalAssignmentValueRules.clearImpactedTab();
                    TerminalAssignmentValueRules.clearImpactedSubTab();
                } 
                impactedTabs.indexOf(tabName) !== -1 && impactedTabs.splice(impactedTabs.indexOf(tabName),1);
                this.setState({ impactedTabs, impactedSubTabs });
                CommonVisibilityRules.updateImpactedTab(impactedTabs);
            }
        }

        removeFromImpactedSubTabs = async (tabName) => {
            await CommonVisibilityRules.clearImpactedSubTab(tabName);
            let impactedSubTabs = [...this.state.impactedSubTabs];
            impactedSubTabs.indexOf(tabName) !== -1 && impactedSubTabs.splice(impactedSubTabs.indexOf(tabName), 1);
            this.setState({ impactedSubTabs }, () => this.checkAndUpdateImpactedTabs());
        }

        checkAndUpdateImpactedTabs = () => {
            let impactedTabs = [];
            this.state.impactedSubTabs.map(item => {
                let splittedItem = item.split('-');
                impactedTabs.indexOf(splittedItem[0]) === -1 && impactedTabs.push(splittedItem[0]);
            });
            this.setState({ impactedTabs });
            CommonVisibilityRules.updateImpactedTab(impactedTabs);
        }

        handleClickSave = (event) => {
            event.preventDefault();
            this.saveConfigData();
        }

        handleDynamicStoreChanged = (dynamicStoreName, changedData) => {
            const {changedDynamicStores} = this.state;
            let scheduling = this.state.scheduling;
            if(!changedDynamicStores.includes(dynamicStoreName)) {
                changedDynamicStores.push(dynamicStoreName);
                this.setState({changedDynamicStores: [...changedDynamicStores]});
            }

            if(dynamicStoreName === SCHEDULING) {                
                scheduling[changedData.scheduleIndex] = changedData;                
            }

            this.setState({ scheduling }, () => this.checkForDuplicateHolidaysExist());
        }

        checkForDuplicateHolidaysExist = () => {
            let disableSaveButton = false;
            this.state.scheduling.map(schedule => {
                let holidaysList = [...schedule.Holidays];                
                holidaysList.map((item, index) => {
                    holidaysList.map((innerItem, innerIndex) => {
                        const isRecurringDuplicate = item.holidayType === 'recurringDaysEveryYear' &&
                            index !== innerIndex &&
                            item.holidayType === innerItem.holidayType && 
                            item.week === innerItem.week && 
                            item.weekday === innerItem.weekday && 
                            item.month === innerItem.month;
                        const isSpecificDateDupicate = item.holidayType === 'specificDateEveryYear' &&
                                index !== innerIndex &&
                                item.holidayType === innerItem.holidayType && 
                                item.month === innerItem.month &&
                                item.date === innerItem.date;
                        const isDateRangeDuplicate = item.holidayType === 'dateRangeEveryYear' &&
                                index !== innerIndex &&
                                item.holidayType === innerItem.holidayType && 
                                item.fromMonth === innerItem.fromMonth &&
                                item.fromDate === innerItem.fromDate && 
                                item.toMonth === innerItem.toMonth &&
                                item.toDate === innerItem.toDate;
                        if(isRecurringDuplicate || isSpecificDateDupicate || isDateRangeDuplicate) {
                            disableSaveButton = true;  
                        }
                    });
                });                
            });
            this.disableSaveButtonHanlder(disableSaveButton, honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ResolveDuplicateHolidaysToEnableTheSaveButton'));                
        }

        updateImpactedTabs = (impactedTabs, impactedSubTabs) => {           
            this.setState({ impactedTabs, impactedSubTabs});
        }

        handleMeasurementTypeChanged = (measurementTypeValue, measurementType) => {
            const dynamicStores = this.props.globalStore.dynamicStores;
            let unitConversionObj = {};
            Object.keys(dynamicStores).filter(s=>s !== SCHEDULING && s !== TERMINAL_ASSIGNMENT).forEach(storeName => {
                const store = dynamicStores[storeName];
                const unitConversionList = [];
                Object.keys(store.items).forEach(propertyKey => {
                    const propertyItem = store.items[propertyKey];
					// 2 corresponds to the unit group for the measure type widget
                    if (propertyItem.unit && propertyItem.unitGroup === 2) {
                        unitConversionList.push(propertyItem);
                    }
                });
                unitConversionObj[storeName] = unitConversionList;
            });

            const jsonObj = {
                deviceHandle: this.props.deviceHandle,
                valueObject: unitConversionObj,
                measurementType: { [measurementType]: measurementTypeValue }
            };
            return HoneywellDeviceWizardRPC.invokeRPC('convertUnits', [{ myJSON: jsonObj }]).then(resp => {
                console.log("data after converted units", resp);
                if(resp && resp.valueObject) {
                    let changedDynamicStores = [];
                    Object.keys(resp.valueObject).forEach(storeName => {
                        if(resp.valueObject[storeName].length) {
                            resp.valueObject[storeName].forEach(propertyValue => {
                                const name = propertyValue.name;
                                let propInGlobalStore = dynamicStores[storeName].items[name];
                                propInGlobalStore.highPrecisionValue = propertyValue.highPrecisionValue;
                                propInGlobalStore.highPrecisionMin = propertyValue.highPrecisionMin;
                                propInGlobalStore.highPrecisionMax = propertyValue.highPrecisionMax;
                                propInGlobalStore.highPrecisionDeadband = propertyValue.highPrecisionDeadband;
                                propInGlobalStore.value = propertyValue.value;
                                propInGlobalStore.min = propertyValue.min;
                                propInGlobalStore.max = propertyValue.max;
                                propInGlobalStore.unitName = propertyValue.unitName;
                                propInGlobalStore.unit = propertyValue.unit;
                                propInGlobalStore.step = propertyValue.step;
                                propInGlobalStore.deadband = propertyValue.deadband;
                                propInGlobalStore.changed = true;
                            });
                            changedDynamicStores.push(storeName);
                            this.setState({changedDynamicStores: [...changedDynamicStores]});
                        }
                    });
                }


            });
        }

		handleAirflowUnitChanged = (airflowUnitValue, airflowUnit) => {
		    const dynamicStores = this.props.globalStore.dynamicStores;
		    let unitConversionObj = {};
		    Object.keys(dynamicStores).filter(s=>s !== SCHEDULING && s !== TERMINAL_ASSIGNMENT).forEach(storeName => {
		        const store = dynamicStores[storeName];
		        const unitConversionList = [];
		        Object.keys(store.items).forEach(propertyKey => {
		            const propertyItem = store.items[propertyKey];
					// 1 corresponds to the unit group for the airflow unit widget
		            if (propertyItem.unit && propertyItem.unitGroup === 1) {
		                unitConversionList.push(propertyItem);
		            }
		        });
		        unitConversionObj[storeName] = unitConversionList;
		    });

            const jsonObj = {
                deviceHandle: this.props.deviceHandle,
                valueObject: unitConversionObj,
                airflowUnit: { [airflowUnit]: airflowUnitValue }
            };
            return HoneywellDeviceWizardRPC.invokeRPC('convertAirflowUnits', [{ myJSON: jsonObj }]).then(resp => {
                console.log("data after converted units", resp);
                if(resp && resp.valueObject) {
                    let changedDynamicStores = [];
                    Object.keys(resp.valueObject).forEach(storeName => {
                        if(resp.valueObject[storeName].length) {
                            resp.valueObject[storeName].forEach(propertyValue => {
                                const name = propertyValue.name;
                                let propInGlobalStore = dynamicStores[storeName].items[name];
                                propInGlobalStore.highPrecisionValue = propertyValue.highPrecisionValue;
                                propInGlobalStore.highPrecisionMin = propertyValue.highPrecisionMin;
                                propInGlobalStore.highPrecisionMax = propertyValue.highPrecisionMax;
                                propInGlobalStore.highPrecisionDeadband = propertyValue.highPrecisionDeadband;
                                propInGlobalStore.value = propertyValue.value;
                                propInGlobalStore.min = propertyValue.min;
                                propInGlobalStore.max = propertyValue.max;
                                propInGlobalStore.unitName = propertyValue.unitName;
                                propInGlobalStore.unit = propertyValue.unit;
                                propInGlobalStore.step = propertyValue.step;
                                propInGlobalStore.deadband = propertyValue.deadband;
                                propInGlobalStore.changed = true;
                            });
                            changedDynamicStores.push(storeName);
                            this.setState({changedDynamicStores: [...changedDynamicStores]});
                        }
                    });
                }


            });
        }

        disableSaveButtonHanlder = (disableSaveButton, disableSaveButtonMsg) => {
            this.setState({ disableSaveButton, disableSaveButtonMsg });
        }

        saveConfigData = () => {
            this.setState({saving: true, loading: true});

            const saveObjects = this.buildSaveObjects();
            const jsonObj = this.state.mainTabIndexesChanged ? {
                deviceHandle: this.props.deviceHandle,
                valueObject: saveObjects,
                mainTabIndexes: this.state.dynamicStores,
                wizardSaveOption: this.state.wizardSaveOption,
            } : {
                deviceHandle: this.props.deviceHandle,
                valueObject: saveObjects,
                wizardSaveOption: this.state.wizardSaveOption,
            };

            let params = [{ myJSON: jsonObj }];

            if (this.state.wizardSaveOption === '') {
                HoneywellDeviceWizardRPC.invokeRPC('getTeachDetailsToShowUserOnSave', params).then(outerResp => {                    
                    if (outerResp === 0) {
                        params[0].myJSON.wizardSaveOption = 0;
                        this.setState({ wizardSaveOption: outerResp });
                        return this.saveDynamicStoreValues(params, saveObjects);
                    } else {
                        this.setState({ wizardSaveOption: outerResp, wizardSaveOptionModal: true, loading: false, saving: false });
                    }
                });
            } else {
                return this.saveDynamicStoreValues(params, saveObjects);
            }
        }
        
        saveDynamicStoreValues(params, saveObjects) {
            return HoneywellDeviceWizardRPC.invokeRPC('saveDynamicStoreValues', params).then(resp=>{
                let errorModal = resp !== '' ? true : false;
                console.log("Number of dynamic store values saved: " + resp);
                Object.keys(saveObjects).forEach(storeName => {
                    storeName !== SCHEDULING && storeName !== TERMINAL_ASSIGNMENT && saveObjects[storeName].forEach(propertyValue => {
                        const propertyName = propertyValue.propertyName;
                        const store = this.props.globalStore.dynamicStores[storeName];
                        store.items[propertyName].changed = false;
                    });
                    storeName === TERMINAL_ASSIGNMENT && this.props.globalStore.terminalStore.terminals.forEach(terminal => {
                        terminal.changed = false;
                    });
                });
                this.setState({saving: false, loading: false, changedDynamicStores: [], errorInfo: resp, errorModal, mainTabIndexesChanged: false, wizardSaveOption: ''});
            });
        }


        buildSaveObjects() {
            const dynamicStores = this.props.globalStore.dynamicStores;
            const storeObject = {};
            Object.keys(dynamicStores).forEach(storeName => {
                const store = dynamicStores[storeName];

                const propertyValues = [];
                storeName !== 'Scheduling' && Object.keys(store.items).forEach(propertyKey => {
                    const propertyItem = store.items[propertyKey];
                    if(propertyItem.componentType === "HeadingLabel" || propertyItem.componentType === "ButtonComponent" || propertyItem.componentType === "StaticLabel") {
                        return;
                    }

                    // for the hiden items, shall we save the value to default or just leave it as is???
//                    if(propertyItem.visible) {
//                        return propertyValues.push({"propertyName": propertyKey, "value": this.getSaveValue(propertyItem.value, propertyItem.saveConvert)});
//                    }
//                    else {
//                        return propertyValues.push({"propertyName": propertyKey, "value": this.getSaveValue(propertyItem.defaultValue, propertyItem.saveConvert)});
//                    }

                    if(propertyItem.changed) {
                        propertyValues.push({
                            "propertyName": propertyKey, 
                            "value": this.getSaveValue(propertyItem.value, propertyItem.saveConvert), 
                            "unit": propertyItem.unit,
                            "step": propertyItem.step,
                            "min": propertyItem.min,
                            "max": propertyItem.max,
                            "deadband": propertyItem.deadband,
                            "highPrecisionValue": propertyItem.highPrecisionValue,
                            "highPrecisionMin": propertyItem.highPrecisionMin,
                            "highPrecisionMax": propertyItem.highPrecisionMax,
                            "highPrecisionDeadband": propertyItem.highPrecisionDeadband,
                            "unitName": propertyItem.unitName,
                            "precision": propertyItem.precision
                        });
                    }
                });

                if(propertyValues.length > 0) {
                    storeObject[storeName] = propertyValues;
                }

                if(this.state.changedDynamicStores.indexOf(SCHEDULING) !== -1) {
                    let scheduling = JSON.parse(JSON.stringify(this.state.scheduling));
                    for(let i=0; i<scheduling.length; i++) {			
                        scheduling[i].Holidays = this.groupByKey(scheduling[i].Holidays, 'holidayType');   
                        storeObject.Scheduling = scheduling;
                    }                                  
				}
                if(this.state.changedDynamicStores.indexOf(TERMINAL_ASSIGNMENT) !== -1) {
                    const terminalStore = this.props.globalStore.terminalStore;
                    storeObject[TERMINAL_ASSIGNMENT] = terminalStore.terminals.map(terminal =>
                        ({ "pinName": terminal.terminalName, "fbName": terminal.terminalAssignedName })
                    );
                }
            });            

            return storeObject;
        }

        groupByKey(arr, key) {
            return arr.reduce((result, item) => {
                // Check if the key already exists in the result, if not, create it
                if (!result[item[key]]) {
                    result[item[key]] = [];
                }
                // Push the item into the array corresponding to its key
                result[item[key]].push(item);
                return result;
            }, {});
        }

        buildTerminalStore(isReadOnly) {
            const {globalStore} = this.props;
            const dynamicStore = globalStore.dynamicStores[TERMINAL_ASSIGNMENT];
            if (dynamicStore) {
                let terminals = Object.keys(dynamicStore.items || {}).filter(i => i !== "index" && i !== "readOnly" && !i.startsWith("svg")).map((key, index) => {
                    let temp = baja.SlotPath.unescape(key).split('~');
                    let terminalName = temp[0];
                    let terminalOriginName = key.split(/\$7e/)[0];
                    let terminalAssignedName = temp[1];
                    let terminalOptions = Object.keys(dynamicStore.items[key] || {}).filter(i => i !== "index" && i !== "readOnly").map((item, index) => ({
                        key: baja.SlotPath.unescape(item),
                        text: baja.SlotPath.unescape(item),
                        value: baja.SlotPath.unescape(item)
                    }));
                    return { terminalName, readOnly: isReadOnly, terminalAssignedName, changed:false, terminalOptions: [{ key: "Unassigned", text: "Unassigned", value: "Unassigned" }, ...terminalOptions] };
                });
                const svgFile = dynamicStore.items["svg"]["svgFile"];
                globalStore.terminalStore = { terminals, svgFile };
            }
        }

        getSaveValue(rawValue, convert) {
            let saveValue = convert ? convert(rawValue).toString() : rawValue.toString();
            return saveValue;
        }
        
        getTabPanes() {
            const { dynamicStores } = this.state;
            const { globalStore } = this.props;

            const dynamicTabs = dynamicStores.flatMap((storeName, index) => {

                let storeData = null;

                if(storeName.startsWith('Scheduling_')) {
                    let scheduleData = globalStore.dynamicStores.Scheduling.filter(item => item.label === storeName.split('Scheduling_')[1]);
                    storeData = scheduleData[0];
                } else {                
                    storeData = globalStore.dynamicStores[storeName];
                }

                if (storeData.componentType === 'ScheduleWidget') {
                        let scheduleIndex = 0;
                        globalStore.dynamicStores.Scheduling.map((item, index) => {
                            if(item.label === storeName.split('Scheduling_')[1]) {
                                scheduleIndex = index;
                            }
                        });
                        return [this.getTabPanesManipulations(storeData, storeName, index, scheduleIndex)];
                } else {
                    return [this.getTabPanesManipulations(storeData, storeName, index)];
                }
            });

            return dynamicTabs;
        }

        onDragStart = (index) => {
            this.setState({ draggedTabIndex: index });
        };

        onDragOver = (e, index) => {
            e.preventDefault();
            const { draggedTabIndex, dynamicStores, activeIndex } = this.state;
            const activeTab = dynamicStores[activeIndex];

            if (index !== draggedTabIndex) {
                const updatedTabs = [...dynamicStores];
                const [draggedTab] = updatedTabs.splice(draggedTabIndex, 1);
                updatedTabs.splice(index, 0, draggedTab);
                let currentActiveTabIndex = updatedTabs.indexOf(activeTab);

                this.setState({ dynamicStores: updatedTabs, activeIndex: currentActiveTabIndex, mainTabIndexesChanged: true, draggedTabIndex: index });
            }
        };

        onDragEnd = () => {
            this.setState({ draggedTabIndex: null });
        };

        getTabPanesManipulations(store, storeName, index, scheduleIndex=0) {
            const { activeIndex, impactedTabs, impactedSubTabs, scheduling, draggedTabIndex } = this.state;
            return {
                menuItem:
                    impactedTabs.indexOf(storeName) !== -1 ? (
                        <MenuItem key={storeName+index} className="vertical-tab-menu" onClick={async () => { const subTabNames = this.getSubTabNames(this.state.dynamicStores[this.state.activeIndex]); 
                                subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[this.state.activeIndex]}-${subTabNames[0].tabInPage}`);
                                await this.removeFromImpactedTabs(storeName);
                            }}
                            draggable
                            onDragStart={() => this.onDragStart(index)}
                            onDragOver={(e) => this.onDragOver(e, index)}
                            onDrop={this.onDragEnd}
                            style={{
                                cursor: draggedTabIndex === null ? 'pointer' : 'grab',
                                transition: 'transform 200ms ease',
                                position: 'relative',
                            }}
                        >
                            {activeIndex === index ? <span className="left-arrow"></span>: <></>}
                            <span className="vertical-tab-menu-item" 
                                // tabIndex={index+100}
                                onKeyDown={e => this.handleKeyDown(e, true, storeName, index)}>{store.label}
                            </span>
                            <span className="dot"></span>
                        </MenuItem>
                    ) : <MenuItem key={storeName+index} className="vertical-tab-menu" onClick={async () => { const subTabNames = this.getSubTabNames(this.state.dynamicStores[this.state.activeIndex]); 
                        subTabNames && await this.removeFromImpactedSubTabs(`${this.state.dynamicStores[this.state.activeIndex]}-${subTabNames[0].tabInPage}`);
                        }}
                        draggable
                        onDragStart={() => this.onDragStart(index)}
                        onDragOver={(e) => this.onDragOver(e, index)}
                        onDrop={this.onDragEnd}
                         style={{
                                cursor: draggedTabIndex === null ? 'pointer' : 'grab',
                                transition: 'transform 200ms ease',
                                position: 'relative',
                            }}
                        >
                            {activeIndex === index ? <span className="left-arrow"></span>: <></>}
                            <span className="vertical-tab-menu-item" 
                                onKeyDown={e => this.handleKeyDown(e, false, storeName, index)} 
                                // tabIndex={index+100}
                                >{store.label}
                            </span>
                        </MenuItem>,
                render: () => (
                    <Tab.Pane>
                        {this.renderTab(storeName, impactedSubTabs, scheduling, scheduleIndex)}
                    </Tab.Pane>
                )
            };
        }
        renderTab(storeName, impactedSubTabs, scheduling, scheduleIndex) {
            if(storeName.startsWith('Scheduling_')) {
                return (<Scheduling onDynamicPageChanged={this.handleDynamicStoreChanged} saving={this.state.saving} globalStore={this.props.globalStore}
                                                   disableSaveButtonHanlder={this.disableSaveButtonHanlder} scheduling={scheduling[scheduleIndex]} readOnly={this.isReadOnly} scheduleIndex={scheduleIndex}/>);
            } else if (storeName === TERMINAL_ASSIGNMENT){
                return (<TerminalAssignment deviceHandle={this.props.deviceHandle}  onDynamicPageChanged={this.handleDynamicStoreChanged} key={storeName} dynamicStoreName={storeName} terminalStore={this.props.globalStore.terminalStore}></TerminalAssignment>);
            } else{
                return (<DynamicPage key={storeName} dynamicStoreName={storeName} globalStore={this.props.globalStore} removeFromImpactedSubTabs={this.removeFromImpactedSubTabs}
                                            updateImpactedTabs={this.updateImpactedTabs} impactedSubTabs={impactedSubTabs} onDynamicPageChanged={this.handleDynamicStoreChanged} onApplyRuleChanged={this.handleTerminalRuleChanged}
                                            onMeasurementTypeChanged={this.handleMeasurementTypeChanged} 
                                            onAirflowUnitChanged={this.handleAirflowUnitChanged} 
                                            unitComponentInTab={this.state.unitComponentInTab} 
                                            unitComponentKey={this.state.unitComponentKey}
                                            airflowUnitComponentInTab={this.state.airflowUnitComponentInTab}
                                            airflowUnitComponentKey={this.state.airflowUnitComponentKey}
                                        />);
            }
        }

        renderMainContent(){
            const {activeIndex, saving, changedDynamicStores, impactedTabs, disableSaveButton, disableSaveButtonMsg, mainTabIndexesChanged} = this.state;
            return (
                <div style={{width:'100%',height:'100%'}}>
                    <Tab
                        menu={{ vertical: true }}
                        grid={{paneWidth: 13, tabWidth: 3}}
                        onTabChange={this.handleTabChange}
                        renderActiveOnly
                        activeIndex={activeIndex}
                        panes={this.getTabPanes()}
                    />
                    <div className="button-group" title={impactedTabs.length > 0 ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.VisitImpactedTabsToEnableTheSaveButton') : disableSaveButton ? disableSaveButtonMsg : ''}>
                        <Button className="ui button" tabIndex="99" loading={saving} disabled={(changedDynamicStores.length === 0 && !mainTabIndexesChanged) || disableSaveButton || impactedTabs.length > 0} onClick={this.handleClickSave}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SAVE')}</Button>
                    </div>
                    <Modal
                        closeOnDimmerClick={false}
                        open={this.state.errorModal}
                        size='small'
                        onClose={() => this.setState({errorModal: false, errorInfo: ''})}
                    >
                            <ModalHeader>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Information')}</ModalHeader>
                            <ModalContent className='honeywell-device-wizard error-info'>{this.state.errorInfo}</ModalContent>
                            <ModalActions>
                                <Button className='error-modal-close-button' onClick={() => this.setState({errorModal: false, errorInfo: ''})}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CLOSE')}</Button>
                            </ModalActions>
                    </Modal>
                    <Modal
                        closeOnDimmerClick={false}
                        open={this.state.wizardSaveOptionModal}
                        size='tiny'
                        onClose={() => this.setState({wizardSaveOptionModal: false, loading: false, saving: false })}
                    >
                            <ModalHeader>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Information')}</ModalHeader>
                            <ModalContent className='honeywell-device-wizard'>
                                <div>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ControllerNotInSyncInfo')}</div>
                                <div>
                                    <DropdownFloatLabel
                                        key={`wizardSaveOption`}
                                        name={'wizardSaveOption'}
                                        className={'wizard-save-option'}
                                        clearable={false}
                                        dataObj={null}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.WizardSaveOption')}
                                        tooltip={''}
                                        visible={true}
                                        options={[
                                                    {
                                                        "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.OverwriteBACnetObjectValuesInDevice'),
                                                        "value": 1,
                                                        "key": 1
                                                    },
                                                    {
                                                        "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.OverwriteBACnetObjectValuesInApplication'),
                                                        "value": 2,
                                                        "key": 2
                                                    }
                                                ] || []}
                                        value={this.state.wizardSaveOption}
                                        onChange={(e, data) => this.setState({ wizardSaveOption: data.value })}
                                    />
                                </div>
                            </ModalContent>
                            <Modal.Actions>
                                <Button basic onClick={() => this.setState({ wizardSaveOptionModal: false, saving: false, loading: false, wizardSaveOption: '' })} color="black" style={{position: "absolute", left: "2%", borderRadius: "4px", height: '37px', fontSize: '12px'}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CANCEL')}</Button>
                                <Button onClick={() => this.setState({ wizardSaveOptionModal: false }, () => this.saveConfigData())} style={{backgroundColor: "#606060", borderRadius: "4px", marginRight: '20px', fontSize: '12px', fontWeight: '500', height: '37px', color: 'white'}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SAVE')}</Button>
                            </Modal.Actions>
                    </Modal>
                </div>
            );
        }
        showTerminalRuleErrors = () => {
            const { terminalRuleErrors } = this.state;
            return `${terminalRuleErrors.join(";")}`;

        }
        closeRuleErrorDialog = () => {
            this.setState({ terminalRuleErrors: [] });
        }
        handleTerminalRuleChanged = (errors, changedTerminals) => {
            if(errors && errors.length) {
                this.setState({ terminalRuleErrors: errors });
            }
            if(changedTerminals && changedTerminals.length){
                this.setState({ changedDynamicStores: [...this.state.changedDynamicStores, TERMINAL_ASSIGNMENT]});
            }
        }


        render() {
            const { loading, terminalRuleErrors, saving, wizardValidationMessage } = this.state;
            return !wizardValidationMessage ? (
                <div className="honeywell-device-wizard">
                    <div>
                        <Menu
                            borderless={"true"}
                            className="menu"
                            inverted
                            style={{ minHeight: 50, borderRadius: '0px', margin: '0px', width: "100%" }}
                        >
                            <Menu.Item name='honeywell'>
                                <HoneywellLogo />
                            </Menu.Item>
                            <div className="verticalLine"></div>
                            <Menu.Item name='Thermo'>
                                <span
                                    id="thermoTitle"
                                    style={{ color: "white" }}
                                >Honeywell Device Configuration Wizard</span>
                            </Menu.Item>
                        </Menu>
                    </div>

                    <Segment className="rightMain" style={{ borderRadius: '0px', width: "100%" }} >
                        <Dimmer active={loading} inverted>
                            <Loader inverted>{saving ? 'Saving in progress' : 'Loading'}</Loader>
                        </Dimmer>
                        {this.renderMainContent()}
                    </Segment>
                    {terminalRuleErrors && <ModalDialog show={terminalRuleErrors.length} title="Warning" message={this.showTerminalRuleErrors()} subMessage="Remove or modify already configured pins to make new configurations." onOk={this.closeRuleErrorDialog} okButtonText="Ok"/>}
                </div>
            ) : (
                <div className="honeywell-device-wizard-home-alert-page">
                    <div className="primary-message">Honeywell Device Wizard can not be loaded. </div>
                    <div>Message: {wizardValidationMessage}</div>
                </div>
            );
        }
    }

    return  WizardHomeProfile;
});