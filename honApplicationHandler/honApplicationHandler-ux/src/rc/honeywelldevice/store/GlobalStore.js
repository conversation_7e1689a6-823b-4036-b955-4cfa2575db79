define([], function () {
    "use strict";

    class GlobalStore {

        constructor() {
            this.dynamicStores = {};
            this.wizardRules = {};
        }

        convertDynamicStoreValues(isReadOnly) {
            if (!this.dynamicStores) {
                return;
            }

            Object.values(this.dynamicStores).forEach(store => {
                Object.values(store.items || []).forEach(obj => {
                    if (obj.loadConvert) {
                        obj.loadConvert = eval("let func = " + obj.loadConvert + "; func;");
                        obj.value = obj.loadConvert(obj.value);
                        obj.defaultValue = obj.loadConvert(obj.defaultValue);
                    } else {
                        if (!isNaN(Number(obj.value))) {
                            obj.value = Number(obj.value);
                        }
                        if (!isNaN(Number(obj.defaultValue))) {
                            obj.defaultValue = Number(obj.defaultValue);
                        }
                    }
                    if (obj.saveConvert) {
                        obj.saveConvert = eval("let func = " + obj.saveConvert + "; func;");
                    }
                    if (isReadOnly === true) {
                        obj.readOnly = isReadOnly;
                    }
                });
            });
        }

    }

    return GlobalStore;
});
