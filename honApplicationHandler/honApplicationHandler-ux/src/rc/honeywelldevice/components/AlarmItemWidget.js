define([
    'react',
    'semantic-ui-react',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min' ],
    (React, SemanticReact) => {
        'use strict';
        const {
            Grid, Popup
        } = SemanticReact;

        class AlarmItemWidget extends React.Component {
            constructor(props) {
                super(props);
            }
            render() {
                const { valueText, alarmName, tooltip, visible } = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";
                const svgIcon = (
                    <svg xmlns="http://www.w3.org/2000/svg" color="blue" width="15" height="12" viewBox="0 0 24 24"><path fill="steelblue" d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm1 18h-2v-8h2v8zm-1-12.25c.69 0 1.25.56 1.25 1.25s-.56 1.25-1.25 1.25-1.25-.56-1.25-1.25.56-1.25 1.25-1.25z" /></svg>
                );
                let column = null;
                if (valueText == "Inactive" || valueText == "Normal") {
                    column = (
                        <>
                            <Grid.Column >
                                <label style={{ fontFamily: "Honeywell Sans Web" }}>{alarmName}</label>
                            </Grid.Column >
                            <Grid.Column>
                                <label style={{ fontFamily: "Honeywell Sans Web" }} >{valueText}</label>
                            </Grid.Column>
                        </>
                    );

                } else if (valueText == "Active(High)") {
                    let tooltipElem = (<div style={{ whiteSpace: "pre-line" }}>
                        {tooltip}
                    </div>);

                    column = (
                        <>
                            <Popup
                                trigger={<Grid.Column >
                                    <label style={{ fontFamily: "Honeywell Sans Web" }}>{alarmName} <>{svgIcon}</></label>
                                </Grid.Column >
                                }
                                content={tooltipElem}
                                basic
                            />

                            <Grid.Column>
                                <label style={{ fontFamily: "Honeywell Sans Web", color: "#ee3124" }} >{valueText}</label>
                            </Grid.Column>
                        </>
                    );

                } else {
                    var tooltipElem = (<div style={{ whiteSpace: "pre-line" }}>
                        {tooltip}
                    </div>);
                    column = (
                        <>
                            <Popup
                                trigger={<Grid.Column >
                                    <label style={{ fontFamily: "Honeywell Sans Web" }}>{alarmName}
                                        <>{svgIcon}</>
                                    </label>
                                </Grid.Column >

                                }
                                content={tooltipElem}
                                basic
                            />

                            <Grid.Column>
                                <label style={{ fontFamily: "Honeywell Sans Web", color: "#f37021" }} >{valueText}</label>
                            </Grid.Column>
                        </>
                    );

                }
                return (
                    <div style={{ marginTop: "10px", marginLeft: "5px", display: display }}>
                        <Grid>
                            <Grid.Row columns={2}>
                                {column}
                            </Grid.Row>
                        </Grid>
                    </div>
                );

            }


        }

        return AlarmItemWidget;
    });
