define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';
        const {
            Input,
            Image, 
            Popup
        } = SemanticReact;

        class TextInputFloatLabel extends React.Component {
            constructor(props) {
                super(props);
                this.inputRef = React.createRef();
            }

            componentDidMount() {
                // Add direct event listener for Escape key as fallback
                if (this.inputRef.current) {
                    // For Semantic UI Input, we need to find the actual input element
                    let inputElement = this.inputRef.current;
                    if (this.inputRef.current.inputRef && this.inputRef.current.inputRef.current) {
                        inputElement = this.inputRef.current.inputRef.current;
                    } else if (this.inputRef.current.querySelector) {
                        const foundInput = this.inputRef.current.querySelector('input');
                        if (foundInput) {
                            inputElement = foundInput;
                        }
                    }
                    
                    if (inputElement && inputElement.addEventListener) {
                        inputElement.addEventListener('keydown', this.handleDirectKeyDown);
                    }
                }
            }

            componentWillUnmount() {
                // Clean up event listener
                if (this.inputRef.current) {
                    let inputElement = this.inputRef.current;
                    if (this.inputRef.current.inputRef && this.inputRef.current.inputRef.current) {
                        inputElement = this.inputRef.current.inputRef.current;
                    } else if (this.inputRef.current.querySelector) {
                        const foundInput = this.inputRef.current.querySelector('input');
                        if (foundInput) {
                            inputElement = foundInput;
                        }
                    }
                    
                    if (inputElement && inputElement.removeEventListener) {
                        inputElement.removeEventListener('keydown', this.handleDirectKeyDown);
                    }
                }
            }

            handleDirectKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleEscapeKey(e);
                }
            }

            handleChange = (e, data) => {
                const {dataObj} = this.props;
                if(this.props.dataObj.value.toString() === data.value) {
                    return;
                }

                const {onChange, name} = this.props;
                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }

            handleBlur = (e) => {
                const {onChange, name, dataObj} = this.props;
                let value = e.target.value;

                if(onChange) {
                    onChange(e, {name: name, dataObj: dataObj, value: value, type: "input"});
                }
            }

            handleKeyPress = (e) => {
                if(e.key === "Enter") {
                    e.preventDefault();
                    this.handleBlur(e);
                }
            }

            handleKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent DynamicPage global handler interference
                    this.handleEscapeKey(e);
                }
            }

            handleEscapeKey = (e) => {
                // Remove focus immediately and visually - force remove CSS focus styling
                const targetElement = e.target;
                targetElement.blur();
                
                // Force remove any lingering focus styles immediately with !important
                targetElement.style.setProperty('outline', 'none', 'important');
                targetElement.style.setProperty('box-shadow', 'none', 'important');
                targetElement.style.setProperty('border', '');
                
                // Clean up the style override after a brief moment
                setTimeout(() => {
                    targetElement.style.setProperty('outline', '');
                    targetElement.style.setProperty('box-shadow', '');
                }, 10);
                
                // Reset tab navigation to first element - simplified approach
                setTimeout(() => {
                    // Simply blur any focused element to reset tab navigation
                    if (document.activeElement && document.activeElement !== document.body) {
                        document.activeElement.blur();
                    }
                }, 1);
            }

            render() {

                const {label, tooltip, value, visible, disabled, defaultValue, readonly, dataObj, width, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <div className="input-container text-input-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled input-label'}/>
                            <Input
                                ref={this.inputRef}
                                tabIndex={display === 'none' ? -1 : index}
                                style={{display: "inline", width: width ?  width : label.length > 30 ? label.length * 7.1 + 'px' : '205.6px'}}
                                value={value}
                                disabled={disabled || (!!readonly)}
                                size="mini-hon"
                                onChange={this.handleChange}
                                onKeyPress={this.handleKeyPress}
                                onKeyDown={this.handleKeyDown}
                                onBlur={this.handleBlur}
                                placeholder={defaultValue}
                            />                            
                        </div>
                        {(dataObj && dataObj.help) &&<Popup
                            trigger={<Image style={{left : label.length > 30 ? -label.length + 'px' : '12px'}} src="/module/honApplicationHandler/rc/images/Help.svg" className='text-input-icon'/>}
                            content={dataObj.help}
                            size='mini'                            
                        />}
                    </div>
                )
            }
        }

        return TextInputFloatLabel;
});