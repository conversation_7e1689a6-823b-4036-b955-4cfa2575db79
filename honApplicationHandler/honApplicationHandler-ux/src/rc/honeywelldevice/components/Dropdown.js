define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
        ConfigLabel
    ) => {
        'use strict';

        var honApplicationHandlerLex = lexicons[0];

        class Dropdown extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj} = this.props;

                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }

            render() {

                const {label, tooltip, value, options, visible, disabled, clearable, readonly, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <SemanticReact.Dropdown
                            tabIndex={display === 'none' ? -1 : index}
                            className="dropdown-mini-hon"
                            fluid={false}
                            disabled={disabled || (!!readonly)}
                            clearable={clearable}
                            selection
                            options={options.filter(option => option.visible || option.visible === undefined || option.value === value)}
                            value={value}
                            onChange={this.handleChange}
                            placeholder={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SelectOneItem')}
                        />
                    </div>
                );
            }
        }

        return Dropdown;
});