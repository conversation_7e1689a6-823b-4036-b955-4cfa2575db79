define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min' ],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        const {
            Label
        } = SemanticReact;

        class StaticLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            render() {

                const { label, tooltip, value, visible } = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{ display: display }}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <Label size="large">{value}</Label>
                    </div>
                );
            }
        }

        return StaticLabel;
});
