define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
        ConfigLabel
    ) => {
        'use strict';
        const {
            Image, 
            Popup
        } = SemanticReact;

        var honApplicationHandlerLex = lexicons[0];
        
        class TimeZoneFloatLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj} = this.props;

                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }            

            render() {

                const {label, tooltip, value, options, visible, disabled, clearable, readonly, dataObj, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <div className="dropdown-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled dropdown-label'}/>
                            <SemanticReact.Dropdown
                                tabIndex={display === 'none' ? -1 : index}
                                className="dropdown-mini-hon timezone-comp"
                                fluid={false}
                                disabled={disabled || (!!readonly)}
                                clearable={clearable}
                                selection
                                options={options.filter(option => option.visible || option.visible === undefined || option.value === value)}
                                value={value}
                                onChange={this.handleChange}
                                placeholder={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SelectOneItem')}
                            />
                        </div>
                        {(dataObj && dataObj.help) && <Popup
                            trigger={<Image src="/module/honApplicationHandler/rc/images/Help.svg" className='dropdown-input-icon'/>}
                            content={dataObj.help}
                            size='mini'                            
                        />}
                    </div>
                );
            }
        }

        return TimeZoneFloatLabel;
});