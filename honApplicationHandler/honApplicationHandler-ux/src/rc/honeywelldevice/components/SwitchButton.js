define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        const {
            Checkbox
        } = SemanticReact;

        class SwitchButton extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj} = this.props;

                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }

            render() {

                const {label, tooltip, value, visible, disabled, readonly} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <Checkbox
                            style={{
                                verticalAlign: "middle"
                            }}
                            className="switchbutton-mini-hon"
                            toggle
                            disabled={disabled || (!!readonly)}
                            checked={value}
                            onChange={this.handleChange}
                        />
                    </div>
                );
            }
        }

        return SwitchButton;
});