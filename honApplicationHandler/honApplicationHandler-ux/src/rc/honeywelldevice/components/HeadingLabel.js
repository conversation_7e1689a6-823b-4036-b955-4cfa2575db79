define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/PopupIcon',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min' ],
    (
        React,
        SemanticReact,
        PopupIcon
    ) => {
        'use strict';
        const {
            Popup,
            Form
        } = SemanticReact;

        class HeadingLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            render() {
                const { visible } = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div style={{ display: display, marginBottom: "13px" }}>
                        <label className="headingLabel">{this.props.label}
                            {
                                this.props.tooltip &&
                                <PopupIcon tooltip={this.props.tooltip} />
                            }
                        </label>
                    </div>
                );
            }
        }

        return HeadingLabel;
});
