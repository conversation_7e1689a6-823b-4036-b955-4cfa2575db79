define([
    'react',
    'nmodule/honApplicationHandler/rc/libs/ReactSlider/ReactSlider',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/UnitConversion',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        ReactSlider,
        ConfigLabel,
        UnitConversion
    ) => {
        'use strict';

        class SingleSlider extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (value, reason) => {
                if(reason.triggeredByUser) {
                    const {name, onChange, dataObj, precision, step} = this.props;
                    if(onChange) {
                        const newValue = UnitConversion.formatNumericValue(precision, value, step);
                        const data = {name: name, dataObj: dataObj, value: newValue};
                        onChange({type: "mousemove"}, data);
                    }
                }
            }

            render() {

                const {label, tooltip, value, visible, disabled, min, max, unit, step, color, readonly, precision, dataObj, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                const formattedValue = UnitConversion.formatNumericValue(precision, value, step);
                const formattedMin = UnitConversion.formatNumericValue(precision, min, step);
                const formattedMax = UnitConversion.formatNumericValue(precision, max, step);

                const configLabel = label + (unit ? (" (" + unit + ")") : "");
                const maxLabel = formattedMax + (unit ? ("" + unit + "") : "");
                const minLabel = formattedMin + (unit ? ("" + unit + "") : "");
                

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel style={{fontFamily: 'Honeywell Sans Web'}} label={configLabel} tooltip={tooltip} help={(dataObj && dataObj.help) ? dataObj.help : ''} />
                        <div className="hon-slider-container">
                            <label style={{ marginRight: "20px", fontFamily: "Honeywell Sans Web", color: '#303030',fontWeight: '500' }}>{minLabel}</label>
                            <ReactSlider
                                disabled={disabled || (!!readonly)}
                                style={{width: "100%", minWidth: "300px", thumb:{backgroundColor: color ? color : "red"}}}
                                value={formattedValue}
                                unit={unit}
                                settings={{start:formattedValue, min: formattedMin, max: formattedMax, step: step === undefined ? 1 : step, onChange: this.handleChange}}
                                index={display === 'none' ? -1 : index}
                            />
                            <label style={{ marginLeft: "20px", fontFamily: "Honeywell Sans Web", color: '#303030',fontWeight: '500' }}>{maxLabel}</label>
                        </div>
                    </div>
                );
            }
        }

        return SingleSlider;
});