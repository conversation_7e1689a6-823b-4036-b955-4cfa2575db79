define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
    ) => {
        'use strict';

        var honApplicationHandlerLex = lexicons[0];

        const {
            Modal,
            Container,
            Button
        } = SemanticReact;

        class ModalDialog extends React.Component {
            constructor(props) {
                super(props);
            }

            handleOk = () => {
                const {onOk} = this.props;

                if(onOk) {
                    onOk();
                }
            }

            handleNo = () => {
                const {onNo} = this.props;

                if(onNo) {
                    onNo();
                }
            }

            handleCancel = () => {
                const {onCancel} = this.props;

                if(onCancel) {
                    onCancel();
                }
            }

            render() {

                const {show, title, message, onOk, onNo, onCancel, okButtonText, noButtonText, cancelButtonText, subMessage} = this.props;

                return (
                    <Modal open={show} >
                        <Modal.Header>{title}</Modal.Header>
                        <Modal.Content>
                            <Container>
                                <p>{message}</p>
                                <p>{subMessage?subMessage: ""}</p>
                            </Container>
                        </Modal.Content>
                        <Modal.Actions>
                            {onOk && <Button onClick={this.handleOk}>{okButtonText ? okButtonText : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Yes')}</Button>}
                            {onNo && <Button onClick={this.handleNo}>{noButtonText ? noButtonText : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.No')}</Button>}
                            {onCancel && <Button onClick={this.handleCancel}>{cancelButtonText ? cancelButtonText : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Cancel')}</Button>}
                        </Modal.Actions>
                    </Modal>
                );
            }
        }

        return ModalDialog;
});