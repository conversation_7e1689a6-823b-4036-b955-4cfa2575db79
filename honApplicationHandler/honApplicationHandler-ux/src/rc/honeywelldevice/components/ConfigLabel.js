define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/PopupIcon',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min' ],
    (
        React,
        SemanticReact,
        PopupIcon
    ) => {
        'use strict';
        const {
            Popup,
            Image
        } = SemanticReact;

        class ConfigLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            render() {
                const stylesObj = {
                    minWidth: "150px",
                    verticalAlign: "middle",
                    fontSize: "0.9rem",
                    minHeight: "20px",
                    fontWeight: "600"
                };
                const styleObjWithPropsStyles = this.props.style ? Object.assign({}, stylesObj, this.props.style) : stylesObj;

                return (
                    <div
                        style={styleObjWithPropsStyles}
                        className={this.props.className ? this.props.className : ''}
                    >
                        {this.props.label}
                        {
                            this.props.help && <Popup
                                trigger={<Image src="/module/honApplicationHandler/rc/images/Help.svg" className='label-icon'/>}
                                content={this.props.help}
                                size='mini'                            
                            />
                        }
                    </div>
                );
            }
        }

        return ConfigLabel;
});
