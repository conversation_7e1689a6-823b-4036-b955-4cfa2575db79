define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        class DateInputFloatLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e) => {
                const {dataObj} = this.props;
                if(this.props.dataObj.value.toString() === e.target.value) {
                    return;
                }

                const {onChange, name} = this.props;
                let data = {};
                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    data.value = e.target.value;
                    onChange(e, data);
                }
            }

            handleBlur = (e) => {
                const {onChange, name, dataObj} = this.props;
                let value = e.target.value;

                if(onChange) {
                    onChange(e, {name: name, dataObj: dataObj, value: value, type: "input"});
                }
            }

            handleKeyPress = (e) => {
                if(e.key === "Enter") {
                    e.preventDefault();
                    this.handleBlur(e);
                }
            }

            handleKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent DynamicPage global handler interference
                    this.handleEscapeKey(e);
                }
            }

            handleEscapeKey = (e) => {
                // Remove focus immediately and visually - force remove CSS focus styling
                const targetElement = e.target;
                targetElement.blur();
                
                // Force remove any lingering focus styles immediately with !important
                targetElement.style.setProperty('outline', 'none', 'important');
                targetElement.style.setProperty('box-shadow', 'none', 'important');
                targetElement.style.setProperty('border', '');
                
                // Clean up the style override after a brief moment
                setTimeout(() => {
                    targetElement.style.setProperty('outline', '');
                    targetElement.style.setProperty('box-shadow', '');
                }, 10);
                
                // Reset tab navigation to first element - simplified approach
                setTimeout(() => {
                    // Simply blur any focused element to reset tab navigation
                    if (document.activeElement && document.activeElement !== document.body) {
                        document.activeElement.blur();
                    }
                }, 1);
            }

            render() {

                const {label, tooltip, value, visible, disabled, defaultValue, readonly} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <div className="input-container text-input-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled input-label'}/>
                            <input
                                style={{display: "inline"}}
                                type="date"
                                data-testid="date-input"
                                value={value}
                                disabled={disabled || (!!readonly)}
                                size="mini-hon"
                                onChange={this.handleChange}
                                onKeyUp={this.handleKeyPress}
                                onKeyDown={this.handleKeyDown}
                                onBlur={this.handleBlur}
                                placeholder={defaultValue}
                            />
                        </div>
                    </div>
                )
            }
        }

        return DateInputFloatLabel;
});