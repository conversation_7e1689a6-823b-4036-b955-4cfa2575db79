define([
    'baja!',
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'
],(
    baja,
    React,
    SemanticReact,
    ConfigLabel,
    DropdownFloatLabel
    
) => {
    'use strict';
    class MeasurementType extends React.Component {
        constructor(props) {
            super(props);
        }

        handleChange = (e, data) => {
            const {name, onChange, dataObj} = this.props;

            if(onChange) {
                data.name = name;
                data.dataObj = dataObj;
                onChange(e, data);
                console.log("MeasurementType: handleChange: data: ", data);
            }

        }
        componentDidMount() {

        }

        render() {

            const {label, tooltip, value, options, visible, disabled, clearable, readonly, index} = this.props;

            return (
                <DropdownFloatLabel
                    index={index}
                    label={label}
                    disabled={disabled || (!!readonly)}
                    tooltip={tooltip}
                    clearable={clearable}
                    visible={visible}
                    options={options.filter(option => option.visible || option.visible === undefined || option.value === value)}
                    value={value}
                    onChange={this.handleChange}
                    placeholder="Select One Item"
                />
            );
        }
    }

    return MeasurementType;
});