define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        const {
            Checkbox
        } = SemanticReact;

        class RadioButtonGroup extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj} = this.props;

                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }

            renderRadioButtons() {
                // options prototype: [{key: number|string, text: string, value: number|string}]
                const {options, group, value, readonly} = this.props;

                const radioButtons = options.map(option => {
                    return (
                        <Checkbox
                            className="hon-radio-button"
                            key={option.key}
                            radio
                            label={option.text}
                            name={group}
                            value={option.value}
                            disabled={(!!readonly)}
                            checked={value === option.value}
                            onChange={this.handleChange}
                        />
                    );
                });

                return radioButtons;
            }

            render() {

                const {label, tooltip, visible} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        {this.renderRadioButtons()}
                    </div>
                );
            }
        }

        return RadioButtonGroup;
});