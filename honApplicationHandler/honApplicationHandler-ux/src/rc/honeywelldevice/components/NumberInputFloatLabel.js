define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/UnitConversion',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel,
        UnitConversion
    ) => {
        'use strict';
        const {
            Input,
            Image, 
            Popup
        } = SemanticReact;

        class NumberInputFloatLabel extends React.Component {
            constructor(props) {
                super(props);
                this.inputRef = React.createRef();
                this.isWorkbench = window.location.href.toString().indexOf("bajaux") !== -1;
            }

            componentDidMount() {
                // Add direct event listener for Escape key as fallback
                if (this.inputRef.current) {
                    // For Semantic UI Input, we need to find the actual input element
                    let inputElement = this.inputRef.current;
                    if (this.inputRef.current.inputRef && this.inputRef.current.inputRef.current) {
                        inputElement = this.inputRef.current.inputRef.current;
                    } else if (this.inputRef.current.querySelector) {
                        const foundInput = this.inputRef.current.querySelector('input');
                        if (foundInput) {
                            inputElement = foundInput;
                        }
                    }
                    
                    if (inputElement && inputElement.addEventListener) {
                        inputElement.addEventListener('keydown', this.handleDirectKeyDown);
                    }
                }
            }

            componentWillUnmount() {
                // Clean up event listener
                if (this.inputRef.current) {
                    let inputElement = this.inputRef.current;
                    if (this.inputRef.current.inputRef && this.inputRef.current.inputRef.current) {
                        inputElement = this.inputRef.current.inputRef.current;
                    } else if (this.inputRef.current.querySelector) {
                        const foundInput = this.inputRef.current.querySelector('input');
                        if (foundInput) {
                            inputElement = foundInput;
                        }
                    }
                    
                    if (inputElement && inputElement.removeEventListener) {
                        inputElement.removeEventListener('keydown', this.handleDirectKeyDown);
                    }
                }
            }

            handleDirectKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleEscapeKey(e);
                }
            }

            handleChange = (e, data) => {
                const {dataObj, step} = this.props;
                if(this.props.dataObj && this.props.dataObj.value.toString() === data.value) {
                    return;
                }
                
                 //in future, we can get precision from global store
                let precision = 2;
                if(data.value !== ""){
                    pattern = new RegExp(`^-?\\d+\\.?\\d{0,${precision}}$`);
                    if(!pattern.test(data.value)){
                        return;
                    }
                }
                // if the input value is changed by clicking the up/down arrow, the value will be effective immediately
                if(!(e.nativeEvent instanceof InputEvent)) {
                    const fakeEvent = Object.assign({}, e);
                    fakeEvent.type = "blur";
                    this.handleBlur(fakeEvent);
                }
                else {
                    const {onChange, name} = this.props;
                    if(onChange) {
                        data.name = name;
                        data.dataObj = dataObj;
                        onChange(e, data);
                        const fakeEvent = Object.assign({}, e);
                        fakeEvent.type = "blur";
                        this.handleBlur(fakeEvent);
                    }
                }
            }

            handleBlur = (e) => {
                const {min, max, onChange, name, dataObj} = this.props;
                let value = e.target.value;
                if(min !== undefined && value < min) {
                    value = min;
                }

                if(max !== undefined && value > max) {
                    value = max;
                }

                if(onChange) {
                    onChange(e, {name: name, dataObj: dataObj, value: value, type: "input"});
                }
            }

            handleKeyPress = (e) => {
                if(e.key === "Enter") {
                    e.preventDefault();
                    this.handleBlur(e);
                }
            }

            handleKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent DynamicPage global handler interference
                    this.handleEscapeKey(e);
                }
            }

            handleEscapeKey = (e) => {
                // Remove focus immediately and visually - force remove CSS focus styling
                const targetElement = e.target;
                targetElement.blur();
                
                // Force remove any lingering focus styles immediately with !important
                targetElement.style.setProperty('outline', 'none', 'important');
                targetElement.style.setProperty('box-shadow', 'none', 'important');
                targetElement.style.setProperty('border', '');
                
                // Clean up the style override after a brief moment
                setTimeout(() => {
                    targetElement.style.setProperty('outline', '');
                    targetElement.style.setProperty('box-shadow', '');
                }, 10);
                
                // Reset tab navigation to first element - simplified approach
                setTimeout(() => {
                    // Simply blur any focused element to reset tab navigation
                    if (document.activeElement && document.activeElement !== document.body) {
                        document.activeElement.blur();
                    }
                }, 1);
            }

            refineSuffixDecimal(unit) {
                const {step, min, max, dataObj, precision} = this.props;
                let suffix = unit;
                if(dataObj && dataObj.highPrecisionMin && dataObj.highPrecisionMax) {
                    let convertedMin = UnitConversion.formatNumericValue(precision, dataObj.highPrecisionMin, step);
                    let convertedMax = UnitConversion.formatNumericValue(precision, dataObj.highPrecisionMax, step);
                    suffix = suffix + " [" + convertedMin + ", " + convertedMax + "]";
                } else if(min !== undefined && max !== undefined){
                    suffix = suffix + " [" + min + ", " + max + "]";
                }

                if(!suffix) {
                    return suffix;
                }

                let newSuffix = suffix;
                if(step === undefined || step === "1" || step === 1) {
                    const pattern = /\d+\.\d+/g;
                    const matches = suffix.matchAll(pattern);
                    const replacements = [];
                    for (const iter of matches) {
                        const value = iter[0];
                        const index = iter.index;
                        const dotIndex = value.indexOf(".");
                        const newValue = value.substring(0, dotIndex);
                        replacements.push({ value, index, newValue });
                    }
                    if(replacements.length === 0) {
                        return newSuffix;
                    }
                    newSuffix = suffix.substring(0, replacements[0].index);
                    for(let i = 0; i<replacements.length; i++) {
                        newSuffix = newSuffix + replacements[i].newValue;
                        if(i < replacements.length - 1) {
                            newSuffix = newSuffix + suffix.substring(replacements[i].index+replacements[i].value.length, replacements[i+1].index);
                        }
                        else {
                            newSuffix = newSuffix + suffix.substring(replacements[i].index+replacements[i].value.length);
                        }
                    }
                }

                return newSuffix;
            }
           


            render() {

                const {label, tooltip, value, visible, disabled, step, unit, defaultValue, readonly, width, dataObj, precision, helpIconNotRequired, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";
                let convertedValue = value;



                if(dataObj && dataObj.highPrecisionValue){
                    convertedValue = UnitConversion.formatNumericValue(precision, dataObj.highPrecisionValue, step);
                }

                const suffix = this.refineSuffixDecimal(unit);

                const charLength = convertedValue !== null || convertedValue !== undefined ? convertedValue.toString().length : 0;
                const labelLeftMargin = charLength > 0 ? (charLength*10)+16+'px' : '34px';

                return (
                    <div className="tc-component" style={{display: display}}>
                        <div className="input-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled input-label'}/>
                            <Input
                                ref={this.inputRef}
                                tabIndex={display === 'none' ? -1 : index}
                                style={{display: "inline", width: width ?  width : label.length > 30 ? label.length * 7.1 + 'px' : '205.6px' }}
                                step={step === undefined ? "any" : step}
                                type="number"
                                value={isNaN(convertedValue) ? "" : convertedValue}
                                disabled={disabled || (!!readonly)}
                                size="mini-hon"
                                onChange={this.handleChange}
                                onKeyPress={this.handleKeyPress}
                                onKeyDown={this.handleKeyDown}
                                onBlur={this.handleBlur}
                                placeholder={defaultValue}
                            />
                            {suffix && <label style={{ position: 'absolute', fontFamily: "Honeywell Sans Web", top: this.isWorkbench ? '23px' : '21px', left: labelLeftMargin }}>{suffix}</label>}                            
                        </div>  
                        {(dataObj && dataObj.help && !helpIconNotRequired) &&<Popup
                            trigger={<Image style={{left : label.length > 30 ? -label.length + 'px' : '12px'}} src="/module/honApplicationHandler/rc/images/Help.svg" className='text-input-icon'/>}
                            content={dataObj.help}
                            size='mini'                            
                        />}                  
                    </div>
                )
            }
        }

        return NumberInputFloatLabel;
});