define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
        ConfigLabel
    ) => {
        'use strict';
        const {
            Image, 
            Popup
        } = SemanticReact;

        var honApplicationHandlerLex = lexicons[0];
        
        class DropdownFloatLabel extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj} = this.props;

                if(onChange) {
                    data.name = name;
                    data.dataObj = dataObj;
                    onChange(e, data);
                }
            }

            handleKeyDown = (e) => {
                if (e.key === "Escape") {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent DynamicPage global handler interference
                    this.handleEscapeKey(e);
                }
            }

            handleEscapeKey = (e) => {
                // Remove focus immediately and visually - force remove CSS focus styling
                const targetElement = e.target;
                targetElement.blur();
                
                // Force remove any lingering focus styles immediately with all box-shadow variants
                targetElement.style.setProperty('outline', 'none', 'important');
                targetElement.style.setProperty('box-shadow', 'none', 'important');
                targetElement.style.setProperty('-webkit-box-shadow', 'none', 'important');
                targetElement.style.setProperty('-moz-box-shadow', 'none', 'important');
                targetElement.style.border = '';
                
                // Handle dropdown specific elements
                const dropdown = targetElement.closest('.ui.dropdown');
                if (dropdown) {
                    dropdown.blur();
                    dropdown.style.setProperty('outline', 'none', 'important');
                    dropdown.style.setProperty('box-shadow', 'none', 'important');
                    dropdown.style.setProperty('-webkit-box-shadow', 'none', 'important');
                    dropdown.style.setProperty('-moz-box-shadow', 'none', 'important');
                    // Close any open dropdown
                    dropdown.classList.remove('active', 'visible');
                    const menu = dropdown.querySelector('.menu');
                    if (menu) {
                        menu.style.display = 'none';
                    }
                }
                
                // Clean up the style override after a brief moment
                setTimeout(() => {
                    targetElement.style.removeProperty('outline');
                    targetElement.style.removeProperty('box-shadow');
                    targetElement.style.removeProperty('-webkit-box-shadow');
                    targetElement.style.removeProperty('-moz-box-shadow');
                    if (dropdown) {
                        dropdown.style.removeProperty('outline');
                        dropdown.style.removeProperty('box-shadow');
                        dropdown.style.removeProperty('-webkit-box-shadow');
                        dropdown.style.removeProperty('-moz-box-shadow');
                    }
                }, 50);
                
                // Reset tab navigation - simplified approach
                setTimeout(() => {
                    if (document.activeElement && document.activeElement !== document.body) {
                        document.activeElement.blur();
                    }
                }, 1);
            }

            render() {

                const {label, tooltip, value, options, visible, disabled, clearable, readonly, dataObj, index, className} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <div className="dropdown-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled dropdown-label'}/>
                            <SemanticReact.Dropdown
                                tabIndex={display === 'none' ? -1 : index}
                                className={className ? `dropdown-mini-hon ${className}` : 'dropdown-mini-hon'}
                                fluid={false}
                                disabled={disabled || (!!readonly)}
                                clearable={clearable}
                                selection
                                options={options.filter(option => option.visible || option.visible === undefined || option.value === value)}
                                value={value}
                                onChange={this.handleChange}
                                onKeyDown={this.handleKeyDown}
                                placeholder={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SelectOneItem')}
                            />
                        </div>
                        {(dataObj && dataObj.help) && <Popup
                            trigger={<Image src="/module/honApplicationHandler/rc/images/Help.svg" className='dropdown-input-icon'/>}
                            content={dataObj.help}
                            size='mini'                            
                        />}
                    </div>
                );
            }
        }

        return DropdownFloatLabel;
});