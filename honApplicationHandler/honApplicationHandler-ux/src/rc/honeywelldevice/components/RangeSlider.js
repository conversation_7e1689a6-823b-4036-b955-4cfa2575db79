define([
    'react',
    'nmodule/honApplicationHandler/rc/libs/ReactSlider/ReactSlider',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/UnitConversion',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        ReactSlider,
        ConfigLabel,
        UnitConversion
    ) => {
        'use strict';

        class RangeSlider extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (value, reason) => {
                if(reason.triggeredByUser) {
                    const {onChange, dataObjList, precision, step} = this.props;
                    if(onChange) {
                        const min = UnitConversion.formatNumericValue(precision, value[0], step);
                        const max = UnitConversion.formatNumericValue(precision, value[1], step);

                        const minObj = dataObjList.find(obj => obj.role === "min");
                        const maxObj = dataObjList.find(obj => obj.role === "max");

                        if(min !== minObj.value) {
                            const data = {name: minObj.name, dataObj: minObj, value: min};
                            onChange({type: "mousemove"}, data);
                        }

                        if(max !== maxObj.value) {
                            const data = {name: maxObj.name, dataObj: maxObj, value: max};
                            onChange({type: "mousemove"}, data);
                        }
                    }
                }
            }

            render() {

                const {label, tooltip, value, visible, disabled, min, max, unit, step, color, deadband, readonly, precision, help, index} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                const formattedFirstValue = UnitConversion.formatNumericValue(precision, value[0], step);
                const formattedSecondValue = UnitConversion.formatNumericValue(precision, value[1], step);
                const newValue = [formattedFirstValue, formattedSecondValue];
                const formattedMin = UnitConversion.formatNumericValue(precision, min, step);
                const formattedMax = UnitConversion.formatNumericValue(precision, max, step);
                const configLabel = label + (unit ? (" (" + unit + ")") : "");
                const maxLabel = formattedMax + (unit ? ("" + unit) : "");
                const minLabel = formattedMin + (unit ? ("" + unit) : "");

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={configLabel} tooltip={tooltip} help={help ? help : ''}/>
                        <div className="hon-slider-container">
                            <label style={{ marginRight: "20px", fontFamily: "Honeywell Sans Web", color: '#303030', fontWeight: '500' }}>{minLabel}</label>
                            <ReactSlider
                                index={display === 'none' ? -1 : index}
                                multiple
                                disabled={disabled || (!!readonly)}
                                fillColor={color ? color : "red"}
                                style={{width: "100%", minWidth: "300px", thumb:{backgroundColor: color ? color : "red"}}}
                                value={newValue}
                                deadband={deadband}
                                unit={unit}
                                roleText={["min", "max"]}
                                settings={{start:newValue, min: formattedMin, max: formattedMax, step: step === undefined ? 1 : step, onChange: this.handleChange}}
                            />
                            <label style={{ marginLeft: "20px", fontFamily: "Honeywell Sans Web", color: '#303030', fontWeight: '500' }}>{maxLabel}</label>
                        </div>
                    </div>
                );
            }
        }

        return RangeSlider;
});