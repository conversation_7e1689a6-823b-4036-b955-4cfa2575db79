define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        const {
            Checkbox
        } = SemanticReact;

        class CheckboxGroup extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {name, onChange, dataObj, value} = this.props;

                if(onChange) {
                    const changedValue = parseFloat(data.value);
                    const extendedData = Object.assign({name, dataObj}, data);

                    extendedData.value = Array.from(value);
                    if(extendedData.value.includes(changedValue)) {
                        extendedData.value = extendedData.value.filter(x => x!==changedValue);
                    }
                    else {
                        extendedData.value.push(changedValue);
                    }

                    onChange(e, extendedData);
                }
            }

            renderCheckboxes() {
                // options prototype: [{key: number|string, text: string, value: number|string}]
                // value is an array of numbers, indicating checked option values
                const {options, value, readonly} = this.props;

                const checkBoxes = options.filter(option => option.visible === undefined || option.visible).map(option => {
                    return (
                        <Checkbox
                            className="hon-radio-button"
                            key={option.key}
                            label={option.text}
                            value={option.value}
                            disabled={option.disabled || (!!readonly)}
                            checked={value.includes(option.value)}
                            onChange={this.handleChange}
                        />
                    );
                });

                return checkBoxes;
            }

            render() {

                const {label, tooltip, visible} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";

                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        {this.renderCheckboxes()}
                    </div>
                );
            }
        }

        return CheckboxGroup;
});