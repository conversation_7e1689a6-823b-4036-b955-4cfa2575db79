define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'
],(
    React,
    SemanticReact,
    ConfigLabel
) => {
    'use strict';
    const {
        Button,
        Image
    } = SemanticReact;

    class SelectWidget extends React.Component {
        constructor(props) {
            super(props);
            this.buttonRefs = new Map();
        }

        handleKeyDown = (event) => {
            const { options, value } = this.props;
            const valueIndex = options.findIndex(item => item.value === value);
            
            switch(event.key) {
                case 'ArrowLeft':
                    if (valueIndex > 0) {
                        event.preventDefault();
                        const newValue = options[valueIndex-1].value;
                        this.handleClick(event, {value: newValue});
                        // Transfer focus to new selected button after state update
                        this.transferFocusToNewButton(newValue);
                    }
                    break;
                case 'ArrowRight':
                    if (valueIndex < options.length - 1) {
                        event.preventDefault();
                        const newValue = options[valueIndex+1].value;
                        this.handleClick(event, {value: newValue});
                        // Transfer focus to new selected button after state update
                        this.transferFocusToNewButton(newValue);
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    event.stopPropagation(); // Prevent DynamicPage global handler interference
                    // Remove focus immediately and visually - force remove CSS focus styling
                    const targetElement = event.target;
                    targetElement.blur();
                    
                    // Force remove any lingering focus styles immediately with setProperty
                    targetElement.style.setProperty('outline', 'none', 'important');
                    targetElement.style.setProperty('box-shadow', 'none', 'important');
                    targetElement.style.setProperty('-webkit-box-shadow', 'none', 'important');
                    targetElement.style.setProperty('-moz-box-shadow', 'none', 'important');
                    targetElement.style.border = '';
                    
                    // Also handle parent elements that might have focus styling
                    const parentButton = targetElement.closest('.ui.button');
                    if (parentButton) {
                        parentButton.style.setProperty('outline', 'none', 'important');
                        parentButton.style.setProperty('box-shadow', 'none', 'important');
                        parentButton.style.setProperty('-webkit-box-shadow', 'none', 'important');
                        parentButton.style.setProperty('-moz-box-shadow', 'none', 'important');
                    }
                    
                    // Handle all buttons in the SelectWidget (listbox) to remove any persistent focus
                    const selectWidget = targetElement.closest('.hon-select-widget');
                    if (selectWidget) {
                        const allButtons = selectWidget.querySelectorAll('.select-widget-button, .ui.button');
                        allButtons.forEach(btn => {
                            btn.style.setProperty('outline', 'none', 'important');
                            btn.style.setProperty('box-shadow', 'none', 'important');
                            btn.style.setProperty('-webkit-box-shadow', 'none', 'important');
                            btn.style.setProperty('-moz-box-shadow', 'none', 'important');
                        });
                    }
                    
                    // Clean up the style override after a brief moment
                    setTimeout(() => {
                        targetElement.style.removeProperty('outline');
                        targetElement.style.removeProperty('box-shadow');
                        targetElement.style.removeProperty('-webkit-box-shadow');
                        targetElement.style.removeProperty('-moz-box-shadow');
                        if (parentButton) {
                            parentButton.style.removeProperty('outline');
                            parentButton.style.removeProperty('box-shadow');
                            parentButton.style.removeProperty('-webkit-box-shadow');
                            parentButton.style.removeProperty('-moz-box-shadow');
                        }
                    }, 50);
                    
                    // Reset tab navigation to first element - simplified approach
                    setTimeout(() => {
                        // Simply blur any focused element to reset tab navigation
                        if (document.activeElement && document.activeElement !== document.body) {
                            document.activeElement.blur();
                        }
                    }, 1);
                    break;
                case 'Enter':
                case ' ':
                    event.preventDefault();
                    // Re-trigger click on current value to ensure consistency
                    this.handleClick(event, {value: value});
                    break;
                default:
                    // Allow other keys to propagate normally
                    break;
            }
        }

        transferFocusToNewButton = (newValue) => {
            // Use setTimeout to ensure the DOM has updated after state change
            setTimeout(() => {
                const newButton = this.buttonRefs.get(newValue);
                if (newButton) {
                    newButton.focus();
                }
            }, 0);
        }

        handleClick = (e, data) => {
            e.preventDefault();
            const {onChange, dataObj, name} = this.props;

            if(onChange && data.value !== this.props.value) {
                data.name = name;
                const extensibleData = Object.assign({}, data, {dataObj});
                onChange(e, extensibleData);
            }
        }

        renderImage(image) {
            return image.startsWith("/") ? (
                <Image src={image} />
            ) : (
                <Image src={'/module/honApplicationHandler/rc/images/' + image} />
            );
        }

        render() {
            const {label, tooltip, value, visible, disabled, options = [], readonly, dataObj, index} = this.props;
            const display = (visible || visible === undefined) ? undefined : "none";
            const safeOptions = options || [];
            
            return (
                <div className="tc-component" style={{display: display}}>
                    <ConfigLabel style={{fontFamily: 'Honeywell Sans Web'}} label={label} tooltip={tooltip} help={(dataObj && dataObj.help) ? dataObj.help : ''} />
                    <div className="hon-select-widget">
                        {safeOptions.map(option => {
                            return (
                                <Button
                                    ref={ref => this.buttonRefs.set(option.value, ref)}
                                    tabIndex={(value === option.value && display !== 'none') ? index : -1}
                                    disabled={disabled || (!!readonly)}
                                    key={option.text}
                                    className={"select-widget-button" + (value === option.value ? " selected" : "") + (option.image ? "" : " no-icon")}
                                    onClick={(e) => {
                                        this.handleClick(e, {value: option.value});
                                    }}
                                    onKeyDown={this.handleKeyDown}
                                >
                                    <Button.Content>
                                        <div>{option.text}</div>
                                        {option.image && this.renderImage(option.image)}
                                    </Button.Content>
                                </Button>
                            );
                        })}
                    </div>
                </div>
            )
        }
    }

    return SelectWidget;
});