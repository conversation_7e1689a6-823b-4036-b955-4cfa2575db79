define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {
        'use strict';

        const {
            Menu
        } = SemanticReact;

        class SelectButton extends React.Component {
            constructor(props) {
                super(props);
            }

            handleChange = (e, data) => {
                const {onChange, dataObj, name} = this.props;

                if(onChange && data.value !== this.props.value) {
                    const extensibleData = Object.assign({dataObj, name}, data);
                    onChange(e, extensibleData);
                }
            }

            render() {

                const {label, tooltip, value, visible, disabled, options = [], itemWidth, readonly} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";
                const safeOptions = options || [];
                
                const menuWidth = safeOptions.length ? safeOptions.length * (itemWidth === 0 ? 0 : (itemWidth || 100)) : 0;

                return (
                    <div className="tc-component" style={{display: display}} data-testid="select-button">
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <div className="hon-select-button" style={{display: "inline-block", width: menuWidth+"px"}}>
                            <Menu widths={safeOptions.length}>
                                {safeOptions.map(option => {
                                    return (
                                        <Menu.Item
                                            key={option.key}
                                            name={option.text}
                                            disabled={(disabled || (!!readonly)) && value !== option.value}
                                            value={option.value}
                                            active={value === option.value}
                                            onClick={this.handleChange}
                                        />
                                    );
                                })}
                            </Menu>
                        </div>
                    </div>
                );
            }
        }

        return SelectButton;
});