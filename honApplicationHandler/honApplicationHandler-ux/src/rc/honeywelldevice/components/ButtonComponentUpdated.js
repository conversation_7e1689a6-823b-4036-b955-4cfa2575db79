define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ConfigLabel
    ) => {        'use strict';

        const {
            Button
        } = SemanticReact;
        
        class ButtonComponent extends React.Component {            constructor(props) {
                super(props);
            }
            
            handleClick = (e, data) => {
                e.preventDefault();
                const {name, onClick, dataObj} = this.props;

                if(onClick) {
                    const extendedData = Object.assign({name, dataObj}, data);
                    onClick(e, extendedData);                }
            }
            
            render() {
                const {label, tooltip, value, visible, className, loading} = this.props;
                const display = (visible || visible === undefined) ? undefined : "none";
                const buttonClassName = className || '';
                
                return (
                    <div className="tc-component" style={{display: display}}>
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <Button 
                            onClick={this.handleClick}
                            className={buttonClassName}
                            data-testid="button-component"
                        >
                            {loading ? (
                                <span data-testid="loading-spinner" className="loading-spinner">
                                    Loading...
                                </span>
                            ) : value}
                        </Button>
                    </div>
                );
            }
        }

        return ButtonComponent;
});