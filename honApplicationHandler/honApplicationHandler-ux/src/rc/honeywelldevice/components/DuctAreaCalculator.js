define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SelectWidget',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/NumberInputFloatLabel',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
        ConfigLabel,
        SelectWidget,
        DropdownFloatLabel,
        NumberInputFloatLabel
    ) => {
        'use strict';

        var honApplicationHandlerLex = lexicons[0];

        const {
            But<PERSON>,
            Modal,
            Image,
            Popup
        } = SemanticReact;

        const DuctAreaCalculatorObjRoles = Object.freeze({
            DUCTTYPE: 'ducttype',
            AREA: 'area',
            WIDTH: 'width',
            HEIGHT: 'height',
            RADIUS: 'radius',
            DIMENSIONUNIT: 'dimensionunit'
        });

        const AreaUnitsByDimensionUnits = Object.freeze({
            Inch: "in²",
            Feet: "ft²",
            Meter: "m²",
            Centimeter: "cm²"
        });

        class DuctAreaCalculator extends React.Component {
            constructor(props) {
                super(props);
                this.state = {
                    showCalculateModal: false,
                    width: 0,
                    height: 0,
                    radius: 0,
                    ductType: 1,
                    dimensionUnit: 1
                };
            }

            componentDidMount() {

                const keydownEvent = (event) => {
                    if (this.state.showCalculateModal && event.key === 'Tab' && document.activeElement.tabIndex === 97) {
                        setTimeout(() => {
                            const ductType = document.querySelector('[tabindex="91"]');
                            ductType.focus();
                        }, 0);
                    }
                    // Note: Escape key handling is now handled generically by DynamicPage for all popups
                }

                document.addEventListener('keydown', keydownEvent);

                return () => {
                    document.removeEventListener("keydown", keydownEvent);
                }
            }

            /*
             * This function is used to get the json object by the role.
             * There are 6 roles for DuctAreaCalculator: ducttype, area, width, height, radius, dimensionunit
            */
            findObjectByRole = (role) => {
                const {dataObjList} = this.props;
                const obj = dataObjList.find(obj => obj.role === role);
                return obj;
            }

            /*
             * This function is used to handle any object change.
            */
            handleChange = (e, data) => {
                if(this.props.onChange) {
                    this.props.onChange(e, data);
                }
            }

            handleClickButton = (e) => {
                e.preventDefault();
                const ductTypeObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.DUCTTYPE);
                const dimensionUnitObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.DIMENSIONUNIT);
                const widthObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.WIDTH);
                const heightObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.HEIGHT);
                const radiusObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.RADIUS);

                this.setState({
                    showCalculateModal: true,
                    width: widthObj.value,
                    height: heightObj.value,
                    radius: radiusObj.value,
                    ductType: ductTypeObj.value,
                    dimensionUnit: dimensionUnitObj.value
                }, () => {
                    const ductType = document.querySelector('[tabindex="91"]');
                    ductType.focus();
                });                
            }

            handleCancel = () => {
                this.setState({showCalculateModal: false});
            }

            handleCalculate = () => {
                const event = {type: "keypress"};

                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.AREA));
                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.DUCTTYPE));
                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.DIMENSIONUNIT));
                this.updateAreaUnit();
                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.WIDTH));
                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.HEIGHT));
                this.handleChange(event, this.buildChangeData(DuctAreaCalculatorObjRoles.RADIUS));

                this.setState({showCalculateModal: false});
            }

            buildChangeData = (role) => {
                const obj = this.findObjectByRole(role);
                const {width, height, radius, ductType, dimensionUnit} = this.state;

                const data = {name: obj.name, dataObj: obj};
                switch(role) {
                    case DuctAreaCalculatorObjRoles.AREA:
                        if(ductType === 1) {
                            data.value = width*height;
                        }
                        else {
                            data.value = Math.PI*radius*radius;
                        }
                        break;
                    case DuctAreaCalculatorObjRoles.DUCTTYPE:
                        data.value = ductType;
                        break;
                    case DuctAreaCalculatorObjRoles.DIMENSIONUNIT:
                        data.value = dimensionUnit;
                        break;
                    case DuctAreaCalculatorObjRoles.WIDTH:
                        data.value = width;
                        break;
                    case DuctAreaCalculatorObjRoles.HEIGHT:
                        data.value = height;
                        break;
                    case DuctAreaCalculatorObjRoles.RADIUS:
                        data.value = radius;
                        break;
                    default:
                        break;
                }
                return data;
            }

            updateAreaUnit = () => {
                const dimensionUnitObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.DIMENSIONUNIT);
                const areaObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.AREA);

                const dimensionUnitText = dimensionUnitObj.options.findLast(option => option.value === dimensionUnitObj.value).text;
                const keyText = Object.keys(AreaUnitsByDimensionUnits).find(key => dimensionUnitText.includes(key));

                areaObj.unit = keyText ? AreaUnitsByDimensionUnits[keyText] : "";
            }

            renderModalContent = () => {
                const ductTypeObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.DUCTTYPE);
                const dimensionUnitObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.DIMENSIONUNIT);
                const widthObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.WIDTH);
                const heightObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.HEIGHT);
                const radiusObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.RADIUS);

                const {ductType, width, height, radius, dimensionUnit} = this.state;

                return (
                    <div>
                        <SelectWidget
                            index="91"
                            key={ductTypeObj.name}
                            label={ductTypeObj.label}
                            tooltip={ductTypeObj.tooltip}
                            value={ductType}
                            visible={ductTypeObj.visible}
                            disabled={ductTypeObj.disabled}
                            readonly={ductTypeObj.readOnly}
                            options={ductTypeObj.options}
                            onChange={(e, data) => this.setState({ductType: parseFloat(data.value)})}
                        />
                        <ConfigLabel label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.DuctArea')} />
                        <DropdownFloatLabel
                            key={dimensionUnitObj.name}
                            name={dimensionUnitObj.name}
                            disabled={dimensionUnitObj.disabled}
                            readonly={dimensionUnitObj.readOnly}
                            clearable={dimensionUnitObj.clearable}
                            label={dimensionUnitObj.label}
                            tooltip={dimensionUnitObj.tooltip}
                            visible={dimensionUnitObj.visible}
                            options={dimensionUnitObj.options}
                            value={dimensionUnit}
                            onChange={(e, data) => this.setState({dimensionUnit: parseFloat(data.value)})}
                            index="92"
                        />
                        {ductType === 1 ? <div className="tc-component tc-component-group-inline duct-area-dimension">
                            <NumberInputFloatLabel
                                key={heightObj.name}
                                name={heightObj.name}
                                min={heightObj.min}
                                max={heightObj.max}
                                visible={heightObj.visible}
                                disabled={heightObj.disabled}
                                readonly={heightObj.readOnly}
                                label={heightObj.label}
                                tooltip={heightObj.tooltip}
                                value={height}
                                step={heightObj.step}
                                unit={heightObj.unit}
                                defaultValue={heightObj.defaultValue}
                                width="130px"
                                onChange={(e, data) => this.setState({height: parseFloat(data.value)})}
                                index="93"
                            />
                            <div className="multiply-sign">×</div>
                            <NumberInputFloatLabel
                                key={widthObj.name}
                                name={widthObj.name}
                                min={widthObj.min}
                                max={widthObj.max}
                                visible={widthObj.visible}
                                disabled={widthObj.disabled}
                                readonly={widthObj.readOnly}
                                label={widthObj.label}
                                tooltip={widthObj.tooltip}
                                value={width}
                                step={widthObj.step}
                                unit={widthObj.unit}
                                defaultValue={widthObj.defaultValue}
                                width="130px"
                                onChange={(e, data) => this.setState({width: parseFloat(data.value)})}
                                index="94"
                            />
                        </div> :
                        <div className="tc-component tc-component-group-inline duct-area-dimension">
                            <div className="multiply-sign" style={{position: "relative", top: "-3px", left: "2px"}}>π</div>
                            <div className="multiply-sign">×</div>
                            <NumberInputFloatLabel
                                key={radiusObj.name}
                                name={radiusObj.name}
                                min={radiusObj.min}
                                max={radiusObj.max}
                                visible={radiusObj.visible}
                                disabled={radiusObj.disabled}
                                readonly={radiusObj.readOnly}
                                label={radiusObj.label}
                                tooltip={radiusObj.tooltip}
                                value={radius}
                                step={radiusObj.step}
                                unit={radiusObj.unit}
                                defaultValue={radiusObj.defaultValue}
                                width="150px"
                                onChange={(e, data) => this.setState({radius: parseFloat(data.value)})}
                                index="95"
                            />
                            <div className="multiply-sign" style={{position: "relative", top: "-10px", left: "-10px", fontSize: "x-large"}}>²</div>
                        </div>}
                    </div>
                );
            }

            render() {
                const areaObj = this.findObjectByRole(DuctAreaCalculatorObjRoles.AREA);
                const {visible, readOnly} = areaObj;
                const display = (visible || visible === undefined) ? undefined : "none";

                const {showCalculateModal} = this.state;
                return (
                    <div className="tc-component tc-component-group-inline" style={{display: display}}>
                        <NumberInputFloatLabel
                            key={areaObj.name}
                            dataObj={areaObj}
                            name={areaObj.name}
                            min={areaObj.min}
                            max={areaObj.max}
                            visible={areaObj.visible}
                            disabled={areaObj.disabled || readOnly}
                            readonly={areaObj.readOnly}
                            label={areaObj.label}
                            tooltip={areaObj.tooltip}
                            value={areaObj.value}
                            step={areaObj.step}
                            unit={areaObj.unit}
                            defaultValue={areaObj.defaultValue}
                            onChange={this.handleChange}
                            helpIconNotRequired={true}
                            index={areaObj.index}
                        />
                        <Image 
                            tabIndex={areaObj.index}
                            src="/module/honApplicationHandler/rc/images/Calculate.svg" 
                            disabled={readOnly}
                            style={{
                                fontSize: "2.2rem",
                                padding: "0.1rem 0.5rem",
                                fontWeight: "500",
                                lineHeight: "1.5rem",
                                position: "relative",
                                left: "-52px",
                                top: '-5px',
                                cursor: 'pointer',
                            }} 
                            onKeyDown={(e) => { e.key === 'Enter' && this.handleClickButton(e) }}
                            onClick={this.handleClickButton}
                        />
                        <Popup
                            trigger={<Image src="/module/honApplicationHandler/rc/images/Help.svg" className='duct-help-icon'/>}
                            content={areaObj.help}
                            size='mini'                            
                        />
                        <Modal
                            className="honeywell-device-wizard duct-area-calculator-modal"
                            open={showCalculateModal}
                            size="mini"
                            closeOnDimmerClick={false}
                            closeOnDocumentClick={false}
                            onClose={() => this.setState({showCalculateModal: false})}
                        >
                            <Modal.Header style={{padding: "1rem"}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.DuctAreaCalculator')}</Modal.Header>
                            <Modal.Content style={{padding: "0.5rem 1rem"}}>
                                {this.renderModalContent()}
                            </Modal.Content>
                            <Modal.Actions>
                                <Button basic onClick={this.handleCancel} color="black" style={{position: "absolute", left: "1%", borderRadius: "4px", lineHeight: '18px', fontSize: '12px'}} tabIndex="96" >{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CANCEL')}</Button>
                                <Button disabled={readOnly} onClick={this.handleCalculate} style={{backgroundColor: "#606060", borderRadius: "4px", marginRight: '10px', fontSize: '12px', fontWeight: '500', lineHeight: '18px'}} tabIndex="97" >{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CALCULATE')}</Button>
                            </Modal.Actions>
                        </Modal>
                    </div>
                )
            }
        }

        return DuctAreaCalculator;
});