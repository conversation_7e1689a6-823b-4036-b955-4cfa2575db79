define([
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/TextInputFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DateInputFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/NumberInputFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/CheckboxGroup',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SelectWidget',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',    
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        lexicons,
        DropdownFloatLabel,
        TextInputFloatLabel,
        DateInputFloatLabel,
        NumberInputFloatLabel,
        CheckboxGroup,
        SelectWidget,
    ) => {
        'use strict';

        var honApplicationHandlerLex = lexicons[0];

        const {
            Tab,
            Popup,
            Image,
            Modal,
            ModalHeader,
            ModalContent,
            ModalActions,
            Button,
            FormField, 
            Form, 
            Radio
        } = SemanticReact;

        class Scheduling extends React.Component {

            constructor(props) {
                super(props);
                this.state = {
                    selectedDaysForCopy: [],
                    contextMenuPosition: null,
                    activeTabIndex: 0,
                    addEventModal: false,
                    copyEventModal: false,
                    configureHolidayModal: false,
                    informationModal: false,
                    selectedEventIndex: '',
                    selectedDay: '',
                    localScheduleType: props.scheduling.defaultLocalScheduleType || 0,
                    state: 1,
                    startAt: '00:00',
                    endAt: '00:15',
                    events: props.scheduling.events || [],
                    eventsOverlapping: false,
                    startEndTimeMismatch: false,
                    holidayType: 'recurringDaysEveryYear',
                    holidaysList: props.scheduling.Holidays || [],
                    countryHolidays: 'USHolidays',
                    name: '',
                    month: 'anyMonth',
                    week: 'anyWeek',
                    weekday: 'anyDay',
                    date: 1,
                    fromDate: 1,
                    fromMonth: 'anyMonth',
                    fromMonthIndex: 0,
                    toDate: 1,
                    toMonth: 'anyMonth',
                    toMonthIndex: 0,
                    selectedHoliday: '',
                    daysOptions: [],
                    holidayAlreadyExist: false,
                    nameAlreadyExist: false,
                    toMonthDateMismatch: false,
                    everyOnAllPlaces: false,
                    duplicateHolidayIndex: [],
                    dragging: null,
                    draggingEvent: null, // { index, initialX, startSlotOffset }
                    creatingEvent: null, // { day, startSlot, endSlot, mouseMove }
                    tooltip: {
                        visible: false,
                        x: 0,
                        y: 0,
                        text: '',
                    },
                };
                this.contextMenuRef = React.createRef();
                this.divRef = React.createRef();
                this.uploadeRef = React.createRef();
                this.isWorkbench = window.location.href.toString().indexOf("bajaux") !== -1;
                this.tooltipRAF = null;
            }

            USHolidaysList = [
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "New Year's Day",
                  "month": "January",
                  "date": 1,
                  "flag": "New"
                },
                {
                  "holidayType": "recurringDaysEveryYear",
                  "name": "Memorial Day",
                  "week": "last",
                  "weekday": "monday",
                  "month": "May",
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Independence Day",
                  "month": "July",
                  "date": 4,
                  "flag": "New"
                },
                {
                  "holidayType": "recurringDaysEveryYear",
                  "name": "Labor Day",
                  "week": "first",
                  "weekday": "monday",
                  "month": "September",
                  "flag": "New"
                },
                {
                  "holidayType": "dateRangeEveryYear",
                  "name": "Thanks giving and Day after",
                  "fromMonth": "November",
                  "fromDate": 27,
                  "toMonth": "November",
                  "toDate": 28,
                  "flag": "New"
                },
                {
                  "holidayType": "dateRangeEveryYear",
                  "name": "Christmas Evening and Day after",
                  "fromMonth": "December",
                  "fromDate": 25,
                  "toMonth": "December",
                  "toDate": 26,
                  "flag": "New"
                }
            ];
                              
            CanadaHolidaysList = [
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "New Year's Day",
                  "month": "January",
                  "date": 1,
                  "flag": "New"
                },
                {
                  "holidayType": "recurringDaysEveryYear",
                  "name": "Labor Day",
                  "week": "first",
                  "weekday": "monday",
                  "month": "September",
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Canada Day",
                  "month": "July",
                  "date": 1,
                  "flag": "New"
                },
                {
                  "holidayType": "recurringDaysEveryYear",
                  "name": "Thanks Giving Day",
                  "week": "second",
                  "weekday": "monday",
                  "month": "October",
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Remembrance Day",
                  "month": "November",
                  "date": 11,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Christmas Evening",
                  "month": "December",
                  "date": 25,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Boxing Day",
                  "month": "December",
                  "date": 26,
                  "flag": "New"
                }
            ];
            
            EuropeanHolidaysList = [
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "New Year's Day",
                  "month": "January",
                  "date": 1,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Christmas Day",
                  "month": "December",
                  "date": 25,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Boxing Dayr",
                  "month": "December",
                  "date": 26,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "Labor Day",
                  "month": "May",
                  "date": 1,
                  "flag": "New"
                },
                {
                  "holidayType": "specificDateEveryYear",
                  "name": "All Saints Day",
                  "month": "November",
                  "date": 1,
                  "flag": "New"
                }
            ];

            hours = [
                '00:00', '00:15', '00:30', '00:45', '01:00', '01:15',
                '01:30', '01:45', '02:00', '02:15', '02:30', '02:45',
                '03:00', '03:15', '03:30', '03:45', '04:00', '04:15',
                '04:30', '04:45', '05:00', '05:15', '05:30', '05:45',
                '06:00', '06:15', '06:30', '06:45', '07:00', '07:15',
                '07:30', '07:45', '08:00', '08:15', '08:30', '08:45',
                '09:00', '09:15', '09:30', '09:45', '10:00', '10:15',
                '10:30', '10:45', '11:00', '11:15', '11:30', '11:45',
                '12:00', '12:15', '12:30', '12:45', '13:00', '13:15',
                '13:30', '13:45', '14:00', '14:15', '14:30', '14:45',
                '15:00', '15:15', '15:30', '15:45', '16:00', '16:15',
                '16:30', '16:45', '17:00', '17:15', '17:30', '17:45',
                '18:00', '18:15', '18:30', '18:45', '19:00', '19:15',
                '19:30', '19:45', '20:00', '20:15', '20:30', '20:45',
                '21:00', '21:15', '21:30', '21:45', '22:00', '22:15',
                '22:30', '22:45', '23:00', '23:15', '23:30', '23:45',
                '24:00'
            ];

            days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

            daysFull = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            monthIndex = {anyMonth: 0,January: 1, February: 2, March: 3, April: 4,
                May: 5, June: 6, July: 7, August: 8,
                September: 9, October: 10, November: 11, December: 12
            }

            hoursOptions = this.hours.map(hour => {
                return {
                    "text": hour,
                    "value": hour,
                    "key": hour
                }
            }); 

            handleTabChange=(e, {activeIndex})=>{
                this.setState({ activeTabIndex: activeIndex });
            }

            onChange = (e, data) => {
                let eventsOverlapping, startEndTimeMismatch = false;
                let endAt = data.name === 'endAt' ? data.value : this.state.endAt;
                let fromMonth = (data.name === 'toMonth' && data.value === 'anyMonth') || data.name === 'fromMonth' ? data.value : this.state.fromMonth;                
                let toMonth = data.name === 'fromMonth' || data.name === 'toMonth' ? data.value : this.state.toMonth; 
                let fromMonthIndex = (data.name === 'toMonth' && data.value === 'anyMonth') || data.name === 'fromMonth' ? this.monthIndex[fromMonth] : this.state.fromMonthIndex;                
                let toMonthIndex = data.name === 'fromMonth' || data.name === 'toMonth' ? this.monthIndex[toMonth] : this.state.toMonthIndex;                               
                if(data.name === 'startAt' || data.name === 'endAt') {
                    if (data.name === 'startAt') {
                        eventsOverlapping = this.isEventOverlap({start: data.value, end: this.hours[this.hours.indexOf(data.value)+1]}, this.state.events);
                    } else if (data.name === 'endAt') {
                        eventsOverlapping = this.isEventOverlap({start: this.state.startAt, end: data.value}, this.state.events);
                    }
                    if (data.name === 'startAt' && this.state.endAt <= data.value) {
                        endAt = this.hours[this.hours.indexOf(data.value)+1];
                    } else if (data.name === 'endAt' && this.state.startAt >= data.value) {
                        startEndTimeMismatch = true;
                    }
                }
                this.setState({ [data.name]: data.value, endAt, fromMonth, toMonth, eventsOverlapping, startEndTimeMismatch, fromMonthIndex, toMonthIndex }, 
                    () => this.afterOnchange(data.name));
            }

            addToListHandler = () => {
                let holidaysList = [...this.state.holidaysList];
                let countryHolidaysList = [];
                let holidayAlreadyExist = false;
                switch (this.state.countryHolidays) {
                    case 'USHolidays':
                        holidaysList = [...holidaysList,...this.USHolidaysList];
                        countryHolidaysList = [...this.USHolidaysList];
                        break;
                    case 'CanadianhHolidays':
                        holidaysList = [...holidaysList,...this.CanadaHolidaysList];
                        countryHolidaysList = [...this.CanadaHolidaysList];
                        break;
                    case 'EuropeanHoildays':
                        holidaysList = [...holidaysList,...this.EuropeanHolidaysList];
                        countryHolidaysList = [...this.EuropeanHolidaysList];
                        break;
                    default:
                        break;
                }    
                countryHolidaysList.map(holidayItem => {
                    this.state.holidaysList.map((item) => {
                        if(item.holidayType === holidayItem.holidayType && 
                            item.week === holidayItem.week && 
                            item.weekday === holidayItem.weekday && 
                            item.month === holidayItem.month && 
                            item.fromMonth === holidayItem.fromMonth && 
                            item.fromDate === holidayItem.fromDate && 
                            item.toMonth === holidayItem.toMonth && 
                            item.toDate === holidayItem.toDate 
                        ) {
                            holidayAlreadyExist = true;                        
                        }
                    });
                });
                if(holidayAlreadyExist) {
                    this.setState({informationModal: true})
                } else {
                    this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType,
                        label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: holidaysList, scheduleIndex: this.props.scheduleIndex
                    });
                    this.setState({holidaysList});
                }            
            }

            afterOnchange = (name) => {
                if(name === 'localScheduleType') {
                    this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType, 
                        label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex })
                } else if (name === 'month') {
                    this.getDays();
                }

                let holidaysList = [...this.state.holidaysList];
                let holidayAlreadyExist = false;
                let nameAlreadyExist = false;
                let toMonthDateMismatch = false;
                let everyOnAllPlaces = false;
                holidaysList.map((item, index) => {
                    if((item.holidayType === 'recurringDaysEveryYear' &&
                        index !== this.state.selectedHoliday &&
                        item.holidayType === this.state.holidayType && 
                        item.week === this.state.week && 
                        item.weekday === this.state.weekday && 
                        item.month === this.state.month) || 
                        (item.holidayType === 'specificDateEveryYear' &&
                            index !== this.state.selectedHoliday &&
                            item.holidayType === this.state.holidayType && 
                            item.month === this.state.month &&
                            item.date === this.state.date
                        ) ||
                        (item.holidayType === 'specificDateEveryYear' &&
                            index !== this.state.selectedHoliday &&
                            item.holidayType === this.state.holidayType && 
                            item.fromMonth === this.state.fromMonth &&
                            item.fromDate === this.state.fromDate && 
                            item.toMonth === this.state.toMonth &&
                            item.toDate === this.state.toDate
                        )
                    ) {
                        holidayAlreadyExist = true;                        
                    } else if(index !== this.state.selectedHoliday && item.name.toString().toLowerCase() === this.state.name.toString().toLowerCase()) {
                        nameAlreadyExist = true;
                    } 
                });
                if(this.state.holidayType === 'dateRangeEveryYear' && ((this.state.fromMonthIndex > this.state.toMonthIndex) ||
                        (this.state.fromMonthIndex === this.state.toMonthIndex && this.state.fromDate > this.state.toDate))) {
                    toMonthDateMismatch = true;
                } else if(this.state.holidayType === 'recurringDaysEveryYear' && this.state.week === 'anyWeek' && this.state.weekday === 'anyDay' && this.state.month === 'anyMonth') {
                    everyOnAllPlaces = true;
                }
                this.setState({ holidayAlreadyExist, nameAlreadyExist, toMonthDateMismatch, everyOnAllPlaces });
            }
            // Function to check if the new event overlaps with existing events
            isEventOverlap = (newEvent, events) => {
                for (let [index, event] of events.entries()) {
                  // Check if the new event starts or ends during an existing event
                  if ((newEvent.start < event.end && newEvent.end > event.start) && this.state.selectedDay === event.day && this.state.selectedEventIndex !== index) {
                    return true; // There's an overlap
                  }
                }
                return false; // No overlap
            }

            addEvent = () => {
                let events = [...this.state.events];
                events.push({
                    eventId: events.length,
                    day: this.state.selectedDay,
                    start: this.state.startAt,
                    end: this.state.endAt,
                    status: this.state.state
                });
                this.props.onDynamicPageChanged('Scheduling', { events: events, defaultLocalScheduleType: this.state.localScheduleType, 
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                 });
                this.setState({addEventModal: false, events, startAt: '00:00', endAt: '00:15', state: 1 });
            }

            editEvent = () => {
                let events = [...this.state.events];
                events[this.state.selectedEventIndex].day = this.state.selectedDay;
                events[this.state.selectedEventIndex].start = this.state.startAt;
                events[this.state.selectedEventIndex].end = this.state.endAt;
                events[this.state.selectedEventIndex].status = this.state.state;
                this.props.onDynamicPageChanged('Scheduling', { events: events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                 });
                this.setState({addEventModal: false, events, startAt: '00:00', endAt: '00:15', state: 1, selectedEventIndex: '' });
            }

            copyTo = () => {
                let events = [...this.state.events];  
                let currentEventsLength = this.state.events.length;              
                let eventsCopy = [...this.state.events];    
                let eventsIndexToDelete = [];    
                this.state.selectedDaysForCopy.map(day => {
                    events.map((e,index) => {
                        (day.slice(0,3) === e.day && day.slice(0,3) !== this.state.selectedDay) && eventsIndexToDelete.push(index);
                    });
                });
                eventsIndexToDelete.sort((a,b) => b - a); 
                eventsIndexToDelete.map(item => {
                    events.splice(item,1);
                });
                this.state.selectedDaysForCopy.map(day => {
                    if (this.state.selectedEventIndex !== '') {
                        day.slice(0,3) !== eventsCopy[this.state.selectedEventIndex].day && events.push({
                            eventId: currentEventsLength,
                            day: day.slice(0,3),
                            start: eventsCopy[this.state.selectedEventIndex].start,
                            end: eventsCopy[this.state.selectedEventIndex].end,
                            status: eventsCopy[this.state.selectedEventIndex].status
                        });
                        ++currentEventsLength;
                    } else {
                        eventsCopy.map((event) => {
                            day.slice(0,3) !== this.state.selectedDay && event.day === this.state.selectedDay && events.push({
                                eventId: currentEventsLength,
                                day: day.slice(0,3),
                                start: event.start,
                                end: event.end,
                                status: event.status
                            });
                            ++currentEventsLength;
                        });
                    }                    
                });
                this.props.onDynamicPageChanged('Scheduling', { events: events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                 });
                this.setState({copyEventModal: false, events, selectedDaysForCopy: [] });
            }

            deleteEvent = () => {
                this.setState({ contextMenuPosition: null });
                let events = [...this.state.events];
                this.state.selectedEventIndex !== '' ? events.splice(this.state.selectedEventIndex,1) : events = events.filter(event => {
                    return event.day !== this.state.selectedDay;
                });
                this.props.onDynamicPageChanged('Scheduling', { events: events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label,  Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                 });
                this.setState({ events, selectedEventIndex: '' });
            }

            copyEvent = () => {
                this.setState({ contextMenuPosition: null, copyEventModal: true });
            }

            copyEventMF = () => {
                this.setState({ contextMenuPosition: null, selectedDaysForCopy: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']},() => this.copyTo());                
            }

            handleChange = (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const holidaysList = JSON.parse(e.target.result);
                        this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType,
                            label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: holidaysList, scheduleIndex: this.props.scheduleIndex
                        });
                        this.setState({ holidaysList });
                    };
                    reader.readAsText(file);
                }
            };

            showOpenFileDialog = () => {
                this.uploadeRef.current.click();
            };

            downloadJSON = () => {
                const jsonData = new Blob([JSON.stringify(this.state.holidaysList)], { type: 'application/json' });
                const jsonURL = URL.createObjectURL(jsonData);
                const link = document.createElement('a');
                link.href = jsonURL;
                link.download = 'holidayList.json';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            };

            getNumberWithPostfix = (number) => {
                const suffixes = ["th", "st", "nd", "rd"];
                const ones = number % 10;
                const tens = Math.floor(number / 10) % 10;
            
                // Special case for 11, 12, and 13
                if (tens === 1) {
                    return number + suffixes[0];
                }

                // Return the appropriate suffix based on the ones place
                return number + (suffixes[ones] || suffixes[0]);
            }
            
            onDayDoubleClick = (e, day) => {
                const div = this.divRef.current;
                let startAt = '00:00', endAt= '00:15';
                if (div) {
                    const rect = div.getBoundingClientRect();
                    const clickX = e.clientX - rect.left; // distance from left
                    const percentage = (clickX / rect.width) * 100;
                    startAt = this.hours[Math.round(percentage.toFixed(2) * 96 / 100)];
                    endAt = this.hours[Math.round(percentage.toFixed(2) * 96 / 100)+1];
                }
                this.setState({addEventModal: true, selectedDay: day, startAt, endAt});
            }

            onAddEventIconClickHandler = (day) => {
                let start = this.getAvailableTimeSlot(day);
                this.setState({addEventModal: true, selectedDay: day, startAt: this.hours[this.hours.indexOf(start)], endAt: this.hours[this.hours.indexOf(start)+1]});
            }

            getAvailableTimeSlot = (day) => {
                // Filter events for the given day
                let dayEvents = this.state.events.filter(event => event.day === day);

                // Sort events by start time
                dayEvents.sort((a, b) => a.start.localeCompare(b.start));

                // Start checking from the beginning of the day
                let currentTime = "00:00";

                for (let event of dayEvents) {
                    if (currentTime < event.start) {
                        return currentTime; // Immediate available time slot found
                    }
                    currentTime = event.end; // Update current time to end of last occupied slot
                }

                return currentTime; // If no gaps, return next available slot
            }

            panes = () => {
                let outerEventId, outerStatus, outerStart, outerEnd, outerEventIndex = '';                

                return [
                {
                  menuItem: `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Scheduling')}`,
                  key: `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Scheduling')}${this.props.scheduleIndex}`,
                  render: () => <Tab.Pane attached={false}>
                    <div className="scheduling-container">
                        <DropdownFloatLabel
                            key={`localScheduleType${this.props.scheduleIndex}`}
                            name={'localScheduleType'}
                            disabled={this.props.readOnly}
                            clearable={false}
                            dataObj={null}
                            label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.DefaultState')}
                            tooltip={''}
                            visible={true}
                            options={this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].items.Schedules.localScheduleTypeOptions || []}
                            value={this.state.localScheduleType}
                            onChange={this.onChange}
                        />
                        <div className="timeline-container">
                            <span>0:00</span>
                            <span>3:00</span>
                            <span>6:00</span>
                            <span>9:00</span>
                            <span>12:00</span>
                            <span>15:00</span>
                            <span>18:00</span>
                            <span>21:00</span>
                            <span>24:00</span>
                        </div>
                        {this.days.map(day => {                            
                            return (
                                <div key={day} className="timeline-bar-container">
                                    <div className="timeline-bar-day">{day}</div>
                                    <div className="timeline-bar">
                                        <div style={{width: "100%", cursor: this.state.creatingEvent ? 'grabbing' : this.state.dragging ? 'ew-resize' : 'grab'}}
                                            onDoubleClick={(e) => { if(!this.props.readOnly) { this.onDayDoubleClick(e, day); }}}
                                            ref={this.divRef}
                                            onContextMenu={e => {this.handleContextMenu(e, ''); this.setState({ selectedDay: day })}} // Listen for right-click
                                            onClick={() => {
                                                if(!this.props.readOnly){
                                                    this.setState({
                                                        addEventModal: false,
                                                        selectedDay: day,
                                                        selectedEventIndex: "",
                                                        state: 1,
                                                        startAt: '00:00',
                                                        endAt: '00:15'
                                                    })
                                                }
                                            }}
                                            onMouseDown={(e) => this.handleTimelineMouseDown(e, day)}
                                            onMouseMove={(e) => this.handleTimelineMouseMove(e)}
                                            onMouseUp={(e) => this.handleTimelineMouseUp(e)}
                                            onMouseLeave={this.handleTimelineMouseLeave}
                                        >
                                        </div>
                                        {
                                            this.state.events.map((eventItem, index) => {
                                                if (day !== eventItem.day) {
                                                    return null;
                                                }
                                                const startIndex = this.hours.indexOf(eventItem.start);
                                                const endIndex = this.hours.indexOf(eventItem.end);
                                                const width = ((endIndex - startIndex) / 96.0 * 100.0).toFixed(4) + "%";
                                                const left = (startIndex / 96.0 * 100.0).toFixed(4) + "%";
                                                return (
                                                    <div
                                                        key={eventItem.start}
                                                        className={`scheduleType${eventItem.status}-color`}
                                                        style={{
                                                            cursor: this.props.readOnly ? "not-allowed" : "pointer",
                                                            width: width,
                                                            position: "absolute",
                                                            left: left,
                                                            borderRadius: "4px",
                                                            height: "100%",
                                                            border: "solid 1px #DCDCDC",
                                                            display: 'flex',
                                                        }}
                                                        onContextMenu={e => {this.handleContextMenu(e, index); this.setState({ selectedDay: day })}} // Listen for right-click
                                                        onClick={() => {
                                                            if(!this.props.readOnly && !this.state.dragging) {
                                                                this.setState({
                                                                    addEventModal: true,
                                                                    selectedDay: day,
                                                                    selectedEventIndex: index,
                                                                    state: eventItem.status,
                                                                    startAt: eventItem.start,
                                                                    endAt: eventItem.end
                                                                })
                                                            }
                                                        }}
                                                        onMouseMove={(e) => {
                                                            if(!this.state.creatingEvent) {
                                                                this.setState({
                                                                    tooltip: {
                                                                        visible: true,
                                                                        x: this.isWorkbench ? e.clientX - 16 : e.clientX - 60,
                                                                        y: e.clientY - 120,
                                                                        text: `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.StartAt')}: ${eventItem.start} \n${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EndAt')}: ${eventItem.end}`,
                                                                    }
                                                                });
                                                            } else {
                                                                this.handleTimelineMouseMove(e)
                                                            }
                                                        }}
                                                        onMouseUp={(e) => { this.state.creatingEvent && this.handleTimelineMouseUp(e); }}
                                                        onMouseLeave={this.handleTimelineMouseLeave}
                                                        onMouseDown={this.handleEventMouseDown(index)}
                                                    >
                                                    <div
                                                        style={{ width: "2px", cursor: "ew-resize", opacity: 0, pointerEvents: "auto" }}
                                                        onMouseDown={this.handleMouseDown(index, "left")}
                                                    />
                                                    <div style={{ flexGrow: 1 }} /> {/* filler */}
                                                    <div
                                                        style={{ width: "2px", cursor: "ew-resize", opacity: 0, pointerEvents: "auto" }}
                                                        onMouseDown={this.handleMouseDown(index, "right")}
                                                    />
                                                    </div>
                                                );
                                            })
                                        }                                        
                                        {
                                            this.state.creatingEvent && this.state.creatingEvent.day === day && (() => {
                                            const { startSlot, endSlot } = this.state.creatingEvent;
                                            const sortedSlots = [startSlot, endSlot].sort((a, b) => a - b);
                                            const left = (sortedSlots[0] / 96.0 * 100.0).toFixed(4) + "%";
                                            const width = ((sortedSlots[1] - sortedSlots[0]) / 96.0 * 100.0).toFixed(4) + "%";
                                            const status = this.state.localScheduleType

                                            return (
                                                this.state.creatingEvent.mouseMove && <div
                                                className={`ghost-event-scheduleType${status}`}
                                                style={{
                                                    position: "absolute",
                                                    left,
                                                    width,
                                                    height: "100%",
                                                    border: "1px dashed #a0c4ff",
                                                    borderRadius: "4px",
                                                    zIndex: 5,
                                                    pointerEvents: "none"
                                                }}
                                                />
                                            );
                                            })()
                                        }
                                        <div className="add-event" style={{ cursor: this.props.readOnly ? "not-allowed" : "pointer" }}  onClick={() =>{ if(!this.props.readOnly){this.onAddEventIconClickHandler(day);}}}>
                                            <Image src="/module/honApplicationHandler/rc/images/PlusIcon.svg" className="tab-menu-icon"/>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                        {this.state.tooltip.visible && (
                            <div
                                className="event-tooltip"
                                style={{
                                position: 'absolute',
                                left: this.state.tooltip.x,
                                top: this.state.tooltip.y,
                                backgroundColor: 'rgba(0,0,0,0.7)',
                                color: '#fff',
                                fontSize: '12px',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                pointerEvents: 'none',
                                whiteSpace: 'pre-line',
                                zIndex: 9999,
                                }}
                            >
                                {this.state.tooltip.text}
                            </div>
                        )}
                        <div className="event-info-container">
                            <div className="event-info">
                                <div>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.NoEvent')}</div>
                                <div className="no-event"></div>
                                <div className="no-event-info">
                                    <div>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EventInfo1')}</div>
                                    <div>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EventInfo2')}</div>
                                </div>
                            </div>
                            {
                                this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].items.Schedules.localScheduleTypeOptions.map(item => {
                                    return  <div className="event-info">
                                                <div className="event-info-status">{item.text}</div>
                                                <div className={`scheduleType${item.value}`}></div>
                                            </div>
                                })
                            }
                        </div>
                    </div>
                  </Tab.Pane>,
                },
                {
                  menuItem: honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Holidays'),
                  key: honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Holidays'),
                  render: () => <Tab.Pane attached={false}>                    
                    <div className="holiday-list-container">
                        {honApplicationHandlerLex.get('HoneywellDeviceWizardLex.HolidayList')}
                        <div className="holiday-list">
                            {this.state.holidaysList.map((holidayItem,index) => {
                                return <div key={holidayItem.name} className="holiday-item">
                                    <div className="holiday-name-icon" style={{top: this.isWorkbench ? '3px' : '0px'}}>
                                        {holidayItem.flag && <Image title={holidayItem.flag} className='icon new-modified-info-icon' src="/module/honApplicationHandler/rc/images/Tick.svg" />}
                                        {this.state.duplicateHolidayIndex.indexOf(index) !== -1 && <Image title='Duplicate' style={{top: this.isWorkbench ? '-3px' : '0px'}} className='icon new-modified-info-icon' src="/module/honApplicationHandler/rc/images/Error.svg" />}
                                        {(holidayItem.holidayType === 'recurringDaysEveryYear' && holidayItem.week !== 'lastWeek') && `${holidayItem.name} - ${holidayItem.week === 'anyWeek' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryWeek') : holidayItem.week.charAt(0).toUpperCase() + holidayItem.week.slice(1) +' '+honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Week')} ${holidayItem.weekday === 'anyDay' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of') + ' '+ honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryDay') : holidayItem.weekday.charAt(0).toUpperCase() + holidayItem.weekday.slice(1)} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${holidayItem.month === 'anyMonth' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryMonth'): holidayItem.month}`}
                                        {(holidayItem.holidayType === 'recurringDaysEveryYear' && holidayItem.week === 'lastWeek') && `${holidayItem.name} - ${holidayItem.weekday === 'anyDay' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryDay') : holidayItem.weekday.charAt(0).toUpperCase() + holidayItem.weekday.slice(1)} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.LastWeek')} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${holidayItem.month === 'anyMonth' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryMonth') : holidayItem.month}`}
                                        {holidayItem.holidayType === 'specificDateEveryYear' && `${holidayItem.name} - ${this.getNumberWithPostfix(holidayItem.date)} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${holidayItem.month === 'anyMonth' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryMonth') : holidayItem.month}`}
                                        {holidayItem.holidayType === 'dateRangeEveryYear' && `${holidayItem.name} - ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.from')} ${this.getNumberWithPostfix(holidayItem.fromDate)} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${holidayItem.fromMonth === 'anyMonth' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryMonth'): holidayItem.fromMonth} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.to')} ${this.getNumberWithPostfix(holidayItem.toDate)} ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.of')} ${holidayItem.toMonth === 'anyMonth' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryMonth'): holidayItem.toMonth}`}
                                    </div>
                                    <div className="edit-delete-icon-container">
                                        <Image disabled={this.props.readOnly} className='icon' src="/module/honApplicationHandler/rc/images/Edit.svg" onClick={() => {if(!this.props.readOnly){this.editHoliday(index)}}}/>
                                        <Image disabled={this.props.readOnly} className='icon' src="/module/honApplicationHandler/rc/images/Delete.svg" onClick={() => {if(!this.props.readOnly){this.deleteHoliday(index)}}} />
                                    </div>
                                </div>
                            })}
                        </div>
                        <div className="add-holiday-container">
                            <div className="add-holiday-button" style={{ cursor: this.props.readOnly ? "not-allowed" : "pointer" }}  onClick={() => { if(!this.props.readOnly){this.setState({ configureHolidayModal: true, holidayType: 'recurringDaysEveryYear' }); this.afterOnchange(''); }}}>
                                <Image style={{marginTop: this.isWorkbench ? '-2px' : '0px'}} src="/module/honApplicationHandler/rc/images/PlusIcon.svg" className="tab-menu-icon add-holiday-icon"/>
                                {honApplicationHandlerLex.get('HoneywellDeviceWizardLex.AddHoliday')}
                            </div>
                        </div>
                        <hr />
                        <div className="country-holidays-container">
                            <DropdownFloatLabel
                                key={'countryHolidays'}
                                name={'countryHolidays'}
                                disabled={this.props.readOnly}
                                clearable={false}
                                dataObj={null}
                                label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.PredefinedHolidaysList')}
                                tooltip={''}
                                visible={true}
                                options={[
                                    {
                                        "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.USholidays'),
                                        "value": "USHolidays",
                                        "key": "USHolidays"
                                    },
                                    {
                                        "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Canadianholidays'),
                                        "value": "CanadianhHolidays",
                                        "key": "CanadianHolidays"
                                    },
                                    {
                                        "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Europeanhoildays'),
                                        "value": "EuropeanHoildays",
                                        "key": "EuropeanHoildays"
                                    },
                                ]}
                                value={this.state.countryHolidays}
                                onChange={this.onChange}
                            />
                            <div>
                                {/* <Popup
                                    trigger={<Image style={{left : '11px', top: '11px'}} src="/module/honApplicationHandler/rc/images/Help.svg" className='text-input-icon'/>}
                                    content={''}
                                    size='mini'                            
                                /> */}
                                <Button disabled={this.props.readOnly} className='add-to-list-button' onClick={(e) => {e.preventDefault(); this.addToListHandler();}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ADDTOLIST')}</Button>
                                <input
                                    ref={this.uploadeRef}
                                    type="file"
                                    style={{ display: "none" }}
                                    onChange={this.handleChange}
                                />
                                <div className="import-export-btn-container">
                                    <Button disabled={this.props.readOnly} className='add-to-list-button' onClick={(e) => {e.preventDefault(); this.showOpenFileDialog();}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.IMPORT')}</Button>
                                    <Button disabled={this.props.readOnly} className='export-button' onClick={(e) => {e.preventDefault(); this.downloadJSON()}}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EXPORT')}</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                    </Tab.Pane>,
                },
            ]}; 

            updateTooltipPosition = (e, text) => {
                if (this.tooltipRAF) {
                    cancelAnimationFrame(this.tooltipRAF);
                }

                this.tooltipRAF = requestAnimationFrame(() => {
                    this.setState({
                        tooltip: {
                            visible: true,
                            x: this.isWorkbench ? e.clientX - 16 : e.clientX - 60,
                            y: e.clientY - 120,
                            text
                        }
                    });
                });
            };

            handleTimelineMouseDown = (e, day) => {
                if (this.props.readOnly) return;

                const rect = this.divRef.current.getBoundingClientRect();
                const slotWidth = rect.width / 96;
                const offsetX = e.clientX - rect.left;
                const startSlot = Math.floor(offsetX / slotWidth);

                this.setState({
                    creatingEvent: {
                        day,
                        startSlot,
                        endSlot: startSlot + 1,
                        mouseMove: false,
                    }
                });
            };

            handleTimelineMouseMove = (e) => {
                const { creatingEvent, events } = this.state;
                if (this.props.readOnly) return;

                const rect = this.divRef.current.getBoundingClientRect();
                const slotWidth = rect.width / 96;
                const offsetX = e.clientX - rect.left;
                const currentSlot = Math.floor(offsetX / slotWidth);

                //  Calculate hover time
                const hoverTime = this.hours[currentSlot] || '';

                if (!creatingEvent) {
                    this.setState({
                        tooltip: {
                            visible: true,
                            x: this.isWorkbench ? e.clientX - 16 : e.clientX - 60,
                            y: e.clientY - 120,
                            text: hoverTime,
                        }
                    });
                    return;
                }

                let endSlot = Math.max(currentSlot, creatingEvent.startSlot + 1);

                // Prevent overlapping next event
                const sameDayEvents = events
                    .filter(ev => ev.day === creatingEvent.day)
                    .sort((a, b) => this.hours.indexOf(a.start) - this.hours.indexOf(b.start));

                const startSlot = creatingEvent.startSlot;

                for (let ev of sameDayEvents) {
                    const eventStartSlot = this.hours.indexOf(ev.start);
                    if (eventStartSlot > startSlot && eventStartSlot < endSlot) {
                        endSlot = eventStartSlot; // restrict to before next event
                        break;
                    }
                }

                creatingEvent.endSlot = endSlot;
                creatingEvent.mouseMove = true;

                const start = this.hours[Math.min(startSlot, endSlot)];
                const end = this.hours[Math.max(startSlot, endSlot)];
                this.setState({
                    creatingEvent,
                    tooltip: {
                        visible: true,
                        x: this.isWorkbench ? e.clientX - 16 : e.clientX - 60,
                        y: e.clientY - 120,
                        text: `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.StartAt')}: ${start} \n${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EndAt')}: ${end}`,
                    }
                });
            };

            handleTimelineMouseLeave = () => {
                this.setState({
                    tooltip: {
                        visible: false,
                        x: 0,
                        y: 0,
                        text: '',
                    }
                });
            }

            handleTimelineMouseUp = () => {
                const { creatingEvent, events, localScheduleType } = this.state;
                if (!creatingEvent) return;

                const { day, startSlot, endSlot } = creatingEvent;
                const sortedSlots = [startSlot, endSlot].sort((a, b) => a - b);
                const start = this.hours[sortedSlots[0]];
                const end = this.hours[Math.min(sortedSlots[1], 96)];

                const newEvent = {
                    eventId: events.length,
                    day,
                    start,
                    end,
                    status: localScheduleType,
                };

                creatingEvent.mouseMove && events.push(newEvent);

                this.props.onDynamicPageChanged('Scheduling', { events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                });

                this.setState((prevState) => ({
                    events,
                    creatingEvent: null,
                    tooltip: {
                        visible: false,
                        x: 0,
                        y: 0,
                        text: '',
                    }
                }));
            };

            // Handle right-click event
            handleContextMenu = (e, eventIndex) => {
                this.setState({ selectedEventIndex: eventIndex })
                e.preventDefault(); // Prevent the default context menu from showing
                this.setState({
                  contextMenuPosition: { x: e.clientX, y: e.clientY },
                });
            };
            
            // Close the context menu if clicked outside
            handleClickOutside = (e) => {
                if (
                    this.contextMenuRef.current &&
                    !this.contextMenuRef.current.contains(e.target) &&
                    !this.divRef.current.contains(e.target)
                ) {
                    this.setState({ contextMenuPosition: null });
                }
            };

            toggleDaysSelection = day => {
                let selectedDaysForCopy = [...this.state.selectedDaysForCopy];
                if(selectedDaysForCopy.indexOf(day) !== -1) {
                    selectedDaysForCopy.splice(selectedDaysForCopy.indexOf(day),1);
                } else {
                    selectedDaysForCopy.push(day);
                }
                this.setState({ selectedDaysForCopy });
            }

            selectAll = () => {
                this.setState({ selectedDaysForCopy: this.daysFull });
            }

            deselectAll = () => {
                this.setState({ selectedDaysForCopy: []});
            }  
            
            addHoliday = () => {
                let holidaysList = [...this.state.holidaysList];
                if (this.state.holidayType === 'recurringDaysEveryYear') {
                    holidaysList.push({
                        holidayType: this.state.holidayType,
                        name: this.state.name,
                        week: this.state.week,
                        weekday: this.state.weekday,
                        month: this.state.month,
                        flag: 'New'
                    });
                } else if (this.state.holidayType === 'specificDateEveryYear') {
                    holidaysList.push({
                        holidayType: this.state.holidayType,
                        name: this.state.name,
                        month: this.state.month,                    
                        date: this.state.date, 
                        flag: 'New'                   
                    });
                } else {
                    holidaysList.push({
                        holidayType: this.state.holidayType,
                        name: this.state.name,
                        fromMonth: this.state.fromMonth,                    
                        fromDate: this.state.fromDate,    
                        toMonth: this.state.toMonth,                    
                        toDate: this.state.toDate,   
                        flag: 'New'             
                    });
                }
                this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: holidaysList, scheduleIndex: this.props.scheduleIndex
                });
                this.setState({ holidaysList, selectedHoliday: '', configureHolidayModal: false, name: '', week: 'anyWeek', 
                                    weekday: 'anyDay', month: 'anyMonth', date: 1, fromMonth: 'anyMonth', fromDate: 1, 
                                    toMonth: 'anyMonth', toDate: 1, holidayAlreadyExist: false, nameAlreadyExist: false, toMonthDateMismatch: false, everyOnAllPlaces: false });
            }

            editHolidayButton = () => {
                let holidaysList = [...this.state.holidaysList];
                if (this.state.holidayType === 'recurringDaysEveryYear') {
                    holidaysList[this.state.selectedHoliday].name = this.state.name;
                    holidaysList[this.state.selectedHoliday].week = this.state.week;
                    holidaysList[this.state.selectedHoliday].weekday = this.state.weekday;
                    holidaysList[this.state.selectedHoliday].month = this.state.month;
                    holidaysList[this.state.selectedHoliday].flag = 'Edited';
                } else if (this.state.holidayType === 'specificDateEveryYear') {
                    holidaysList[this.state.selectedHoliday].name = this.state.name;
                    holidaysList[this.state.selectedHoliday].month = this.state.month;
                    holidaysList[this.state.selectedHoliday].date = this.state.date;
                    holidaysList[this.state.selectedHoliday].flag = 'Edited';
                } else {
                    holidaysList[this.state.selectedHoliday].name = this.state.name;
                    holidaysList[this.state.selectedHoliday].fromMonth = this.state.fromMonth;
                    holidaysList[this.state.selectedHoliday].fromDate = this.state.fromDate;
                    holidaysList[this.state.selectedHoliday].toMonth = this.state.toMonth;
                    holidaysList[this.state.selectedHoliday].toDate = this.state.toDate;
                    holidaysList[this.state.selectedHoliday].flag = 'Edited';
                }
                this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: holidaysList, scheduleIndex: this.props.scheduleIndex
                });
                this.setState({ holidaysList, selectedHoliday: '', configureHolidayModal: false, name: '', week: 'anyWeek', 
                    weekday: 'anyDay', month: 'anyMonth', date: 1, fromMonth: 'anyMonth', fromDate: 1, 
                    toMonth: 'anyMonth', toDate: 1, holidayAlreadyExist: false, nameAlreadyExist: false, toMonthDateMismatch: false, everyOnAllPlaces: false }, () => this.checkForDuplicateHolidaysExist());
            }

            deleteHoliday = (selectedIndex) => {
                let holidaysList = [...this.state.holidaysList];
                holidaysList.splice(selectedIndex, 1);
                this.props.onDynamicPageChanged('Scheduling', { events: this.state.events, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: holidaysList, scheduleIndex: this.props.scheduleIndex
                });
                this.setState({ holidaysList }, () => this.checkForDuplicateHolidaysExist());
            }
            
            editHoliday = (selectedIndex) => {
                if (this.state.holidaysList[selectedIndex].holidayType === 'recurringDaysEveryYear') {
                    this.setState({ configureHolidayModal: true, holidayType: this.state.holidaysList[selectedIndex].holidayType, selectedHoliday: selectedIndex,
                        name: this.state.holidaysList[selectedIndex].name, 
                        week: this.state.holidaysList[selectedIndex].week,
                        weekday: this.state.holidaysList[selectedIndex].weekday,
                        month: this.state.holidaysList[selectedIndex].month,
                    });
                } else if (this.state.holidaysList[selectedIndex].holidayType === 'specificDateEveryYear') {
                    this.setState({ configureHolidayModal: true, holidayType: this.state.holidaysList[selectedIndex].holidayType, selectedHoliday: selectedIndex,
                        name: this.state.holidaysList[selectedIndex].name, 
                        month: this.state.holidaysList[selectedIndex].month,
                        date: this.state.holidaysList[selectedIndex].date,
                    });
                } else {
                    this.setState({ configureHolidayModal: true, holidayType: this.state.holidaysList[selectedIndex].holidayType, selectedHoliday: selectedIndex,
                        name: this.state.holidaysList[selectedIndex].name, 
                        fromMonth: this.state.holidaysList[selectedIndex].fromMonth,
                        fromDate: this.state.holidaysList[selectedIndex].fromDate,
                        toMonth: this.state.holidaysList[selectedIndex].toMonth,
                        toDate: this.state.holidaysList[selectedIndex].toDate,
                    });
                }
            }

            getDays = () => {
                let numberOfDays;
                let days = [];
                switch (this.state.month) {
                    case 'January':
                    case 'March':
                    case 'May':
                    case 'July':
                    case 'August':
                    case 'October':
                    case 'December':
                    case 'anyMonth':
                        numberOfDays = 31;
                        break;

                    case 'April':
                    case 'June':
                    case 'September':
                    case 'November':
                        numberOfDays = 30;
                        break;

                    case 'February':
                        numberOfDays = 28;
                        break;
                }

                for(let i=1; i<=numberOfDays; i++) {
                    days.push({
                        "text": i, 
                        "value": i, 
                        "key": i
                    })
                }
                this.setState({ daysOptions: days });
            }
            
            getMonth = () => { 
                return [
                    {"text": "Every Month", "value": "anyMonth", "key": "anyMonth"},
                    {"text": "January", "value": "January", "key": "January"},
                    {"text": "February", "value": "February", "key": "February"},
                    {"text": "March", "value": "March", "key": "March"},
                    {"text": "April", "value": "April", "key": "April"},
                    {"text": "May", "value": "May", "key": "May"},
                    {"text": "June", "value": "June", "key": "June"},
                    {"text": "July", "value": "July", "key": "July"},
                    {"text": "August", "value": "August", "key": "August"},
                    {"text": "September", "value": "September", "key": "September"},
                    {"text": "October", "value": "October", "key": "October"},
                    {"text": "November", "value": "November", "key": "November"},
                    {"text": "December", "value": "December", "key": "December"}
                ]
            }

            checkForDuplicateHolidaysExist = () => {
                let holidaysList = [...this.state.holidaysList];
                let duplicateHolidayIndex = [];
                let disableSaveButton = false;
                holidaysList.map((item, index) => {
                    holidaysList.map((innerItem, innerIndex) => {
                        const isRecurringDuplicate = item.holidayType === 'recurringDaysEveryYear' &&
                            index !== innerIndex &&
                            item.holidayType === innerItem.holidayType && 
                            item.week === innerItem.week && 
                            item.weekday === innerItem.weekday && 
                            item.month === innerItem.month;
                        const isSpecificDateDupicate = item.holidayType === 'specificDateEveryYear' &&
                            index !== innerIndex &&
                            item.holidayType === innerItem.holidayType && 
                            item.month === innerItem.month &&
                            item.date === innerItem.date;
                        const isDateRangeDuplicate = item.holidayType === 'dateRangeEveryYear' &&
                            index !== innerIndex &&
                            item.holidayType === innerItem.holidayType && 
                            item.fromMonth === innerItem.fromMonth &&
                            item.fromDate === innerItem.fromDate && 
                            item.toMonth === innerItem.toMonth &&
                            item.toDate === innerItem.toDate;
                        if(isRecurringDuplicate || isSpecificDateDupicate || isDateRangeDuplicate) {
                            duplicateHolidayIndex.push(index, innerIndex);
                            disableSaveButton = true;                           
                        }
                    });
                });
                this.setState({ duplicateHolidayIndex });
            }

            componentDidMount() {
                document.addEventListener('click', this.handleClickOutside);
                this.getDays();  
                this.checkForDuplicateHolidaysExist();       
                document.addEventListener("mousemove", this.handleMouseMove);
                document.addEventListener("mouseup", this.handleMouseUp);       
            }
            
            componentWillUnmount() {
                document.removeEventListener('click', this.handleClickOutside);
                document.removeEventListener("mousemove", this.handleMouseMove);
                document.removeEventListener("mouseup", this.handleMouseUp);
                if (this.tooltipRAF) cancelAnimationFrame(this.tooltipRAF);
            }     

            handleEventMouseDown = (index) => (e) => {
                if (this.props.readOnly || this.state.dragging) return;

                const timelineRect = this.divRef.current.getBoundingClientRect();
                const offsetX = e.clientX - timelineRect.left;

                const event = this.state.events[index];
                const startSlot = this.hours.indexOf(event.start);
                const slotWidth = timelineRect.width / 96.0;
                const startSlotOffset = offsetX - startSlot * slotWidth;

                this.setState({
                    draggingEvent: {
                    index,
                    startSlotOffset,
                    },
                });

                document.addEventListener("mousemove", this.handleEventMouseMove);
                document.addEventListener("mouseup", this.handleEventMouseUp);
            };

            handleEventMouseMove = (e) => {
                const { draggingEvent, events } = this.state;
                if (!draggingEvent) return;

                const { index, startSlotOffset } = draggingEvent;
                const timelineRect = this.divRef.current.getBoundingClientRect();
                const slotWidth = timelineRect.width / 96.0;

                const rawX = e.clientX - timelineRect.left - startSlotOffset;
                let newStartSlot = Math.round(rawX / slotWidth);

                const currentEvent = events[index];
                const durationSlots = this.hours.indexOf(currentEvent.end) - this.hours.indexOf(currentEvent.start);

                const prevEvent = events.filter((ev, i) => ev.day === currentEvent.day && this.hours.indexOf(ev.end) <= this.hours.indexOf(currentEvent.start) && index !==i).sort((a, b) => this.hours.indexOf(a.start) - this.hours.indexOf(b.start)).slice(-1)[0];
                const nextEvent = events.filter((ev, i) => ev.day === currentEvent.day && this.hours.indexOf(ev.start) >= this.hours.indexOf(currentEvent.end) && index !==i).sort((a, b) => this.hours.indexOf(a.start) - this.hours.indexOf(b.start))[0];

                // Boundaries check
                if (prevEvent) {
                    const prevEndSlot = this.hours.indexOf(prevEvent.end);
                    newStartSlot = Math.max(newStartSlot, prevEndSlot);
                } else {
                    newStartSlot = Math.max(newStartSlot, 0);
                }

                if (nextEvent) {
                    const nextStartSlot = this.hours.indexOf(nextEvent.start);
                    if (newStartSlot + durationSlots >= nextStartSlot) {
                        newStartSlot = nextEvent ? nextStartSlot - durationSlots : newStartSlot;
                    }
                } else {
                    newStartSlot = Math.min(newStartSlot, 96 - durationSlots);
                }

                const newStart = this.hours[newStartSlot];
                const newEnd = this.hours[newStartSlot + durationSlots];

                this.setState({
                    tooltip: {
                        visible: true,
                        x: this.isWorkbench ? e.clientX - 16 : e.clientX - 60,
                        y: e.clientY - 120,
                        text: `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.StartAt')}: ${newStart} \n${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EndAt')}: ${newEnd}`,
                    }
                });

                const updatedEvents = events;
                currentEvent.start = newStart;
                currentEvent.end = newEnd;
                updatedEvents[index] = currentEvent;

                this.props.onDynamicPageChanged('Scheduling', { events: updatedEvents, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                });

                this.setState({ events: updatedEvents });
            };

            handleEventMouseUp = () => {
                this.setState({ draggingEvent: null,
                    tooltip: {
                        visible: false,
                        x: 0,
                        y: 0,
                        text: '',
                    }
                 });
                document.removeEventListener("mousemove", this.handleEventMouseMove);
                document.removeEventListener("mouseup", this.handleEventMouseUp);
            };
            
            handleMouseDown = (index, edge) => (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.setState({ dragging: { index, edge } });
            };

            handleMouseMove = (e) => {
                const { dragging, events } = this.state;
                if (!dragging) return;

                const rect = this.divRef.current.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const percent = x / rect.width;
                const totalIntervals = 96; // 15-minute intervals per day
                let timeIndex = Math.round(percent * totalIntervals);
                timeIndex = Math.max(0, Math.min(96, timeIndex)); // Clamp

                const updatedEvents = events;
                const event = updatedEvents[dragging.index];
                const eventDay = event.day;

                const otherEvents = events.filter((e, i) => 
                    i !== dragging.index && e.day === eventDay
                );

                const originalStartIndex = this.hours.indexOf(event.start);
                const originalEndIndex = this.hours.indexOf(event.end);

                if (dragging.edge === 'left') {
                    const newStartIndex = timeIndex;
                    const currentEndIndex = this.hours.indexOf(event.end);
                    const currentStartIndex = this.hours.indexOf(event.start);

                    if (currentEndIndex - newStartIndex >= 1 && newStartIndex !== currentStartIndex) {
                        const overlaps = otherEvents.some(ev => {
                            const evStart = this.hours.indexOf(ev.start);
                            const evEnd = this.hours.indexOf(ev.end);
                            return newStartIndex < evEnd && currentEndIndex > evStart;
                        });

                        if (!overlaps) {
                            event.start = this.hours[newStartIndex];
                        }
                    }
                } else if (dragging.edge === 'right') {
                    const newEndIndex = timeIndex;
                    const currentStartIndex = this.hours.indexOf(event.start);
                    const currentEndIndex = this.hours.indexOf(event.end);

                    if (newEndIndex > currentStartIndex && newEndIndex !== currentEndIndex) {
                        const overlaps = otherEvents.some(ev => {
                            const evStart = this.hours.indexOf(ev.start);
                            const evEnd = this.hours.indexOf(ev.end);
                            // Collision check for new end range
                            return currentStartIndex < evEnd && newEndIndex > evStart;
                        });

                        if (!overlaps) {
                            event.end = this.hours[newEndIndex];
                        }
                    }
                }

                updatedEvents[dragging.index] = event;
                this.props.onDynamicPageChanged('Scheduling', { events: updatedEvents, defaultLocalScheduleType: this.state.localScheduleType,
                    label: this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].label, Holidays: this.state.holidaysList, scheduleIndex: this.props.scheduleIndex
                });
                const tooltipText = `${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.StartAt')}: ${event.start} \n${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EndAt')}: ${event.end}`;
                this.updateTooltipPosition(e, tooltipText);
                this.setState({
                    events: updatedEvents,
                });
            };

            handleMouseUp = () => {
                if (this.state.dragging) {
                    setTimeout(() => {
                        this.setState({ dragging: null,
                        tooltip: {
                            visible: false,
                            x: 0,
                            y: 0,
                            text: '',
                        }
                     });
                    }, 10);                    
                }
            };

            static getDerivedStateFromProps(props, state) {
                if(props.saving) {
                    let holidaysList = [...state.holidaysList];
                    holidaysList.forEach((holiday) => holiday.flag && delete holiday.flag);
                    return { holidaysList };
                }
                return null; // Must return null when no state changes are needed
            }


            render() {
                const { contextMenuPosition } = this.state;
                return (
                    <div className="tc-component">
                        <Tab 
                        className="hon-dynamic-page-tab"
                        menu={{ secondary: true, pointing: true }}
                        onTabChange={this.handleTabChange}
                        renderActiveOnly
                        activeIndex={this.state.activeTabIndex}
                        panes={this.panes()} />
                        <Modal
                            closeOnDimmerClick={false}
                            open={this.state.addEventModal}
                            style={{width: '485px'}}
                            onClose={() => this.setState({addEventModal: false})}
                        >
                            <ModalHeader>{this.state.selectedEventIndex === '' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.AddEvent') : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EditEvent')}</ModalHeader>
                            <ModalContent className='honeywell-device-wizard add-event-modal'>
                                <div>
                                    <DropdownFloatLabel
                                        key={'state'}
                                        name={'state'}
                                        disabled={this.props.readOnly}
                                        clearable={false}
                                        dataObj={null}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.State')}
                                        tooltip={''}
                                        visible={true}
                                        options={this.props.globalStore.dynamicStores.Scheduling[this.props.scheduleIndex].items.Schedules.localScheduleTypeOptions || []}
                                        value={this.state.state}
                                        onChange={this.onChange}
                                    />
                                </div>
                                <div className="tc-component-group-inline">
                                    <DropdownFloatLabel
                                        key={'startAt'}
                                        name={'startAt'}
                                        disabled={false}
                                        clearable={false}
                                        dataObj={null}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.StartAt')}
                                        tooltip={''}
                                        visible={true}
                                        options={this.hoursOptions}
                                        value={this.state.startAt}
                                        onChange={this.onChange}
                                    />
                                    <DropdownFloatLabel
                                        key={'endAt'}
                                        name={'endAt'}
                                        disabled={false}
                                        clearable={false}
                                        dataObj={null}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EndAt')}
                                        tooltip={''}
                                        visible={true}
                                        options={this.hoursOptions}
                                        value={this.state.endAt}
                                        onChange={this.onChange}
                                    />
                                </div>
                                <div className={this.state.eventsOverlapping ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ExistingScheduleOverlapping')}</div>
                                <div className={this.state.startEndTimeMismatch ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ScheduelEndTimeError')}</div>
                            </ModalContent>
                            <ModalActions>
                                <Button 
                                className='add-event-cancel-button'
                                onClick={() => this.setState({addEventModal: false, startAt: '00:00', endAt: '00:15', state: 1, selectedEventIndex: '', eventsOverlapping: false, startEndTimeMismatch: false })}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CANCEL')}</Button>
                                <Button disabled={(this.state.eventsOverlapping || this.state.startEndTimeMismatch) ? true : false } 
                                className='add-event-button'
                                onClick={this.state.selectedEventIndex === '' ? this.addEvent : this.editEvent}>{this.state.selectedEventIndex === '' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ADDEVENT') : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EDITEVENT')}
                                </Button>
                            </ModalActions>
                        </Modal>
                        <Modal
                            closeOnDimmerClick={false}
                            open={this.state.copyEventModal}
                            size='tiny'
                            onClose={() => this.setState({copyEventModal: false})}
                        >
                            <ModalHeader>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CopyEvent')}</ModalHeader>
                            <ModalContent className='honeywell-device-wizard'>
                            <div className="copy-event-container">
                                <div className="copy-event-days-container">
                                    <div className="target-weekday">{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Targetweekday')}</div>
                                    {this.daysFull.map(day => {
                                        return <div key={day} onClick={() => this.toggleDaysSelection(day)} 
                                            className={(this.state.events[this.state.selectedEventIndex] && this.state.events[this.state.selectedEventIndex || 0].day === day.slice(0,3)) ? "event-day event-day-selected-green" :
                                             (this.state.selectedEventIndex === '' && day.slice(0,3) === this.state.selectedDay) ? 
                                            "event-day event-day-selected-green" : this.state.selectedDaysForCopy.indexOf(day) !== -1 ? 
                                            "event-day event-day-selected-blue" : "event-day"}>
                                            {day}{this.state.selectedEventIndex === '' ? day.slice(0,3) === this.state.selectedDay && 
                                            ` ${honApplicationHandlerLex.get('HoneywellDeviceWizardLex.AllDay')}` : this.state.events[this.state.selectedEventIndex] && 
                                            (this.state.events[this.state.selectedEventIndex || 0].day === day.slice(0,3) && 
                                            ` (${this.state.events[this.state.selectedEventIndex || 0].start} - ${this.state.events[this.state.selectedEventIndex || 0].end})`)}
                                        </div>
                                    })}
                                </div>
                                <div className="copy-event-button-container">
                                    <div className="select-all-button-container"><Button onClick={this.selectAll} className='select-all-button'>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SELECTALL')}</Button></div>
                                    <div><Button onClick={this.deselectAll} className='select-all-button'>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.DESELECTALL')}</Button></div>
                                </div>
                            </div>
                            </ModalContent>
                            <ModalActions>
                                <Button className='copyto-cancel-button' onClick={() => this.setState({copyEventModal: false, selectedDaysForCopy: [] })}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CANCEL')}</Button>
                                <Button className="select-all-button-container copyto-button" onClick={this.copyTo}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.COPYTO')}</Button>
                            </ModalActions>
                        </Modal>
                        <Modal
                            closeOnDimmerClick={false}
                            open={this.state.configureHolidayModal}
                            style={{width: '714px'}}
                            onClose={() => this.setState({configureHolidayModal: false})}
                        >
                            <ModalHeader>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ConfigureHoliday')}</ModalHeader>
                            <ModalContent className='honeywell-device-wizard configure-holiday-modal'>
                                <div>
                                <div className="holiday-container">
                                    <SelectWidget
                                        key={'holidayType'}
                                        name={'holidayType'}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.HolidayType')}
                                        tooltip={''}
                                        value={this.state.holidayType}
                                        visible={true}
                                        disabled={false}
                                        readonly={false}
                                        options={[
                                            {
                                                "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.RECURRINGEVENTEVERYYEAR'),
                                                "value": "recurringDaysEveryYear",
                                                "key": "recurringDaysEveryYear"
                                            },
                                            {
                                                "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SPECIFICDATEEVERYYEAR'),
                                                "value": "specificDateEveryYear",
                                                "key": "specificDateEveryYear"
                                            },
                                            {
                                                "text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.DATERANGEEVERYYEAR'),
                                                "value": "dateRangeEveryYear",
                                                "key": "dateRangeEveryYear"
                                            },
                                        ]}
                                        onChange={this.onChange}
                                        dataObj={{value: this.state.holidayType, name: 'holidayType'}}
                                    />
                                </div>
                                    <TextInputFloatLabel
                                        key={'name'}
                                        name={'name'}
                                        dataObj={{value: this.state.name}}
                                        visible={true}
                                        disabled={false}
                                        readonly={false}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Name')}
                                        tooltip={''}
                                        value={this.state.name}
                                        onChange={this.onChange}
                                        defaultValue={''}
                                    />                                      
                                    <div className="tc-component-group-inline">                                  
                                        {this.state.holidayType === 'recurringDaysEveryYear' && <><DropdownFloatLabel
                                            key={'week'}
                                            name={'week'}
                                            disabled={false}
                                            clearable={false}
                                            dataObj={null}
                                            label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Week')}
                                            tooltip={''}
                                            visible={true}
                                            options={[
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryWeek'), "value": "anyWeek", "key": "anyWeek"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.First'), "value": "first", "key": "first"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Second'), "value": "second", "key": "second"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Third'), "value": "third", "key": "third"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Fourth'), "value": "fourth", "key": "fourth"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Fifth'), "value": "fifth", "key": "fifth"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Last'), "value": "lastWeek", "key": "lastWeek"},
                                            ]}
                                            value={this.state.week}
                                            onChange={this.onChange}
                                        />
                                        <DropdownFloatLabel
                                            key={'weekday'}
                                            name={'weekday'}
                                            disabled={false}
                                            clearable={false}
                                            dataObj={null}
                                            label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Weekday')}
                                            tooltip={''}
                                            visible={true}
                                            options={[
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EveryDay'), "value": "anyDay", "key": "anyDay"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Sunday'), "value": "sunday", "key": "sunday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Monday'), "value": "monday", "key": "monday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Tuesday'), "value": "tuesday", "key": "tuesday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Wednesday'), "value": "wednesday", "key": "wednesday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Thursday'), "value": "thursday", "key": "thursday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Friday'), "value": "friday", "key": "friday"},
                                                {"text": honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Saturday'), "value": "saturday", "key": "saturday"},
                                            ]}
                                            value={this.state.weekday}
                                            onChange={this.onChange}
                                        />
                                        </>}
                                        {this.state.holidayType !== 'dateRangeEveryYear' && <DropdownFloatLabel
                                            key={'month'}
                                            name={'month'}
                                            disabled={false}
                                            clearable={false}
                                            dataObj={null}
                                            label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Month')}
                                            tooltip={''}
                                            visible={true}
                                            options={this.getMonth()}
                                            value={this.state.month}
                                            onChange={this.onChange}
                                        />}                                        
                                    </div>
                                    {this.state.holidayType === 'specificDateEveryYear' && <DropdownFloatLabel
                                        key={'date'}
                                        name={'date'}
                                        disabled={false}
                                        clearable={false}
                                        dataObj={null}
                                        label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Date')}
                                        tooltip={''}
                                        visible={true}
                                        options={this.state.daysOptions}
                                        value={this.state.date}
                                        onChange={this.onChange}
                                    />}
                                    {this.state.holidayType === 'dateRangeEveryYear' && <>
                                        <div className="tc-component-group-inline">
                                            <DropdownFloatLabel
                                                key={'fromMonth'}
                                                name={'fromMonth'}
                                                disabled={false}
                                                clearable={false}
                                                dataObj={null}
                                                label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.FromMonth')}
                                                tooltip={''}
                                                visible={true}
                                                options={this.getMonth()}
                                                value={this.state.fromMonth}
                                                onChange={this.onChange}
                                            />
                                            <DropdownFloatLabel
                                                key={'fromDate'}
                                                name={'fromDate'}
                                                disabled={false}
                                                clearable={false}
                                                dataObj={null}
                                                label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.FromDate')}
                                                tooltip={''}
                                                visible={true}
                                                options={this.state.daysOptions}
                                                value={this.state.fromDate}
                                                onChange={this.onChange}
                                            />
                                        </div>
                                        <div className="tc-component-group-inline">
                                            <DropdownFloatLabel
                                                key={'toMonth'}
                                                name={'toMonth'}
                                                disabled={this.state.fromMonth === 'anyMonth' ? true : false}
                                                clearable={false}
                                                dataObj={null}
                                                label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ToMonth')}
                                                tooltip={''}
                                                visible={true}
                                                options={this.getMonth()}
                                                value={this.state.toMonth}
                                                onChange={this.onChange}
                                            />
                                            <DropdownFloatLabel
                                                key={'toDate'}
                                                name={'toDate'}
                                                disabled={false}
                                                clearable={false}
                                                dataObj={null}
                                                label={honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ToDate')}
                                                tooltip={''}
                                                visible={true}
                                                options={this.state.daysOptions}
                                                value={this.state.toDate}
                                                onChange={this.onChange}
                                            />
                                        </div>
                                    </>
                                    }
                                    <div className={this.state.nameAlreadyExist ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SameNameaAreadyExist')}</div>
                                    <div className={this.state.holidayAlreadyExist ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SameHolidayAlreadyExist')}</div>
                                    <div className={this.state.toMonthDateMismatch ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ToDateMonthError')}</div>
                                    <div className={this.state.everyOnAllPlaces ? "warning-info" : "hide"}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.atleastOneOptionWeekWeekDayMonthError')}</div>
                                </div>                                
                            </ModalContent>
                            <ModalActions>
                                <Button className='add-event-cancel-button' onClick={() => this.setState({configureHolidayModal: false, name: '', week: 'anyWeek', 
                                    weekday: 'anyDay', month: 'anyMonth', date: 1, fromMonth: 'anyMonth', fromDate: 1, selectedHoliday: '',
                                    toMonth: 'anyMonth', toDate: 1, holidayAlreadyExist: false, nameAlreadyExist: false, toMonthDateMismatch: false, everyOnAllPlaces: false })}>CANCEL</Button>
                                <Button className='add-event-button' disabled={(this.state.holidayAlreadyExist || this.state.nameAlreadyExist || this.state.toMonthDateMismatch || this.state.everyOnAllPlaces) ? true : this.state.holidayType === 'recurringDaysEveryYear' ? 
                                this.state.name === '' ? true : false : (this.state.name === '' || this.state.duration=== '') ? true : false}
                                onClick={this.state.selectedHoliday === '' ? this.addHoliday : this.editHolidayButton}>{this.state.selectedHoliday === '' ? honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ADDHOLIDAY') : honApplicationHandlerLex.get('HoneywellDeviceWizardLex.EDITHOLIDAY')}</Button>
                            </ModalActions>
                        </Modal>
                        <Modal
                            closeOnDimmerClick={false}
                            open={this.state.informationModal}
                            size='tiny'
                            onClose={() => this.setState({informationModal: false})}
                        >
                            <ModalHeader>Information</ModalHeader>
                            <ModalContent className='honeywell-device-wizard'>
                                <div>
                                {honApplicationHandlerLex.get('HoneywellDeviceWizardLex.SameHolidayAlreadyExist')}
                                </div>
                            </ModalContent>
                            <ModalActions>
                                <Button className='select-all-button-container copyto-button' onClick={() => this.setState({informationModal: false })}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.CANCEL')}</Button>
                            </ModalActions>
                        </Modal>
                        {contextMenuPosition && (
                            <ul
                                ref={this.contextMenuRef}
                                style={{
                                position: 'absolute',
                                top: contextMenuPosition.y-35,
                                left: contextMenuPosition.x-45,
                                listStyle: 'none',
                                backgroundColor: 'white',
                                border: '1px solid #ccc',
                                padding: '10px 0',
                                margin: 0,
                                boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
                                borderRadius: '4px',
                                }}
                            >
                                <li style={{ padding: '8px 20px', cursor: this.props.readOnly ? "not-allowed" : "pointer" }} onClick={this.copyEvent}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Copy')}</li>
                                <li style={{ padding: '8px 20px', cursor: this.props.readOnly ? "not-allowed" : "pointer" }} onClick={this.copyEventMF}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.ApplyMF')}</li>
                                <li style={{ padding: '8px 20px', cursor: this.props.readOnly ? "not-allowed" : "pointer" }} onClick={this.deleteEvent}>{honApplicationHandlerLex.get('HoneywellDeviceWizardLex.Delete')}</li>
                            </ul>
                        )}
                    </div>
                );
            }
        }

        return Scheduling;
});