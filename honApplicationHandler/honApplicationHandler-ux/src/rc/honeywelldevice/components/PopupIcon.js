define([
    'react',
    'semantic-ui-react'
],
    (
        <PERSON><PERSON>,
        SemanticReact
    ) => {
        'use strict';
        const {
            Popup
        } = SemanticReact;

        class PopupIcon extends React.Component {
            constructor(props) {
                super(props);
            }

            renderSvg() {
                return (
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        color="blue"
                        width="15"
                        height="12"
                        viewBox="0 0 24 24"
                    >
                        <path
                            fill="steelblue"
                            d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm1 18h-2v-8h2v8zm-1-12.25c.69 0 1.25.56 1.25 1.25s-.56 1.25-1.25 1.25-1.25-.56-1.25-1.25.56-1.25 1.25-1.25z"
                        />
                    </svg>
                );
            }

            render() {

                return (
                    <Popup
                        hoverable
                        trigger={
                            <label><>{this.renderSvg()}</></label>
                        }
                        position="bottom left"
                    >
                        <div
                            style={{ whiteSpace: "pre-line", maxHeight: "300px", overflow: "auto" }}
                        >
                            {this.props.tooltip}
                        </div>
                    </Popup>
                );

            }
        }

        return PopupIcon;
});
