define([
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/CommonVisibilityRules'
],
    function (
        CommonVisibilityRules
    ) {
        'use strict';

        const RuleType = Object.freeze({
            TERMINAL_ASSIGNMENT_TARGET_VALUE: 'TERMINAL_ASSIGNMENT_TARGET_VALUE'
        });
        const UNASSIGNED = "Unassigned";

        let impactedTabs = [];
        let impactedSubTabs = [];

        class TerminalAssignmentValueRules {

            static clearImpactedTab() {
                impactedTabs = [];
            }

            static clearImpactedSubTab() {
                impactedSubTabs = [];
            }

            static applyValueRules(globalStore, componentName) {
                const { dynamicStores, wizardRules, terminalStore } = globalStore;
                const { terminals } = terminalStore;
                if (!wizardRules || !terminals) {
                    return;
                }
                let terminalRuleErrors = [];
                let succeedRules = [];
                wizardRules.forEach(ruleForItem => {
                    const { terminalAssignmentValueRules } = ruleForItem;
                    if (terminalAssignmentValueRules) {
                        const conditionRule = terminalAssignmentValueRules["if"];
                        const passValueRules = terminalAssignmentValueRules["then"];
                        const failValueRules = terminalAssignmentValueRules["else"];
                        const result = CommonVisibilityRules.executeRule(conditionRule, dynamicStores);
                        if (result) {
                            const groupPassValueRules = TerminalAssignmentValueRules.groupByFunctionBlock(passValueRules);
                            TerminalAssignmentValueRules.processGroupedRules(groupPassValueRules, terminals, terminalRuleErrors, succeedRules);

                        } else {
                            const groupFailValueRules = TerminalAssignmentValueRules.groupByFunctionBlock(failValueRules);
                            TerminalAssignmentValueRules.processGroupedRules(groupFailValueRules, terminals, terminalRuleErrors, succeedRules);

                        }
                        (conditionRule.targetItem === componentName && impactedTabs.indexOf("Terminal$20Assignment") === -1) && impactedTabs.push("Terminal$20Assignment");
                        (conditionRule.targetItem === componentName && impactedSubTabs.indexOf('Terminal$20Assignment-Terminal$20Assignment') === -1) && impactedSubTabs.push('Terminal$20Assignment-Terminal$20Assignment');
                    }
                });

                if (terminalRuleErrors.length === 0) {
                    succeedRules.forEach(terminalOfRule => {
                        terminalOfRule.terminal.terminalAssignedName = terminalOfRule.assignedName;
                        terminalOfRule.terminal.changed = true;
                    });
                }
                return { terminalImpactedTabs: impactedTabs, terminalImpactedSubTabs: impactedSubTabs, terminalRuleErrors, changedTerminals: succeedRules.map(rule => ({ "pinName": rule.terminal.terminalName, "fbName": rule.terminal.terminalAssignedName })) };
            }
            static processGroupedRules(groupedRules, terminals, terminalRuleErrors, succeedRules) {
                Object.keys(groupedRules).forEach(fbName => {
                    const valueRules = groupedRules[fbName];
                    const result = TerminalAssignmentValueRules.processRule(fbName, terminals, valueRules, terminalRuleErrors, succeedRules);
                    if (result !== null && result !== undefined) {
                        succeedRules.push(result);
                    }

                });

            }
            static processRule(fbName, terminals, valueRules, errorFunctions, succeedRules) {
                if (TerminalAssignmentValueRules.checkIfFunctionBeAssigned(fbName, terminals, succeedRules) &&
                    !TerminalAssignmentValueRules.checkIfRuleIncludeUnassigned(valueRules)) {
                    console.log(fbName, "has been assigned, ignore the rule", valueRules);
                } else {
                    const concatenatedTargetValues = valueRules.map(rule => rule.resultRule.targetValue).join(' or ');
                    const foundTerminal = TerminalAssignmentValueRules.executeValueRule(valueRules, terminals, fbName, succeedRules);
                    if (undefined === foundTerminal) {
                        errorFunctions.push(fbName + " requires " + concatenatedTargetValues);
                    }
                    return foundTerminal;
                }
                return null;
            }
            static groupByFunctionBlock(rules) {
                const grouped = rules.reduce((acc, rule) => {
                    const item = rule.item;
                    if (!acc[item]) {
                        acc[item] = [];
                    }
                    acc[item].push({
                        resultRule: rule.resultRule,
                        itemStore: rule.itemStore
                    });
                    return acc;
                }, {});
                return grouped;

            }


            static executeValueRule(ruleObjs, terminals, fbName, succeedRules) {
                if (!ruleObjs) {
                    return null;
                }
                let foundTerminal;
                for (const ruleObj of ruleObjs) {
                    const { resultRule } = ruleObj;
                    const { type, targetValue } = resultRule;
                    if (type === RuleType.TERMINAL_ASSIGNMENT_TARGET_VALUE && targetValue) {
                        let sortedTerminals = terminals.sort((a, b) => {
                            const numA = parseInt(a.terminalName.match(/\d+$/)[0], 10);
                            const numB = parseInt(b.terminalName.match(/\d+$/)[0], 10);
                            return numA - numB;
                        });
                        for (let i = 0; i < sortedTerminals.length; i++) {
                            const terminal = terminals[i];
                            if (targetValue === UNASSIGNED) {
                                const assignedTerminal = TerminalAssignmentValueRules.findTerminalByFbName(fbName, terminals);
                                if (assignedTerminal) {
                                    foundTerminal = { terminal: assignedTerminal, assignedName: UNASSIGNED };
                                } else {
                                    console.log(fbName, " has not been assigned previously, don't need to clean responding terminal.");
                                    return null;
                                }
                                break;
                            } else {
                                if (terminal.terminalAssignedName === UNASSIGNED && terminal.terminalName.startsWith(targetValue) &&
                                    terminal.terminalOptions.some(option => option.value === fbName) && !succeedRules.some(rule => rule.terminal.terminalName === terminal.terminalName)) {
                                    foundTerminal = { terminal, assignedName: fbName };
                                    break;
                                }
                            }

                        }
                        if (foundTerminal) {
                            break;
                        }
                    }
                }
                if (!foundTerminal) {
                    console.error("No terminal found for item: " + fbName);
                }
                return foundTerminal;
            }

            static checkIfFunctionBeAssigned(fbName, terminals, succeedRules) {
                return terminals.some(terminal => terminal.terminalAssignedName === fbName) || succeedRules.some(rule => rule.terminal.terminalAssignedName === fbName);
            }
            static checkIfRuleIncludeUnassigned(valueRules) {
                return valueRules.some(rule => rule.resultRule.targetValue === UNASSIGNED);

            }
            static findTerminalByFbName(fbName, terminals) {
                return terminals.find(terminal => terminal.terminalAssignedName === fbName);
            }

        }

        return TerminalAssignmentValueRules;
    });
