define([],
    function () {
        'use strict';

        const RuleType = Object.freeze({
            EQUALS: 'EQUALS',
            NOT_EQUALS: 'NOT_EQUALS',
            GREATER_THAN: 'GREATER_THAN',
            LESS_THAN: 'LESS_THAN',
            GREATER_THAN_OR_EQUALS: 'GREATER_THAN_OR_EQUALS',
            LESS_THAN_OR_EQUALS: 'LESS_THAN_OR_EQUALS',
            VALUE: 'VALUE',
            TARGETVALUE: 'TARGETVALUE',
            IS_VISIBLE: 'IS_VISIBLE',
            RULES: 'RULES',
            CALCULATION: 'CALCULATION',
			IS_HIDDEN: 'IS_HIDDEN'
        });

        let impactedTabs = [];
        let impactedSubTabs = [];

        class CommonVisibilityRules {
            
            static clearImpactedTab(tabName) {
                impactedTabs.indexOf(tabName) !== -1 && impactedTabs.splice(impactedTabs.indexOf(tabName), 1);
            }

            static clearImpactedSubTab(tabName) {
                impactedSubTabs.indexOf(tabName) !== -1 && impactedSubTabs.splice(impactedSubTabs.indexOf(tabName), 1);
            }

            static updateImpactedTab(tabs) {
                impactedTabs = tabs;
            }

            static filterDataByTargetItemName(data, targetItemName) {
                const dependencyQueue = [ targetItemName ];
                const seenItems = new Set();
                const result = [];
            
                const makeItemKey = (item, store) => `${ item } IN ${ store }`;
            
                while (dependencyQueue.length > 0) {
                    const currentItem = dependencyQueue.shift();
                    if (seenItems.has(currentItem)) continue;
                    seenItems.add(currentItem);
            
                    for (const entry of data) {
                        let isRelated = false;
            
                        // --- Check valueRules ---
                        if (entry.valueRules) {
                            const ifRule = entry.valueRules.if.rules || [ entry.valueRules.if ];
                            const { then = [], else: elseRules = [] } = entry.valueRules;
            
                            if (ifRule && ifRule.some(rule => {
                                if (rule.rules) {
                                    return rule.rules.some(innerRule => innerRule.targetItem === currentItem)
                                } else {
                                   return rule.targetItem === currentItem 
                                }
                            })) {
                                isRelated = true;
                                [ ...then, ...elseRules ].forEach(rule => {
                                    if (rule.item) dependencyQueue.push(rule.item);
                                });
                            } else {
                                const outputMatch = [ ...then, ...elseRules ].some(rule => rule.item === currentItem);
                                if (outputMatch) {
                                    isRelated = true;
                                    if (ifRule && ifRule.targetItem) dependencyQueue.push(ifRule.targetItem);
                                }
                            }
                        }
            
                        // --- Check visibilityRule ---
                        if (entry.visibilityRule) {
                            const rules = entry.visibilityRule.rules || [ entry.visibilityRule ];
                            const { trueVisibleItems = [], falseVisibleItems = [], trueInvisibleItems = [], falseInvisibleItems = [] } = entry;
                            if (rules.some(rule => rule.targetItem === currentItem)) {
                                isRelated = true;
                                [ ...trueVisibleItems, ...falseVisibleItems, ...trueInvisibleItems, ...falseInvisibleItems ].forEach(rule => {
                                    if (rule.split(" IN ")[0]) dependencyQueue.push(rule.split(" IN ")[0]);
                                });
                            } else {
                                const outputMatch = [ ...trueVisibleItems, ...falseVisibleItems, ...trueInvisibleItems, ...falseInvisibleItems ].some(rule => rule.split(" IN ")[0] === currentItem);
                                if (outputMatch) {
                                    isRelated = true;
                                    if (rules.targetItem) dependencyQueue.push(rules.targetItem);
                                }
                            }
                        }
            
                        // --- Check visibilityItems ---
                        [ 'trueVisibleItems', 'falseVisibleItems', 'trueInvisibleItems', 'falseInvisibleItems' ].forEach(key => {
                            (entry[key] || []).forEach(fullItem => {
                                const itemName = fullItem.split(" IN ")[0];
                                if (itemName === currentItem) {
                                    isRelated = true;
                                }
                            });
                        });
            
                        if (isRelated && !result.includes(entry)) {
                            result.push(entry);
                        }
                    }
                }
            
                return result;
            }

            static applyVisibleRules(globalStore, componentName, tabNameWithSubTabName) {
                
                const { dynamicStores, wizardRules } = globalStore;
                if (!wizardRules) {
                    return;
                }

                let filteredWizardRules = componentName ? this.filterDataByTargetItemName(wizardRules, componentName) : wizardRules;

                let anyRuleApplied = false;
				filteredWizardRules.forEach(ruleForItem => {
					const { trueVisibleItems, falseVisibleItems, trueInvisibleItems, falseInvisibleItems, visibilityRule } = ruleForItem;
					if (visibilityRule) {
						const result = CommonVisibilityRules.executeRule(visibilityRule, dynamicStores);
						if (result && ((trueVisibleItems && trueVisibleItems.length > 0) || (trueInvisibleItems && trueInvisibleItems.length > 0))) {
						    trueVisibleItems && trueVisibleItems.forEach(item => {
                                let values = item.split(" IN ");
                                dynamicStores[values[1]].items[values[0]].visible = true;
                            });
                            trueInvisibleItems && trueInvisibleItems.forEach(item => {
                                let values = item.split(" IN ");
                                dynamicStores[values[1]].items[values[0]].visible = false;
                            });
                            anyRuleApplied = true;
						}
						if (!result && ((falseVisibleItems && falseVisibleItems.length > 0) || (falseInvisibleItems && falseInvisibleItems.length > 0))) {
						    falseVisibleItems && falseVisibleItems.forEach(item => {
                                let values = item.split(" IN ");
                                dynamicStores[values[1]].items[values[0]].visible = true;
                            });
                            falseInvisibleItems && falseInvisibleItems.forEach(item => {
                                let values = item.split(" IN ");
                                dynamicStores[values[1]].items[values[0]].visible = false;
                            });
                            anyRuleApplied = true;
						}
                        if (visibilityRule.rules) {
                            visibilityRule.rules.map(rule => {
                                trueVisibleItems && trueVisibleItems.map(trueVisibleItem => {
                                    let values = trueVisibleItem.split(" IN ");
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                                });
                                falseVisibleItems && falseVisibleItems.map(falseVisibleItem => {
                                    let values = falseVisibleItem.split(" IN ");
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                                });
                                trueInvisibleItems && trueInvisibleItems.map(trueInvisibleItem => {
                                    let values = trueInvisibleItem.split(" IN ");
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                                });
                                falseInvisibleItems && falseInvisibleItems.map(falseInvisibleItem => {
                                    let values = falseInvisibleItem.split(" IN ");
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                    (rule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                                });
                            });                                    
                        } else {
                            trueVisibleItems && trueVisibleItems.map(trueVisibleItem => {
                                let values = trueVisibleItem.split(" IN ");
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                            });
                            falseVisibleItems && falseVisibleItems.map(falseVisibleItem => {
                                let values = falseVisibleItem.split(" IN ");
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                            });
                            trueInvisibleItems && trueInvisibleItems.map(trueInvisibleItem => {
                                let values = trueInvisibleItem.split(" IN ");
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                            });
                            falseInvisibleItems && falseInvisibleItems.map(falseInvisibleItem => {
                                let values = falseInvisibleItem.split(" IN ");
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedTabs.indexOf(values[1]) === -1) && impactedTabs.push(values[1]);
                                (visibilityRule.targetItem === componentName && tabNameWithSubTabName !== `${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }` && impactedSubTabs.indexOf(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`) === -1) && impactedSubTabs.push(`${ values[1] }-${ dynamicStores[values[1]].items[values[0]].tabInPage }`);
                            });
                        }
					}
				});

                if (anyRuleApplied) {
				    CommonVisibilityRules.updateGroupHeadingVisible(globalStore);
				}
                return { impactedTabs, impactedSubTabs };
			}

            static applyValueRules(globalStore, componentName, tabNameWithSubTabName) {
                const { dynamicStores, wizardRules } = globalStore;
                if (!wizardRules) {
                    return;
                }

                let filteredWizardRules = componentName ? this.filterDataByTargetItemName(wizardRules, componentName) : wizardRules;

				filteredWizardRules.forEach(ruleForItem => {
					const { valueRules } = ruleForItem;
					if (valueRules) {
						const conditionRule = valueRules["if"];
						const passValueRules = valueRules["then"];
						const failValueRules = valueRules["else"];
						const result = CommonVisibilityRules.executeRule(conditionRule, dynamicStores);
						if (result) {
							passValueRules.forEach(passValueRule => {
								const { item, resultRule, itemStore } = passValueRule;
								const finalResult = CommonVisibilityRules.executeValueRule(resultRule, dynamicStores);
								console.log("item: " + item + "; Result: " + finalResult);
								dynamicStores[itemStore].items[item].value = finalResult;
								if (dynamicStores[itemStore].items[item].highPrecisionValue != null &&
                                   								    dynamicStores[itemStore].items[item].highPrecisionValue !== undefined) {
								    dynamicStores[itemStore].items[item].highPrecisionValue = "" + finalResult;
								}
                                (conditionRule.targetItem === componentName && tabNameWithSubTabName !== `${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }` && impactedTabs.indexOf(itemStore) === -1) && impactedTabs.push(itemStore);
                                (conditionRule.targetItem === componentName && tabNameWithSubTabName !== `${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }` && impactedSubTabs.indexOf(`${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }`) === -1) && impactedSubTabs.push(`${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }`);

							});
						} else {
							failValueRules.forEach(failValueRule => {
								const { item, resultRule, itemStore } = failValueRule;
								const finalResult = CommonVisibilityRules.executeValueRule(resultRule, dynamicStores);
								console.log("item: " + item + "; Result: " + finalResult);
								dynamicStores[itemStore].items[item].value = finalResult;
								if (dynamicStores[itemStore].items[item].highPrecisionValue != null &&
								    dynamicStores[itemStore].items[item].highPrecisionValue !== undefined) {
								    dynamicStores[itemStore].items[item].highPrecisionValue = "" + finalResult;
								}
                                (conditionRule.targetItem === componentName && tabNameWithSubTabName !== `${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }` && impactedTabs.indexOf(itemStore) === -1) && impactedTabs.push(itemStore);
                                (conditionRule.targetItem === componentName && tabNameWithSubTabName !== `${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }` && impactedSubTabs.indexOf(`${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }`) === -1) && impactedSubTabs.push(`${ itemStore }-${ dynamicStores[itemStore].items[item].tabInPage }`);
							});
						}
					}
				});
                return { impactedTabs, impactedSubTabs };
            }

            static applyConstraintRules(srcItem, globalStore, beforeChangedValueOfTarget) {
                const { dynamicStores, wizardRules } = globalStore;
                if (!wizardRules) {
                    return;
                }
                wizardRules.forEach(ruleForItem => {
                    const { constraintRules, defaultPageStore } = ruleForItem;
                    if (constraintRules) {
                        CommonVisibilityRules.executeConstraintRule(srcItem, ruleForItem, dynamicStores, defaultPageStore, beforeChangedValueOfTarget);
                    }
                });
            }

            static executeRule(ruleObj, dynamicStores) {
                const { type, targetStore, expectedValue, operator, rules, expectedStore } = ruleObj;
				var targetItem;
				if (targetStore) {
                	targetItem = dynamicStores[targetStore].items[ruleObj.targetItem];
				}
				let finalExpectedValue = expectedValue;
				if (expectedStore) {
					finalExpectedValue = dynamicStores[expectedStore].items[ruleObj.expectedValue];
				}
                const targetValue = targetItem ? targetItem.value : undefined;
                let rulesFinalResult = operator === 'AND' ? true : (operator === 'OR' ? false : undefined);
                switch (type) {
                    case RuleType.EQUALS:
                        //here remove strict equal, because for switch button, its value is 0/1 in store, but in front end, it will be
                        //converted to boolean with loadConvert function.
                        return targetValue == finalExpectedValue;
                    case RuleType.NOT_EQUALS:
                        return targetValue != finalExpectedValue;
                    case RuleType.GREATER_THAN:
                        return targetValue > finalExpectedValue;
                    case RuleType.LESS_THAN:
                        return targetValue < finalExpectedValue;
                    case RuleType.GREATER_THAN_OR_EQUALS:
                        return targetValue >= finalExpectedValue;
                    case RuleType.LESS_THAN_OR_EQUALS:
                        return targetValue <= finalExpectedValue;
                    case RuleType.IS_VISIBLE:
                        return targetItem && (targetItem.visible || targetItem.visible === undefined);
                    case RuleType.IS_HIDDEN:
                        return targetItem && (targetItem.visible !== undefined && !targetItem.visible);
                    case RuleType.RULES:
                        rules.forEach(rule => {
                            const result = CommonVisibilityRules.executeRule(rule, dynamicStores);
                            if (operator === 'AND') {
                                rulesFinalResult = rulesFinalResult && result;
                            } else if (operator === 'OR') {
                                rulesFinalResult = rulesFinalResult || result;
                            }
                        });
                        return rulesFinalResult;
                    default:
                        return false;
                }
            }

            static executeValueRule(ruleObj, dynamicStores) {
                const { type, targetStore, targetValue, operator, values } = ruleObj;
				var targetItem;
				if (targetStore) {
					targetItem = ruleObj.targetItem ? dynamicStores[targetStore].items[ruleObj.targetItem] : undefined;
				}
                
                let rulesFinalResult = 0;
                switch (type) {
                    case RuleType.VALUE:
                        return targetValue;
                    case RuleType.TARGETVALUE:
                        return targetItem ? targetItem.value : undefined;
                    case RuleType.CALCULATION:
                        values.forEach((value, index) => {
                            const result = CommonVisibilityRules.executeValueRule(value, dynamicStores);
                            if (operator === 'ADD') {
                                rulesFinalResult = rulesFinalResult + result;
                            } else if (operator === 'SUBTRACT') {
                                if (index === 0) {
                                    rulesFinalResult = result;
                                } else {
                                    rulesFinalResult = rulesFinalResult - result;
                                }
                            }
                        });
                        return rulesFinalResult;
                    default:
                        return 0;
                }
            }

            static saveBeforeChangedItemValues(targetItem, beforeChangedValueOfTarget, items, pageStore, dynamicStores) {
                const beforeChangedItemValues = {};

                beforeChangedItemValues[targetItem] = {
                    itemInGlobalStore: CommonVisibilityRules.getItemFromDynamicStore(targetItem, dynamicStores, pageStore),
                    value: beforeChangedValueOfTarget
                };
                items.forEach(item => {
                    const itemInGlobalStore = CommonVisibilityRules.getItemFromDynamicStore(item, dynamicStores, pageStore);
                    beforeChangedItemValues[item] = { itemInGlobalStore: itemInGlobalStore, value: itemInGlobalStore.value };
                });

                return beforeChangedItemValues;
            }
            static revertChangedItemValuesWhenRuleFailed(beforeChangedItemValues) {
                for (const key in beforeChangedItemValues) {
                    if (beforeChangedItemValues.hasOwnProperty(key)) {
                        const item = beforeChangedItemValues[key];
                        item.itemInGlobalStore.value = item.value;
                    }
                }

            }


            static executeConstraintRule(targetItem, ruleObj, dynamicStores, pageStore, beforeChangedValueOfTarget) {
                // targetItem is the item that trigger the constraint rule
                // 1. go through all constraints in the ruleObj which is in notFinishedConstraints, and update the items that not satisfy the constraint,
                //    and then push them in affectedItems, and remove the constraint from notFinishedConstraints
                // 2. pop the affectedItems and fetch a new targetItem, then do the same as step 1
                // 3. when the affectedItems is empty, the constraint rule is done

                const affectedItems = [ targetItem ];
                const { items, constraintRules } = ruleObj;
                let notFinishedConstraints = Array.from(constraintRules);
                const beforeChangedItemValues = CommonVisibilityRules.saveBeforeChangedItemValues(targetItem, beforeChangedValueOfTarget, items, pageStore, dynamicStores);


                while (affectedItems.length > 0) {
                    const currentItem = affectedItems.pop();
                    if (!CommonVisibilityRules.itemIncludesInConstraintRule(currentItem, items, pageStore) || notFinishedConstraints.length === 0) {
                        continue;
                    }

                    const newNotFinishedConstraints = [];
                    let isFailed = false;

                    for (let i = 0; i < notFinishedConstraints.length; i++) {
                        const constraint = notFinishedConstraints[i];
                        if (!CommonVisibilityRules.isConstraintEnabled(constraint, dynamicStores, pageStore)) {
                            continue;
                        }
                        if (!CommonVisibilityRules.itemIncludesInConstraintRule(currentItem, constraint.items, pageStore)) {
                            newNotFinishedConstraints.push(constraint);
                            continue;
                        }

                        const newAffectedItems = CommonVisibilityRules.updateValueFromConstraintRule(currentItem, constraint, dynamicStores, pageStore);
                        if (newAffectedItems === false) {
                            isFailed = true;
                            break;
                        }


                        if (newAffectedItems.length > 0) {
                            affectedItems.push(...newAffectedItems);
                        }
                    }
                    if (isFailed) {
                        CommonVisibilityRules.revertChangedItemValuesWhenRuleFailed(beforeChangedItemValues);
                        return;
                    }

                    notFinishedConstraints = Array.from(newNotFinishedConstraints);
                }


            }

            static isConstraintEnabled(constraint, dynamicStores, pageStore) {
                const { enableRule } = constraint;
                if (enableRule === undefined) {
                    return true;
                }
                if (typeof (enableRule) !== "object") {
                    return !!enableRule;
                }

                return CommonVisibilityRules.executeRule(enableRule, dynamicStores, pageStore);
            }

            static itemIncludesInConstraintRule(item, itemsInConstraintRule, pageStore) {
                if (typeof (item) === 'string') {
                    return itemsInConstraintRule.includes(item) ||
                    itemsInConstraintRule.some(itemInRule => typeof (itemInRule) === 'object' && (itemInRule.targetStore === undefined || itemInRule.targetStore === pageStore) && itemInRule.targetItem === item);
                }
                if (typeof (item) === 'object') {
                    return itemsInConstraintRule.some(itemInRule => typeof (itemInRule) === 'object' && (itemInRule.targetStore === undefined || itemInRule.targetStore === pageStore) && itemInRule.targetItem === item.targetItem);
                }
                return false;
            }

            static itemIndexOf(item, itemsInConstraintRule, pageStore) {
                if (typeof (item) === 'string') {
                    if (itemsInConstraintRule.includes(item)) {
                        return itemsInConstraintRule.indexOf(item);
                    } else {
                        return itemsInConstraintRule.findIndex(itemInRule => typeof (itemInRule) === 'object' && (itemInRule.targetStore === undefined || itemInRule.targetStore === pageStore) && itemInRule.targetItem === item);
                    }
                }
                if (typeof (item) === 'object') {
                    return itemsInConstraintRule.findIndex(itemInRule => typeof (itemInRule) === 'object' && (itemInRule.targetStore === undefined || itemInRule.targetStore === pageStore) && itemInRule.targetItem === item.targetItem);
                }
                return -1;
            }

            static getItemFromDynamicStore(item, dynamicStores, pageStore) {
                if (typeof (item) === 'string') {
                    return dynamicStores[pageStore].items[item];
                }
                if (typeof (item) === 'object') {
                    return dynamicStores[item.targetStore ? item.targetStore : pageStore].items[item.targetItem];
                }
                return undefined;
            }

            static updateValueFromConstraintRule(targetItem, constraint, dynamicStores, pageStore) {
                const { items, type, withDeadbands } = constraint;
                let deadband = constraint.deadband === undefined ? 0 : constraint.deadband;
                if (typeof (deadband) === 'object') {
                    const deadbandItem = CommonVisibilityRules.getItemFromDynamicStore(deadband, dynamicStores, pageStore);
                    deadband = deadbandItem.visible ? deadbandItem.value : 0;
                }

                const targetValue = CommonVisibilityRules.getItemFromDynamicStore(targetItem, dynamicStores, pageStore).value;
                const targetIndex = CommonVisibilityRules.itemIndexOf(targetItem, items, pageStore);
                const affectedItems = [];
                for (let itemIndex = 0; itemIndex < items.length; itemIndex++) {
                    let item = items[itemIndex];
                    const itemObject = CommonVisibilityRules.getItemFromDynamicStore(item, dynamicStores, pageStore);
                    if (itemIndex === targetIndex) {
                        continue;
                    }

                    const relatedItemValue = itemObject.value;

                    switch (type) {
                        case RuleType.EQUALS:
                            if (relatedItemValue !== targetValue) {
                                if (targetValue <= itemObject.max && targetValue >= itemObject.min) {
                                    itemObject.value = targetValue;
                                    affectedItems.push(item);
                                } else {
                                    return false;
                                }
                            }
                            break;
                        case RuleType.GREATER_THAN:
                        case RuleType.GREATER_THAN_OR_EQUALS:
                            const resultGreater = CommonVisibilityRules.updateValueForGreater(itemIndex, targetIndex, deadband, targetValue, relatedItemValue, itemObject, withDeadbands, item, affectedItems);
                            if (resultGreater === false) {
                               return false;
                            }
                            break;
                        case RuleType.LESS_THAN:
                        case RuleType.LESS_THAN_OR_EQUALS:
                            const resultLess = CommonVisibilityRules.updateValueForLess(itemIndex, targetIndex, deadband, targetValue, relatedItemValue, itemObject, withDeadbands, item, affectedItems);
                            if (resultLess === false) {
                                return false;
                            }
                            break;
                        default:
                            break;
                    }
                };

                return affectedItems;
            }
            static updateValueForGreater(itemIndex, targetIndex, deadband, targetValue, relatedItemValue, itemObject, withDeadbands, item, affectedItems) {
                const itemHasDeadband = withDeadbands[itemIndex];
                const targetHasDeadband = withDeadbands[targetIndex];
                if (itemIndex < targetIndex) {
                    if (itemHasDeadband && relatedItemValue < targetValue + deadband) {
                        if (targetValue + deadband <= itemObject.max && targetValue + deadband >= itemObject.min) {
                            itemObject.value  = targetValue + deadband;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }

                    } else if (!itemHasDeadband && relatedItemValue < targetValue) {
                        if (targetValue <= itemObject.max && targetValue >= itemObject.min) {
                            itemObject.value  = targetValue;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    }
                } else {
                    if (targetHasDeadband && relatedItemValue > targetValue - deadband) {
                        if (targetValue - deadband <= itemObject.max && targetValue - deadband >= itemObject.min) {
                            itemObject.value = targetValue - deadband;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    } else if (!targetHasDeadband && relatedItemValue > targetValue) {
                        if (targetValue <= itemObject.max && targetValue >= itemObject.min) {
                            itemObject.value = targetValue;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    }
                }
            }

            static updateValueForLess(itemIndex, targetIndex, deadband, targetValue, relatedItemValue, itemObject, withDeadbands, item, affectedItems) {
                const itemHasDeadband = withDeadbands[itemIndex];
                const targetHasDeadband = withDeadbands[targetIndex];
                if (itemIndex < targetIndex) {
                    if (itemHasDeadband && relatedItemValue > targetValue - deadband) {
                        if (targetValue - deadband <= itemObject.max && targetValue - deadband >= itemObject.min) {
                            itemObject.value  = targetValue - deadband;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    } else if (!itemHasDeadband && relatedItemValue > targetValue) {
                        if (targetValue <= itemObject.max && targetValue >= itemObject.min) {
                            itemObject.value = targetValue;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    }
                } else {
                    if (targetHasDeadband && relatedItemValue < targetValue + deadband) {
                        if (targetValue + deadband <= itemObject.max && targetValue + deadband >= itemObject.min) {
                            itemObject.value = targetValue + deadband;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    } else if (!targetHasDeadband && relatedItemValue < targetValue) {
                        if (targetValue <= itemObject.max && targetValue >= itemObject.min) {
                            itemObject.value = targetValue;
                            affectedItems.push(item);
                        } else {
                            return false;
                        }
                    }

                }
            }

            static updateGroupHeadingVisible(globalStore) {
                const { dynamicStores } = globalStore;
                const dynamicStoreList = Object.values(dynamicStores);
                dynamicStoreList.forEach(dynamicStore => {
                    if (dynamicStore[0] && dynamicStore[0].componentType === 'ScheduleWidget') {
                        for (let i = 0; i < dynamicStore.length; i++) {
                            const { items } = dynamicStore[i];
                            this.updateGroupHeadingVisibleUpdate(items);
                        }
                    } else {
                        const { items } = dynamicStore;
                        this.updateGroupHeadingVisibleUpdate(items);
                    }
                });
            }

            static updateGroupHeadingVisibleUpdate(items) {
                if (!items)  return;
                const groupHeadingList = Object.values(items).filter(item => item.componentType === 'HeadingLabel');
                groupHeadingList.forEach(groupHeading => {
                    const { itemsInGroup } = groupHeading;
                    if (!itemsInGroup) {
                        groupHeading.visible = false;
                    } else {
                        const visibleItems = itemsInGroup.split(",").filter(item => items[item] ? items[item].visible : false);
                        groupHeading.visible = visibleItems.length > 0;
                    }
                });
            }
        }

        return CommonVisibilityRules;
});
