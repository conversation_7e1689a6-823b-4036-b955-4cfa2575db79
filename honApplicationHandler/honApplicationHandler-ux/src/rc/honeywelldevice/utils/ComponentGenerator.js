define([
    'react',
    'lex!honApplicationHandler',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/HeadingLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/Dropdown',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/TimeZoneFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/RadioButtonGroup',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SelectButton',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SwitchButton',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/NumberInput',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/NumberInputFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/TextInputFloatLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/CheckboxGroup',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/StaticLabel',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ButtonComponent',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/AlarmItemWidget',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SelectWidget',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SingleSlider',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/RangeSlider',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DuctAreaCalculator',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/MeasurementType',
	'nmodule/honApplicationHandler/rc/honeywelldevice/components/AirflowUnit'
    ],
    function (
        React,
        lexicons,
        HeadingLabel,
        Dropdown,
        DropdownFloatLabel,
        TimeZoneFloatLabel,
        RadioButtonGroup,
        SelectButton,
        SwitchButton,
        NumberInput,
        NumberInputFloatLabel,
        TextInputFloatLabel,
        CheckboxGroup,
        StaticLabel,
        ButtonComponent,
        AlarmItemWidget,
        SelectWidget,
        SingleSlider,
        RangeSlider,
        DuctAreaCalculator,
        MeasurementType,
		AirflowUnit
    ) {
        "use strict";

        var honApplicationHandlerLex = lexicons[0];

        class ComponentGenerator {

            // generate components according to the store
            static generateComponent(obj, onChange) {
                if (obj.componentType === "NumberInput") {
                    return ComponentGenerator.generateNumberInput(obj, onChange);
                }

                if (obj.componentType === "TextInput") {
                    return ComponentGenerator.generateTextInput(obj, onChange);
                }

                if (obj.componentType === "Dropdown") {
                    return ComponentGenerator.generateDropdown(obj, onChange);
                }

                if (obj.componentType === "Timezone") {
                    return ComponentGenerator.generateTimeZone(obj, onChange);
                }

                if (obj.componentType === "RadioButtonGroup") {
                    return ComponentGenerator.generateRadioButtonGroup(obj, onChange);
                }

                if (obj.componentType === "SelectButton") {
                    return ComponentGenerator.generateSelectButton(obj, onChange);
                }

                if (obj.componentType === "SwitchButton") {
                    return ComponentGenerator.generateSwitchButton(obj, onChange);
                }

                if (obj.componentType === "StaticLabel") {
                    return ComponentGenerator.generateStaticLabel(obj);
                }

                if (obj.componentType === "ButtonComponent") {
                    return ComponentGenerator.generateButtonComponent(obj);
                }

                if (obj.componentType === "CheckboxGroup") {
                    return ComponentGenerator.generateCheckboxGroup(obj, onChange);
                }

                if (obj.componentType === "HeadingLabel") {
                    return ComponentGenerator.generateHeadingLabel(obj);
                }

                if (obj.componentType === "AlarmItemWidget") {
                    return ComponentGenerator.generateAlarmItemWidget(obj);
                }

                if (obj.componentType === "SelectWidget") {
                    return ComponentGenerator.generateSelectWidget(obj, onChange);
                }

                if (obj.componentType === "SingleSlider") {
                    return ComponentGenerator.generateSingleSlider(obj, onChange);
                }
                if (obj.componentType === "MeasurementType") {
                    return ComponentGenerator.generateMeasurementType(obj, onChange);
                }
				if (obj.componentType === "AirflowUnit") {
				    return ComponentGenerator.generateAirflowUnit(obj, onChange);
				}
            }

            static generateComplexComponent(objList, onChange) {
                if (objList[0].componentType === "RangeSlider") {
                    objList.sort((a, b) => a.role === "min" ? -1 : 1);
                    return ComponentGenerator.generateRangeSlider(objList, onChange);
                }
                if (objList[0].componentType === "DuctAreaCalculator") {
                    return ComponentGenerator.generateDuctAreaCalculator(objList, onChange);
                }
            }

            static generateNumberInput(obj, onChange) {
                return (
                    <NumberInputFloatLabel
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        min={obj.min}
                        max={obj.max}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        value={obj.value}
                        step={obj.step}
                        onChange={onChange}
                        unit={obj.unit}
                        precision={obj.precision}
                        defaultValue={obj.defaultValue}
                        index={obj.index}
                    />
                );
            }

            static generateTextInput(obj, onChange) {
                return (
                    <TextInputFloatLabel
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        value={obj.value}
                        onChange={onChange}
                        defaultValue={obj.defaultValue}
                        index={obj.index}
                    />
                );
            }

            static generateDropdown(obj, onChange) {
                return (
                    <DropdownFloatLabel
                        key={obj.name}
                        name={obj.name}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        clearable={obj.clearable}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        options={obj.options}
                        value={obj.value}
                        onChange={onChange}
                        index={obj.index === 0 ? 1 : obj.index}
                    />
                );
            }

            static generateTimeZone(obj, onChange) {
                return (
                    <TimeZoneFloatLabel
                        key={obj.name}
                        name={obj.name}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        clearable={obj.clearable}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        options={obj.options}
                        value={obj.value}
                        onChange={onChange}
                        index={obj.index}
                    />
                );
            }

            static generateRadioButtonGroup(obj, onChange) {
                return (
                    <RadioButtonGroup
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        readonly={obj.readOnly}
                        options={obj.options}
                        value={obj.value}
                        group={obj.name}
                        onChange={onChange}
                    />
                );
            }

            static generateSelectButton(obj, onChange) {
                return (
                    <SelectButton
                        key={obj.name}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        value={obj.value}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        options={obj.options}
                        onChange={onChange}
                        dataObj={obj}
                    />
                );
            }

            static generateSwitchButton(obj, onChange) {
                return (
                    <SwitchButton
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        value={obj.value}
                        onChange={onChange}
                    />
                );
            }

            static generateSelectWidget(obj, onChange) {
                return (
                    <SelectWidget
                        key={obj.name}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        value={obj.value}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        options={obj.options}
                        onChange={onChange}
                        dataObj={obj}
                        index={obj.index}
                    />
                );
            }

            static generateStaticLabel(obj) {
                return (
                    <StaticLabel
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        value={obj.value}
                    />
                );
            }

            static generateButtonComponent(obj) {
                return (
                    <ButtonComponent
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        value={obj.value}
                        onClick={obj.onClick}
                    />
                );
            }

            static generateCheckboxGroup(obj, onChange) {
                return (
                    <CheckboxGroup
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        readonly={obj.readOnly}
                        options={obj.options}
                        value={obj.value}
                        onChange={onChange}
                    />
                );
            }

            static generateSingleSlider(obj, onChange) {
                return (
                    <SingleSlider
                        key={obj.name}
                        name={obj.name}
                        dataObj={obj}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        value={obj.value}
                        min={obj.min}
                        max={obj.max}
                        step={obj.step}
                        onChange={onChange}
                        unit={obj.unit}
                        color={obj.color}
                        precision={obj.precision}
                        index={obj.index}
                    />
                );
            }

            static generateMeasurementType(obj, onChange) {
                return (
                    <MeasurementType
                        key={obj.name}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        value={obj.value}
                        visible={obj.visible}
                        disabled={obj.disabled}
                        readonly={obj.readOnly}
                        options={obj.options}
                        onChange={onChange}
                        dataObj={obj}
                        index={obj.index}
                    />
                );
            }

			static generateAirflowUnit(obj, onChange) {
			    return (
			        <AirflowUnit
			            key={obj.name}
			            label={obj.label}
			            tooltip={obj.tooltip}
			            value={obj.value}
			            visible={obj.visible}
			            disabled={obj.disabled}
			            readonly={obj.readOnly}
			            options={obj.options}
			            onChange={onChange}
			            dataObj={obj}
                        index={obj.index}
			        />
			    );
			}

            static generateRangeSlider(objList, onChange) {
                return (
                    <RangeSlider
                        key={objList[0].name}
                        dataObjList={objList}
                        label={objList[0].label}
                        help={objList[0].help}
                        tooltip={objList[0].tooltip}
                        visible={objList[0].visible}
                        disabled={objList[0].disabled}
                        readonly={objList[0].readOnly}
                        value={[ objList[0].value, objList[1].value ]}
                        deadband={objList[0].deadband}
                        min={objList[0].min}
                        max={objList[0].max}
                        step={objList[0].step}
                        unit={objList[0].unit}
                        color={objList[0].color}
                        onChange={onChange}
                        precision={objList[0].precision}
                        index={objList[0].index}
                    />
                );
            }

            static generateDuctAreaCalculator(objList, onChange) {
                return (
                    <DuctAreaCalculator
                        dataObjList={objList}
                        onChange={onChange}
                    />
                );
            }

            static generateHeadingLabel(obj) {
                return (
                    <HeadingLabel
                        key={obj.name}
                        label={obj.label}
                        tooltip={obj.tooltip}
                        visible={obj.visible}
                    />
                );
            }

            static generateAlarmItemWidget(obj) {
                let alarmName = obj.label;
                if (obj.multipleValue) {
                    let value = Number(obj.value);
                    let tooltips = [], valueTexts = [];
                    (obj.options || []).forEach(item => {
                        let optionValue = Number(item.value);
                        if ((value & optionValue) === optionValue) {
                            tooltips.push(item.tooltip || "");
                            valueTexts.push(item.text || "");
                        }

                    });
                    return (
                        <AlarmItemWidget
                           alarmName={alarmName}
                           tooltip={tooltips.join("\n")}
                           valueText={valueTexts.join(",")}
                           visible={obj.visible}
                        />
                    );
                } else {
                    let option = (obj.options || []).find(o => o.value == obj.value);
                    let tooltip = "", valueText = "";
                    if (option) {
                        tooltip = option.tooltip || "";
                        valueText = option.text || "";
                    } else {
                        console.log("can not find option from given value." + alarmName);
                        tooltip = honApplicationHandlerLex.get('HoneywellDeviceWizardLex.UnknownValue');
                        valueText = honApplicationHandlerLex.get('HoneywellDeviceWizardLex.UnknownValue');
                    }
                    return (
                        <AlarmItemWidget
                           alarmName={alarmName}
                           tooltip={tooltip}
                           valueText={valueText}
                           visible={obj.visible}
                        />
                    );
                }

            }
        }

        return ComponentGenerator;
});
