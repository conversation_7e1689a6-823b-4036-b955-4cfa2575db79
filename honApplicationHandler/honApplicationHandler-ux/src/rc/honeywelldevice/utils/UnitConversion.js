define([],
    function () {
        'use strict';
        class UnitConversion {
            static formatNumericValue(precision, value, step) {
                if (step === 0.5) {
                    return Math.round(parseFloat(value) * 2.0) / 2.0;
                } else {
                    if (precision === 0) {
                        return Math.round(value);
                    } else {
                        return Number(parseFloat(value).toFixed(precision));
                    }
                }
            }

        }
        return UnitConversion;
    });
