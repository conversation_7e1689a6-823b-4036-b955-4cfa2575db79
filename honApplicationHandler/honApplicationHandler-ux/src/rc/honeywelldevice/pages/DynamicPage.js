define([
    'react',
    'semantic-ui-react',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/ComponentGenerator',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/CommonVisibilityRules',
    'nmodule/honApplicationHandler/rc/honeywelldevice/utils/TerminalAssignmentValueRules',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ComponentInlineContainer',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (
        React,
        SemanticReact,
        ComponentGenerator,
        CommonVisibilityRules,
        TerminalAssignmentValueRules,
        ComponentInlineContainer
    ) => {
        'use strict';

        const {
            Tab,
            MenuItem
        } = SemanticReact;

        class DynamicPage extends React.Component {
            constructor(props) {
                super(props);
                this.state = {
                    dynamicStore: {},
                    activeTabIndex: 0
                };
                this.numberOfSubTabs = 0;
                this.lastSaveTimestamp = 0;
            }

            // 0 index for Si Metric, 1 index for Imperial
            timeZoneBasedUnitMapping = [
                [0, 1, 9, 13, 16, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 47, 48, 50, 51, 52, 53, 58, 59, 60, 
                61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80],
                [2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 44, 45, 46, 49, 54, 55, 56, 57]
            ];

            // 0: Cubic Feet Per Minute (CFM)  1: Cubic Meter Per Hour (m³/h)  2: Liter Per Second (L/s)
            timeZoneBasedAirFlowUnitMapping = [
                [ // 0: Cubic Feet Per Minute (CFM)
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 20
                ],
                [ // 1: Cubic Meter Per Hour (m³/h)
                    0, 13, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 
                    47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64,  65, 66, 67, 68, 69, 76, 77, 75, 79, 80
                ],
                [ // 2: Liter Per Second (L/s)
                    70, 71, 72, 73, 74, 78
                ]
            ]

            componentDidMount() {
                const { dynamicStoreName, globalStore } = this.props;
                const dynamicStore = globalStore.dynamicStores[dynamicStoreName];
                if(dynamicStore) {
                    this.setState({ dynamicStore: dynamicStore.items });
                }

                this.keydownHandler = async (event) => {
                    const objectsByTab = this.getObjectsByTabInPage(dynamicStore.label);
                    // Handle Ctrl+Left/Right arrow keys for tab navigation
                    if (event.ctrlKey && event.key === 'ArrowLeft' && this.state.activeTabIndex > 0) {
                        // Always allow tab switching with Ctrl+Arrow, regardless of focus location
                        event.preventDefault(); 
                        await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex].tabInPage}`); 
                        await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex-1].tabInPage}`);
                        let activeTabIndex = this.state.activeTabIndex-1;
                        
                        // Clear any save button highlights before switching tabs
                        this.removeSaveButtonHighlight();
                        
                        this.setState({ activeTabIndex });

                    } else if (event.ctrlKey && event.key === 'ArrowRight' && this.state.activeTabIndex < this.numberOfSubTabs - 1) {
                        // Always allow tab switching with Ctrl+Arrow, regardless of focus location
                        event.preventDefault();
                        await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex].tabInPage}`); 
                        await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex+1].tabInPage}`); 
                        let activeTabIndex = this.state.activeTabIndex+1;
                        
                        // Clear any save button highlights before switching tabs
                        this.removeSaveButtonHighlight();
                        
                        this.setState({ activeTabIndex });
                        
                    }
                    // Handle save keyboard shortcuts (Ctrl+S, Ctrl+Shift+S, Alt+S, F2)
                    else if ((event.ctrlKey && event.key === 's') || (event.ctrlKey && event.key === 'S')) {
                        this.handleSaveAction('keyboard-shortcut');
                    }
                    else if ((event.ctrlKey && event.shiftKey && event.key === 's') || 
                             (event.ctrlKey && event.shiftKey && event.key === 'S')) {
                        this.handleSaveAction('keyboard-shortcut-all');
                    }
                    else if ((event.altKey && event.key === 's') || 
                             (event.altKey && event.key === 'S') || 
                             event.key === 'F2') {
                        this.handleSaveAction('keyboard-shortcut-alt');
                    }
                    // Handle Alt+H for help popup
                    else if (event.altKey && (event.key === 'h' || event.key === 'H')) {
                        event.preventDefault();
                        this.showHelpForFocusedElement();
                    }
                    // Handle Tab navigation between form inputs
                    else if (event.key === 'Tab') {
                        this.handleTabNavigation(event);
                    }
                    // Handle Escape key to return focus to wizard if it goes outside
                    else if (event.key === 'Escape') {
                        // Check if event was already handled by a component
                        if (!event.defaultPrevented) {
                            this.handleEscapeKey(event);
                        }
                    }
                };

                // Detect save button clicks for post-save focus management
                this.clickHandler = (event) => {
                    const target = event.target;
                    
                    // Clear save button highlights on any click (user is navigating away)
                    this.removeSaveButtonHighlight();
                    
                    const isButton = target.tagName === 'BUTTON' || target.classList.contains('button');
                    const isInButtonGroup = target.closest('.button-group');
                    const containsUIButton = target.classList.contains('ui') && target.classList.contains('button');
                    const buttonText = target.textContent || target.innerText || '';
                    const isSaveButton = buttonText.toLowerCase().includes('save') || 
                                       target.classList.contains('save-button') ||
                                       target.type === 'submit';
                    
                    if ((isButton || containsUIButton) && (isInButtonGroup || isSaveButton)) {
                        this.handleSaveAction('button-click');
                    }
                };

                document.addEventListener('keydown', this.keydownHandler);
                document.addEventListener('click', this.clickHandler);

                // Listen for form submissions that might trigger saves
                this.submitHandler = (event) => {
                    this.handleSaveAction('form-submit');
                };
                document.addEventListener('submit', this.submitHandler);
                
                // Add global focus change monitoring
                this.focusHandler = (event) => {
                    const wizardContainer = this.findWizardContainer();
                    const focusedElement = event.target;
                    
                    // Clear save button highlights immediately when focus changes to any element
                    this.removeSaveButtonHighlight();
                    
                    // Check if focus is in a legitimate popup - if so, allow it
                    if (this.isElementInWizardRelatedPopup(focusedElement)) {
                        return; // Don't interfere with popup navigation
                    }
                    
                    // If focus goes outside wizard scope, attempt to bring it back
                    if (wizardContainer && !wizardContainer.contains(focusedElement)) {
                        // Attempt to bring focus back to wizard after a small delay
                        setTimeout(() => {
                            this.forceFocusBackToWizard();
                        }, 100);
                    }
                };
                document.addEventListener('focusin', this.focusHandler);

                // Add focus out monitoring for more comprehensive coverage
                this.focusOutHandler = (event) => {
                    // Clear save button highlights when focus leaves any element
                    this.removeSaveButtonHighlight();
                };
                document.addEventListener('focusout', this.focusOutHandler);

                // Add popup close monitoring
                this.popupCloseHandler = (event) => {
                    const target = event.target;
                    
                    // Check if a popup close button was clicked
                    const isCloseButton = target.classList.contains('close') || 
                                        target.classList.contains('cancel') ||
                                        target.classList.contains('ok') ||
                                        target.closest('.ui.button.cancel') ||
                                        target.closest('.ui.button.ok') ||
                                        target.closest('[data-action="close"]') ||
                                        target.closest('.popup-close');
                    
                    if (isCloseButton) {
                        // Popup was closed via action button - force focus back to wizard after a delay
                        setTimeout(() => {
                            const wizardContainer = this.findWizardContainer();
                            const currentFocus = document.activeElement;
                            
                            // If focus is outside wizard boundaries, bring it back
                            if (wizardContainer && !wizardContainer.contains(currentFocus)) {
                                this.forceFocusBackToWizard();
                            }
                        }, 150); // Slightly longer delay to allow popup to fully close
                    }
                };
                document.addEventListener('click', this.popupCloseHandler);
            }

            componentDidUpdate(prevProps, prevState) {
                // Detect save completion and trigger focus reset
                const prevChangedItems = Object.values(prevState.dynamicStore).filter(item => item.changed === true);
                const currentChangedItems = Object.values(this.state.dynamicStore).filter(item => item.changed === true);
                
                // Multiple save detection methods
                const wasSaving = prevProps.saving === true;
                const isNoLongerSaving = this.props.saving === false;
                const hadChanges = prevChangedItems.length > 0;
                const noLongerHasChanges = currentChangedItems.length === 0;
                
                // Check if changedDynamicStores prop indicates save completion
                const prevChangedStores = prevProps.changedDynamicStores || [];
                const currentChangedStores = this.props.changedDynamicStores || [];
                const storeWasChanged = prevChangedStores.includes(this.props.dynamicStoreName);
                const storeNoLongerChanged = !currentChangedStores.includes(this.props.dynamicStoreName);
                
                // Trigger save reset if any save completion indicator is met
                const saveCompleted = (hadChanges && noLongerHasChanges) || 
                                    (wasSaving && isNoLongerSaving) ||
                                    (storeWasChanged && storeNoLongerChanged);
                
                if (saveCompleted) {
                    // Check if this happened recently (within last 15 seconds) to catch both button and keyboard saves
                    const timeSinceLastSave = Date.now() - this.lastSaveTimestamp;
                    if (timeSinceLastSave < 15000) {
                        this.handlePostSaveFocusReset();
                    } else {
                        // Even if no recent save action was recorded, if we detect save completion, handle it
                        // This covers cases where save might be triggered from outside our component
                        this.handlePostSaveFocusReset();
                    }
                }
            }

            componentWillUnmount() {
                // Clean up event listeners to prevent memory leaks
                const keydownHandler = this.keydownHandler;
                const clickHandler = this.clickHandler;
                const submitHandler = this.submitHandler;
                const focusHandler = this.focusHandler;
                const focusOutHandler = this.focusOutHandler;
                const popupCloseHandler = this.popupCloseHandler;
                
                if (keydownHandler) {
                    document.removeEventListener('keydown', keydownHandler);
                }
                if (clickHandler) {
                    document.removeEventListener('click', clickHandler);
                }
                if (submitHandler) {
                    document.removeEventListener('submit', submitHandler);
                }
                if (focusHandler) {
                    document.removeEventListener('focusin', focusHandler);
                }
                if (focusOutHandler) {
                    document.removeEventListener('focusout', focusOutHandler);
                }
                if (popupCloseHandler) {
                    document.removeEventListener('click', popupCloseHandler);
                }
                
                // Clean up visual highlights on component unmount
                this.removeSaveButtonHighlight();
                this.removeElementHighlight();
                
                // Clean up any lingering dropdown states
                const wizardContainer = this.findWizardContainer();
                if (wizardContainer) {
                    this.cleanupDropdownFocusStates(wizardContainer);
                }
            }

            handleTabNavigation = (event) => {
                // Check if we're in a legitimate popup/modal context
                const currentTarget = event.target;
                const isInPopup = this.isElementInWizardRelatedPopup(currentTarget);
                
                // If we're in a popup, allow normal tab navigation
                if (isInPopup) {
                    return; // Let browser handle natural tab navigation in popups
                }
                
                // Otherwise, apply wizard boundary restrictions
                const wizardContainer = this.findWizardContainer();
                
                if (!wizardContainer || !wizardContainer.contains(currentTarget)) {
                    // Focus has gone outside wizard boundaries - force it back
                    event.preventDefault();
                    this.forceFocusBackToWizard();
                    return;
                }

                // Enhanced popup cleanup: Check if any popups were recently closed
                this.cleanupClosedPopups(wizardContainer);

                // Clean up any lingering dropdown focus states before navigation
                this.cleanupDropdownFocusStates(wizardContainer);

                // Enhanced multi-tab detection for workbench compatibility
                let activeTabPane = this.getActiveTabPane();
                
                if (!activeTabPane) {
                    // If no specific tab pane found, use the entire container as fallback
                    activeTabPane = wizardContainer;
                }

                // Clear save button highlights when focus changes
                this.removeSaveButtonHighlight();

                // Get all focusable elements within the current active context
                const focusableElements = this.getFocusableElementsInPane(activeTabPane, wizardContainer);

                if (focusableElements.length === 0) {
                    // Fallback: if no focusable elements found in pane, search in entire wizard container
                    const fallbackElements = this.getFocusableElementsInPane(wizardContainer, wizardContainer);
                    if (fallbackElements.length > 0) {
                        // Simple fallback: focus the first element
                        const firstElement = fallbackElements[0];
                        if (firstElement && wizardContainer.contains(firstElement)) {
                            event.preventDefault();
                            this.focusElement(firstElement);
                        }
                    }
                    return;
                }

                const currentElement = document.activeElement;
                let currentIndex = focusableElements.indexOf(currentElement);
                
                // Enhanced special handling: if current element is not in the pane but is within wizard boundaries
                if (currentIndex === -1 && wizardContainer.contains(currentElement)) {
                    // Check if it's a save button or other valid wizard element
                    const isSaveButton = this.isSaveButton(currentElement);
                    
                    if (isSaveButton) {
                        // Add the current element to the navigation array temporarily
                        focusableElements.push(currentElement);
                        currentIndex = focusableElements.length - 1;
                    }
                }
                
                // If we still can't find the current element, try to find a reasonable starting point
                if (currentIndex === -1) {
                    // For shift+tab, go to last element; for tab, go to first
                    currentIndex = event.shiftKey ? focusableElements.length - 1 : 0;
                    if (focusableElements[currentIndex]) {
                    event.preventDefault();
                        this.focusElement(focusableElements[currentIndex]);
                    return;
                    }
                }

                let nextIndex = this.calculateNextTabIndex(currentIndex, focusableElements.length, event.shiftKey, currentElement);
                
                // Handle special cases
                if (nextIndex === 'NAVIGATE_TO_SAVE') {
                    // Always try to find the save button since it's common to all inputs
                    const saveButton = this.findSaveButton(wizardContainer);
                    
                    if (saveButton) {
                        // Save button found - navigate to it
                        event.preventDefault();
                        this.focusElement(saveButton);
                        this.addSaveButtonHighlight(saveButton);
                        return;
                    } else {
                        // This should rarely happen since save button should always exist
                        // But if it does, fallback to wrapping to first element
                        nextIndex = 0;
                        const firstElement = focusableElements[nextIndex];
                        if (firstElement && wizardContainer.contains(firstElement)) {
                            event.preventDefault();
                            this.focusElement(firstElement);
                            return;
                        }
                    }
                } else if (nextIndex === currentIndex) {
                    // This case should now rarely happen since we removed the "stay on save button" logic
                    // But keep it as a safety net for other edge cases
                    event.preventDefault();
                    // Re-apply save button highlight if it's a save button
                    if (this.isSaveButton(currentElement)) {
                        this.addSaveButtonHighlight(currentElement);
                    }
                    return;
                }
                
                // Normal tab navigation
                const nextElement = focusableElements[nextIndex];
                if (nextElement && wizardContainer.contains(nextElement)) {
                    event.preventDefault();
                    this.focusElement(nextElement);
                }
            }

            isElementInWizardRelatedPopup = (element) => {
                if (!element) return false;

                // Check if element is within a visible Semantic UI popup
                const semanticPopup = element.closest('.ui.popup.visible, .ui.popup.active');
                if (semanticPopup) {
                    // Double-check the popup is actually visible
                    const rect = semanticPopup.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(semanticPopup).display !== 'none' &&
                                    window.getComputedStyle(semanticPopup).visibility !== 'hidden';
                    if (isVisible) {
                        return true;
                    }
                }

                // Check if element is within a visible Semantic UI modal
                const semanticModal = element.closest('.ui.modal.active, .ui.modal.visible, .duct-area-calculator-modal, .honeywell-device-wizard.ui.modal');
                if (semanticModal) {
                    // Double-check the modal is actually visible
                    const rect = semanticModal.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(semanticModal).display !== 'none' &&
                                    window.getComputedStyle(semanticModal).visibility !== 'hidden';
                    if (isVisible) {
                        return true;
                    }
                }

                // Check for calculator popup specifically (common pattern) - with visibility check
                const calculatorPopup = element.closest('[class*="calculator"], [class*="Calculator"]');
                if (calculatorPopup) {
                    const rect = calculatorPopup.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(calculatorPopup).display !== 'none' &&
                                    window.getComputedStyle(calculatorPopup).visibility !== 'hidden';
                    if (isVisible) {
                        return true;
                    }
                }

                // Check for popup content containers - with visibility check
                const popupContent = element.closest('[data-testid*="popup"], [class*="popup-content"], .popup-container');
                if (popupContent) {
                    const rect = popupContent.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(popupContent).display !== 'none' &&
                                    window.getComputedStyle(popupContent).visibility !== 'hidden';
                    if (isVisible) {
                        return true;
                    }
                }

                // Check if element is in a help popup (from our Alt+H feature)
                const helpTooltip = element.closest('.alt-h-help-tooltip');
                if (helpTooltip) {
                    return true;
                }

                // Check for dropdown menus that are currently open and visible
                const dropdownMenu = element.closest('.ui.dropdown .menu.visible, .ui.dropdown .menu.active');
                if (dropdownMenu) {
                    const rect = dropdownMenu.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0;
                    if (isVisible) {
                        return true;
                    }
                }

                // Check for any element with popup-related attributes - with visibility check
                const hasPopupAttributes = element.closest('[data-popup], [aria-describedby], [role="dialog"], [role="tooltip"]');
                if (hasPopupAttributes) {
                    // Additional validation: ensure it's actually visible
                    const rect = hasPopupAttributes.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0;
                    const computedStyle = window.getComputedStyle(hasPopupAttributes);
                    const isDisplayed = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
                    
                    if (isVisible && isDisplayed) {
                        return true;
                    }
                }

                return false;
            }

            isValidNavigationElement = (element) => {
                if (!element) return false;
                
                // Check if element is focusable and interactive
                const tagName = element.tagName.toLowerCase();
                const interactiveTags = ['button', 'input', 'select', 'textarea', 'a'];
                
                if (interactiveTags.includes(tagName)) {
                    return !element.disabled && element.style.display !== 'none';
                }
                
                // Check for elements with tabindex
                const tabIndex = element.getAttribute('tabindex');
                if (tabIndex && tabIndex !== '-1') {
                    return true;
                }
                
                // Check for semantic-ui components
                if (element.classList.contains('ui') && 
                    (element.classList.contains('button') || element.classList.contains('dropdown'))) {
                    return !element.classList.contains('disabled');
                }
                
                return false;
            }

            handleTabNavigationFallback = (focusableElements, currentElement, event) => {
                // Fallback navigation when main detection fails
                const currentIndex = focusableElements.indexOf(currentElement);
                let nextIndex = this.calculateNextTabIndex(currentIndex, focusableElements.length, event.shiftKey, currentElement);
                
                // Handle smart save button navigation in fallback
                if (nextIndex === 'NAVIGATE_TO_SAVE') {
                    const wizardContainer = this.findWizardContainer();
                    const saveButton = this.findSaveButton(wizardContainer);
                    
                    if (saveButton && wizardContainer && wizardContainer.contains(saveButton)) {
                        event.preventDefault();
                        this.focusElement(saveButton);
                        this.addSaveButtonHighlight(saveButton);
                        return;
                    } else {
                        nextIndex = 0;
                    }
                }
                
                const nextElement = focusableElements[nextIndex];
                if (nextElement) {
                    event.preventDefault();
                    this.focusElement(nextElement);
                }
            }

            cleanupDropdownFocusStates = (container) => {
                // Find all semantic-ui dropdowns in the container
                const dropdowns = container.querySelectorAll('.ui.dropdown');
                
                dropdowns.forEach(dropdown => {
                    // Only clean up dropdowns that are not currently focused
                    if (dropdown !== document.activeElement) {
                        // Remove semantic-ui focus classes
                        dropdown.classList.remove('focused', 'active', 'visible');
                        
                        // Clear any manual styles that might be lingering
                        dropdown.style.boxShadow = '';
                        dropdown.style.outline = '';
                        
                        // Also clean up any dropdown inputs
                        const dropdownInput = dropdown.querySelector('input.search, input');
                        if (dropdownInput && dropdownInput !== document.activeElement) {
                            dropdownInput.blur();
                            dropdownInput.style.boxShadow = '';
                            dropdownInput.style.outline = '';
                        }
                        
                        // Close any open dropdown menus
                        const dropdownMenu = dropdown.querySelector('.menu');
                        if (dropdownMenu) {
                            dropdownMenu.style.display = 'none';
                        }
                    }
                });
            }

            cleanupClosedPopups = (wizardContainer) => {
                // Check for recently closed popups and clean up their focus states
                const currentFocus = document.activeElement;
                
                // Look for popup elements that might still be affecting focus
                const allPopups = document.querySelectorAll('.ui.popup, .ui.modal, [class*="calculator"], [class*="Calculator"], .popup-container');
                
                allPopups.forEach(popup => {
                    // If popup is not visible but still contains the focused element, it was likely just closed
                    const isVisible = popup.style.display !== 'none' && 
                                    !popup.classList.contains('hidden') &&
                                    popup.offsetParent !== null;
                    
                    if (!isVisible && popup.contains(currentFocus)) {
                        // Popup was closed but focus is still inside it - force focus back to wizard
                        this.forceFocusBackToWizard();
                        return;
                    }
                });
                
                // Also check if current focus is outside wizard boundaries but not in any visible popup
                if (currentFocus && !wizardContainer.contains(currentFocus)) {
                    // Check if it's genuinely in a visible popup
                    const isInVisiblePopup = this.isElementInWizardRelatedPopup(currentFocus);
                    
                    if (!isInVisiblePopup) {
                        // Focus is outside wizard and not in a legitimate popup - bring it back
                        this.forceFocusBackToWizard();
                    }
                }
            }

            getActiveTabPane = () => {
                // Method 1: Use activeTabIndex with semantic-ui Tab structure
                const semanticTabPanes = document.querySelectorAll('.hon-dynamic-page-tab .ui.tab.segment');
                if (semanticTabPanes.length > 0 && this.state.activeTabIndex < semanticTabPanes.length) {
                    const targetPane = semanticTabPanes[this.state.activeTabIndex];
                    // Verify this pane is actually active/visible
                    if (targetPane && !targetPane.classList.contains('loading')) {
                        return targetPane;
                    }
                }

                // Method 2: Look for active tab pane by CSS class within hon-dynamic-page-tab
                let activePane = document.querySelector('.hon-dynamic-page-tab .ui.tab.segment.active');
                if (activePane) {
                    return activePane;
                }

                // Method 3: Look for any visible tab.pane that is active
                activePane = document.querySelector('.hon-dynamic-page-tab .ui.tab.active');
                if (activePane) {
                    return activePane;
                }

                // Method 4: Enhanced vertical menu detection
                activePane = document.querySelector('.vertical-menu .active-item, .sidebar-menu .active, .nav-vertical .active');
                if (activePane) {
                    // Find the content area associated with this vertical menu item
                    const contentArea = activePane.querySelector('.content-area') || 
                                      document.querySelector('.content-panel.active') ||
                                      document.querySelector('.main-content');
                    if (contentArea) {
                        return contentArea;
                    }
                    return activePane;
                }

                // Method 5: Look for visible tab content within tc-page-container inside tabs
                activePane = document.querySelector('.hon-dynamic-page-tab .tc-page-container');
                if (activePane) {
                    // Check if this container is in an active tab pane
                    const parentTabPane = activePane.closest('.ui.tab.segment, .ui.tab');
                    if (parentTabPane && (parentTabPane.classList.contains('active') || 
                                         !parentTabPane.style.display || 
                                         parentTabPane.style.display !== 'none')) {
                        return activePane;
                    }
                }

                // Method 6: Look for visible tab content within tc-page-container (legacy support)
                activePane = document.querySelector('.tc-page-container .ui.tab.segment.active');
                if (activePane) {
                    return activePane;
                }

                // Method 6: For single tab or no-tab cases, find the main tc-page-container
                activePane = document.querySelector('.tc-page-container');
                if (activePane) {
                    // Make sure it's visible and not inside a hidden tab
                    const rect = activePane.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(activePane).display !== 'none' &&
                                    window.getComputedStyle(activePane).visibility !== 'hidden';
                    
                    if (isVisible) {
                        return activePane;
                    }
                }

                // Method 8: Enhanced workbench compatibility - look for any visible tab content
                const allTabSegments = document.querySelectorAll('.ui.tab.segment');
                for (const segment of allTabSegments) {
                    const rect = segment.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 && 
                                    window.getComputedStyle(segment).display !== 'none' &&
                                    window.getComputedStyle(segment).visibility !== 'hidden';
                    
                    if (isVisible && segment.classList.contains('active')) {
                        return segment;
                    }
                }

                // Method 9: Menu-agnostic container detection - find any visible content container
                const containerSelectors = [
                    '.main-content.active',
                    '.content-panel.active', 
                    '.page-content.active',
                    '.wizard-content.active',
                    '.form-container.active'
                ];
                
                for (const selector of containerSelectors) {
                    const activePane = document.querySelector(selector);
                    if (activePane) {
                        const rect = activePane.getBoundingClientRect();
                        const isVisible = rect.width > 0 && rect.height > 0;
                        if (isVisible) {
                            return activePane;
                        }
                    }
                }

                // Method 10: Ultimate fallback - return any visible content container
                return document.querySelector('.hon-dynamic-page-tab, .tc-page-container');
            }

            getFocusableElementsInPane = (activePane, wizardContainer) => {
                if (!activePane) return [];

                const focusableSelectors = [
                    'input[type="text"]:not([disabled]):not([readonly])',
                    'input[type="number"]:not([disabled]):not([readonly])',
                    'input[type="email"]:not([disabled]):not([readonly])',
                    'input[type="password"]:not([disabled]):not([readonly])',
                    'input[type="url"]:not([disabled]):not([readonly])',
                    'input[type="search"]:not([disabled]):not([readonly])',
                    'input[type="tel"]:not([disabled]):not([readonly])',
                    'input[type="date"]:not([disabled]):not([readonly])',
                    'input[type="time"]:not([disabled]):not([readonly])',
                    'input[type="datetime-local"]:not([disabled]):not([readonly])',
                    'input[type="month"]:not([disabled]):not([readonly])',
                    'input[type="week"]:not([disabled]):not([readonly])',
                    'input[type="range"]:not([disabled])',
                    'input[type="checkbox"]:not([disabled])',
                    'input[type="radio"]:not([disabled])',
                    'select:not([disabled])',
                    'textarea:not([disabled]):not([readonly])',
                    'button:not([disabled])',
                    '.ui.dropdown:not(.disabled)',
                    '.ui.button:not(.disabled)',
                    '[tabindex]:not([tabindex="-1"]):not([disabled])',
                    '[contenteditable="true"]'
                ].join(', ');

                const allElements = Array.from(activePane.querySelectorAll(focusableSelectors));
                
                return allElements.filter(el => {
                    // Basic visibility check
                    const rect = el.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0;
                    
                    if (!isVisible) return false;

                    // Style visibility check
                    const computedStyle = window.getComputedStyle(el);
                    const isStyleVisible = computedStyle.display !== 'none' && 
                                         computedStyle.visibility !== 'hidden' &&
                                         computedStyle.opacity !== '0';
                    
                    if (!isStyleVisible) return false;

                    // Container checks
                    const isWithinWizard = wizardContainer ? wizardContainer.contains(el) : true;
                    const isWithinActivePane = activePane.contains(el);
                    
                    if (!isWithinWizard || !isWithinActivePane) return false;

                    // Scope and group control checks
                    const isOutsideScope = this.isElementOutsideWizardScope(el);
                    const isInGroupControl = this.isElementInGroupControl(el);
                    
                    if (isOutsideScope || isInGroupControl) return false;

                    // Handle disabled semantic-ui elements
                    if (el.classList.contains('disabled') || el.closest('.disabled')) return false;

                    // Check for hidden parent containers
                    let parent = el.parentElement;
                    while (parent && parent !== activePane) {
                        const parentStyle = window.getComputedStyle(parent);
                        if (parentStyle.display === 'none' || parentStyle.visibility === 'hidden') {
                            return false;
                        }
                        parent = parent.parentElement;
                    }

                    return true;
                }).sort((a, b) => {
                    // Sort by tab order if specified, otherwise by DOM order
                    const aTabIndex = parseInt(a.getAttribute('tabindex')) || 0;
                    const bTabIndex = parseInt(b.getAttribute('tabindex')) || 0;
                    
                    if (aTabIndex !== bTabIndex) {
                        return aTabIndex - bTabIndex;
                    }
                    
                    // Fall back to DOM order
                    return a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
                });
            }

            calculateNextTabIndex = (currentIndex, totalElements, isShiftTab, currentElement) => {
                if (isShiftTab) {
                    // Shift+Tab: go to previous element
                    return currentIndex <= 0 ? totalElements - 1 : currentIndex - 1;
                } else {
                    // Tab: check for smart save navigation only if we're at the last element
                    if (currentIndex >= totalElements - 1) {
                        // At last element - check for unsaved changes
                        const hasChanges = this.checkForUnsavedChanges();
                        
                        if (hasChanges) {
                            // Always navigate to save button when there are changes
                            // Remove the "already on save button" check since save button is common to all panes
                            return 'NAVIGATE_TO_SAVE';
                        }
                        // No changes, wrap to first element
                        return 0;
                    } else {
                        // Normal progression to next element
                        return currentIndex + 1;
                    }
                }
            }

            focusElement = (element) => {
                if (!element) return;

                // Properly blur the previously focused element to clear its visual state
                const previousElement = document.activeElement;
                if (previousElement && previousElement !== element && previousElement !== document.body) {
                    // Special handling for semantic-ui dropdowns to ensure clean blur
                    if (previousElement.classList.contains('ui') && previousElement.classList.contains('dropdown')) {
                        // Clear dropdown focus state
                        previousElement.classList.remove('focused', 'active');
                        const dropdownInput = previousElement.querySelector('input.search') || 
                                            previousElement.querySelector('input');
                        if (dropdownInput) {
                            dropdownInput.blur();
                        }
                    }
                    
                    // Blur the previous element
                    previousElement.blur();
                    
                    // Clear any lingering focus styles
                    previousElement.style.outline = '';
                    previousElement.style.boxShadow = '';
                }

                // Add visual highlight
                this.addElementHighlight(element);

                // Focus the element
                element.focus();

                // Handle different element types
                if (element.classList.contains('ui') && element.classList.contains('dropdown')) {
                    // Semantic-ui dropdown
                    setTimeout(() => {
                        const dropdownInput = element.querySelector('input.search') || 
                                            element.querySelector('input') || 
                                            element.querySelector('.text');
                        if (dropdownInput) {
                            dropdownInput.focus();
                        }
                    }, 10);
                } else if (element.classList.contains('ui') && element.classList.contains('button')) {
                    // Semantic-ui button - ensure it's properly focused
                    element.focus();
                    
                    // Save button handling
                    const buttonText = (element.textContent || element.innerText || '').toLowerCase();
                    const isSaveButton = buttonText.includes('save') || 
                                       element.classList.contains('save-button') ||
                                       element.type === 'submit' ||
                                       element.getAttribute('tabindex') === '99';
                    
                    if (isSaveButton) {
                        // Add special save button highlight
                        this.addSaveButtonHighlight(element);
                    }
                } else if (element.tagName === 'BUTTON') {
                    // Native button element
                    element.focus();
                    
                    // Check if it's a save button
                    const buttonText = (element.textContent || element.innerText || '').toLowerCase();
                    const isSaveButton = buttonText.includes('save') || 
                                       element.classList.contains('save-button') ||
                                       element.type === 'submit' ||
                                       element.getAttribute('tabindex') === '99';
                    
                    if (isSaveButton) {
                        this.addSaveButtonHighlight(element);
                    }
                } else if (element.tagName === 'SELECT') {
                    // Native select element
                    element.focus();
                } else if (element.contentEditable === 'true') {
                    // Contenteditable element
                    element.focus();
                    // Set cursor to end
                    const range = document.createRange();
                    const selection = window.getSelection();
                    range.selectNodeContents(element);
                    range.collapse(false);
                    selection.removeAllRanges();
                    selection.addRange(range);
                }
            }

            // Handle Escape key to return focus to wizard if it goes outside boundaries
            handleEscapeKey = (event) => {
                const currentFocus = document.activeElement;
                
                // Check if we're in a popup/modal - if so, handle input focus highlighting
                if (currentFocus && this.isElementInWizardRelatedPopup(currentFocus)) {
                    // For input controls and dropdowns in popups, force remove focus highlighting immediately
                    if (currentFocus.tagName === 'INPUT' || currentFocus.tagName === 'TEXTAREA' || 
                        currentFocus.classList.contains('ui') || currentFocus.closest('.ui.input, .ui.dropdown')) {
                        
                        // Immediately blur and remove focus styling
                        currentFocus.blur();
                        
                        // Force remove any lingering focus styles immediately for all box-shadow variants
                        currentFocus.style.setProperty('outline', 'none', 'important');
                        currentFocus.style.setProperty('box-shadow', 'none', 'important');
                        currentFocus.style.setProperty('-webkit-box-shadow', 'none', 'important');
                        currentFocus.style.setProperty('-moz-box-shadow', 'none', 'important');
                        currentFocus.style.border = '';
                        
                        // Special handling for SelectWidget (listbox) elements
                        const selectWidget = currentFocus.closest('.hon-select-widget, .select-widget-button');
                        if (selectWidget) {
                            // Remove box-shadow from SelectWidget buttons
                            const buttons = selectWidget.querySelectorAll('.select-widget-button, .ui.button');
                            buttons.forEach(btn => {
                                btn.blur();
                                btn.style.setProperty('outline', 'none', 'important');
                                btn.style.setProperty('box-shadow', 'none', 'important');
                                btn.style.setProperty('-webkit-box-shadow', 'none', 'important');
                                btn.style.setProperty('-moz-box-shadow', 'none', 'important');
                            });
                        }
                        
                        // Special handling for DuctAreaCalculator elements
                        const ductCalculator = currentFocus.closest('.duct-area-calculator-modal, [class*="duct"]');
                        if (ductCalculator) {
                            // Remove box-shadow from all focusable elements in calculator
                            const calcInputs = ductCalculator.querySelectorAll('input, button, .ui.dropdown, .ui.input');
                            calcInputs.forEach(elem => {
                                elem.style.setProperty('outline', 'none', 'important');
                                elem.style.setProperty('box-shadow', 'none', 'important');
                                elem.style.setProperty('-webkit-box-shadow', 'none', 'important');
                                elem.style.setProperty('-moz-box-shadow', 'none', 'important');
                            });
                        }
                        
                        // Handle dropdown specific elements
                        const dropdown = currentFocus.closest('.ui.dropdown');
                        if (dropdown) {
                            dropdown.blur();
                            dropdown.style.setProperty('outline', 'none', 'important');
                            dropdown.style.setProperty('box-shadow', 'none', 'important');
                            dropdown.style.setProperty('-webkit-box-shadow', 'none', 'important');
                            dropdown.style.setProperty('-moz-box-shadow', 'none', 'important');
                            // Close any open dropdown
                            dropdown.classList.remove('active', 'visible');
                            const menu = dropdown.querySelector('.menu');
                            if (menu) {
                                menu.style.display = 'none';
                            }
                        }
                        
                        // Clean up the style override after a brief moment
                        setTimeout(() => {
                            currentFocus.style.removeProperty('outline');
                            currentFocus.style.removeProperty('box-shadow');
                            currentFocus.style.removeProperty('-webkit-box-shadow');
                            currentFocus.style.removeProperty('-moz-box-shadow');
                            if (dropdown) {
                                dropdown.style.removeProperty('outline');
                                dropdown.style.removeProperty('box-shadow');
                                dropdown.style.removeProperty('-webkit-box-shadow');
                                dropdown.style.removeProperty('-moz-box-shadow');
                            }
                        }, 50);
                        
                        // Also check for any Semantic UI Input elements that might be focused
                        const semanticInputs = document.querySelectorAll('.ui.input input:focus, .ui.dropdown:focus, .ui.dropdown.active, .ui.dropdown.visible');
                        semanticInputs.forEach(input => {
                            input.blur();
                            input.style.setProperty('outline', 'none', 'important');
                            input.style.setProperty('box-shadow', 'none', 'important');
                            input.style.setProperty('-webkit-box-shadow', 'none', 'important');
                            input.style.setProperty('-moz-box-shadow', 'none', 'important');
                            // Remove active/visible states from dropdowns
                            input.classList.remove('active', 'visible');
                            setTimeout(() => {
                                input.style.removeProperty('outline');
                                input.style.removeProperty('box-shadow');
                                input.style.removeProperty('-webkit-box-shadow');
                                input.style.removeProperty('-moz-box-shadow');
                            }, 50);
                        });
                        
                        // Reset tab navigation - simplified approach
                        setTimeout(() => {
                            if (document.activeElement && document.activeElement !== document.body) {
                                document.activeElement.blur();
                            }
                        }, 1);
                        
                        return; // Exit early for popup input handling
                    }
                }
                
                // Original logic for non-popup elements
                // Enhanced to handle dropdowns and input focus highlighting immediately
                if (currentFocus && currentFocus !== document.body) {
                    // Check if it's a dropdown or input that needs immediate highlight removal
                    const isDropdown = currentFocus.classList.contains('dropdown') || 
                                      currentFocus.closest('.ui.dropdown') ||
                                      currentFocus.tagName === 'SELECT';
                    const isInput = currentFocus.tagName === 'INPUT' || 
                                   currentFocus.tagName === 'TEXTAREA' ||
                                   currentFocus.closest('.ui.input');
                    
                    if (isDropdown || isInput) {
                        // Force remove focus highlighting immediately for dropdowns and inputs
                        currentFocus.style.setProperty('outline', 'none', 'important');
                        currentFocus.style.setProperty('box-shadow', 'none', 'important');
                        currentFocus.style.setProperty('-webkit-box-shadow', 'none', 'important');
                        currentFocus.style.setProperty('-moz-box-shadow', 'none', 'important');
                        
                        // Handle dropdown specific cleanup
                        if (isDropdown) {
                            const dropdown = currentFocus.closest('.ui.dropdown') || currentFocus;
                            dropdown.classList.remove('active', 'visible', 'focused');
                            const menu = dropdown.querySelector('.menu');
                            if (menu) {
                                menu.style.display = 'none';
                            }
                            dropdown.style.setProperty('outline', 'none', 'important');
                            dropdown.style.setProperty('box-shadow', 'none', 'important');
                            dropdown.style.setProperty('-webkit-box-shadow', 'none', 'important');
                            dropdown.style.setProperty('-moz-box-shadow', 'none', 'important');
                        }
                        
                        // Clean up the style override after a brief moment
                        setTimeout(() => {
                            currentFocus.style.removeProperty('outline');
                            currentFocus.style.removeProperty('box-shadow');
                            currentFocus.style.removeProperty('-webkit-box-shadow');
                            currentFocus.style.removeProperty('-moz-box-shadow');
                            if (isDropdown) {
                                const dropdown = currentFocus.closest('.ui.dropdown') || currentFocus;
                                dropdown.style.removeProperty('outline');
                                dropdown.style.removeProperty('box-shadow');
                                dropdown.style.removeProperty('-webkit-box-shadow');
                                dropdown.style.removeProperty('-moz-box-shadow');
                            }
                        }, 50);
                    }
                    
                    // Immediately blur the current focused element to remove cursor
                    currentFocus.blur();
                }
                
                // Give a small delay to allow any popup/modal to close, then refocus within wizard
                setTimeout(() => {
                    // Use the boundary checking method for consistency
                    if (!this.isCurrentFocusWithinWizardBoundaries()) {
                        // Focus the wizard container instead of first input to avoid cursor reappearing
                        const wizardContainer = this.findWizardContainer();
                        if (wizardContainer) {
                            wizardContainer.focus();
                        }
                    }
                }, 100);
            }

            // Centralized save action handler for both button clicks and keyboard shortcuts
            handleSaveAction = (triggerType) => {
                this.lastSaveTimestamp = Date.now();
                
                // Set up immediate save detection after a delay
                const checkSaveCompletion = () => {
                    const changedItems = Object.values(this.state.dynamicStore).filter(item => item.changed === true);
                    
                    if (changedItems.length === 0) {
                        this.handlePostSaveFocusReset();
                    } else {
                        // If still have changes, try again after a short delay (max 3 attempts)
                        if ((Date.now() - this.lastSaveTimestamp) < 3000) {
                            setTimeout(checkSaveCompletion, 500);
                        }
                    }
                };
                
                // Start checking for save completion
                setTimeout(checkSaveCompletion, 1000);
                
                // Also set up fallback detection in case the above doesn't catch it
                setTimeout(() => {
                    const changedItems = Object.values(this.state.dynamicStore).filter(item => item.changed === true);
                    if (changedItems.length === 0) {
                        this.handlePostSaveFocusReset();
                    }
                }, 2000);
            }

            findWizardContainer = () => {
                // Find Niagara widget container
                let container = document.querySelector('.bajaux-widget-container');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                container = document.querySelector('.bajaux-widget');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // Find WebShell content area
                container = document.querySelector('.WebShell-content .content');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                container = document.querySelector('.WebShell .content');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // Find specific tab container
                container = document.querySelector('.hon-dynamic-page-tab');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // Find React root container
                container = document.getElementById('Home');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // Traditional wizard containers
                container = document.querySelector('.honeywell-device-wizard');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                container = document.querySelector('.wizard-home-profile');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                container = document.querySelector('.hon-application-handler-wizard');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // Page container fallback
                container = document.querySelector('.tc-page-container');
                if (container) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                    return container;
                }
                
                // UI container or modal fallbacks
                container = document.querySelector('.ui.modal.active') || 
                           document.querySelector('.ui.container') || 
                           document.body;
                
                if (container && container !== document.body) {
                    if (!container.hasAttribute('tabindex')) {
                        container.setAttribute('tabindex', '-1');
                    }
                }
                return container;
            }

            // Boundary enforcement for tab switching operations
            isCurrentFocusWithinWizardBoundaries = () => {
                const currentFocus = document.activeElement;
                
                // If we're in a legitimate popup, consider it as valid
                if (this.isElementInWizardRelatedPopup(currentFocus)) {
                    return true;
                }
                
                const wizardContainer = this.findWizardContainer();
                
                // Check if no wizard container found
                if (!wizardContainer) {
                    return false;
                }
                
                // Check if current focus is within wizard container
                if (!wizardContainer.contains(currentFocus)) {
                    return false;
                }
                
                // Additional check: Ensure focus is not in excluded areas even within the container
                if (this.isElementOutsideWizardScope(currentFocus)) {
                    return false;
                }
                
                // Check for specific problematic focus targets
                const isOnBody = currentFocus.tagName === 'BODY' || currentFocus.tagName === 'HTML';
                const isOnIframe = currentFocus.tagName === 'IFRAME';
                const isInAddressBar = currentFocus.type === 'url' || currentFocus.name === 'address' || currentFocus.id === 'urlbar';
                const isInBrowserChrome = currentFocus.closest('.browser-chrome') || 
                                         currentFocus.closest('.browser-toolbar') || 
                                         currentFocus.closest('.browser-nav');
                const isInWorkbenchNav = currentFocus.closest('.WebShell-header') || 
                                        currentFocus.closest('.WebShell-nav') ||
                                        currentFocus.closest('.workbench-nav') ||
                                        currentFocus.closest('.workbench-menu');
                
                if (isOnBody || isOnIframe || isInAddressBar || isInBrowserChrome || isInWorkbenchNav) {
                    return false;
                }
                
                return true;
            }

            isElementOutsideWizardScope = (element) => {
                // Check if element is in Niagara workbench chrome/navigation
                if (element.closest('.WebShell-header') || element.closest('.WebShell-nav') || 
                    element.closest('.WebShell-sidebar') || element.closest('.WebShell-toolbar')) {
                    return true;
                }
                
                // Check if element is in browser navigation areas
                if (element.closest('nav') || element.closest('.browser-nav') || 
                    element.closest('.address-bar') || element.id === 'urlbar' ||
                    element.name === 'address' || element.type === 'url') {
                    return true;
                }
                
                // Check if element is part of browser chrome or application shell
                if (element.closest('.browser-chrome') || element.closest('.app-shell') ||
                    element.closest('.browser-toolbar') || element.closest('.url-bar')) {
                    return true;
                }
                
                // Check for Niagara workbench specific elements
                if (element.closest('.workbench-nav') || element.closest('.workbench-menu') ||
                    element.closest('.workbench-tree') || element.closest('.workbench-toolbar')) {
                    return true;
                }
                
                // Check if element is outside the bajaux widget container
                const widgetContainer = document.querySelector('.bajaux-widget-container') || 
                                      document.querySelector('.bajaux-widget') ||
                                      document.getElementById('Home');
                if (widgetContainer && !widgetContainer.contains(element)) {
                    return true;
                }
                
                // Check for elements with data attributes indicating external scope
                if (element.dataset && (element.dataset.external || element.dataset.browserControl ||
                    element.dataset.workbench || element.dataset.niagaraNav)) {
                    return true;
                }
                
                // Check if element is in a different window/frame context
                try {
                    if (element.ownerDocument !== document) {
                        return true;
                    }
                } catch (e) {
                    return true;
                }
                
                return false;
            }

            // Method to detect if element is a non-active item in a group control
            isElementInGroupControl = (element) => {
                // Check for SelectButton groups (hon-select-button with Menu.Item components)
                const selectButtonContainer = element.closest('.hon-select-button');
                if (selectButtonContainer && element.classList.contains('item')) {
                    // In SelectButton, only the active item should be focusable
                    const isActive = element.classList.contains('active');
                    if (!isActive) {
                        return true; // Exclude non-active items
                    }
                }
                
                // Check for SelectWidget groups (hon-select-widget with select-widget-button)
                const selectWidgetContainer = element.closest('.hon-select-widget');
                if (selectWidgetContainer && element.classList.contains('select-widget-button')) {
                    // SelectWidget already handles tabIndex correctly, but double-check
                    const tabIndex = element.getAttribute('tabindex');
                    if (tabIndex === '-1') {
                        return true; // Exclude items with tabindex="-1"
                    }
                }
                
                // Check for Button Groups (button-group)
                const buttonGroup = element.closest('.button-group');
                if (buttonGroup && element.tagName === 'BUTTON') {
                    // For button groups, check if this is a non-selected radio-style button
                    const isSelected = element.classList.contains('active') || 
                                     element.classList.contains('selected') ||
                                     element.getAttribute('aria-pressed') === 'true';
                    
                    // Count total buttons in group
                    const allButtons = buttonGroup.querySelectorAll('button');
                    const selectedButtons = buttonGroup.querySelectorAll('button.active, button.selected, button[aria-pressed="true"]');
                    
                    // If this is a radio-style group (only one should be active), exclude non-selected buttons
                    if (allButtons.length > 1 && selectedButtons.length <= 1 && !isSelected) {
                        return true; // Exclude non-selected buttons in radio-style groups
                    }
                }
                
                // Check for Radio Button Groups (similar logic for radio inputs)
                if (element.type === 'radio') {
                    const radioGroup = element.closest('fieldset') || 
                                     document.querySelectorAll('input[name="' + element.name + '"]');
                    if (radioGroup && radioGroup.length > 1 && !element.checked) {
                        return true; // Exclude non-checked radio buttons
                    }
                }
                
                return false; // Element is not in a group control or is the active item
            }

            // Helper method to find closest valid navigation element
            findClosestValidElement = (currentElement, focusableElements) => {
                if (!currentElement || !focusableElements.length) return null;
                
                const currentRect = currentElement.getBoundingClientRect();
                let closestElement = null;
                let closestDistance = Infinity;
                
                focusableElements.forEach(element => {
                    const elementRect = element.getBoundingClientRect();
                    const distance = Math.sqrt(
                        Math.pow(elementRect.left - currentRect.left, 2) + 
                        Math.pow(elementRect.top - currentRect.top, 2)
                    );
                    
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestElement = element;
                    }
                });
                
                return closestElement;
            }

            // Enhanced validation for navigation elements
            isValidNavigationElement = (element) => {
                if (!element) return false;
                
                // Basic focusable element check
                const tagName = element.tagName.toLowerCase();
                const focusableTags = ['input', 'select', 'textarea', 'button', 'a'];
                const hasFocusableTag = focusableTags.includes(tagName);
                const hasTabIndex = element.hasAttribute('tabindex') && element.getAttribute('tabindex') !== '-1';
                const isContentEditable = element.contentEditable === 'true';
                
                if (!hasFocusableTag && !hasTabIndex && !isContentEditable) return false;
                
                // Check if element is disabled or readonly
                if (element.disabled || element.readOnly) return false;
                
                // Check visibility
                const rect = element.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                if (!isVisible) return false;
                
                // Check CSS visibility
                const computedStyle = window.getComputedStyle(element);
                const isStyleVisible = computedStyle.display !== 'none' && 
                                     computedStyle.visibility !== 'hidden' &&
                                     computedStyle.opacity !== '0';
                
                return isStyleVisible;
            }

            // Visual feedback methods for better tab navigation visibility
            addSaveButtonHighlight = (saveButton) => {
                if (!saveButton) return;
                
                // Remove any existing highlight classes
                this.removeSaveButtonHighlight();
                
                // Add highlight class
                saveButton.classList.add('tab-nav-save-highlight');
                
                // Apply the same subtle styles as input controls
                const originalStyles = {
                    boxShadow: saveButton.style.boxShadow,
                    transition: saveButton.style.transition
                };
                
                // Use the same subtle highlight as input controls - no borders or outlines
                saveButton.style.boxShadow = '0 0 0 2px #90adee, 0 0 10px rgba(144, 173, 238, 0.3)';
                saveButton.style.transition = 'all 0.2s ease';
                
                // Store original styles for cleanup
                saveButton._originalStyles = originalStyles;
                
                // Clear any existing timeout to prevent conflicts
                if (saveButton._highlightTimeout) {
                    clearTimeout(saveButton._highlightTimeout);
                    delete saveButton._highlightTimeout;
                }
                
                // No auto-removal timeout - only remove when focus moves elsewhere
            }
            
            removeSaveButtonHighlight = () => {
                // Remove both types of save button highlights
                const highlightedButtons = document.querySelectorAll('.tab-nav-save-highlight, .save-button-highlighted');
                highlightedButtons.forEach(button => {
                    button.classList.remove('tab-nav-save-highlight', 'save-button-highlighted');
                    
                    // Restore original styles if they were stored
                    if (button._originalStyles) {
                        button.style.boxShadow = button._originalStyles.boxShadow;
                        button.style.transition = button._originalStyles.transition;
                        delete button._originalStyles;
                    } else {
                        // Fallback: clear styles directly
                        button.style.boxShadow = '';
                        button.style.outline = '';
                    }
                    
                    // Clear timeout if it exists
                    if (button._highlightTimeout) {
                        clearTimeout(button._highlightTimeout);
                        delete button._highlightTimeout;
                    }
                });
            }
            
            addElementHighlight = (element, persistent = true) => {
                if (!element) return;
                
                // Remove any existing highlights
                this.removeElementHighlight();
                
                // Add subtle highlight for normal tab navigation
                element.classList.add('tab-nav-element-highlight');
                
                const originalStyles = {
                    outline: element.style.outline,
                    boxShadow: element.style.boxShadow
                };
                
                // Apply subtle outline styling consistent with input textbox focus style
                element.style.outline = '1px solid #90adee64';
                element.style.boxShadow = 'none'; // Remove any enhanced box-shadow styling
                
                element._originalHighlightStyles = originalStyles;
                
                // Only auto-remove highlight for transient navigation, not for persistent focus
                if (!persistent) {
                    if (element._elementHighlightTimeout) {
                        clearTimeout(element._elementHighlightTimeout);
                    }
                    element._elementHighlightTimeout = setTimeout(() => {
                        this.removeElementHighlight();
                    }, 1500);
                }
                // For persistent focus (inputs, popups, dropdowns), highlight stays until user moves focus
            }
            
            removeElementHighlight = () => {
                const currentlyFocused = document.activeElement;
                const highlightedElements = document.querySelectorAll('.tab-nav-element-highlight');
                
                highlightedElements.forEach(element => {
                    // Only remove highlight if this element is not currently focused
                    if (element !== currentlyFocused) {
                        element.classList.remove('tab-nav-element-highlight');
                        
                        if (element._originalHighlightStyles) {
                            element.style.outline = element._originalHighlightStyles.outline;
                            element.style.boxShadow = element._originalHighlightStyles.boxShadow;
                            delete element._originalHighlightStyles;
                        }
                        
                        if (element._elementHighlightTimeout) {
                            clearTimeout(element._elementHighlightTimeout);
                            delete element._elementHighlightTimeout;
                        }
                    }
                });

                // Additional cleanup for semantic-ui dropdowns that might have lingering focus styles
                const dropdowns = document.querySelectorAll('.ui.dropdown.focused, .ui.dropdown.active');
                dropdowns.forEach(dropdown => {
                    if (dropdown !== document.activeElement) {
                        dropdown.classList.remove('focused', 'active');
                        dropdown.style.boxShadow = '';
                        dropdown.style.outline = '';
                    }
                });
            }

            findSaveButton = (container) => {
                // Save button is always present and common to all controls - find it aggressively
                
                // Most comprehensive save button selectors
                const saveSelectors = [
                    // Explicit save button patterns (most specific first)
                    'button[tabindex="99"]:not([disabled])', // WizardHomeProfile specific tabIndex
                    '.button-group .ui.button:not([disabled])',
                    '.button-group button:not([disabled])',
                    '.save-button:not([disabled])',
                    'button[type="submit"]:not([disabled])',
                    '.btn-save:not([disabled])',
                    
                    // Workbench and wizard specific patterns
                    '.workbench-buttons button:not([disabled])',
                    '.wizard-buttons button:not([disabled])',
                    '.action-buttons button:not([disabled])',
                    
                    // Include disabled buttons too since save button may be disabled when no changes
                    'button[tabindex="99"]', // WizardHomeProfile pattern (even if disabled)
                    '.button-group .ui.button',
                    '.button-group button',
                    'button[type="submit"]',
                    
                    // Generic button patterns (broadest search)
                    '.ui.button',
                    'button'
                ];
                
                // Search strategy: Cast the widest net possible since save button is always there
                const searchContainers = [
                    // Start with document-wide search for fastest results
                    document,
                    // Then try wizard container if available
                    this.findWizardContainer(),
                    // Then provided container
                    container
                ].filter(Boolean);
                
                for (const searchContainer of searchContainers) {
                    for (const selector of saveSelectors) {
                        const buttons = searchContainer.querySelectorAll(selector);
                        
                        for (const button of buttons) {
                            if (this.isSaveButton(button)) {
                                // Found a save button - verify it's visible
                                const rect = button.getBoundingClientRect();
                                const isVisible = rect.width > 0 && rect.height > 0 && 
                                                window.getComputedStyle(button).display !== 'none' &&
                                                window.getComputedStyle(button).visibility !== 'hidden';
                                
                                if (isVisible) {
                                    return button;
                                }
                            }
                        }
                    }
                }
                
                // Ultimate fallback: find ANY button that could be a save button by position/styling
                const allButtons = document.querySelectorAll('button, .ui.button');
                for (const button of allButtons) {
                    // Check if it's in a button group and is likely a save button
                    const buttonGroup = button.closest('.button-group');
                    if (buttonGroup) {
                        const allGroupButtons = buttonGroup.querySelectorAll('button, .ui.button');
                        const buttonIndex = Array.from(allGroupButtons).indexOf(button);
                        
                        // Check if it's the last button or has save-like styling
                        const isLastButton = buttonIndex === allGroupButtons.length - 1;
                        const hasSaveStyle = button.classList.contains('primary') || 
                                           button.classList.contains('positive') ||
                                           button.classList.contains('green') ||
                                           button.getAttribute('tabindex') === '99';
                        
                        if (isLastButton || hasSaveStyle) {
                            const rect = button.getBoundingClientRect();
                            const isVisible = rect.width > 0 && rect.height > 0;
                            if (isVisible) {
                                return button;
                            }
                        }
                    }
                }
                
                return null;
            }

            // Enhanced save button detection logic
            isSaveButton = (button) => {
                if (!button) return false;
                
                // Text-based detection (most reliable) - enhanced for multi-language
                const buttonText = (button.textContent || button.innerText || '').toLowerCase().trim();
                const savePatterns = ['save', 'submit', 'apply', 'confirm', 'ok', 'accept', 'guardar', 'enregistrer', 'speichern'];
                if (savePatterns.some(pattern => buttonText.includes(pattern))) {
                    return true;
                }
                
                // ARIA-label detection for accessibility and icon-only buttons
                const buttonAriaLabel = button.getAttribute('aria-label');
                if (buttonAriaLabel && savePatterns.some(pattern => buttonAriaLabel.toLowerCase().includes(pattern))) {
                    return true;
                }
                
                // Title attribute detection
                const buttonTitle = button.getAttribute('title');
                if (buttonTitle && savePatterns.some(pattern => buttonTitle.toLowerCase().includes(pattern))) {
                    return true;
                }
                
                // Class-based detection
                if (button.classList.contains('save-button') || 
                    button.classList.contains('btn-save') ||
                    button.classList.contains('submit-button') ||
                    button.classList.contains('apply-button')) {
                    return true;
                }
                
                // Type-based detection
                if (button.type === 'submit') {
                    return true;
                }
                
                // TabIndex-based detection (WizardHomeProfile pattern)
                if (button.getAttribute('tabindex') === '99') {
                    return true;
                }
                
                // Data attribute detection
                if (button.getAttribute('data-action') === 'save' ||
                    button.getAttribute('data-type') === 'save' ||
                    button.getAttribute('data-role') === 'save') {
                    return true;
                }
                
                // Button group context detection with enhanced positioning logic
                const buttonGroup = button.closest('.button-group');
                if (buttonGroup) {
                    // In button groups, look for save-related attributes or positioning
                    const allButtonsInGroup = buttonGroup.querySelectorAll('button, .ui.button');
                    const buttonIndex = Array.from(allButtonsInGroup).indexOf(button);
                    
                    // Often save button is the last or first button in the group
                    if (buttonIndex === allButtonsInGroup.length - 1 || buttonIndex === 0) {
                        // Additional check: does the button have save-like properties?
                        const hasSubmitLikeClass = button.classList.contains('primary') || 
                                                 button.classList.contains('positive') ||
                                                 button.classList.contains('green') ||
                                                 button.classList.contains('blue');
                        
                        if (hasSubmitLikeClass) {
                            return true;
                        }
                    }
                }
                
                // Aria-label based detection
                const saveButtonAriaLabel = button.getAttribute('aria-label') || '';
                if (saveButtonAriaLabel.toLowerCase().includes('save')) {
                    return true;
                }
                
                // Title attribute detection
                const saveButtonTitle = button.getAttribute('title') || '';
                if (saveButtonTitle.toLowerCase().includes('save')) {
                    return true;
                }
                
                return false;
            }

            checkForUnsavedChanges = () => {
                const {globalStore} = this.props;
                
                // Validate global store structure
                if (!globalStore || !globalStore.dynamicStores) {
                    return false;
                }
                
                // Iterate through all dynamic stores to detect unsaved changes
                const allStoreNames = Object.keys(globalStore.dynamicStores);
                let hasAnyChanges = false;
                let changedStoresList = [];
                
                for (const storeName of allStoreNames) {
                    const store = globalStore.dynamicStores[storeName];
                    
                    // Handle array-based stores (e.g., Scheduling with multiple instances)
                    if (Array.isArray(store)) {
                        for (let i = 0; i < store.length; i++) {
                            const storeItem = store[i];
                            if (storeItem && storeItem.items) {
                                const hasStoreChanges = this.checkStoreForChanges(storeItem.items, `${storeName}[${i}]`);
                                if (hasStoreChanges) {
                                    hasAnyChanges = true;
                                    changedStoresList.push(`${storeName}[${i}]`);
                                }
                            }
                        }
                    } else if (store && store.items && typeof store.items === 'object') {
                        // Handle object-based stores (e.g., DynamicPage, TerminalAssignment)
                        const hasStoreChanges = this.checkStoreForChanges(store.items, storeName);
                        if (hasStoreChanges) {
                            hasAnyChanges = true;
                            changedStoresList.push(storeName);
                        }
                    }
                }
                
                return hasAnyChanges;
            }
            
            /**
             * Validates individual store items for unsaved changes
             * @param {Object} storeItems - Store items to validate
             * @param {string} storeName - Store name for identification
             * @returns {boolean} True if store contains valid unsaved changes
             */
            checkStoreForChanges = (storeItems, storeName) => {
                if (!storeItems || typeof storeItems !== 'object') {
                    return false;
                }
                
                const itemValues = Object.values(storeItems);
                
                const changedItems = itemValues.filter(item => {
                    // Validate item structure
                    if (!item || typeof item !== 'object') return false;
                    
                    // Require explicit change flag
                    if (item.changed !== true) return false;
                    
                    // Exclude programmatic changes to prevent false positives
                    if (item.isProgrammatic === true) return false;
                    
                    // Require meaningful value (non-empty, non-null)
                    if (item.value === undefined || item.value === null || item.value === '') return false;
                    
                    // Validate change against original value if available
                    if (item.originalValue !== undefined && item.value === item.originalValue) return false;
                    
                    // Handle boolean value changes
                    if (typeof item.value === 'boolean' && item.originalValue !== undefined) {
                        return item.value !== item.originalValue;
                    }
                    
                    // Handle numeric value changes with precision threshold
                    if (typeof item.value === 'number' && typeof item.originalValue === 'number') {
                        const difference = Math.abs(item.value - item.originalValue);
                        return difference > 0.0001; // Precision threshold for meaningful change
                    }
                    
                    return true;
                });
                
                return changedItems.length > 0;
            }

            handleComponentChange = (e, data, isProgrammatic = false) => {
                const {dynamicStore} = this.state;
                const {dynamicStoreName, globalStore, onApplyRuleChanged, onMeasurementTypeChanged, onAirflowUnitChanged} = this.props;
                const obj = data.dataObj;
                let beforeChangedValue = null;
                let isMeasurementTypeChanged = false;
				let isAirflowUnitChanged = false;

                if(obj) {
                    // for switch button
                    if(obj.componentType === "SwitchButton") {
                        if(obj.value !== data.checked) {
                            obj.value = data.checked;
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                    }
                    else if(obj.componentType === "CheckboxGroup" || obj.componentType === "TextInput") {
                        if(obj.value !== data.value) {
                            obj.value = data.value;
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                    } else if(obj.componentType === "NumberInput") {
                        if(parseFloat(obj.value.toFixed(obj.precision)) !== parseFloat(data.value)) {
                            beforeChangedValue = obj.value;
                            obj.value = parseFloat(data.value);
                            obj.highPrecisionValue = data.value.toString();
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                    } else if (obj.componentType === "MeasurementType" ) {
                        if(obj.value !== data.value) {
                            isMeasurementTypeChanged = true;
                            obj.value = data.value;
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                    } else if (obj.componentType === "AirflowUnit") {
                        if (obj.value !== data.value) {
                            isAirflowUnitChanged = true;
                            obj.value = data.value;
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                   }else if (obj.componentType === "Timezone" ) {
                        if(obj.value !== data.value) {
                            obj.value = data.value;
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                            if(this.props.unitComponentInTab && this.props.unitComponentInTab !== '' && this.props.unitComponentKey && this.props.unitComponentKey !== '' && 
                               this.props.globalStore && this.props.globalStore.dynamicStores && this.props.globalStore.dynamicStores[this.props.unitComponentInTab]) { 
                                let unitObj = this.props.globalStore.dynamicStores[this.props.unitComponentInTab].items[this.props.unitComponentKey];
                                unitObj.value = this.timeZoneBasedUnitMapping[0].indexOf(data.value) !== -1 ? 0 : 1;
                                unitObj.changed = false; // Always false for programmatic cascade changes
                                unitObj.dataObj = unitObj;
                                this.handleComponentChange({}, unitObj, true); // Mark as programmatic
                            }
                            if(this.props.airflowUnitComponentInTab && this.props.airflowUnitComponentInTab !== '' && this.props.airflowUnitComponentKey && this.props.airflowUnitComponentKey !== '' &&
                               this.props.globalStore && this.props.globalStore.dynamicStores && this.props.globalStore.dynamicStores[this.props.airflowUnitComponentInTab]) { 
                                let airflowUnitObj = this.props.globalStore.dynamicStores[this.props.airflowUnitComponentInTab].items[this.props.airflowUnitComponentKey];
                                airflowUnitObj.value = this.timeZoneBasedAirFlowUnitMapping[0].indexOf(data.value) !== -1 ? 1 : 
                                this.timeZoneBasedAirFlowUnitMapping[1].indexOf(data.value) !== -1 ? 2 : 3;
                                airflowUnitObj.changed = false; // Always false for programmatic cascade changes
                                airflowUnitObj.dataObj = airflowUnitObj;
                                this.handleComponentChange({}, airflowUnitObj, true); // Mark as programmatic
                            }
                        }

                    } else {
                        if(obj.value !== parseFloat(data.value)) {
                            beforeChangedValue = obj.value;
                            obj.value = parseFloat(data.value);
                            obj.highPrecisionValue = data.value.toString();
                            obj.changed = !isProgrammatic; // Only mark as changed if not programmatic
                        }
                    }

                    if(obj.componentType !== "NumberInput" || e.type === "blur" || e.type === "keypress") {
                        let impactedTabsVisibleRules = CommonVisibilityRules.applyVisibleRules(globalStore, data.dataObj.name, `${dynamicStoreName}-${data.dataObj.tabInPage}`);
                        let impactedTabsValueRules = CommonVisibilityRules.applyValueRules(globalStore, data.dataObj.name, `${dynamicStoreName}-${data.dataObj.tabInPage}`);
                        CommonVisibilityRules.applyConstraintRules(obj.name,  globalStore, beforeChangedValue);
                        const {terminalImpactedTabs, terminalImpactedSubTabs,terminalRuleErrors, changedTerminals} = TerminalAssignmentValueRules.applyValueRules(globalStore, data.dataObj.name);
                        if(terminalRuleErrors.length || changedTerminals) {
                            onApplyRuleChanged(terminalRuleErrors, changedTerminals);
                        }

                        let mergedImpactedTabs = impactedTabsVisibleRules.impactedTabs.concat(impactedTabsValueRules.impactedTabs).concat(terminalImpactedTabs);
                        let finalImpactedTabs = mergedImpactedTabs.filter((item, index) => mergedImpactedTabs.indexOf(item) === index);

                        let mergedImpactedSubTabs = impactedTabsVisibleRules.impactedSubTabs.concat(impactedTabsValueRules.impactedSubTabs).concat(terminalImpactedSubTabs);
                        let finalImpactedSubTabs = mergedImpactedSubTabs.filter((item, index) => mergedImpactedSubTabs.indexOf(item) === index);

                        this.props.updateImpactedTabs(finalImpactedTabs, finalImpactedSubTabs);

                        if(this.props.onDynamicPageChanged && obj.changed) {
                            this.props.onDynamicPageChanged(dynamicStoreName);
                        }
                    }
                    //first apply rules, then do unit conversion
                    //if we do unit conversion first, value maybe changed by the rules so that it is not expected value.

                    if(isMeasurementTypeChanged && onMeasurementTypeChanged) {
                        const selectedOption = data.options.find(i => i.value === data.value);
                        const selectedText = selectedOption ? selectedOption.text : '';
                        onMeasurementTypeChanged(obj.value, selectedText);
                    }

					if(isAirflowUnitChanged && onAirflowUnitChanged) {
					    const selectedOption = data.options.find(i => i.value === data.value);
					    const selectedText = selectedOption ? selectedOption.text : '';
					    onAirflowUnitChanged(obj.value, selectedText);
					}
                }

                // Update state with changes
                this.setState({dynamicStore: Object.assign({}, dynamicStore)});
            }

            handleTabChange=(e, data)=>{
                this.setState({ activeTabIndex: data.activeIndex });
                
                // Do NOT auto-focus first input when switching tabs via UI
                // This matches the behavior of main vertical menu switching
            }

            focusFirstInput = (showHighlight = true) => {
                // Use same selectors as handleTabNavigation
                const focusableSelectors = [
                    'input[type="text"]:not([disabled]):not([readonly])',
                    'input[type="number"]:not([disabled]):not([readonly])',
                    'input[type="email"]:not([disabled]):not([readonly])',
                    'input[type="password"]:not([disabled]):not([readonly])',
                    'select:not([disabled])',
                    'textarea:not([disabled]):not([readonly])',
                    'button:not([disabled])',
                    '.ui.dropdown:not(.disabled)',
                    '[tabindex]:not([tabindex="-1"]):not([disabled])'
                ].join(', ');

                // Use the same dynamic tab detection as handleTabNavigation
                const allTabPanes = document.querySelectorAll('.hon-dynamic-page-tab .ui.tab.segment');
                let activeTabPane = null;
                
                if (allTabPanes.length > 0 && this.state.activeTabIndex < allTabPanes.length) {
                    activeTabPane = allTabPanes[this.state.activeTabIndex];
                } else {
                    // Fallback for single tab or no tab structure
                    activeTabPane = document.querySelector('.tc-page-container .ui.tab.segment.active') ||
                                   document.querySelector('.tc-page-container');
                }
                
                if (!activeTabPane) {
                    return;
                }

                const wizardContainer = this.findWizardContainer();
                
                // Get all focusable elements and filter out group control non-active items
                const allFocusableElements = Array.from(activeTabPane.querySelectorAll(focusableSelectors))
                    .filter(el => {
                        // Filter out hidden elements
                        const rect = el.getBoundingClientRect();
                        const isVisible = rect.width > 0 && rect.height > 0 && 
                                        window.getComputedStyle(el).visibility !== 'hidden';
                        
                        // Filter out elements outside wizard
                        const isWithinWizard = !wizardContainer || wizardContainer.contains(el);
                        
                        // Respect group controls - exclude non-active items in groups
                        const isInGroupControl = this.isElementInGroupControl(el);
                        
                        return isVisible && isWithinWizard && !isInGroupControl;
                    });
                
                const firstFocusable = allFocusableElements.length > 0 ? allFocusableElements[0] : null;
                
                if (firstFocusable && (!wizardContainer || wizardContainer.contains(firstFocusable))) {
                    firstFocusable.focus();
                    
                    // Add visual highlight only when requested (not for Escape key)
                    if (showHighlight) {
                        this.addElementHighlight(firstFocusable);
                    }
                    
                    // Handle special case for semantic-ui dropdowns
                    if (firstFocusable.classList.contains('ui') && firstFocusable.classList.contains('dropdown')) {
                        setTimeout(() => {
                            const input = firstFocusable.querySelector('input') || firstFocusable.querySelector('.text');
                            if (input) {
                                input.focus();
                            }
                        }, 10);
                    }
                } else {
                    // No focusable element found in current tab
                }
            }

            // Post-save focus reset with optimized approach
            handlePostSaveFocusReset = () => {
                this.lastSaveTimestamp = Date.now();
                
                // Clear any save button highlights immediately
                this.removeSaveButtonHighlight();
                
                // Simple single-attempt focus reset
                const resetFocus = () => {
                    const wizardContainer = this.findWizardContainer();
                    if (!wizardContainer) {
                        return;
                    }

                    // Use enhanced active tab detection
                    const activeTabPane = this.getActiveTabPane();
                    if (!activeTabPane) {
                        return;
                    }
                    
                    // No longer auto-focus first element - let user continue natural navigation
                    // Just ensure wizard container is properly configured for tabbing
                    if (wizardContainer.getAttribute('tabindex') === null) {
                        wizardContainer.setAttribute('tabindex', '-1');
                    }
                };
                
                // Single attempt with minimal delay
                setTimeout(resetFocus, 50);
            }

            // Method to force focus back into wizard when it goes outside
            forceFocusBackToWizard = () => {
                const wizardContainer = this.findWizardContainer();
                if (!wizardContainer) {
                    return;
                }
                
                // Use the enhanced active tab detection
                const activeTabPane = this.getActiveTabPane();
                if (!activeTabPane) {
                    return;
                }
                
                // Get focusable elements using the enhanced method
                const focusableElements = this.getFocusableElementsInPane(activeTabPane, wizardContainer);
                const firstInput = focusableElements.length > 0 ? focusableElements[0] : null;
                
                if (firstInput) {
                    this.focusElement(firstInput);
                    
                    // Use requestAnimationFrame for better timing instead of setTimeout
                    requestAnimationFrame(() => {
                        const actualFocused = document.activeElement;
                        if (!wizardContainer.contains(actualFocused)) {
                            // Still outside, focus wizard container as fallback
                            if (wizardContainer.getAttribute('tabindex') === null) {
                                wizardContainer.setAttribute('tabindex', '-1');
                            }
                            wizardContainer.focus();
                        }
                    });
                } else {
                    // Fallback: focus the wizard container itself
                    if (wizardContainer.getAttribute('tabindex') === null) {
                        wizardContainer.setAttribute('tabindex', '-1');
                    }
                    wizardContainer.focus();
                }
            }

            showHelpForFocusedElement = () => {
                const focusedElement = document.activeElement;
                
                if (!focusedElement) {
                    return;
                }

                // Find the nearest Help.svg icon associated with the focused element
                const helpIcon = this.findNearestHelpIcon(focusedElement);
                
                if (helpIcon) {
                    // Simply click the help icon to trigger existing popup
                    helpIcon.click();
                }
            }

            findNearestHelpIcon = (element) => {
                // Strategy 1: Look in the same container as the focused element
                let container = element.closest('.field, .ui.field, .form-group, .input-container, .tc-component');
                
                if (container) {
                    const helpIcon = container.querySelector('img[src*="Help.svg"]');
                    if (helpIcon) {
                        return helpIcon;
                    }
                }

                // Strategy 2: Look in parent containers by traversing up
                let currentElement = element.parentElement;
                while (currentElement && currentElement !== document.body) {
                    const helpIcon = currentElement.querySelector('img[src*="Help.svg"]');
                    if (helpIcon) {
                        return helpIcon;
                    }
                    currentElement = currentElement.parentElement;
                }

                // Strategy 3: Look for the next Help.svg in the document after the focused element
                const allHelpIcons = document.querySelectorAll('img[src*="Help.svg"]');
                const focusedRect = element.getBoundingClientRect();
                
                // Find the closest help icon by distance
                let closestIcon = null;
                let closestDistance = Infinity;
                
                allHelpIcons.forEach(icon => {
                    const iconRect = icon.getBoundingClientRect();
                    // Calculate distance from focused element to help icon
                    const distance = Math.sqrt(
                        Math.pow(iconRect.left - focusedRect.left, 2) + 
                        Math.pow(iconRect.top - focusedRect.top, 2)
                    );
                    
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestIcon = icon;
                    }
                });

                return closestIcon;
            }

            getComponents(storeValues) {
                // filter out objects with same belong to avoid generating duplicate complex components
                const storeValuesWithoutSameBelong = storeValues.filter((value, index, array) => {
                    return value.belong === undefined || array.findIndex(obj => obj.belong === value.belong) === index;
                });

                // generate simple component with object without belong,
                // and also complex component by joining multiple objects with same belong
                const components = storeValuesWithoutSameBelong.map((obj, index) => {
                    if(obj.belong === undefined) {
                        const component = ComponentGenerator.generateComponent(obj, this.handleComponentChange);
                        return React.cloneElement(component, { key: obj.name || `component-${index}` });
                    }
                    else {
                        const objList = storeValues.filter(value => value.belong === obj.belong);
                        const component = ComponentGenerator.generateComplexComponent(objList, this.handleComponentChange);
                        return React.cloneElement(component, { key: obj.belong || `complex-component-${index}` });
                    }
                });

                return this.joinComponentsInline(components, storeValuesWithoutSameBelong);
            }

            joinComponentsInline(components, objects) {
                const newComponents = [];
                let index = 0;
                let inlineComponents = [];
                let curInline = null;
                while(index < components.length && index < objects.length) {
                    const component = components[index];
                    const inline = objects[index].inline;

                    if(!inline) {
                        if(inlineComponents.length > 0) {
                            newComponents.push(
                                <ComponentInlineContainer key={index}>
                                    {inlineComponents}
                                </ComponentInlineContainer>
                            );
                            inlineComponents = [];
                            curInline = null;
                        }
                        newComponents.push(component);
                    }
                    else {
                        if(inline === curInline) {
                            inlineComponents.push(component);
                        }
                        else {
                            if(inlineComponents.length > 0) {
                                newComponents.push(
                                    <ComponentInlineContainer key={index}>
                                        {inlineComponents}
                                    </ComponentInlineContainer>
                                );
                            }
                            inlineComponents = [component];
                            curInline = inline;
                        }
                    }

                    index++;
                }

                if(inlineComponents.length > 0) {
                    newComponents.push(
                        <ComponentInlineContainer key={index}>
                            {inlineComponents}
                        </ComponentInlineContainer>
                    );
                }

                return newComponents;
            }

            getObjectsByTabInPage(storeLabel) {
                const {dynamicStore} = this.state;

                const storeValues = Object.values(dynamicStore);
                if(storeValues.length === 0) {
                    return [];
                }

                storeValues.sort((a, b) => a.index - b.index);

                const result = [];
                storeValues.forEach(obj => {
                    const objTabName = obj.tabInPage ? obj.tabInPage : storeLabel;
                    let objectsInTab = result.findLast(x => objTabName === x.tabInPage);

                    if(objectsInTab) {
                        objectsInTab.objects.push(obj);
                    }
                    else {
                        objectsInTab = {tabInPage: objTabName, objects: [obj]};
                        result.push(objectsInTab);
                    }
                });
                this.numberOfSubTabs = result.length;

                return result;
            }

            getTabPanes(objectsByTab) {
                return objectsByTab.map((tabObject, index) => {
                    return (
                        {
                            key: `tab-pane-${tabObject.tabInPage || index}`,
                            menuItem: <MenuItem 
                            // tabIndex="1"
                            key={tabObject.tabInPage} 
                            onClick={async () => { await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex].tabInPage}`); 
                            await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${tabObject.tabInPage}`); }}
                            onKeyDown={async (e) => { if(e.key === 'Enter') { 
                                    await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${objectsByTab[this.state.activeTabIndex].tabInPage}`); 
                                    await this.props.removeFromImpactedSubTabs(`${this.props.dynamicStoreName}-${tabObject.tabInPage}`); 
                                    this.setState({ activeTabIndex: index });
                                }
                            }}
                            >
                                        <span>{tabObject.tabInPage}</span>
                                        {this.props.impactedSubTabs.indexOf(`${this.props.dynamicStoreName}-${tabObject.tabInPage}`) !== -1 && <span className="submenu-dot"></span>}
                                    </MenuItem>,
                            render: () => (
                                <Tab.Pane key={`tab-content-${tabObject.tabInPage || index}`}>
                                    <div className="tc-page-container">
                                        {this.getComponents(tabObject.objects)}
                                    </div>
                                </Tab.Pane>
                            )
                        }
                    );
                });
            }

            render() {
                const dynamicStore = this.props.globalStore.dynamicStores[this.props.dynamicStoreName];
                const objectsByTabInPage = this.getObjectsByTabInPage(dynamicStore.label);

                if(objectsByTabInPage.length === 0) {
                    return (
                        <div className="tc-page-container"></div>
                    );
                }

                if(objectsByTabInPage.length === 1 && objectsByTabInPage[0].tabInPage === "") {
                    return (
                        <div className="tc-page-container">
                            {this.getComponents(objectsByTabInPage[0].objects)}
                        </div>
                    );
                }

                return (
                    <Tab
                        className="hon-dynamic-page-tab"
                        menu={{ secondary: true, pointing: true }}
                        onTabChange={this.handleTabChange}
                        renderActiveOnly
                        activeIndex={this.state.activeTabIndex}
                        panes={this.getTabPanes(objectsByTabInPage)}
                    />
                );
            }
        }

        return DynamicPage;
});