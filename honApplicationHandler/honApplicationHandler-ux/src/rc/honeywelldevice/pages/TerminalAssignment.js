define([
    'baja!',
    'react',
    'semantic-ui-react',
    'lex!honApplicationHandler',
    'html-react-parser',
    'resize-observer-polyfill',
    'nmodule/honApplicationHandler/rc/factory/HoneywellDeviceWizardRPC',
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ModalDialog',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
    'css!nmodule/honApplicationHandler/rc/css/TerminalAssignment',
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min'],
    (baja, React, SemanticReact, lexicons, HtmlReactParser, ResizeObserver, HoneywellDeviceWizardRPC, ModalDialog) => {
        'use strict';

        var honApplicationHandlerLex = lexicons[0];

        const {
            Grid, Popup, Dropdown
        } = SemanticReact;
        const UNASSIGNED = "Unassigned";
        const X_OFFSET = 150;
        const DROPDOWN_WIDTH = 135;
        const MAX_DROPDOWN_OPEN_HEIGHT = 160;
        const OFFSET = 4;
        const LINE_THRESHOLD = 3;
        const DROPDOWN_LINE_Y_OFFSET = 12.5;
        const TEXT_LEFT_LINE_X_OFFSET = 14.5;
        const TEXT_RIGHT_LINE_X_OFFSET = 32;
        const TEXT_LINE_Y_OFFSET = 2.5;
        const DROPDOWN_ITEM_HEIGHT = 25;

        class TerminalAssignment extends React.Component {
            constructor(props) {
                super(props);
                this.state = {
                    swapDialogText: "",
                    width: 0,
                    height: 0,
                    viewBox: "",
                    svgStyle: "",
                    svgContent: "",
                    pinSlotCoordinates: {},
                    textCoordinates: {},
                    highLightTerminalName: "",
                    highLightTerminalNameFocused: "",
                    dropdownScale: 1
                };
                this.svgRef = React.createRef();
            }
            componentDidMount() {
                this.getSVGAndDrCoordinate();
                window.addEventListener('resize', this.updateDropdownScale);
                if (this.svgRef.current) {
                    this.resizeObserver = new ResizeObserver(() => {
                        this.updateDropdownScale();
                    });
                    this.resizeObserver.observe(this.svgRef.current);
                }

            }
            componentWillUnmount() {
                window.removeEventListener('resize', this.updateDropdownScale);
                if (this.resizeObserver) {
                    this.resizeObserver.unobserve(this.svgRef.current);
                }


            }

            componentDidUpdate(prevProps, prevState) {
                if (this.state.svgContent && this.state.svgContent !== prevState.svgContent) {
                    this.updateDropdownScale();
                }
            }

            orderByRenderingIndex(obj) {
                return Object.fromEntries(
                    Object.entries(obj).sort((a, b) => {
                    const [a0, a1] = a[1];
                    const [b0, b1] = b[1];

                    const floorA0 = Math.floor(a0);
                    const floorB0 = Math.floor(b0);

                    if (floorA0 !== floorB0) {
                        return floorA0 - floorB0;
                    }

                    return a1 - b1;
                    })
                );
            }

            getSVGAndDrCoordinate() {
                let jsonObj = {
                    deviceHandle: this.props.deviceHandle
                };
                const params = [{ myJSON: jsonObj }];

                HoneywellDeviceWizardRPC.invokeRPC('getTerminalAssignmentView', params).then(terminals => {
                    const pinSlotCoordinates = this.orderByRenderingIndex(this.getPinSlotCoordinates(terminals.textCoordinateMap, terminals.viewBox));
                    const { minX, minY, width, height } = this.getBoundingBox(pinSlotCoordinates, terminals.viewBox);
                    this.setState({
                        svgContent: terminals.svgContent,
                        viewBox: minX + " " + minY + " " + width + " " + height,
                        svgStyle: terminals.style,
                        pinSlotCoordinates: pinSlotCoordinates,
                        textCoordinates: terminals.textCoordinateMap,
                        height: height,
                        width: width
                    })
                    this.convertStyleToCSSClass(terminals.style);
                });

            }

            getPinSlotCoordinates = (textCoordinateMap, viewBox) => {
                    const updatedMap = {};
                    const leftPoints = [];
                    const rightPoints = [];
                    const midX = (viewBox[0] + viewBox[2]) / 2.0;

                    // Separate points into left and right groups
                    Object.entries(textCoordinateMap).forEach(([key, [x, y]]) => {
                        if (x < midX) {
                            leftPoints.push([key, x, y]);
                        } else {
                            rightPoints.push([key, x, y]);
                        }
                    });

                    // Function to evenly distribute y values
                    const distributeYValues = (points, viewBox) => {
                        const step = (viewBox[3] - viewBox[1]) / (points.length + 1);
                        return points.sort((a, b) => a[2] - b[2]).map(([key, x], index) => [key, x, viewBox[1] + step * (index + 1)]);
                    };

                    // Distribute y values for left and right points
                    const leftDistributed = distributeYValues(leftPoints, viewBox);
                    const rightDistributed = distributeYValues(rightPoints, viewBox);

                    // Update the map with new coordinates,make sure all x coordinates are same
                    let leftStartX = null;
                    leftDistributed.forEach(([key, x, y]) => {
                        x = x - X_OFFSET - DROPDOWN_WIDTH;
                        leftStartX === null? leftStartX = x :x = leftStartX;
                        updatedMap[key] = [x, y];
                    });
                    let distanceWithCenter = (viewBox[0] + viewBox[2]) / 2 - leftStartX;
                    let rightStartX = (viewBox[0] + viewBox[2]) / 2 + distanceWithCenter - DROPDOWN_WIDTH;

                    rightDistributed.forEach(([key, x, y]) => {
                        updatedMap[key] = [rightStartX, y];
                    });

                    return updatedMap;
            }
            getSvgScale = (svgElement) => {
                const viewBox = svgElement.getAttribute('viewBox').split(' ').map(Number);
                const [viewBoxWidth, viewBoxHeight] = [viewBox[2], viewBox[3]];
                const clientWidth = svgElement.clientWidth;
                const clientHeight = svgElement.clientHeight;
                return {
                    scaleX: clientWidth / viewBoxWidth,
                    scaleY: clientHeight / viewBoxHeight,
                };
            }
            updateDropdownScale = () => {
                const svgElement = document.getElementById('svgCanvas');
                if (!svgElement) return;

                const { scaleX, scaleY } = this.getSvgScale(svgElement);
                const scale = Math.min(scaleX, scaleY);
                console.log("scaleX:", scaleX, "scaleY:", scaleY);
                this.setState({ dropdownScale: scale });
            };


            getBoundingBox = (pinSlotCoordinates, viewBox) => {
                let minX = viewBox[0];
                let minY = viewBox[1];
                let maxX = viewBox[0] + viewBox[2];
                let maxY = viewBox[1] + viewBox[3];

                Object.values(pinSlotCoordinates).forEach(([x, y]) => {
                    if (x < minX) {
                        minX = x;
                    }
                    if (y < minY) {
                        minY = y;
                    }
                    if (x > maxX) {
                        maxX = x;
                    }
                    if (y > maxY) {
                        maxY = y;
                    }
                });

                return { minX: Math.min(minX, viewBox[0]), minY: Math.min(viewBox[1], minY), width: Math.max(maxX + DROPDOWN_WIDTH  - minX, viewBox[2]), height: Math.max(maxY  - viewBox[1], viewBox[3]) };
            };


            createDropdownStyle = (terminal) => {
                const {highLightTerminalName} = this.state;
                return {
                    border: terminal.changed ? "1px solid #E03BF9" : (highLightTerminalName === terminal.terminalName?"1.5px solid #5F8FF1":"1px solid #606060"),
                    color: terminal.changed ? "#E03BF9" : "black"

                }
            }
            handleChange = (e, data, terminal) => {
                const previousTerminal = terminal.terminalAssignedName;
                if (data.value !== previousTerminal) {
                    this.swapOutTerminal(terminal, terminal.terminalAssignedName, data.value);

                }
            }

            handleDropdownFocusEnter = (terminalName) => {
                this.setState({
                    highLightTerminalName: terminalName,
                    highLightTerminalNameFocused: terminalName
                })

            }
            handleDropdownFocusLeave = (terminalName) => {
                this.setState({
                    highLightTerminalName: terminalName,
                    highLightTerminalNameFocused: ""
                })
            }


            handleDropdownMouseEnter = (terminalName) => {
                this.setState({
                    highLightTerminalName: terminalName
                })

            }
            handleDropdownMouseLeave = (terminalName) => {
                this.setState({
                    highLightTerminalName: this.state.highLightTerminalNameFocused ? this.state.highLightTerminalNameFocused : ""
                })
            }
            handlePolylineMouseEnter = (terminalName) => {
                this.setState({
                    highLightTerminalName: terminalName
                })
            }
            handlePolylineMouseLeave = (terminalName) => {
                this.setState({
                    highLightTerminalName: this.state.highLightTerminalNameFocused ? this.state.highLightTerminalNameFocused : ""
                })
            }


            notifyDataChanged = () => {
                const { terminalStore } = this.props;
                const { onDynamicPageChanged, dynamicStoreName } = this.props;
                onDynamicPageChanged(dynamicStoreName, terminalStore.terminals.map(terminal => ({ "pinName": terminal.terminalName, "fbName": terminal.terminalAssignedName })));
            }

            swapOutTerminal = (currentTerminal, prevFb, nextFb) => {
                let { swapDialogText } = this.state;
                let { terminalStore } = this.props;
                let foundTerminal = terminalStore.terminals.filter(terminal => terminal.terminalAssignedName === nextFb && nextFb !== UNASSIGNED);
                const { onDynamicPageChanged } = this.props;
                if (foundTerminal.length === 1) {
                    foundTerminal.forEach(terminal => {
                        if (terminal.terminalOptions.filter(t => t.value === prevFb).length === 0) {
                            swapDialogText = honApplicationHandlerLex.get('HoneywellDeviceWizardLex.characteristicsNotSupportedTerminalError', prevFb, terminal.terminalName);
                            this.swapTerminal = terminal;
                            this.curTerminal = currentTerminal;
                            this.fb = nextFb;
                            this.setState({ swapDialogText });
                        } else {
                            terminal.terminalAssignedName = prevFb;
                            currentTerminal.terminalAssignedName = nextFb;
                            terminal.changed = true;
                            currentTerminal.changed = true;
                            swapDialogText = '';
                            this.setState({ swapDialogText }, () => {
                                this.notifyDataChanged();
                            });
                        }
                    });
                } else if (foundTerminal.length === 0) {
                    currentTerminal.terminalAssignedName = nextFb;
                    swapDialogText = '';
                    currentTerminal.changed = true;
                    this.setState({ swapDialogText }, () => {
                        this.notifyDataChanged();
                    });
                } else {
                    console.log("error configuration, too many terminal configured with same function block:", nextFb)
                }

            }
            onOk = () => {
                if (this.swapTerminal) {
                    this.swapTerminal.terminalAssignedName = UNASSIGNED;
                    this.curTerminal.terminalAssignedName = this.fb;
                    this.swapTerminal.changed = true;
                    this.curTerminal.changed = true;
                    this.swapTerminal = null;
                    this.curTerminal = null;
                    this.fb = null;
                    this.setState({ swapDialogText: "" }, () => {
                        this.notifyDataChanged();
                    });
                }
            }
            onNo = () => {
                this.setState({ swapDialogText: "" });
            }

            createSvg = () => {
                const { svgContent } = this.state;
                if (!svgContent) {
                    return (
                        <div>
                            <h1>Terminal Assignment is not supported in the device</h1>
                        </div>
                    )
                }
                return HtmlReactParser(svgContent, {
                    compatMode: true
                })

            }
            convertStyleToCSSClass = (selectedText) => {
                const { svgStyle } = this.state;
                // Remove previous style element if it exists
                const existingStyleElement = document.getElementById('svgStyleElement');
                if (existingStyleElement) {
                    existingStyleElement.remove();
                }
                const styleElement = document.createElement('style');
                styleElement.id = 'svgStyleElement';
                styleElement.innerHTML = svgStyle;
                document.head.appendChild(styleElement);
            };
            reverseScaling = (terminalName, x, y) => {
                const { dropdownScale, textCoordinates } = this.state;
                let newX = x + DROPDOWN_WIDTH;
                let newY = y + 10;
                const isLeft = x < textCoordinates[terminalName][0];

                return isLeft ? `translate(${newX} ${newY}) scale(${1 / dropdownScale}) translate(${-newX} ${-newY})`:
                    `translate(${x} ${newY}) scale(${1 / dropdownScale}) translate(${-x} ${-newY})`


            }

            createForeignObjectDropDown = () => {
                const { width, height, viewBox, pinSlotCoordinates } = this.state;
                const { terminalStore } = this.props;
                const viewBoxArr = viewBox.split(' ').map(Number);
                const pinSlotCoordinatesKeys  = Object.keys(pinSlotCoordinates);
                return (
                    <foreignObject width={width} height={height + 200} x={viewBoxArr[0]} y={viewBoxArr[1]}>
                        <div style={{ position: 'relative', width: '100%', height: '100%'}}>
                            {terminalStore.terminals.map(terminal => {
                                const coordinate = this.getDropDownCoordinate(terminal.terminalName);
                                if (!coordinate) return null;
                                const dropdownHeight = Math.min(Array.isArray(terminal.terminalOptions) ? terminal.terminalOptions.length * DROPDOWN_ITEM_HEIGHT : 0, MAX_DROPDOWN_OPEN_HEIGHT);

                                return (
                                    <div
                                        key={terminal.terminalName}
                                        style={{
                                            position: 'absolute',
                                            left: `${coordinate.x+viewBoxArr[0]*(-1)}px`,
                                            top: `${coordinate.y}px`,
                                            pointerEvents: 'auto',
                                        }}
                                    >
                                        <Dropdown
                                            className="dropdown-mini-hon"
                                            fluid={false}
                                            selection
                                            disabled={terminal.disabled || !!terminal.readOnly}
                                            options={terminal.terminalOptions}
                                            value={terminal.terminalAssignedName || UNASSIGNED}
                                            onChange={(e, data) => this.handleChange(e, data, terminal)}
                                            onFocus={() => this.handleDropdownFocusEnter(terminal.terminalName)}
                                            onBlur={() => this.handleDropdownFocusLeave(terminal.terminalName)}
                                            onMouseEnter={() => this.handleDropdownMouseEnter(terminal.terminalName)}
                                            onMouseLeave={() => this.handleDropdownMouseLeave(terminal.terminalName)}
                                            placeholder="Select Function Block"
                                            style={this.createDropdownStyle(terminal)}
                                            title={terminal.terminalName}
                                            upward={coordinate.y + dropdownHeight > viewBoxArr[3]}
                                            tabIndex={pinSlotCoordinatesKeys.indexOf(terminal.terminalName)+1}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                    </foreignObject>
                )


            }

            drawLineBetweenPinSlotAndDropdown = () => {
               const { pinSlotCoordinates, textCoordinates } = this.state;
               let lefts = [];
               let rights = [];

               Object.entries(pinSlotCoordinates).sort(([, a], [, b]) => b[1] - a[1]) // Sort by Y value in descending order
                   .forEach(([key, [x1, y1]]) => {
                       if (textCoordinates[key]) {
                           const [x2, y2] = textCoordinates[key];
                           if (x1 < x2) {
                               lefts.push({key, pointArray:[x1 + DROPDOWN_WIDTH + 3, y1 + DROPDOWN_LINE_Y_OFFSET, x2 - TEXT_LEFT_LINE_X_OFFSET, y2 - TEXT_LINE_Y_OFFSET]});
                           } else {
                               rights.push({key, pointArray:[x2 + TEXT_RIGHT_LINE_X_OFFSET, y2 - TEXT_LINE_Y_OFFSET, x1-3, y1 + DROPDOWN_LINE_Y_OFFSET]});
                           }
                       }
               });
               const getPolyLines = (points, isLeft) => {
                   let topCnt = 0, bottomCnt = 0;
                   let lines = [];
                   for(const item of points) {
                       const element = item.pointArray;
                       const key = item.key;
                       if(Math.abs(element[3] - element[1]) < LINE_THRESHOLD) {
                           lines.push({terminalName: key, line: `${element[0]},${element[1]}  ${element[2]},${element[3]}`});
                       } else {
                           if(isLeft) {
                               if(element[3] > element[1]) {
                                   let offset = 10;
                                   let x = element[0] + offset + OFFSET*topCnt;
                                   lines.push({terminalName: key, line:`${element[0]},${element[1]} ${x},${element[1]} ${x},${element[3]} ${element[2]},${element[3]}`});
                                   topCnt++;
                              }else{
                                   let offset = 50;
                                   let x = element[0] + offset - OFFSET*bottomCnt;
                                   lines.push({terminalName: key, line:`${element[0]},${element[1]} ${x},${element[1]} ${x},${element[3]} ${element[2]},${element[3]}`});
                                   bottomCnt++;
                              }
                           }else {
                               if(element[3] > element[1]) {
                                     let offset = 50;
                                     let x = element[2] - offset + OFFSET*topCnt;
                                     lines.push({terminalName: key, line:`${element[0]},${element[1]} ${x},${element[1]} ${x},${element[3]} ${element[2]},${element[3]}`});
                                     topCnt++;
                                }else{
                                     let offset = 10;
                                     let x = element[2] - offset - OFFSET*bottomCnt;
                                     lines.push({terminalName: key, line:`${element[0]},${element[1]} ${x},${element[1]} ${x},${element[3]} ${element[2]},${element[3]}`});
                                     bottomCnt++;
                                }
                           }

                       }
                   }
                   return lines;

               };
               let lines = [];
               lines = lines.concat(getPolyLines(lefts, true));
               lines = lines.concat(getPolyLines(rights, false));

               return lines;

            }

            getDropDownCoordinate = (terminalName) => {
                const { pinSlotCoordinates } = this.state;
                if (pinSlotCoordinates[terminalName]) {
                    const x = pinSlotCoordinates[terminalName] ? pinSlotCoordinates[terminalName][0] : 0;
                    const y = pinSlotCoordinates[terminalName] ? pinSlotCoordinates[terminalName][1] : 0;
                    return { x, y };
                }
            }
            render() {
                const { swapDialogText, viewBox, highLightTerminalName } = this.state;
                const { terminalStore } = this.props;
                return (
                    <div className="terminalContainer">
                        <div className="svg" id="svg" ref={this.svgRef}>
                            {viewBox &&
                                <svg id="svgCanvas" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox={viewBox} preserveAspectRatio="xMidYMid meet">
                                    <defs>
                                        <marker id="arrowStart" markerWidth="8" markerHeight="8" refX="0" refY="2" orient="auto" markerUnits="strokeWidth">
                                            <path d="M0,2 L6,4 L6,0 Z" fill="#606060" />
                                        </marker>
                                        <marker id="arrowEnd" markerWidth="4" markerHeight="4" refX="6" refY="2" orient="auto" markerUnits="strokeWidth">
                                            <path d="M0,0 L6,2 L0,4 Z" fill="#606060" />
                                        </marker>
                                        <marker id="highlightArrowStart" markerWidth="8" markerHeight="8" refX="0" refY="2" orient="auto" markerUnits="strokeWidth">
                                            <path d="M0,2 L6,4 L6,0 Z" fill="#5F8FF1" />
                                        </marker>
                                        <marker id="highlightArrowEnd" markerWidth="4" markerHeight="4" refX="6" refY="2" orient="auto" markerUnits="strokeWidth">
                                            <path d="M0,0 L6,2 L0,4 Z" fill="#5F8FF1" />
                                        </marker>
                                    </defs>
                                    <g>
                                        {this.createSvg()}
                                    </g>
                                    <g>
                                        {this.createForeignObjectDropDown()}
                                    </g>
                                    <g>
                                        {
                                            this.drawLineBetweenPinSlotAndDropdown().map((point, index) => (
                                                <g key={index}>
                                                    <polyline
                                                        points={point.line}
                                                        style={{
                                                            fill: 'none',
                                                            stroke: 'transparent',
                                                            strokeWidth: 10,
                                                        }}
                                                        onMouseEnter={() => this.handlePolylineMouseEnter(point.terminalName)}
                                                        onMouseLeave={() => this.handlePolylineMouseLeave(point.terminalName)}
                                                    />
                                                    <polyline
                                                        points={point.line}
                                                        style={{
                                                            fill: 'none',
                                                            stroke: highLightTerminalName === point.terminalName ? '#5F8FF1' : '#606060',
                                                            strokeWidth: 1,
                                                            strokeDasharray: '2,1',
                                                        }}
                                                        markerStart={highLightTerminalName === point.terminalName ? "url(#highlightArrowStart)" : "url(#arrowStart)"}
                                                        markerEnd={highLightTerminalName === point.terminalName ? "url(#highlightArrowEnd)" : "url(#arrowEnd)"}
                                                    />
                                                </g>
                                            ))
                                        }
                                    </g>
                                </svg>
                            }
                            {swapDialogText && <ModalDialog show={!!swapDialogText} title="Unsupported Selection!!!" message={swapDialogText} onOk={this.onOk} onNo={this.onNo} />}
                        </div>
                    </div>
                )
            }
        }
        return TerminalAssignment;
    });