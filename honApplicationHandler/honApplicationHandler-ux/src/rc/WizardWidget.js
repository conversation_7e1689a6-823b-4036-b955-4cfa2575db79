define([
    'bajaux/Widget',
    'react',
    'react-dom',
    'bajaux/mixin/subscriberMixIn',
    'nmodule/honApplicationHandler/rc/honeywelldevice/store/GlobalStore',
    'nmodule/honApplicationHandler/rc/honeywelldevice/profile/WizardHomeProfile',
    'hbs!nmodule/honApplicationHandler/rc/template/home'
],
function (
    Widget,
    React,
    ReactDOM,
    subscriberMixIn,
    GlobalStore,
    HomeProfile,
    template
) {

    let WizardWidget = function () {
        Widget.apply(this, arguments);
        subscriberMixIn(this);
    };

    WizardWidget.prototype = Object.create(Widget.prototype);
    WizardWidget.prototype.constructor = WizardWidget;
    WizardWidget.prototype.doInitialize = function (dom, comp) {
        dom.html(template({}));
    };

    WizardWidget.prototype.doLoad = function (e) {
        const deviceHandle = e.$handle;
        const deviceName = e.$propInParent.$displayName;
        const globalStore = new GlobalStore();

        const dom = document.getElementById('Home');
        ReactDOM.render(React.createElement(HomeProfile, { deviceHandle: deviceHandle, deviceName: deviceName, globalStore: globalStore }), dom);

        let updateProfile = function (prop) {
            console.log("refresh event catched. propName: " + prop.getName());
            if (prop.getName() === "refreshSubscribeObject") {
                ReactDOM.render(React.createElement(HomeProfile, { refresh: prop.$getValue(), deviceHandle: deviceHandle, deviceName: deviceName, globalStore: globalStore }), dom);
            }
        };

        this.getSubscriber().attach("changed", updateProfile);
    };

    WizardWidget.prototype.doDestroy = function (e) {
		const dom = document.getElementById('Home');
		ReactDOM.unmountComponentAtNode(dom);
        const subscriber = this.getSubscriber();
        subscriber.unsubscribeAll();
        subscriber.detach();
    };

    return WizardWidget;
});
