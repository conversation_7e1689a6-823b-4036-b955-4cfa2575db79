define(['baja!'], function (baja) {
  'use strict';

  return {
    invokeRPC:async function invokeRPC(methodName,params) {
      return baja.rpc({
        typeSpec : 'honApplicationHandler:HoneywellDeviceWizardRPC',
        method: methodName,
        args : params
      })
    },
    invokeRPCWithNoParam:async function invokeRPCWithNoParam(methodName) {
      return baja.rpc({
        typeSpec : 'honApplicationHandler:HoneywellDeviceWizardRPC',
        method: methodName
      })
    }
  };
});