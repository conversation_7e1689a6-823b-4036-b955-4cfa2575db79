define([
    'react',
    'react-dom',
    'semantic-ui-react',
    'resize-observer-polyfill',
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard',
], (React, ReactDOM, SemanticReact, ResizeObserver) => {

    'use strict';
    const {
        Input
    } = SemanticReact;

    const defaultStyles = {
        range: {
            width: "100%",
            height: "55px",
        },
        tipsInner: {
            position: "relative",
            width: "100%",
            height: "35px"
        },
        tipsContainer: {
            position: "absolute",
            top: "5px",
            left: "30px",
            height: "20px",
            display: "flex",
            justifyContent: "center"
        },
        tips: {
            backgroundColor: "rgba(255, 255, 255, 1)",
            color: "white",
            padding: "0 5px",
            borderRadius: "3px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center"
        },
        inner: {
            cursor: "pointer",
            margin: "0 10px 0 10px",
            height: "20px",
            position: "relative",
        },
        track: {
            position: "absolute",
            width: "100%",
            height: "4px",
            borderRadius: "4px",
            top: "9px",
            left: "0",
            backgroundColor: "rgba(0,0,0,.05)",
        },
        invertedTrack: {
            backgroundColor: "rgba(255,255,255,.08)",
        },
        trackFill: {
            position: "absolute",
            width: "0",
            height: "4px",
            borderRadius: "4px",
            top: "9px",
            left: "0",
            backgroundColor: "#1b1c1d",
        },
        invertedTrackFill: {
            backgroundColor: "#545454",
        },
        thumb: {
            position: "absolute",
            top: "0",
            left: "0",
            height: "20px",
            width: "20px",
            background: "#fff linear-gradient(transparent, rgba(0, 0, 0, 0.5))",
            background: "#fff -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0.5))",
            background: "#fff -o-linear-gradient(transparent, rgba(0, 0, 0, 0.5))",
            background: "#fff -moz-linear-gradient(transparent, rgba(0, 0, 0, 0.5))",
            borderRadius: "100%",
            backgroundColor: "rgba(255, 255, 255, 1)",
            boxShadow:
              "0 1px 2px 0 rgba(34,36,38,.15),0 0 0 1px rgba(34,36,38,.15) inset",
        },
        roleTextInTips: {
            range: {
                height: "70px"
            },
            tipsInner: {
                height: "50px"
            },
            tipsContainer: {
                height: "35px"
            }
        },
        red: {
            backgroundColor: "#DB2828",
        },
        "inverted-red": {
            backgroundColor: "#FF695E",
        },
        /* Orange */
        orange: {
            backgroundColor: "#F2711C",
        },
        "inverted-orange": {
            backgroundColor: "#FF851B",
        },
        /* Yellow */
        yellow: {
            backgroundColor: "#FBBD08",
        },
        "inverted-yellow": {
            backgroundColor: "#FFE21F",
        },
        /* Olive */
        olive: {
            backgroundColor: "#B5CC18",
        },
        "inverted-olive": {
            backgroundColor: "#D9E778",
        },
        /* Green */
        green: {
            backgroundColor: "#21BA45",
        },
        "inverted-green": {
            backgroundColor: "#2ECC40",
        },
        /* Teal */
        teal: {
            backgroundColor: "#00B5AD",
        },
        "inverted-teal": {
            backgroundColor: "#6DFFFF",
        },
        /* Blue */
        blue: {
            backgroundColor: "#2185D0",
        },
        "inverted-blue": {
            backgroundColor: "#54C8FF",
        },
        /* Violet */
        violet: {
            backgroundColor: "#6435C9",
        },
        "inverted-violet": {
            backgroundColor: "#A291FB",
        },
        /* Purple */
        purple: {
            backgroundColor: "#A333C8",
        },
        "inverted-purple": {
            backgroundColor: "#DC73FF",
        },
        /* Pink */
        pink: {
            backgroundColor: "#E03997",
        },
        "inverted-pink": {
            backgroundColor: "#FF8EDF",
        },
        /* Brown */
        brown: {
            backgroundColor: "#A5673F",
        },
        "inverted-brown": {
            backgroundColor: "#D67C1C",
        },
        /* Grey */
        grey: {
            backgroundColor: "#767676",
        },
        "inverted-grey": {
            backgroundColor: "#DCDDDE",
        },
        /* Black */
        black: {
            backgroundColor: "#1b1c1d",
        },
        "inverted-black": {
            backgroundColor: "#545454",
        },
        /*--------------
            Disabled
        ---------------*/
        disabled: {
            cursor: "not-allowed",
            opacity: ".5",
        },

        /*--------------
            Disabled
        ---------------*/

        disabledTrackFill: {
            backgroundColor: "#ccc",
        },

        /*--------------
            Invalid-Input
        ---------------*/
        invalidInputTrack: {
            cursor: "not-allowed",
            opacity: ".3",
            background: "#ff0000",
        },
        invalidInputTrackFill: {
            opacity: ".0",
        },
    };

    class ReactSlider extends React.Component {
        constructor(props) {
            super(props);
            let value = this.props.value
                ? this.props.value
                : (props.multiple
                    ? props.settings.start.concat()
                    : props.settings.start);
            let inputVisible = props.multiple ? [false, false] : false;
            this.state = {
                value: value,
                inputValue: value,
                inputVisible: inputVisible,
                position: props.multiple ? [] : 0,
                numberOfThumbs: props.multiple ? value.length : 1,
                offset: 10,
                precision: 0,
                mouseDown: false,
                draggingThumbIndex: undefined
            };
            this.determinePosition = this.determinePosition.bind(this);
            this.rangeMouseUp = this.rangeMouseUp.bind(this);
            this.mouseMoveListener = this.mouseMoveListener.bind(this);
            this.resizeListener = this.resizeListener.bind(this);
            this.inputRef = React.createRef();
            this.inputRef0 = React.createRef();
            this.inputRef1 = React.createRef();
            this.spanRef = React.createRef();
            this.labelRef = React.createRef();
            this.spanRef0 = React.createRef();
            this.labelRef0 = React.createRef();
            this.spanRef1 = React.createRef();
            this.labelRef1 = React.createRef();
            this.isWorkbench = window.location.href.toString().indexOf("bajaux") !== -1;
        }

        componentDidMount() {
            this.determinePrecision();
            const value = this.props.value ? this.props.value : this.state.value;
            this.setValuesAndPositions(value, false);
            window.addEventListener("mouseup", this.rangeMouseUp);
            window.addEventListener("mousemove", this.mouseMoveListener);

            this.resizeObserver = new ResizeObserver(this.resizeListener);
            this.resizeObserver.observe(this.slider);

            this.updateInputWidth();
        }

        UNSAFE_componentWillReceiveProps(nextProps) {
            const isValueUnset = nextProps.value === null || nextProps.value === undefined;
            if (!isValueUnset && this.isDifferentValue(nextProps.value, this.state.value)) {
                this.setValuesAndPositions(nextProps.value, false);
            }
        }

        componentWillUnmount() {
            this.resizeObserver.unobserve(this.slider);

            this.slider = undefined;
            this.inner = undefined;
            this.innerLeft = undefined;
            this.innerRight = undefined;
            window.removeEventListener("mouseup", this.rangeMouseUp);
            window.removeEventListener("mousemove", this.mouseMoveListener);
        }

        setValuesAndPositions(value, triggeredByUser) {
            this.updatePositions(value);
            this.setValue(value, triggeredByUser);
        }

        updatePositions(value) {
            if (this.props.multiple) {
                const positions = this.state.position.concat();
                value.forEach((val, i) => {
                    positions[i] = this.determinePosition(val);
                });
                this.setState({
                    position: positions,
                });
            } else {
                this.setState({
                    position: this.determinePosition(value),
                });
            }
        }

        isDifferentValue(a, b) {
            if(Array.isArray(a) && Array.isArray(b)) {
                let different = false;
                a.some((val, i) => {
                    if (val !== b[i]) {
                        different = true;
                        return true;
                    }
                });
                return different;
            }

            return a !== b;
        }

        determinePosition(value) {
            const trackLeft = ReactDOM.findDOMNode(this.track).getBoundingClientRect().left;
            const innerLeft = ReactDOM.findDOMNode(this.inner).getBoundingClientRect().left;
            const ratio = (value - this.props.settings.min) / (this.props.settings.max - this.props.settings.min);
            const position = Math.round(ratio * this.inner.offsetWidth) + trackLeft - innerLeft - this.state.offset;
            return position;
        }

        determinePrecision() {
            let split = String(this.props.settings.step).split(".");
            let decimalPlaces;
            if (split.length === 2) {
                decimalPlaces = split[1].length;
            } else {
                decimalPlaces = 0;
            }
            this.setState({
                precision: Math.pow(10, decimalPlaces),
            });
        }

        determineValue(startPos, endPos, currentPos) {
            let ratio = (currentPos - startPos) / (endPos - startPos);
            let range = this.props.settings.max - this.props.settings.min;
            let difference = Math.round((ratio * range) / this.props.settings.step) * this.props.settings.step;
            // Use precision to avoid ugly Javascript floating point rounding issues
            // (like 35 * .01 = 0.35000000000000003)
            difference = Math.round(difference * this.state.precision) / this.state.precision;
            let result = difference + this.props.settings.min;
            return this.formatValue(result);
        }

        determineThumb(position) {
            if (!this.props.multiple) {
                return 0;
            }
            if (position <= this.state.position[0]) {
                return 0;
            }
            if (position >= this.state.position[this.state.numberOfThumbs - 1]) {
                return this.state.numberOfThumbs - 1;
            }
            let index = 0;

            for (let i = 0; i < this.state.numberOfThumbs - 1; i++) {
                if (position >= this.state.position[i] && position < this.state.position[i + 1]) {
                    const distanceToSecond = Math.abs(
                        position - this.state.position[i + 1]
                    );
                    const distanceToFirst = Math.abs(position - this.state.position[i]);
                    if (distanceToSecond <= distanceToFirst) {
                        return i + 1;
                    } else {
                        return i;
                    }
                }
            }
            return index;
        }

        setValue(value, triggeredByUser, thumbIndex) {
            if (typeof triggeredByUser === "undefined") {
                triggeredByUser = true;
            }
            if(this.props.multiple && thumbIndex === undefined) {
                const newValue = value.concat();
                this.setState({
                    value: newValue,
                    inputValue: newValue,
                });
            }
            else {
                const currentValue = this.props.multiple ? this.state.value[thumbIndex] : this.state.value;
                if (currentValue !== value) {
                    let newValue = [];
                    if (this.props.multiple) {
                        newValue = this.state.value.concat();
                        newValue[thumbIndex] = value;
                        this.setState({
                            value: newValue,
                            inputValue: newValue,
                        });
                    } else {
                        this.setState({
                            value: value,
                            inputValue: value,
                        });
                    }
                }
            }
            this.updateInputWidth();
        }

        setValuePosition(value, triggeredByUser, thumbIndex) {
            if (this.props.multiple) {
                const positions = this.state.position.concat();
                positions[thumbIndex] = this.determinePosition(value);
                this.setState({
                    position: positions,
                });
            } else {
                this.setState({
                    position: this.determinePosition(value),
                });
            }
            this.setValue(value, triggeredByUser, thumbIndex);
        }

        setPosition(position, thumbIndex) {
            if (this.props.multiple) {
                const newPosition = this.state.position.concat();
                newPosition[thumbIndex] = position;
                this.setState({
                    position: newPosition,
                });
            } else {
                this.setState({
                    position: position,
                });
            }
        }

        rangeMouseDown(isTouch, e) {
            e.stopPropagation();
            if (!this.props.disabled) {
                if (!isTouch) {
                    e.preventDefault();
                }

                this.setState({
                    mouseDown: true,
                });
                let innerBoundingClientRect = ReactDOM.findDOMNode(
                    this.inner
                ).getBoundingClientRect();
                this.innerLeft = innerBoundingClientRect.left;
                this.innerRight = this.innerLeft + this.inner.offsetWidth;
                this.rangeMouse(isTouch, e);
            }
        }

        rangeMouse(isTouch, e) {
            let pageX;
            let event = isTouch ? e.touches[0] : e;
            if (event.pageX) {
                pageX = event.pageX;
            } else {
                console.log("PageX undefined");
            }

            if (pageX >= this.innerLeft && pageX <= this.innerRight) {
                let value = this.determineValue(this.innerLeft, this.innerRight, pageX);
                const position = pageX - this.innerLeft - this.state.offset;
                let thumbIndex = this.props.multiple ? (
                    this.state.draggingThumbIndex === undefined ? this.determineThumb(position) : this.state.draggingThumbIndex
                ) : undefined;

                const newValuePosition = this.updateValuePositionWithDeadband(thumbIndex, value, position, this.props.deadband === undefined ? 0 : this.props.deadband);
                if (newValuePosition.value >= this.props.settings.min && newValuePosition.value <= this.props.settings.max) {
                    if(this.state.draggingThumbIndex === undefined) {
                        this.setState({draggingThumbIndex: thumbIndex});
                    }
                    if (this.props.discrete) {
                        this.setValuePosition(newValuePosition.value, true, thumbIndex);
                    } else {
                        this.setPosition(newValuePosition.position, thumbIndex);
                        this.setValue(newValuePosition.value, true, thumbIndex);
                    }
                }
            }
        }

        updateValuePositionWithDeadband(draggingThumbIndex, value, position, deadband) {
            if(!this.props.multiple) {
                return {value, position};
            }

            const refThumbIndex = draggingThumbIndex === 0 ? 1 : 0;
            const refValue = this.state.value[refThumbIndex];

            if(draggingThumbIndex < refThumbIndex && value > (refValue - deadband)) {
                return {value: this.formatValue(refValue - deadband), position: this.determinePosition(refValue - deadband)};
            }

            if(draggingThumbIndex > refThumbIndex && value < (refValue + deadband)) {
                return {value: this.formatValue(refValue + deadband), position: this.determinePosition(refValue + deadband)};
            }

            return {value, position};
        }
        formatValue(value){
            let split = String(this.props.settings.step).split(".");
            let decimalPlaces;
            if (split.length === 2) {
                decimalPlaces = split[1].length;
            } else {
                decimalPlaces = 0;
            }
            return parseFloat(value.toFixed(decimalPlaces));
        }


        rangeMouseMove(isTouch, e) {
            e.stopPropagation();
            if (!isTouch) {
                e.preventDefault();
            }
            if (this.state.mouseDown) {
                this.rangeMouse(isTouch, e);
            }
        }

        rangeMouseUp() {
            if (this.state.mouseDown) {
                this.setState({
                    mouseDown: false,
                    draggingThumbIndex: undefined
                }, () => {
                    this.updatePositions(this.state.value);
                    if (this.props.settings.onChange) {
                        this.props.settings.onChange(this.state.value, {
                            triggeredByUser: true
                        });
                    }
                });
            }
        }

        mouseMoveListener(e) {
            if(e.mozInputSource !== undefined && e.mozInputSource === 5) {
                this.state.mouseDown && this.rangeMouseMove(true, e);
            }
            else {
                this.state.mouseDown && this.rangeMouseMove(false, e);
            }
        }

        resizeListener(entries) {
            for (const entry of entries) {
                if (entry.target === this.slider) {
                    const {value} = this.state;
                    this.updatePositions(value);
                }
            }
        }

        // Method to update input width based on the content
        updateInputWidth = () => {
            const unit = this.props.unit || '';
            if(this.props.multiple) {
                if (this.spanRef0.current) {
                    const newWidth = this.spanRef0.current.offsetWidth + 28 + unit.length + 6; // Add padding to the width
                    const newLabelLeft = this.spanRef0.current.offsetWidth + 5 + unit.length + 5; // Add padding to the width
                    if (this.inputRef0.current && this.inputRef0.current.inputRef && this.inputRef0.current.inputRef.current) {
                        this.inputRef0.current.inputRef.current.style.width = `${newWidth}px`;
                    }
                    if (this.labelRef0.current) {
                        this.labelRef0.current.style.left = `${newLabelLeft}px`;
                    }
                } 
                if(this.spanRef1.current) {
                    const newWidth = this.spanRef1.current.offsetWidth + 28 + unit.length + 6; // Add padding to the width
                    const newLabelLeft = this.spanRef1.current.offsetWidth + 5 + unit.length + 5; // Add padding to the width
                    if (this.inputRef1.current && this.inputRef1.current.inputRef && this.inputRef1.current.inputRef.current) {
                        this.inputRef1.current.inputRef.current.style.width = `${newWidth}px`;
                    }
                    if (this.labelRef1.current) {
                        this.labelRef1.current.style.left = `${newLabelLeft}px`;
                    }
                }

            } else {
                if (this.spanRef.current) {
                    const newWidth = this.spanRef.current.offsetWidth + 28 + unit.length + 6; // Add padding to the width
                    const newLabelLeft = this.spanRef.current.offsetWidth + 5 + unit.length + 5; // Add padding to the width
                    if (this.inputRef.current && this.inputRef.current.inputRef && this.inputRef.current.inputRef.current) {
                        this.inputRef.current.inputRef.current.style.width = `${newWidth}px`;
                    }
                    if (this.labelRef.current) {
                        this.labelRef.current.style.left = `${newLabelLeft}px`;
                    }
                }
            }
        }

        inputOnchangeHandler = (e, data) => {
            if(this.props.multiple) {
                let index = this.state.inputVisible[0] ? 0 : 1;
                let inputValue = [...this.state.inputValue]; 
                if (index === 0 && data.value >= inputValue[1]) {
                    data.value = inputValue[1]-1;
                }  else if (index === 1 && data.value > this.props.settings.max) {
                    data.value = this.props.settings.max
                } 
                inputValue[index] = data.value;
                this.setState({ inputValue }, () => this.updateInputWidth());
            } else {
                if (data.value > this.props.settings.max) {
                    data.value = this.props.settings.max;
                }
                this.setState({ inputValue: data.value }, () => this.updateInputWidth());
            }            
        }

        inputOnBlurHandler = (e) => {
            let inputValue = this.props.multiple ? [...this.state.inputValue] : this.state.inputValue;
            let value = this.props.multiple ? [...this.state.value] : this.state.value;
            if(this.props.multiple) {    
                if (this.state.inputVisible[1] && inputValue[1] <= inputValue[0]) {
                    inputValue[1] = this.state.inputValue[0]+1;
                } else if (this.state.inputVisible[0] && inputValue[0] < this.props.settings.min) {
                    inputValue[0] = this.props.settings.min;
                }    
                let inputVisible = [...this.state.inputVisible]; 
                inputVisible[0] = false;
                inputVisible[1] = false;
                this.setState({ inputVisible });                
            } else {
                if (inputValue < this.props.settings.min) {
                    inputValue = this.props.settings.min;
                }
                this.setState({inputVisible: false });
            }            
            if((!this.props.multiple && inputValue !== '' && value.toString() !== inputValue.toString()) || (this.props.multiple && (inputValue[0] !== '' && inputValue[1] !== '') && 
            (value[0].toString() !== inputValue[0].toString() || value[1].toString() !== inputValue[1].toString()))) { 
                this.setValue(inputValue);
                this.updatePositions(inputValue);
                this.props.settings.onChange(inputValue, {
                    triggeredByUser: true
                });
            }
        }

        render() {           
            let labelLeftMargin, labelLeftMargin0, labelLeftMargin1, inputValueCharLength, inputValueCharLength0, inputValueCharLength1; 
            if (this.props.multiple) {
                inputValueCharLength0 = this.state.inputValue[0] !== null || this.state.inputValue[0] !== undefined ? this.state.inputValue[0].toString().length : 0;
                labelLeftMargin0 = inputValueCharLength0 > 0 ? (inputValueCharLength0*6)+'px' : '14px';
                inputValueCharLength1 = this.state.inputValue[1] !== null || this.state.inputValue[1] !== undefined ? this.state.inputValue[1].toString().length : 0;
                labelLeftMargin1 = inputValueCharLength1 > 0 ? (inputValueCharLength1*6)+'px' : '14px';
            } else {
                inputValueCharLength = this.state.inputValue !== null || this.state.inputValue !== undefined ? this.state.inputValue.toString().length : 0;
                labelLeftMargin = inputValueCharLength > 0 ? (inputValueCharLength*10)+4+'px' : '14px';
            }
            return (
                <div
                    style={{width: this.props.style.width}}
                    ref={(slider) => {
                        this.slider = slider;
                    }}
                >
                    <div
                        style={Object.assign({},
                            defaultStyles.range,
                            this.props.roleText ? defaultStyles.roleTextInTips.range : {},
                            this.props.disabled ? defaultStyles.disabled : {},
                            this.props.style ? this.props.style : {}
                        )}
                    >
                        <div style={Object.assign({},
                                defaultStyles.tipsInner,
                                this.props.roleText ? defaultStyles.roleTextInTips.tipsInner : {}
                            )}
                        >
                            {this.props.multiple ? (
                                this.state.position.map((pos, i) => (
                                    <div
                                        key={i}
                                        style={Object.assign({},
                                            defaultStyles.tipsContainer,
                                            this.props.roleText ? defaultStyles.roleTextInTips.tipsContainer : {},
                                            {top: this.state.inputVisible[i] ? '15px' : '10px', 
                                            left: (() => {
                                                const [pos0, pos1] = this.state.position;
                                                const isOverlapping = pos1 - pos0 < 54;
                                                const isMostOverlapping = pos1 - pos0 < 24;
                                                const spanWidth0 = this.spanRef0.current ? this.spanRef0.current.offsetWidth : 0;
                                                const spanWidth1 = this.spanRef1.current ? this.spanRef1.current.offsetWidth : 0;
                                                const pos = this.state.position[i];
                                                const isInputVisible = this.state.inputVisible[i];
                                            
                                                if (isOverlapping) {
                                                  if (i === 0) {
                                                    if (isMostOverlapping) {
                                                        return (isInputVisible ? pos1 - 20 - spanWidth0 : pos1 - 14 - spanWidth0) + "px";
                                                    } else {
                                                        return (isInputVisible ? pos1 - 40 - spanWidth0 : pos1 - 34 - spanWidth0) + "px";
                                                    }                                                        
                                                  } else {
                                                    if (isMostOverlapping) {
                                                        return (isInputVisible ? pos + 19 : pos + 40 - spanWidth1) + "px";
                                                    } else {
                                                        return (isInputVisible ? pos : pos + 20 - spanWidth1) + "px";
                                                    }                                                       
                                                  }
                                                } else {
                                                  return (isInputVisible ? pos - 8 : pos-5) + "px";
                                                }
                                              })()}
                                        )}
                                    >
                                        <div          
                                            className={this.state.inputVisible[i] ? 'hide' : ''}                                   
                                            style={Object.assign({},
                                            defaultStyles.tips,
                                            {backgroundColor: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor : defaultStyles.thumb.backgroundColor},
                                            this.props.style
                                                ? this.props.style.tips
                                                    ? this.props.style.tips
                                                    : {}
                                                : {}
                                        )}>
                                            <div       
                                                tabIndex={this.props.index}
                                                onFocus={e => { 
                                                        e.preventDefault();
                                                        this.updateInputWidth();
                                                        let inputVisible = this.state.inputVisible; 
                                                        inputVisible[i] = true;
                                                        this.setState({ inputVisible }, () => i === 0 ? this.inputRef0.current.focus() : this.inputRef1.current.focus());
                                                }}                                          
                                                onClick={() => {
                                                    this.updateInputWidth();
                                                    let inputVisible = this.state.inputVisible; 
                                                    inputVisible[i] = true;
                                                    this.setState({ inputVisible }, () => i === 0 ? this.inputRef0.current.focus() : this.inputRef1.current.focus());
                                                }} 
                                                style={{textAlign: "center", lineHeight: "15px"}}>
                                                <span>{this.state.value[i]}</span>
                                                {this.props.unit && <span>{this.props.unit}</span>}
                                            </div>
                                            {this.props.roleText && this.props.roleText[i] &&
                                                <div style={{textAlign: "center", lineHeight: "10px"}}>
                                                    {this.props.roleText[i]}
                                                </div>
                                            }
                                        </div>
                                        <div className={this.state.inputVisible[i] ? '' : 'hide'}>
                                            <Input 
                                                tabIndex={this.props.index}
                                                min={this.props.settings.min}
                                                max={this.props.settings.max}
                                                ref={i === 0 ? this.inputRef0 : this.inputRef1}
                                                style={{width: 'auto', minWidth: '38px', backgroundColor: this.props.style.thumb.backgroundColor ? 
                                                    this.props.style.thumb.backgroundColor : defaultStyles.thumb.backgroundColor, 
                                                    borderRadius: '4px', color: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor === 'white' ? 'black' : 'white' : 
                                                    defaultStyles.thumb.backgroundColor === 'white' ? 'black' : 'white'}}
                                                className={`slider-input`}
                                                type='number' 
                                                name={'rangeValue'+i}
                                                id={'rangeValue'+i}
                                                value={this.state.inputValue[i]} 
                                                onChange={this.inputOnchangeHandler}
                                                onBlur={this.inputOnBlurHandler}
                                            />
                                            <label ref={i===0 ? this.labelRef0 : this.labelRef1} style={{ color: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor === 'white' ? 'black' : 'white' : 
                                            defaultStyles.thumb.backgroundColor === 'white' ? 'black' : 'white', position: 'absolute', fontFamily: '"Source Sans Pro", SourceSansPro, sans-serif', left: i=== 0 ? labelLeftMargin0 : labelLeftMargin1, top: this.isWorkbench ? '3px' : '0.6px' }}>{this.props.unit}</label>
                                        </div>
                                        <span style={{ visibility: 'hidden' }} ref={this.spanRef0}>{this.state.inputValue[0]}</span>
                                        <span style={{ visibility: 'hidden' }} ref={this.spanRef1}>{this.state.inputValue[1]}</span>
                                    </div>
                                ))
                            ) : (
                                <div
                                    style={Object.assign({},
                                        defaultStyles.tipsContainer,
                                        {left: (this.state.inputVisible && inputValueCharLength > 1) ? (this.state.position-6) - (inputValueCharLength*1.5) + "px" : (this.state.position+3) + "px"}
                                    )}
                                >
                                    <div 
                                        className={this.state.inputVisible ? 'hide' : ''}
                                        tabIndex={this.props.index}
                                        onFocus={e => { 
                                                e.preventDefault();
                                                this.updateInputWidth(); 
                                                this.setState({ inputVisible: true }, () => this.inputRef.current.focus());
                                        }}
                                        onClick={() => { this.updateInputWidth(); this.setState({ inputVisible: true }, () => this.inputRef.current.focus())}}
                                        style={Object.assign({},
                                        defaultStyles.tips,
                                        {backgroundColor: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor : defaultStyles.thumb.backgroundColor},
                                        this.props.style
                                            ? this.props.style.tips
                                                ? this.props.style.tips
                                                : {}
                                            : {}
                                    )}>
                                        <div style={{textAlign: "center", lineHeight: "15px"}}>
                                            <span>{this.state.value}</span>
                                            {this.props.unit && <span>{this.props.unit}</span>}
                                        </div>
                                    </div>
                                    <div className={this.state.inputVisible ? '' : 'hide'}>
                                        <Input 
                                            tabIndex={this.props.index}
                                            min={this.props.settings.min}
                                            max={this.props.settings.max}
                                            ref={this.inputRef}
                                            style={{width: 'auto', minWidth: '38px', backgroundColor: this.props.style.thumb.backgroundColor ? 
                                                this.props.style.thumb.backgroundColor : defaultStyles.thumb.backgroundColor, 
                                                borderRadius: '4px', color: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor === 'white' ? 'black' : 'white' : 
                                                defaultStyles.thumb.backgroundColor === 'white' ? 'black' : 'white'}}
                                            className={`slider-input`}
                                            type='number' 
                                            name={'rangeValue'}
                                            id={'rangeValue'}
                                            value={this.state.inputValue} 
                                            onChange={this.inputOnchangeHandler}
                                            onBlur={this.inputOnBlurHandler}
                                        />
                                        <label ref={this.labelRef} style={{ color: this.props.style.thumb.backgroundColor ? this.props.style.thumb.backgroundColor === 'white' ? 'black' : 'white' : 
                                        defaultStyles.thumb.backgroundColor === 'white' ? 'black' : 'white', position: 'absolute', 
                                        fontFamily: '"Source Sans Pro", SourceSansPro, sans-serif', left: labelLeftMargin, top: this.isWorkbench ? '3px' : '0.6px' }}>{this.props.unit}</label>                                        
                                    </div>
                                    <span style={{ visibility: 'hidden' }} ref={this.spanRef}>{this.state.inputValue}</span>
                                </div>
                            )}
                        </div>
                        <div
                            className="semantic_ui_range_inner"
                            ref={(inner) => {
                                this.inner = inner;
                            }}
                            style={Object.assign({},
                                defaultStyles.inner,
                                this.props.style
                                    ? this.props.style.inner
                                        ? this.props.style.inner
                                        : {}
                                    : {}
                            )}
                            onMouseDown={(event) => this.rangeMouseDown(false, event)}
                            onMouseMove={(event) => this.rangeMouseMove(false, event)}
                            onMouseUp={(event) => this.rangeMouseUp(false, event)}
                            onTouchEnd={(event) => this.rangeMouseUp(true, event)}
                            onTouchMove={(event) => this.rangeMouseMove(true, event)}
                            onTouchStart={(event) => this.rangeMouseDown(true, event)}
                        >
                            <div
                                ref={(track) => {
                                    this.track = track;
                                }}
                                style={Object.assign({},
                                    defaultStyles.track,
                                    this.props.inverted ? defaultStyles.invertedTrack : {},
                                    this.props.style
                                        ? this.props.style.track
                                            ? this.props.style.track
                                            : {}
                                        : {}
                                )}
                            />
                            {this.props.fillColor && <div
                                ref={(trackFill) => {
                                    this.trackFill = trackFill;
                                }}
                                style={Object.assign({},
                                    defaultStyles.trackFill,
                                    this.props.inverted ? defaultStyles.invertedTrackFill : {},
                                    defaultStyles[
                                        this.props.inverted
                                        ? "inverted-" + this.props.fillColor
                                        : this.props.fillColor
                                    ] ? defaultStyles[
                                        this.props.inverted
                                        ? "inverted-" + this.props.fillColor
                                        : this.props.fillColor
                                    ] : {
                                        backgroundColor: this.props.fillColor
                                    },
                                    this.props.style
                                        ? this.props.style.trackFill
                                            ? this.props.style.trackFill
                                            : {}
                                        : {},
                                    this.props.disabled ? defaultStyles.disabledTrackFill : {},
                                    this.props.style
                                        ? this.props.style.disabledTrackFill
                                            ? this.props.style.disabledTrackFill
                                            : {}
                                        : {},
                                    { width: this.state.position + this.state.offset + "px" },
                                    this.props.multiple && this.state.position.length > 0
                                        ? {
                                            left: this.state.position[0],
                                            width: this.state.position[this.state.numberOfThumbs - 1] - this.state.position[0],
                                        }
                                        : {}
                                )}
                            />}

                            {this.props.multiple ? (
                                this.state.position.map((pos, i) => (
                                    <div
                                        key={i}
                                        style={Object.assign({},
                                            defaultStyles.thumb,
                                            this.props.style
                                                ? this.props.style.thumb
                                                    ? this.props.style.thumb
                                                    : {}
                                                : {},
                                            { left: pos + "px" }
                                        )}
                                    />
                                ))
                            ) : (
                                <div
                                    style={Object.assign({},
                                        defaultStyles.thumb,
                                        this.props.style
                                            ? this.props.style.thumb
                                                ? this.props.style.thumb
                                                : {}
                                            : {},
                                        { left: this.state.position + "px" }
                                    )}
                                />
                            )}
                        </div>
                    </div>
                </div>
            );
        }
    }

    return ReactSlider;
});