/* Add this to your CSS file */

@font-face {
    font-family: 'Honeywell Sans Web';
    src: url('semantic/themes/default/assets/fonts/HoneywellCondWeb-Book.eot');
    src: url('semantic/themes/default/assets/fonts/HoneywellCondWeb-Book.eot?#iefix') format('embedded-opentype'),
         url('semantic/themes/default/assets/fonts/HoneywellSans-Medium.otf') format('opentype'),
         url('semantic/themes/default/assets/fonts/HoneywellSans-Light.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.honeywell-device-wizard .terminalContainer{
    display: flex;
    flex-direction: column;
    height: 100%;
    margin-left:-20px;
}

.honeywell-device-wizard .terminalContainer .dropdown-mini-hon.ui.selection.dropdown {
    width: 135px;
    height: 25px;
    padding-bottom: 0.05em;
    padding-left: 0.4em;
    padding-right: 0.5em;
    min-width: 0px;
    min-height: 0px;
    border: 1px solid #606060;
    border-radius: 0.25em;
}
.honeywell-device-wizard .terminalContainer .dropdown-mini-hon.ui.selection.dropdown .divider.text {
    font-size: 11px;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-weight: 700;
}
.honeywell-device-wizard .terminalContainer .dropdown-mini-hon.ui.selection.dropdown .dropdown.icon {
    padding: 4px;
}

.honeywell-device-wizard .zoom-buttons {
    z-index: 1000;
}
.honeywell-device-wizard .zoom-buttons button {
    width: 30px;
    height: 30px;
}
.honeywell-device-wizard .svg {
    display: block;
    margin: 0 auto;
    width: 98%;
    height: 100%;
}
.honeywell-device-wizard .svg svg{
    width: 100%;
    height: 100%;
}

.honeywell-device-wizard .terminalContainer .dropdown-mini-hon.ui.selection.dropdown .menu.transition>.item {
    font-size: 12px;
    font-weight: 500 !important;
    padding: 0.5em !important;
    height: 25px;
    align-content: center;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.honeywell-device-wizard .terminalContainer .visible.menu.transition {
    max-height: 160px;
    min-height: 120px;
    overflow-y: auto;
    border: 1.5px solid #5F8FF1 !important;
}
