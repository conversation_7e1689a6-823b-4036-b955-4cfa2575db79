/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ux;

import com.tridium.history.ux.BHistoryJsBuild;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraSingleton;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.web.js.BJsBuild;
import javax.baja.webeditors.ux.BWebEditorsJsBuild;

@NiagaraType
@NiagaraSingleton
public class BHoneywellDeviceWizardJsBuild extends BJsBuild {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ux.BHoneywellDeviceWizardJsBuild(3933907701)1.0$ @*/
/* Generated Mon Sep 23 15:20:18 CST 2024 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */
  
  public static final BHoneywellDeviceWizardJsBuild INSTANCE = new BHoneywellDeviceWizardJsBuild();

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHoneywellDeviceWizardJsBuild.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private BHoneywellDeviceWizardJsBuild() {
        super("honApplicationHandler",
                new BOrd[]{BOrd.make("module://honApplicationHandler/rc/honApplicationHandler.built.min.js")},
                new Type[]{BWebEditorsJsBuild.TYPE, BHistoryJsBuild.TYPE});
    }
}
