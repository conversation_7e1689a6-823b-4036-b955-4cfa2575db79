/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ux;

import com.tridium.web.BICollectionSupport;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BSingleton;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.web.BIFormFactorMax;
import javax.baja.web.BIOffline;
import javax.baja.web.js.BIJavaScript;
import javax.baja.web.js.JsInfo;

@NiagaraType(agent = @AgentOn(types = {"honApplicationHandler:IHoneywellConfigurableDevice"}))
public class BHoneywellDeviceWizard extends BSingleton implements BIJavaScript, BIFormFactorMax, BIOffline, BICollectionSupport {

    public static final BHoneywellDeviceWizard INSTANCE = new BHoneywellDeviceWizard();

/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ux.BHoneywellDeviceWizard(2478517633)1.0$ @*/
/* Generated Mon Sep 23 15:17:28 CST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHoneywellDeviceWizard.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final JsInfo jsInfo= JsInfo.make(BOrd.make("module://honApplicationHandler/rc/WizardWidget.js"), BHoneywellDeviceWizardJsBuild.TYPE);

    @Override
    public JsInfo getJsInfo(Context context) {
        return jsInfo;
    }
}
