/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ux;

import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.baja.file.BDataFile;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BComponent;
import javax.baja.sys.Property;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class TerminalAssignmentUtil {
    private static final Map<String, String> PIN_SLOT_IN_SVG_TEXT = new HashMap<>();

    private static final Map<String, List<String>> PIN_SLOT_SVG_TEXT_MAP = new HashMap<>();

    static {
        PIN_SLOT_IN_SVG_TEXT.put("IO", "IO");
        PIN_SLOT_IN_SVG_TEXT.put("DO", "DO");
        PIN_SLOT_IN_SVG_TEXT.put("NO", "NO");
        PIN_SLOT_IN_SVG_TEXT.put("RO", "RO");
        PIN_SLOT_IN_SVG_TEXT.put("SR", "SR");
        PIN_SLOT_IN_SVG_TEXT.put("AO", "AO");
        PIN_SLOT_IN_SVG_TEXT.put("TO", "TO");
        PIN_SLOT_IN_SVG_TEXT.put("AO-", "AO-");
        PIN_SLOT_IN_SVG_TEXT.put("DO-", "DO-");
        PIN_SLOT_IN_SVG_TEXT.put("UI-", "UI-");


        PIN_SLOT_SVG_TEXT_MAP.put("UIO", Arrays.asList("IO"));
        PIN_SLOT_SVG_TEXT_MAP.put("CO", Arrays.asList("DO"));
        PIN_SLOT_SVG_TEXT_MAP.put("RO", Arrays.asList("NO", "RO", "DO-"));
        PIN_SLOT_SVG_TEXT_MAP.put("SSR", Arrays.asList("SR"));
        PIN_SLOT_SVG_TEXT_MAP.put("AO", Arrays.asList("AO", "AO-"));
        PIN_SLOT_SVG_TEXT_MAP.put("TO", Arrays.asList("TO"));
        PIN_SLOT_SVG_TEXT_MAP.put("UI", Arrays.asList("UI", "UI-"));


    }
    private TerminalAssignmentUtil(){

    }

    /**
     * parse css style and SVG content
     * @param filePath, file path
     * @param terminalComp, terminal component
     * @param textCoordinateMap, text of pin slot coordinate
     * @param viewBox, viewbox
     * @return css style and SVG content
     */
    public static  String[] extractStyleAndContent(String filePath, BComponent terminalComp, Map<String, List<Double>> textCoordinateMap, List<Double> viewBox) {
        try {
            BOrd xmlOrd = BOrd.make(filePath);
            BDataFile dataFile = (BDataFile) xmlOrd.get();
            if (null == dataFile) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.terminalAssignment.svgNotFound", filePath));
                return null;
            }
            InputStream inputStream = dataFile.getInputStream();
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder documentBuilder = factory.newDocumentBuilder();
            Document document = documentBuilder.parse(inputStream);

            // Extract style content
            NodeList styleNodes = document.getElementsByTagName("style");
            StringBuilder styleContent = new StringBuilder();
            for (int i = 0; i < styleNodes.getLength(); i++) {
                styleContent.append(styleNodes.item(i).getTextContent());
            }

            // Remove style nodes from the document
            for (int i = 0; i < styleNodes.getLength(); i++) {
                Node styleNode = styleNodes.item(i);
                styleNode.getParentNode().removeChild(styleNode);
            }
            viewBox.addAll(getViewBox(document));
            List<TerminalAssignment> terminalAssignments = parseTerminalAssignments(terminalComp);
            // Traverse the document to find text nodes with IO1 to IO16
            NodeList textNodes = document.getElementsByTagName("text");

            for (int i = 0; i < textNodes.getLength(); i++) {
                Node textNode = textNodes.item(i);
                String textContent = getTextContent(textNode);
                Pattern pattern = Pattern.compile("^(\\D+)(\\d+)$");
                Matcher matcher = pattern.matcher(textContent);
                if (matcher.matches()) {
                    String nonDigitPart = matcher.group(1);
                    String numPart = matcher.group(2);
                    if (PIN_SLOT_IN_SVG_TEXT.containsKey(nonDigitPart)) {
                        for (TerminalAssignment terminalAssignment : terminalAssignments) {
                            String pinSlotName = terminalAssignment.getName().split("-")[0];
                            List<String> matchedText = PIN_SLOT_SVG_TEXT_MAP.get(pinSlotName);
                            if(matchedText.contains(nonDigitPart)){
                                String newPinText = pinSlotName + "-" + numPart;
                                List<Double> textCoordinate = getTextCoordinate(textNode);
                                //List<Double> pinSlotCoordinates = getPinSlotCoordinates(viewBox, textCoordinate);
                                textCoordinateMap.put(newPinText, textCoordinate);
                            }
                        }
                    }
                }
            }

            // Convert the remaining document to string
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));
            String svgWithoutStyle = writer.getBuffer().toString();
            String[] lines = svgWithoutStyle.split("\n");
            StringBuilder result = new StringBuilder();

            for (int i = 2; i < lines.length - 1; i++) {
                result.append(lines[i]).append("\n");
            }

            return new String[]{styleContent.toString(), convertSelfClosingTags(result.toString())};
        }catch (ParserConfigurationException | SAXException | IOException | TransformerException e) {
            HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.terminalAssignment.parseSvgError"), e);
            return null;
        }
    }

    /**
     * Convert self-closing tags to non-self-closing tags
     * As html-react-parser can not parse self-closing tag correctly, so we need to convert self-closing tags to non-self-closing tags
     * @param xmlContent, xml content
     * @return converted xml content
     */
    private static String convertSelfClosingTags(String xmlContent) {
        // Regular expression to find self-closing tags
        String selfClosingTagPattern = "<(\\w+)([^>]*)\\/>";
        Pattern pattern = Pattern.compile(selfClosingTagPattern);
        Matcher matcher = pattern.matcher(xmlContent);

        // Replace self-closing tags with non-self-closing tags
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String tagName = matcher.group(1);
            String tagAttributes = matcher.group(2);
            String replacement = "<" + tagName + tagAttributes + "></" + tagName + ">";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * Get the viewBox attribute from the SVG document
     * @param document, xml document
     * @return view box list
     */
    private static List<Double> getViewBox(Document document) {
        // Get the viewBox attribute
        String viewBox = document.getDocumentElement().getAttribute("viewBox");
        List<Double> viewBoxValues = new ArrayList<>();
        if (!viewBox.isEmpty()) {
            // Split the viewBox attribute into individual components
            String[] values = viewBox.split(" ");
            for (String value : values) {
                // Convert each component to an integer and add to the list
                viewBoxValues.add(Double.parseDouble(value));
            }
        }
        return viewBoxValues;

    }

    /**
     * get text content, support text node with tspan
     * @param textNode, text node
     * @return text
     */
    private static String getTextContent(Node textNode) {
        StringBuilder textContent = new StringBuilder();
        NodeList childNodes = textNode.getChildNodes();
        if (childNodes.getLength() == 0) {
            textContent.append(textNode.getTextContent());
        } else {
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node childNode = childNodes.item(i);
                if ("tspan".equals(childNode.getNodeName())) {
                    textContent.append(childNode.getTextContent());
                }
            }
        }
        return textContent.toString();
    }

    /**
     * get text coordinate, if the text has translate, need to consider translate value
     * @param textNode
     * @return
     */
    private static List<Double> getTextCoordinate(Node textNode) {
        List<Double> position = new ArrayList<>();
        String transform = textNode.getAttributes().getNamedItem("transform") != null
                ? textNode.getAttributes().getNamedItem("transform").getNodeValue()
                : null;
        if (transform == null) {
            Double x = textNode.getAttributes().getNamedItem("x") != null
                    ? Double.parseDouble(textNode.getAttributes().getNamedItem("x").getNodeValue())
                    : 0;
            Double y = textNode.getAttributes().getNamedItem("y") != null
                    ? Double.parseDouble(textNode.getAttributes().getNamedItem("y").getNodeValue())
                    : 0;
            position.add(x);
            position.add(y);
        }else{
            Pattern pattern = Pattern.compile("translate\\(([^)]+)\\)");
            Matcher matcher = pattern.matcher(transform);
            if (matcher.find()) {
                String[] values = matcher.group(1).split(" ");
                Double x = Double.parseDouble(values[0]);
                Double y = Double.parseDouble(values[1]);
                position.add(x);
                position.add(y);
            }
        }
        return position;

    }


    /**
     * parse terminal assignment from global store
     * @param terminalAssignmentComp, terminal assignment component in global store
     * @return terminal list
     */
    private static List<TerminalAssignment> parseTerminalAssignments(BComponent terminalAssignmentComp) {
        List<TerminalAssignment> terminalAssignments = new ArrayList<>();
        for (Property terminalProp : terminalAssignmentComp.getDynamicPropertiesArray()) {
            if(!terminalProp.getName().equals("svg")){
                TerminalAssignment terminalAssignment = new TerminalAssignment();
                String[] splits = SlotPath.unescape(terminalProp.getName()).split("~");
                String pinSlotName = splits[0];
                String pinValue = splits[1];
                List<String> options = new ArrayList<>();

                for (Property optionProp : ((BComponent)terminalAssignmentComp.get(terminalProp)).getDynamicPropertiesArray()) {
                    options.add(SlotPath.unescape(optionProp.getName()));
                }
                terminalAssignment.setName(pinSlotName);
                terminalAssignment.setValue(pinValue);
                terminalAssignment.setOptions(options);
                terminalAssignments.add(terminalAssignment);
            }
        }
        return terminalAssignments;
    }

    private static final class TerminalAssignment{
        private String name;
        private String value;

        private List<String> options;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public List<String> getOptions() {
            return options;
        }

        public void setOptions(List<String> options) {
            this.options = options;
        }
    }

}
