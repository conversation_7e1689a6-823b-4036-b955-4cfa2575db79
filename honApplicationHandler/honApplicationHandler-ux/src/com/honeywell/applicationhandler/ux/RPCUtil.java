/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ux;

import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.tridium.json.JSONObject;

import javax.baja.naming.BOrd;

public final class RPCUtil {
    private RPCUtil() {
    }

    public static BIHoneywellConfigurableDevice getDevice(JSONObject jsonObject) {
        String handle = jsonObject.getString(Constants.DEVICE_HANDLE);
        return (BIHoneywellConfigurableDevice) BOrd.make(Constants.STATION_PATH + handle).get();
    }
}
