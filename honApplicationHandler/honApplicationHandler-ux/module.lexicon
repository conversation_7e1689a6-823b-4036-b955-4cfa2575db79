#
# Lexicon for the honApplicationHandler module.
#

HoneywellDeviceWizardLex.displayName=Honeywell Device Wizard
HoneywellDeviceWizardLex.Scheduling=Scheduling
HoneywellDeviceWizardLex.Information=Information
HoneywellDeviceWizardLex.CLOSE=CLOSE
HoneywellDeviceWizardLex.DefaultState=Default State
HoneywellDeviceWizardLex.StartAt=Start at
HoneywellDeviceWizardLex.EndAt=End at
HoneywellDeviceWizardLex.NoEvent=No event
HoneywellDeviceWizardLex.EventInfo1=NB: No event will default to user
HoneywellDeviceWizardLex.EventInfo2=selected Default state
HoneywellDeviceWizardLex.Occupied=Occupied
HoneywellDeviceWizardLex.Unoccupied=Unoccupied
HoneywellDeviceWizardLex.Standby=Standby
HoneywellDeviceWizardLex.Holidays=Holidays
HoneywellDeviceWizardLex.HolidayType=Holiday Type
HoneywellDeviceWizardLex.RECURRINGEVENTEVERYYEAR=RECURRING EVENT EVERY YEAR
HoneywellDeviceWizardLex.SPECIFICDATEEVERYYEAR=SPECIFIC DATE EVERY YEAR
HoneywellDeviceWizardLex.DATERANGEEVERYYEAR=DATE RANGE EVERY YEAR
HoneywellDeviceWizardLex.HolidayList=Holiday List
HoneywellDeviceWizardLex.EveryWeek=Every Week
HoneywellDeviceWizardLex.EveryDay=Every Day
HoneywellDeviceWizardLex.EveryMonth=Every Month
HoneywellDeviceWizardLex.Week=Week
HoneywellDeviceWizardLex.LastWeek=Last Week
HoneywellDeviceWizardLex.of=of
HoneywellDeviceWizardLex.to=to
HoneywellDeviceWizardLex.from=from
HoneywellDeviceWizardLex.AddHoliday=Add Holiday
HoneywellDeviceWizardLex.PredefinedHolidaysList=Predefined Holidays List
HoneywellDeviceWizardLex.USholidays=US holidays
HoneywellDeviceWizardLex.Canadianholidays=Canadian holidays
HoneywellDeviceWizardLex.Europeanhoildays=European hoildays
HoneywellDeviceWizardLex.ADDTOLIST=ADD TO LIST
HoneywellDeviceWizardLex.IMPORT=IMPORT
HoneywellDeviceWizardLex.EXPORT=EXPORT
HoneywellDeviceWizardLex.State=State
HoneywellDeviceWizardLex.ExistingScheduleOverlapping=Existing schedule overlapping.
HoneywellDeviceWizardLex.ScheduelEndTimeError=Schedule end time should be greater than start time.
HoneywellDeviceWizardLex.CALCULATE=CALCULATE
HoneywellDeviceWizardLex.CANCEL=CANCEL
HoneywellDeviceWizardLex.Cancel=Cancel
HoneywellDeviceWizardLex.Yes=Yes
HoneywellDeviceWizardLex.No=No
HoneywellDeviceWizardLex.ADDEVENT=ADD EVENT
HoneywellDeviceWizardLex.EDITEVENT=EDIT EVENT
HoneywellDeviceWizardLex.AddEvent=Add Event
HoneywellDeviceWizardLex.EditEvent=Edit Event
HoneywellDeviceWizardLex.CopyEvent=Copy Event
HoneywellDeviceWizardLex.Targetweekday=Target weekday
HoneywellDeviceWizardLex.AllDay=(All Day)
HoneywellDeviceWizardLex.SELECTALL=SELECT ALL
HoneywellDeviceWizardLex.DESELECTALL=DESELECT ALL
HoneywellDeviceWizardLex.COPYTO=COPY TO
HoneywellDeviceWizardLex.ConfigureHoliday=Configure Holiday
HoneywellDeviceWizardLex.Name=Name
HoneywellDeviceWizardLex.EveryWeek=Every Week
HoneywellDeviceWizardLex.First=First
HoneywellDeviceWizardLex.Second=Second
HoneywellDeviceWizardLex.Third=Third
HoneywellDeviceWizardLex.Fourth=Fourth
HoneywellDeviceWizardLex.Fifth=Fifth
HoneywellDeviceWizardLex.Last=Last
HoneywellDeviceWizardLex.Weekday=Weekday
HoneywellDeviceWizardLex.Sunday=Sunday
HoneywellDeviceWizardLex.Monday=Monday
HoneywellDeviceWizardLex.Tuesday=Tuesday
HoneywellDeviceWizardLex.Wednesday=Wednesday
HoneywellDeviceWizardLex.Thursday=Thursday
HoneywellDeviceWizardLex.Friday=Friday
HoneywellDeviceWizardLex.Saturday=Saturday
HoneywellDeviceWizardLex.Month=Month
HoneywellDeviceWizardLex.Date=Date
HoneywellDeviceWizardLex.FromMonth=From Month
HoneywellDeviceWizardLex.FromDate=From Date
HoneywellDeviceWizardLex.ToMonth=To Month
HoneywellDeviceWizardLex.ToDate=To Date
HoneywellDeviceWizardLex.SameNameaAreadyExist=Same name already exist.
HoneywellDeviceWizardLex.SameHolidayAlreadyExist=Same holiday already exist.
HoneywellDeviceWizardLex.ToDateMonthError=To Month and To Date should be greater than or equal to From Month and From Date
HoneywellDeviceWizardLex.atleastOneOptionWeekWeekDayMonthError=Please select atleast one value for Week or Weekday or Month
HoneywellDeviceWizardLex.ADDHOLIDAY=ADD HOLIDAY
HoneywellDeviceWizardLex.EDITHOLIDAY=EDIT HOLIDAY
HoneywellDeviceWizardLex.Copy=Copy
HoneywellDeviceWizardLex.ApplyMF=Apply M-F
HoneywellDeviceWizardLex.Delete=Delete
HoneywellDeviceWizardLex.SelectOneItem=Select One Item
HoneywellDeviceWizardLex.DuctArea=Duct Area
HoneywellDeviceWizardLex.DuctAreaCalculator=Duct Area Calculator
HoneywellDeviceWizardLex.SelectFunctionBlock=Select Function Block
HoneywellDeviceWizardLex.SAVE=SAVE
HoneywellDeviceWizardLex.characteristicsNotSupportedTerminalError={0} characteristics is not supported for the terminal {1}. \r\n Do you want to proceed with swapping the terminals?
HoneywellDeviceWizardLex.UnknownValue=Unknown value
HoneywellDeviceWizardLex.VisitImpactedTabsToEnableTheSaveButton=Visit impacted tabs to enable the save button.
HoneywellDeviceWizardLex.ResolveDuplicateHolidaysToEnableTheSaveButton=Resolve duplicate holidays to enable the save button.
HoneywellDeviceWizardLex.WizardSaveOption=Wizard save option
HoneywellDeviceWizardLex.ControllerNotInSyncInfo=Controller is not in sync with BACnet data in database. Please choose an option to continue.
HoneywellDeviceWizardLex.OverwriteBACnetObjectValuesInDevice=Overwrite BACnet object values in Device
HoneywellDeviceWizardLex.OverwriteBACnetObjectValuesInApplication=Overwrite BACnet object values in Application

HoneywellDeviceWizard.displayName=Honeywell Device Wizard

HoneywellDeviceWizard.rule.validation.fail="Error occurred while parsing rules. Please validate the rules and try again"