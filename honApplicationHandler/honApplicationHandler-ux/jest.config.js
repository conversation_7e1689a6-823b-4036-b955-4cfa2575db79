module.exports = {
  testEnvironment: 'jsdom',
  rootDir: '.',
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '^baja!(.*)$': '<rootDir>/srcTest/mocks/bajaMock.js',
    '^lex!(.*)$': '<rootDir>/srcTest/mocks/lexMock.js',
    '^nmodule/(.*)$': '<rootDir>/src/$1',
    '\\.(jpg|jpeg|png|gif|svg)$': 'jest-transform-stub',
    '.+/src/rc/honeywelldevice/profile/WizardHomeProfile$': '<rootDir>/srcTest/mocks/WizardHomeProfile.js',
    '^nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel$': '<rootDir>/src/rc/honeywelldevice/components/ConfigLabel.js'
  },
  setupFilesAfterEnv: [
    '<rootDir>/srcTest/setupTests.js'
  ],
  moduleDirectories: [
    'node_modules',
    'src'
  ],
  transform: {
    '^.+\\.(js|jsx)$': ['babel-jest', { configFile: './jest.babel.config.js' }]
  },
  // Modified to include only unit tests
  testMatch: [
    '<rootDir>/srcTest/unit/**/*.test.js'
  ],
  transformIgnorePatterns: [
    '/node_modules/(?!semantic-ui-react).+\\.js$'
  ],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'src/rc/honeywelldevice/**/*.js',
    'src/rc/libs/**/*.js',
    '!src/rc/honeywelldevice/**/*.built.js',
    '!src/rc/honeywelldevice/**/*.min.js',
    '!src/rc/libs/**/*.built.js',
    '!src/rc/libs/**/*.min.js',
    '!**/node_modules/**'
  ],
  coverageReporters: ['lcov', 'text', 'text-summary'],
  coverageThreshold: {
    global: {
      statements: 50,
      branches: 40,
      functions: 50,
      lines: 50
    }
  }
};
