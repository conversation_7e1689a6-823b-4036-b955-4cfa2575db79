// filepath: c:\Users\<USER>\Documents\GitHub\HonApplicationHandler1\honApplicationHandler\honApplicationHandler-ux\srcTest\helpers\customMatchers.js
const customMatchers = {
  toBeVisibleInDOM: function(received) {
    const element = received;

    // Check if element exists and is visible
    const pass =
      element !== null &&
      element.nodeType === 1 &&
      window.getComputedStyle(element).display !== 'none' &&
      window.getComputedStyle(element).visibility !== 'hidden';

    return {
      pass,
      message: () => pass
        ? `Expected element not to be visible in DOM`
        : `Expected element to be visible in DOM`
    };
  },

  toHaveBeenCalledWithMatch: function(received, expected) {
    const calls = received.mock.calls;

    // Check if any call matches the expected pattern
    const pass = calls.some(call => {
      return JSON.stringify(call).includes(JSON.stringify(expected));
    });

    return {
      pass,
      message: () => pass
        ? `Expected function not to have been called with arguments matching ${JSON.stringify(expected)}`
        : `Expected function to have been called with arguments matching ${JSON.stringify(expected)}`
    };
  }
};

export default customMatchers;