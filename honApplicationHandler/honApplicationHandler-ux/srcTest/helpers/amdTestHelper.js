/**
 * Helper functions for testing AMD modules in a unit-testing style
 */
import React from 'react';
import { render } from '@testing-library/react';
import { mockAmdModule } from './amdModuleHelper';

// Default mock implementations for common dependencies
const defaultMocks = {
  'baja!': {
    rpc: jest.fn().mockResolvedValue({})
  },
  'lex!honApplicationHandler': [{
    HoneywellDeviceWizard: {
      Terminal_Assignment: 'Terminal Assignment',
      Scheduling: 'Scheduling',
      Save: 'Save',
      Loading: 'Loading',
      Cancel: 'Cancel'
    }
  }]
};

/**
 * Creates a testable component from an AMD module
 * 
 * @param {Array} dependencies - AMD dependencies array
 * @param {Function} factory - AMD factory function
 * @param {Object} customMocks - Custom mocks to override defaults
 * @returns {React.Component} - The instantiated component
 */
export function createTestableComponent(dependencies, factory, customMocks = {}) {
  // Combine default mocks with custom mocks
  const mocks = { ...defaultMocks, ...customMocks };
  return mockAmdModule(dependencies, factory, mocks);
}

/**
 * Renders an AMD component with the specified props
 * 
 * @param {React.Component} Component - The component to render
 * @param {Object} props - Props to pass to the component
 * @returns {Object} - The render result with additional testing utilities
 */
export function renderAmdComponent(Component, props = {}) {
  return render(<Component {...props} />);
}

/**
 * Load an AMD module directly from a file path
 * 
 * @param {string} filePath - Path to the module relative to src directory
 * @param {Object} customMocks - Custom mocks for dependencies
 * @returns {Object} - The exported module
 */
export function loadAmdModule(filePath, customMocks = {}) {
  // Extract the define call from the file
  const fileContent = require(`../../../src/${filePath}`);
  
  // If it's a direct module export
  if (typeof fileContent === 'object' || typeof fileContent === 'function') {
    return fileContent;
  }
  
  // Otherwise, assume it's an AMD module
  if (fileContent && fileContent.__esModule) {
    return fileContent.default;
  }
  
  throw new Error(`Could not load AMD module from ${filePath}`);
}

/**
 * Creates a mock RPC service with configurable behavior
 * 
 * @param {Object} mockResponses - Map of method names to mock responses
 * @returns {Object} - Mock RPC service
 */
export function createMockRpcService(mockResponses = {}) {
  return {
    resetMockData: jest.fn(),
    configure: jest.fn(),
    setMockData: jest.fn(),
    getMockData: jest.fn(),
    invokeRPC: jest.fn((methodName, params) => {
      if (mockResponses[methodName]) {
        if (typeof mockResponses[methodName] === 'function') {
          return Promise.resolve(mockResponses[methodName](params));
        }
        return Promise.resolve(mockResponses[methodName]);
      }
      return Promise.resolve({});
    }),
    invokeRPCWithNoParam: jest.fn(methodName => {
      if (mockResponses[methodName]) {
        if (typeof mockResponses[methodName] === 'function') {
          return Promise.resolve(mockResponses[methodName]());
        }
        return Promise.resolve(mockResponses[methodName]);
      }
      return Promise.resolve({});
    })
  };
}
