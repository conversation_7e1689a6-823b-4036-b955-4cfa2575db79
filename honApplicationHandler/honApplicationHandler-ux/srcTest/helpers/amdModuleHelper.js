import path from 'path';
import React from 'react';

/**
 * Helper to test AMD modules by extracting the module from define call
 * @param {Array} dependencies - The dependencies array from define call
 * @param {Function} factory - The factory function from define call
 * @param {Object} mocks - Mocks for dependencies
 * @returns The instantiated module
 */
export function mockAmdModule(dependencies, factory, mocks = {}) {
  const mockedDependencies = dependencies.map(dep => {
    // Handle require plugins (e.g., 'baja!', 'lex!')
    const pluginMatch = dep.match(/^([a-zA-Z]+)!(.*)$/);
    if (pluginMatch) {
      const [, plugin, module] = pluginMatch;
      if (mocks[dep]) return mocks[dep];
      if (plugin === 'baja') {
        return require('../mocks/bajaMock').default;
      } else if (plugin === 'lex') {
        return require('../mocks/lexMock').default(module);
      }
    }
    if (dep === 'react') return React;
    if (dep === 'semantic-ui-react') return require('../mocks/semantic-ui-react.mock');
    if (mocks[dep]) return mocks[dep];
    if (dep.startsWith('css!')) return {};
    if (dep.startsWith('nmodule/')) {
      try {
        const modulePath = dep.replace('nmodule/', '');
        return jest.requireActual(modulePath);
      } catch {
        return {};
      }
    }
    // Generic fallback for any other dependency
    return createGenericMock(dep);
  });

  try {
    return factory.apply(null, mockedDependencies);
  } catch (error) {
    throw error;
  }
}

/**
 * Extract a module from a file using Jest's module system
 * @param {string} modulePath - Path to the module relative to src directory
 * @param {Object} mocks - Mocks for dependencies
 * @returns The extracted module
 */
export function extractAmdModule(modulePath, mocks = {}) {
  jest.resetModules();
  try {
    // Handle the case when modulePath starts with nmodule/
    let fullPath;
    if (modulePath.startsWith('nmodule/')) {
      // Remove 'nmodule/' prefix and construct the path
      const strippedPath = modulePath.replace('nmodule/', '');
      // Remove the 'honApplicationHandler/' part if it exists
      const cleanPath = strippedPath.replace('honApplicationHandler/', '');
      fullPath = path.resolve(process.cwd(), 'src', cleanPath);
    } else if (modulePath.startsWith('src/')) {
      fullPath = path.resolve(process.cwd(), modulePath);
    } else {
      fullPath = path.resolve(process.cwd(), 'src', modulePath);
    }
    
    const normalizedPath = fullPath.endsWith('.js') ? fullPath : `${fullPath}.js`;
    
    // Try to require the actual file
    try {
      jest.requireActual(normalizedPath);
    } catch (importError) {
      console.warn(`Failed to import module: ${normalizedPath}. Error: ${importError.message}`);
      throw new Error(`Failed to load module from any known path: ${modulePath}`);
    }

    const amdModules = global.__AMD_MODULES__ || {};
    let moduleEntry = amdModules[normalizedPath];
    
    if (!moduleEntry) {
      // Try to find the module by basename
      const baseName = path.basename(modulePath);
      const foundPath = Object.keys(amdModules).find(p => p.includes(baseName));
      
      if (foundPath) {
        moduleEntry = amdModules[foundPath];
        console.warn(`Found module by basename: ${baseName} at ${foundPath}`);
      } else {
        console.warn(`Module not registered via define: ${normalizedPath}. Available modules: ${Object.keys(amdModules).join(', ')}`);
        throw new Error(`Module not registered via define: ${normalizedPath}`);
      }
    }
    
    const moduleResult = mockAmdModule(moduleEntry.dependencies, moduleEntry.factory, mocks);
    if (!moduleResult) {
      throw new Error(`Module factory returned undefined: ${modulePath}`);
    }
    return moduleResult;
  } catch (error) {
    console.warn(`Failed to extract AMD module ${modulePath}: ${error.message}`);
    throw error;
  }
}

/**
 * Create a generic mock for any dependency or utility
 * @param {string} dep - Dependency name or path
 * @returns {Object|Function} A mock object or component
 */
function createGenericMock(dep) {
  // If it looks like a React component (PascalCase or ends with .jsx/.tsx/.js)
  const isComponent = /^[A-Z][A-Za-z0-9]+$/.test(dep) || /\.(jsx?|tsx?)$/.test(dep);
  if (isComponent) {
    return createMockReactComponent(dep);
  }
  // Otherwise, return a generic utility mock
  return createMockUtility(dep);
}

/**
 * Create a mock React component for testing
 * @param {string} componentName - Component name for the mock
 * @returns A React component
 */
function createMockReactComponent(componentName) {
  const MockComponent = props => (
    <div
      data-testid={`mock-${componentName}`}
      className={`mock-${componentName}`}
      {...props}
    >
      {props.children || `Mock ${componentName}`}
    </div>
  );
  MockComponent.displayName = `Mock${componentName}`;
  return MockComponent;
}

/**
 * Create a mock utility object
 * @param {string} utilityPath - Path to the utility
 * @returns A mock utility object
 */
function createMockUtility(utilityPath) {
  const utilityName = path.basename(utilityPath);
  return new Proxy(
    {
      __isMock: true,
      __mockPath: utilityPath,
      __mockName: utilityName,
    },
    {
      get: (target, prop) => {
        if (typeof prop === 'string' && !(prop in target)) {
          target[prop] = jest.fn();
        }
        return target[prop];
      },
    }
  );
}
