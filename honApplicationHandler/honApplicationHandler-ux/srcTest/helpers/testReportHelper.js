/**
 * Generates a detailed test report based on test results
 * @param {Object} results - Jest test results
 * @returns {string} HTML report
 */
export function generateTestReport(results) {
  const reportHeader = `
    <html>
    <head>
      <title>Test Report</title>
      <style>
        body { font-family: Arial, sans-serif; }
        .success { color: green; }
        .failure { color: red; }
        .test-suite { margin-bottom: 20px; }
        .test-case { margin-left: 20px; }
      </style>
    </head>
    <body>
      <h1>Test Report</h1>
      <div class="summary">
        <p>Total: ${results.numTotalTests}</p>
        <p>Passed: ${results.numPassedTests}</p>
        <p>Failed: ${results.numFailedTests}</p>
      </div>
      <div class="test-results">
  `;

  const reportFooter = `
      </div>
    </body>
    </html>
  `;

  let reportBody = '';
  results.testResults.forEach(suite => {
    reportBody += `<div class="test-suite">
      <h2>${suite.name}</h2>`;

    suite.testResults.forEach(test => {
      const status = test.status === 'passed' ? 'success' : 'failure';
      reportBody += `<div class="test-case ${status}">
        <p>${test.title}</p>
        ${test.status !== 'passed' ? `<pre>${test.failureMessages.join('\n')}</pre>` : ''}
      </div>`;
    });

    reportBody += `</div>`;
  });

  return reportHeader + reportBody + reportFooter;
}

/**
 * Save test report to a file
 * @param {Object} results - Jest test results
 * @param {string} filePath - Path to save the report
 */
export function saveTestReport(results, filePath) {
  const fs = require('fs');
  const report = generateTestReport(results);
  fs.writeFileSync(filePath, report);
  console.log(`Test report saved to ${filePath}`);
}