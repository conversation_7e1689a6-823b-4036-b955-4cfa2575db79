import '@testing-library/jest-dom';
import React from 'react';

// Ensure DOM environment is properly set up
if (typeof document === 'undefined') {
  const { JSDOM } = require('jsdom');
  const dom = new JSDOM('<!doctype html><html><body></body></html>', { url: 'http://localhost' });
  global.document = dom.window.document;
  global.window = dom.window;
  global.navigator = dom.window.navigator;
}

// Mock the global lexicon service immediately
const honApplicationHandlerLex = {
  get: jest.fn((key) => {
    const lexicon = {
      'HoneywellDeviceWizardLex.Scheduling': 'Scheduling',
      'HoneywellDeviceWizardLex.DefaultState': 'Default State',
      'HoneywellDeviceWizardLex.StartAt': 'Start At',
      'HoneywellDeviceWizardLex.EndAt': 'End At',
      'HoneywellDeviceWizardLex.NoEvent': 'No Event'
    };
    return lexicon[key] || key.split('.').pop() || 'Mock Text';
  })
};

// Set globally before any tests run
global.honApplicationHandlerLex = honApplicationHandlerLex;
window.honApplicationHandlerLex = honApplicationHandlerLex;
globalThis.honApplicationHandlerLex = honApplicationHandlerLex;

// Mock window properties that are not available in jsdom
window.matchMedia = window.matchMedia || function() {
  return {
    matches: false,
    addListener: function() {},
    removeListener: function() {}
  };
};

// Mock ResizeObserver
window.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Setup AMD/RequireJS define function
global.define = (dependencies, factory) => {
  // Store the module for testing
  if (!global.__AMD_MODULES__) {
    global.__AMD_MODULES__ = {};
  }
  
  // Store the current call details for later extraction
  const stack = new Error().stack;
  const callerLine = stack.split('\n')[2];
  const match = callerLine.match(/\((.+?):\d+:\d+\)/);
  const filePath = match ? match[1] : 'unknown';
  
  global.__AMD_MODULES__[filePath] = { dependencies, factory };
  
  return factory;
};

console.log('Jest setup complete');

// Global setup to run before all tests
beforeAll(() => {
  // Ensure document.body exists for React Testing Library
  if (!document.body) {
    document.body = document.createElement('body');
    document.documentElement.appendChild(document.body);
  }
});

// Add DOM container setup for React Testing Library compatibility
beforeEach(() => {
  // Ensure document.body exists and is clean
  if (!document.body) {
    document.body = document.createElement('body');
    document.documentElement.appendChild(document.body);
  }
  
  // Clear any existing content
  document.body.innerHTML = '';
  
  // Create a clean container for each test
  const testContainer = document.createElement('div');
  testContainer.id = 'test-container';
  document.body.appendChild(testContainer);
  
  // Enhanced JSDOM environment setup
  Object.defineProperty(window, 'location', {
    writable: true,
    value: { href: 'http://localhost' }
  });
  
  Object.defineProperty(window, 'navigator', {
    writable: true,
    value: { userAgent: 'jest' }
  });
});

afterEach(() => {
  // Clean up DOM after each test to prevent pollution
  if (document.body) {
    document.body.innerHTML = '';
  }
  
  // Clean up any global state
  jest.clearAllTimers();
});