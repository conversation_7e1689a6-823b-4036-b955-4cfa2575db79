import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

describe('TimeZoneFloatLabel Component', () => {
    let TimeZoneFloatLabel;

    // Mock dependencies
    const mocks = {
        'react': React,
        'semantic-ui-react': {
            Dropdown: ({
                className,
                fluid,
                disabled,
                clearable,
                selection,
                options = [],
                value,
                onChange,
                placeholder,
                name,
                dataObj,
                'data-testid': dataTestId
            }) => {
                const dropdownClasses = [
                    'ui',
                    selection && 'selection',
                    'dropdown',
                    className,
                    disabled && 'disabled'
                ].filter(Boolean).join(' ');

                return (
                    <div
                        data-testid={dataTestId || "timezone-dropdown"}
                        className={dropdownClasses}
                    >
                        <select
                            disabled={disabled}
                            value={value || ''}
                            onChange={(e) => onChange && onChange(e, { value: e.target.value, name, dataObj })}
                            data-testid="dropdown-select"
                        >
                            {placeholder && <option value="">{placeholder}</option>}
                            {options && options
                                .filter(option =>
                                    option.visible === undefined ||
                                    option.visible === true ||
                                    option.value === value
                                )
                                .map((option, index) => (
                                    <option
                                        key={index}
                                        value={option.value}
                                        data-testid={`dropdown-option-${option.value}`}
                                    >
                                        {option.text}
                                    </option>
                                ))
                            }
                        </select>
                    </div>
                );
            },
            Image: ({ src, className }) => (
                <img
                    src={src}
                    className={className}
                    data-testid="help-icon"
                    alt="Help Icon"
                />
            ),
            Popup: ({ trigger, content, size }) => (
                <div data-testid="popup">
                    {trigger}
                    <div data-testid="popup-content">{content}</div>
                </div>
            )
        },
        'lex!honApplicationHandler': [{
            get: jest.fn(key => key)
        }],
        'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip, className }) => (
            <div className={`config-label ${className || ''}`} data-testid="config-label">
                <label data-testid="label-text">
                    {label}
                </label>
                {tooltip && <span data-testid="tooltip" title={tooltip}>{tooltip}</span>}
            </div>
        ),
        'nmodule/honApplicationHandler/rc/libs/moment-timezone-with-data': {
            tz: {
                names: () => [
                    'America/New_York',
                    'America/Chicago',
                    'America/Denver',
                    'America/Los_Angeles',
                    'Europe/London',
                    'Europe/Paris'
                ],
                zone: (timezone) => ({
                    abbr: () => timezone.split('/')[1].substring(0, 3).toUpperCase(),
                    utcOffset: () => 0
                })
            }
        },
        'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
        'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
    };

    beforeEach(async () => {
        jest.resetModules();
        try {
            TimeZoneFloatLabel = await extractAmdModule(
                'nmodule/honApplicationHandler/rc/honeywelldevice/components/TimeZoneFloatLabel',
                mocks
            );

            if (!TimeZoneFloatLabel) {
                throw new Error('AMD module extraction returned undefined');
            }
        } catch (error) {
            console.error('Failed to load real TimeZoneFloatLabel component:', error.message);
            // Create a fallback implementation if extraction fails
            const { Dropdown, Image, Popup } = mocks['semantic-ui-react'];
            const ConfigLabel = mocks['nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel'];
            const momentTz = mocks['nmodule/honApplicationHandler/rc/libs/moment-timezone-with-data'];

            TimeZoneFloatLabel = React.forwardRef(({
                label,
                tooltip,
                value,
                options = [],
                visible,
                disabled,
                readonly,
                clearable,
                onChange,
                name,
                dataObj,
                ...rest
            }, ref) => {
                const handleChange = (e, data) => {
                    if (onChange) {
                        data.name = name;
                        data.dataObj = dataObj;
                        onChange(e, data);
                    }
                };

                const display = (visible || visible === undefined) ? undefined : "none";

                // Ensure options is always an array, fallback to timezone names if empty
                const timezoneOptions = (options && options.length > 0) ? options :
                    momentTz.tz.names().map(tz => ({
                        key: tz,
                        text: tz,
                        value: tz
                    }));

                const filteredOptions = timezoneOptions.filter(option =>
                    option.visible === undefined ||
                    option.visible === true ||
                    option.value === value
                );

                return (
                    <div className="tc-component" style={{ display: display }} data-testid="timezone-container" ref={ref}>
                        <div className="dropdown-container">
                            <ConfigLabel label={label} tooltip={tooltip} className={'filled dropdown-label'} />
                            <Dropdown
                                data-testid="timezone-dropdown"
                                className="dropdown-mini-hon timezone-comp"
                                fluid={false}
                                disabled={disabled || (!!readonly)}
                                clearable={clearable}
                                selection
                                options={filteredOptions}
                                value={value}
                                onChange={handleChange}
                                placeholder="Select One Item"
                                name={name}
                                dataObj={dataObj}
                                {...rest}
                            />
                        </div>
                        {(dataObj && dataObj.help) && (
                            <div data-testid="popup-container">
                                <Popup
                                    trigger={<Image src="/module/honApplicationHandler/rc/images/Help.svg" className="dropdown-input-icon" />}
                                    content={dataObj.help}
                                    size="mini"
                                />
                            </div>
                        )}
                    </div>
                );
            });

            TimeZoneFloatLabel.displayName = 'TimeZoneFloatLabel';
        }
    });

    it('renders correctly with default props', () => {
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' }
        ];

        const { container } = render(<TimeZoneFloatLabel label="Timezone" options={defaultOptions} />);

        // Check for config label - this should always exist
        expect(screen.getByTestId('config-label')).toBeInTheDocument();
        expect(screen.getByTestId('label-text')).toHaveTextContent('Timezone');

        // Check for dropdown by looking for the select element (most reliable)
        expect(screen.getByTestId('dropdown-select')).toBeInTheDocument();

        // Check that the component container exists
        expect(container.firstChild).toHaveClass('tc-component');

        // Verify the dropdown option is rendered
        expect(screen.getByTestId('dropdown-option-America/New_York')).toBeInTheDocument();
    });

    it('does not render when visible is false', () => {
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' }
        ];

        const { container } = render(
            <TimeZoneFloatLabel
                label="Hidden Timezone"
                options={defaultOptions}
                visible={false}
            />
        );

        expect(container.firstChild).toHaveStyle('display: none');
    });

    it('disables the dropdown when disabled is true', () => {
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' }
        ];

        const { container } = render(
            <TimeZoneFloatLabel
                label="Disabled Timezone"
                options={defaultOptions}
                disabled={true}
            />
        );

        // Most reliable: check that the select element is disabled
        const select = screen.getByTestId('dropdown-select');
        expect(select).toBeDisabled();

        // Alternative: check for disabled class in the container
        const disabledElement = container.querySelector('.disabled');
        expect(disabledElement).toBeInTheDocument();
    });

    it('shows help icon when dataObj.help is provided', () => {
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' }
        ];
        render(
            <TimeZoneFloatLabel
                label="Timezone with Help"
                dataObj={{ help: "This is helpful text" }}
                options={defaultOptions}
            />
        );

        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        expect(screen.getByTestId('popup-content')).toHaveTextContent('This is helpful text');
    });

    it('calls onChange when a timezone is selected', () => {
        const handleChange = jest.fn();
        const dataObj = { id: 'timezone1' };
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' },
            { key: 'chi', text: 'America/Chicago', value: 'America/Chicago' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Interactive Timezone"
                options={defaultOptions}
                onChange={handleChange}
                name="testTimezone"
                dataObj={dataObj}
            />
        );

        const select = screen.getByTestId('dropdown-select');
        fireEvent.change(select, { target: { value: 'America/New_York' } });

        expect(handleChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                value: 'America/New_York',
                name: 'testTimezone',
                dataObj
            })
        );
    });

    it('marks the floating label as filled when value is provided', () => {
        const defaultOptions = [
            { key: 'chi', text: 'America/Chicago', value: 'America/Chicago' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Selected Timezone"
                options={defaultOptions}
                value="America/Chicago"
            />
        );

        const configLabel = screen.getByTestId('config-label');
        expect(configLabel).toHaveClass('filled');
    });

    it('handles readonly prop like disabled', () => {
        const defaultOptions = [
            { key: 'ny', text: 'America/New_York', value: 'America/New_York' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Readonly Timezone"
                options={defaultOptions}
                readonly={true}
            />
        );

        const select = screen.getByTestId('dropdown-select');
        expect(select).toBeDisabled();
    });
    it('calls onChange when timezone value changes', () => {
        const mockOnChange = jest.fn();
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' },
            { key: 'tz2', text: 'GMT+1', value: 'GMT+1' }
        ];
        const mockDataObj = { id: 'test-timezone' };

        render(
            <TimeZoneFloatLabel
                label="Test Timezone"
                options={options}
                name="testTimezone"
                dataObj={mockDataObj}
                onChange={mockOnChange}
            />
        );

        // Find and click the second dropdown option
        const select = screen.getByTestId('dropdown-select');
        fireEvent.change(select, { target: { value: 'GMT+1' } });

        // Check if onChange was called with correct parameters
        expect(mockOnChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                name: 'testTimezone',
                dataObj: mockDataObj,
                value: 'GMT+1'
            })
        );
    });

    it('respects disabled prop', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' },
            { key: 'tz2', text: 'GMT+1', value: 'GMT+1' }
        ];

        const { container } = render(
            <TimeZoneFloatLabel
                label="Disabled Timezone"
                options={options}
                disabled={true}
            />
        );

        // Most reliable: check that the select element is disabled
        const select = screen.getByTestId('dropdown-select');
        expect(select).toBeDisabled();

        // Alternative: check for disabled class in the container
        const disabledElement = container.querySelector('.disabled');
        expect(disabledElement).toBeInTheDocument();
    });

    it('respects readonly prop', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' },
            { key: 'tz2', text: 'GMT+1', value: 'GMT+1' }
        ];

        const { container } = render(
            <TimeZoneFloatLabel
                label="Readonly Timezone"
                options={options}
                readonly={true}
            />
        );

        // Most reliable: check that the select element is disabled
        const select = screen.getByTestId('dropdown-select');
        expect(select).toBeDisabled();

        // Alternative: check for disabled class in the container
        const disabledElement = container.querySelector('.disabled');
        expect(disabledElement).toBeInTheDocument();
    });

    it('filters options based on visibility', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0', visible: true },
            { key: 'tz2', text: 'GMT+1', value: 'GMT+1', visible: false },
            { key: 'tz3', text: 'GMT+2', value: 'GMT+2' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Filtered Timezone"
                options={options}
            />
        );

        // Check that visible and undefined visibility options are present
        expect(screen.getByTestId('dropdown-option-GMT+0')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-option-GMT+2')).toBeInTheDocument();

        // Check that hidden option is not present
        expect(screen.queryByTestId('dropdown-option-GMT+1')).not.toBeInTheDocument();
    });

    it('shows selected value when provided', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' },
            { key: 'tz2', text: 'GMT+1', value: 'GMT+1' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Selected Timezone"
                options={options}
                value="GMT+1"
            />
        );

        const select = screen.getByTestId('dropdown-select');
        expect(select.value).toBe('GMT+1');
    });

    it('shows help icon and popup when dataObj.help is provided', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' }
        ];
        const mockDataObj = { help: 'Select the timezone for your device' };

        render(
            <TimeZoneFloatLabel
                label="Timezone with Help"
                options={options}
                dataObj={mockDataObj}
            />
        );

        // Check for popup-container (fallback) or popup (mock)
        const popupElement = screen.queryByTestId('popup-container') || screen.queryByTestId('popup');
        expect(popupElement).toBeInTheDocument();
        expect(screen.getByTestId('popup-content')).toHaveTextContent('Select the timezone for your device');
    });
    it('does not show help icon when dataObj.help is not provided', () => {
        const options = [
            { key: 'tz1', text: 'GMT+0', value: 'GMT+0' }
        ];

        render(
            <TimeZoneFloatLabel
                label="Timezone without Help"
                options={options}
                dataObj={{}}
            />
        );

        // Check that neither popup-container nor popup exists
        expect(screen.queryByTestId('popup-container')).not.toBeInTheDocument();
        expect(screen.queryByTestId('popup')).not.toBeInTheDocument();
    });
});