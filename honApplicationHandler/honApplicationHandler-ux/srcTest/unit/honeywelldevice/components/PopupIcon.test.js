import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies that match the real semantic-ui-react Popup behavior
const mocks = {
    'react': React,
    'semantic-ui-react': {
        Popup: ({ hoverable, trigger, position, children, ...props }) => {
            const [isOpen, setIsOpen] = React.useState(false);
            
            return (
                <div className="mock-popup" data-testid="popup" data-position={position} data-hoverable={hoverable}>
                    <div 
                        className="popup-trigger" 
                        data-testid="popup-trigger"
                        onMouseEnter={() => setIsOpen(true)}
                        onMouseLeave={() => setIsOpen(false)}
                        onClick={() => setIsOpen(!isOpen)}
                    >
                        {trigger}
                    </div>
                    {isOpen && (
                        <div className="popup-content" data-testid="popup-content">
                            {children}
                        </div>
                    )}
                </div>
            );
        }
    }
};

describe('PopupIcon Component', () => {
    let PopupIcon;
    
    beforeEach(() => {
        // Extract the REAL PopupIcon component for actual code coverage
        try {
            PopupIcon = extractAmdModule('rc/honeywelldevice/components/PopupIcon', mocks);
            console.log('Successfully extracted real PopupIcon component for testing');
        } catch (error) {
            console.error('Failed to extract PopupIcon component:', error.message);
            throw error;
        }
    });

    // Test 1: Basic rendering and constructor coverage (line 14-16)
    it('should render PopupIcon component with constructor and basic structure', () => {
        const tooltipText = "This is a helpful tooltip";
        
        const { container } = render(
            <PopupIcon tooltip={tooltipText} />
        );

        // Verify component renders successfully (constructor and render method covered)
        expect(container.firstChild).toBeDefined();
        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        expect(screen.getByTestId('popup-container')).toHaveAttribute('position', 'bottom left');
    });

    // Test 2: SVG rendering method coverage (lines 17-35)
    it('should call and render renderSvg method with correct SVG structure', () => {
        render(<PopupIcon tooltip="Test tooltip" />);

        // Find the SVG element within the component
        const container = screen.getByTestId('popup-container');
        const svgElement = container.querySelector('svg');
        
        expect(svgElement).toBeTruthy();
        expect(svgElement).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
        expect(svgElement).toHaveAttribute('color', 'blue');
        expect(svgElement).toHaveAttribute('width', '15');
        expect(svgElement).toHaveAttribute('height', '12');
        expect(svgElement).toHaveAttribute('viewBox', '0 0 24 24');
        
        // Verify the path element exists with correct attributes
        const pathElement = svgElement.querySelector('path');
        expect(pathElement).toBeTruthy();
        expect(pathElement).toHaveAttribute('fill', 'steelblue');
        expect(pathElement).toHaveAttribute('d', 'M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm1 18h-2v-8h2v8zm-1-12.25c.69 0 1.25.56 1.25 1.25s-.56 1.25-1.25 1.25-1.25-.56-1.25-1.25.56-1.25 1.25-1.25z');
    });

    // Test 3: Complete render method coverage including Popup props (lines 37-52)
    it('should render complete Popup component with all props and structure', () => {
        const tooltipText = "Multi-line\ntooltip\ncontent";
        
        render(<PopupIcon tooltip={tooltipText} />);

        // Verify Popup component props
        const popup = screen.getByTestId('popup-container');
        expect(popup).toHaveAttribute('position', 'bottom left');

        // Verify trigger structure with label wrapper
        const label = popup.querySelector('label');
        expect(label).toBeTruthy();
        
        // Verify SVG is inside the label
        const svg = label.querySelector('svg');
        expect(svg).toBeTruthy();

        // Verify the tooltip content area exists
        const content = screen.getByTestId('popup-content');
        expect(content).toBeInTheDocument();
        
        // The content should contain a div with the tooltip styling
        // Note: Due to how the mocked Popup works, content might be rendered differently
        expect(content).toBeDefined();
    });

    // Test 4: Label wrapper and accessibility structure
    it('should wrap SVG in label element for accessibility', () => {
        render(<PopupIcon tooltip="Accessibility test" />);

        const container = screen.getByTestId('popup-container');
        const label = container.querySelector('label');
        
        // Verify label wrapper exists for accessibility
        expect(label).toBeTruthy();
        
        // Verify SVG has proper attributes for screen readers
        const svg = label.querySelector('svg');
        expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
        expect(svg).toHaveAttribute('viewBox');
        
        // Verify path element for icon definition
        const path = svg.querySelector('path');
        expect(path).toBeTruthy();
        expect(path).toHaveAttribute('d');
        expect(path).toHaveAttribute('fill');
    });

    // Test 5: Different tooltip content scenarios
    it('should handle various tooltip content types', () => {
        const scenarios = [
            { tooltip: "Simple text", description: "simple text" },
            { tooltip: "", description: "empty string" },
            { tooltip: undefined, description: "undefined value" },
            { tooltip: null, description: "null value" },
            { tooltip: "Very long tooltip content that should wrap and potentially scroll due to maxHeight constraint", description: "long content" },
            { tooltip: "Line 1\nLine 2\nLine 3\nLine 4", description: "multiline content" }
        ];

        scenarios.forEach(({ tooltip, description }) => {
            const { unmount } = render(<PopupIcon tooltip={tooltip} />);
            
            // Verify component renders without errors
            expect(screen.getByTestId('popup-container')).toBeInTheDocument();
            
            // Verify content structure exists
            const content = screen.getByTestId('popup-content');
            expect(content).toBeInTheDocument();
            
            unmount();
        });
    });

    // Test 6: Component lifecycle and method calls
    it('should call all component methods during lifecycle', () => {
        const { rerender } = render(<PopupIcon tooltip="Initial tooltip" />);
        
        // Verify initial render calls both constructor and render methods
        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        
        // Test renderSvg method call by verifying SVG presence
        const container = screen.getByTestId('popup-container');
        const svg = container.querySelector('svg');
        expect(svg).toBeTruthy();
        
        // Test re-render with different props
        rerender(<PopupIcon tooltip="Updated tooltip" />);
        
        // Verify component still renders correctly after prop change
        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        
        // Verify SVG is still rendered (renderSvg called again)
        const updatedSvg = screen.getByTestId('popup-container').querySelector('svg');
        expect(updatedSvg).toBeTruthy();
    });

    // Test 7: Complete props coverage
    it('should handle all possible prop combinations', () => {
        const propCombinations = [
            { tooltip: "Standard tooltip" },
            { tooltip: "Multi\nLine\nTooltip" },
            { tooltip: "🎯 Emoji tooltip content! 🚀" },
            { tooltip: "   Whitespace   \n   Handling   " }
        ];

        propCombinations.forEach((props, index) => {
            const { unmount } = render(<PopupIcon {...props} />);
            
            // Verify each combination renders successfully
            expect(screen.getByTestId('popup-container')).toBeInTheDocument();
            expect(screen.getByTestId('popup-content')).toBeInTheDocument();
            
            unmount();
        });
    });

    // Test 8: Error boundary and edge cases
    it('should handle edge cases gracefully', () => {
        // Test with extreme content
        const extremeContent = "A".repeat(1000) + "\n" + "B".repeat(1000);
        
        const { container } = render(<PopupIcon tooltip={extremeContent} />);
        
        expect(container.firstChild).toBeDefined();
        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        
        // Should handle extreme content without breaking
        expect(screen.getByTestId('popup-content')).toBeInTheDocument();
    });

    // Test 9: Verify Popup component receives correct props
    it('should pass correct props to Popup component', () => {
        render(<PopupIcon tooltip="Test hover behavior" />);
        
        const popupContainer = screen.getByTestId('popup-container');
        
        // Verify position prop is passed correctly
        expect(popupContainer).toHaveAttribute('position', 'bottom left');
        
        // Verify content is present
        expect(screen.getByTestId('popup-content')).toBeInTheDocument();
        
        // Verify trigger structure with SVG inside label
        const label = popupContainer.querySelector('label');
        expect(label).toBeTruthy();
        expect(label.querySelector('svg')).toBeTruthy();
    });

    // Test 10: Verify all styling and structure elements
    it('should have complete structure with all styling elements', () => {
        const tooltipText = "Complete structure test";
        render(<PopupIcon tooltip={tooltipText} />);
        
        const container = screen.getByTestId('popup-container');
        
        // Verify label exists
        const label = container.querySelector('label');
        expect(label).toBeTruthy();
        
        // Verify SVG with all attributes
        const svg = label.querySelector('svg');
        expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
        expect(svg).toHaveAttribute('color', 'blue');
        expect(svg).toHaveAttribute('width', '15');
        expect(svg).toHaveAttribute('height', '12');
        expect(svg).toHaveAttribute('viewBox', '0 0 24 24');
        
        // Verify path with complete definition
        const path = svg.querySelector('path');
        expect(path).toHaveAttribute('fill', 'steelblue');
        
        // Verify content area
        const content = screen.getByTestId('popup-content');
        expect(content).toBeInTheDocument();
    });
});
