import React from 'react';
import { render, screen, fireEvent, within } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

/**
 * DuctAreaCalculator Component Tests
 * 
 * This test suite validates the DuctAreaCalculator component which allows users to:
 * - Calculate duct area for rectangular and circular ducts
 * - Change dimension units (inches, feet, meters, centimeters)
 * - Handle readonly mode with disabled interactions
 * 
 * The component uses AMD module structure and requires proper mocking of:
 * - Semantic UI React components (Button, Modal, Image, Popup)
 * - Custom components (ConfigLabel, SelectWidget, DropdownFloatLabel, NumberInputFloatLabel)
 * - Lexicon service for internationalization
 */

// Mock lexicons - provides internationalization support
const mockLexicons = [{
    get: jest.fn(key => key) // Returns the key as-is for testing
}];

// Mock dependencies
const mocks = {
    'react': React,
    'lex!honApplicationHandler': mockLexicons,
    'semantic-ui-react': {
        Button: ({ basic, onClick, color, style, children, disabled }) => (
            <button 
                data-testid={color === 'black' ? 'cancel-button' : 'select-button'}
                onClick={onClick}
                disabled={disabled}
                style={style}
                className={`${basic ? 'basic' : ''} ${color || ''}`}
                color={color}
            >
                {children}
            </button>
        ),
        Modal: ({ 
            className, 
            open, 
            size, 
            closeOnDimmerClick,
            closeOnDocumentClick,
            onClose,
            children 
        }) => (
            <div 
                data-testid="duct-calculator-modal"
                className={`ui modal ${className || ''} ${size || ''}`}
                style={{ display: open ? 'block' : 'none' }}
            >
                {children}
            </div>
        ),
        'Modal.Header': ({ style, children }) => (
            <div data-testid="duct-calculator-modal-header" style={style}>
                {children}
            </div>
        ),
        'Modal.Content': ({ style, children }) => (
            <div data-testid="duct-calculator-modal-content" style={style}>
                {children}
            </div>
        ),
        'Modal.Actions': ({ children }) => (
            <div data-testid="duct-calculator-modal-actions">
                {children}
            </div>
        ),
        Image: ({ 
            src, 
            disabled, 
            style, 
            onClick,
            className 
        }) => {
            const isCalculateIcon = src && src.includes('Calculate.svg');
            const isHelpIcon = src && src.includes('Help.svg');
            let testId = 'image';
            
            if (isCalculateIcon) {
                testId = 'calculate-icon';
            } else if (isHelpIcon) {
                testId = 'help-icon';
            }
            
            return (
                <img
                    data-testid={testId}
                    src={src}
                    className={className}
                    style={{
                        ...style,
                        opacity: disabled ? 0.5 : 1,
                        pointerEvents: disabled ? 'none' : 'auto'
                    }}
                    onClick={onClick}
                    disabled={disabled}
                    alt=""
                />
            );
        },
        Popup: ({ trigger, content, size }) => (
            <div data-testid="help-popup">
                {trigger}
                <div data-testid="help-popup-content" className={`ui popup ${size}`}>
                    {content}
                </div>
            </div>
        )
    },
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ 
        label, 
        tooltip 
    }) => (
        <div data-testid="config-label">
            {label && <div data-testid="config-label-text">{label}</div>}
            {tooltip && <div data-testid="config-label-tooltip">{tooltip}</div>}
        </div>
    ),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/SelectWidget': ({
        label,
        tooltip,
        value,
        visible,
        disabled,
        readonly,
        options,
        onChange,
        name
    }) => (
        <div 
            data-testid="duct-type-select"
            style={{ display: visible === false ? 'none' : undefined }}
        >
            <select
                data-testid="duct-type-select-input"
                value={value}
                disabled={disabled || readonly}
                onChange={e => onChange(e, { value: e.target.value })}
            >
                {options.map(opt => (
                    <option key={opt.value} value={opt.value}>
                        {opt.text}
                    </option>
                ))}
            </select>
            {label && <div data-testid="duct-type-select-label">{label}</div>}
            {tooltip && <div data-testid="duct-type-select-tooltip">{tooltip}</div>}
        </div>
    ),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel': ({
        name,
        disabled,
        readonly,
        clearable,
        label,
        tooltip,
        visible,
        options,
        value,
        onChange,
        dataObj
    }) => (
        <div 
            data-testid="dimension-unit-dropdown"
            style={{ display: visible === false ? 'none' : undefined }}
        >
            <select
                data-testid="dimension-unit-dropdown-input"
                value={value}
                disabled={disabled || readonly}
                onChange={e => onChange(e, { value: e.target.value })}
            >
                {options.map(opt => (
                    <option key={opt.value} value={opt.value}>
                        {opt.text}
                    </option>
                ))}
            </select>
            {label && <div data-testid="dimension-unit-dropdown-label">{label}</div>}
            {tooltip && <div data-testid="dimension-unit-dropdown-tooltip">{tooltip}</div>}
        </div>
    ),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/NumberInputFloatLabel': ({
        name,
        min,
        max,
        visible,
        disabled,
        readonly,
        label,
        tooltip,
        value,
        step,
        unit,
        defaultValue,
        width,
        onChange,
        dataObj,
        helpIconNotRequired
    }) => (
        <div 
            data-testid={`number-input-${name}`}
            style={{ 
                display: visible === false ? 'none' : undefined,
                width: width 
            }}
        >
            <input
                data-testid={`number-input-${name}-input`}
                type="number"
                min={min}
                max={max}
                disabled={disabled || readonly}
                value={value}
                step={step}
                placeholder={defaultValue}
                onChange={e => onChange(e, { 
                    name, 
                    dataObj, 
                    value: parseFloat(e.target.value),
                    type: 'input'
                })}
            />
            {unit && <span data-testid={`number-input-${name}-unit`}>{unit}</span>}
            {label && <div data-testid={`number-input-${name}-label`}>{label}</div>}
            {tooltip && <div data-testid={`number-input-${name}-tooltip`}>{tooltip}</div>}
        </div>
    )
};

describe('DuctAreaCalculator Component', () => {
    let DuctAreaCalculator;
    
    const mockDataObjList = [
        {
            role: 'ducttype',
            name: 'ductType',
            label: 'Duct Type',
            tooltip: 'Select duct type',
            value: 1,
            visible: true,
            disabled: false,
            readOnly: false,
            options: [
                { value: 1, text: 'Rectangular' },
                { value: 2, text: 'Round' }
            ]
        },
        {
            role: 'area',
            name: 'area',
            label: 'Area',
            value: 0,
            unit: 'in²',
            tooltip: 'Help text for area'
        },
        {
            role: 'width',
            name: 'width',
            label: 'Width',
            tooltip: 'Enter width',
            value: 0,
            min: 0,
            max: 100,
            visible: true,
            disabled: false,
            readOnly: false,
            unit: 'in'
        },
        {
            role: 'height',
            name: 'height',
            label: 'Height',
            tooltip: 'Enter height',
            value: 0,
            min: 0,
            max: 100,
            visible: true,
            disabled: false,
            readOnly: false,
            unit: 'in'
        },
        {
            role: 'radius',
            name: 'radius',
            label: 'Radius',
            tooltip: 'Enter radius',
            value: 0,
            min: 0,
            max: 100,
            visible: true,
            disabled: false,
            readOnly: false,
            unit: 'in'
        },
        {
            role: 'dimensionunit',
            name: 'dimensionUnit',
            label: 'Dimension Unit',
            tooltip: 'Select dimension unit',
            value: 1,
            visible: true,
            disabled: false,
            readOnly: false,
            options: [
                { value: 1, text: 'Inches (in)' },
                { value: 2, text: 'Feet (ft)' },
                { value: 3, text: 'Meters (m)' },
                { value: 4, text: 'Centimeters (cm)' }
            ]
        }
    ];

    beforeEach(async () => {
        DuctAreaCalculator = await extractAmdModule(
            'nmodule/honApplicationHandler/rc/honeywelldevice/components/DuctAreaCalculator',
            mocks
        );
    });

    it('renders correctly with default props', () => {
        render(<DuctAreaCalculator dataObjList={mockDataObjList} />);
        
        // Area input and unit
        expect(screen.getByTestId('number-input-area')).toBeInTheDocument();
        expect(screen.getByTestId('number-input-area-unit')).toHaveTextContent('in²');
        
        // Calculate button
        expect(screen.getByTestId('calculate-icon')).toHaveAttribute('src', '/module/honApplicationHandler/rc/images/Calculate.svg');
        
        // Help popup
        expect(screen.getByTestId('popup-container')).toBeInTheDocument();
        expect(screen.getByTestId('help-icon')).toHaveAttribute('src', '/module/honApplicationHandler/rc/images/Help.svg');
    });

    it('opens calculator modal when calculate button is clicked', () => {
        render(<DuctAreaCalculator dataObjList={mockDataObjList} />);
        
        // Click calculate button
        const calculateButton = screen.getByTestId('calculate-icon');
        fireEvent.click(calculateButton);
        
        // Modal should be visible with all its components
        const modal = screen.getByTestId('modal-component');
        expect(modal).toBeInTheDocument();
        
        // Check duct type selector
        const ductTypeSelect = within(modal).getByTestId('duct-type-select');
        expect(ductTypeSelect).toBeInTheDocument();
        
        // Check dimension unit dropdown
        const dimensionUnitDropdown = within(modal).getByTestId('dimension-unit-dropdown');
        expect(dimensionUnitDropdown).toBeInTheDocument();
    });

    it('calculates rectangular duct area correctly', () => {
        const mockOnChange = jest.fn();
        render(
            <DuctAreaCalculator 
                dataObjList={mockDataObjList} 
                onChange={mockOnChange}
            />
        );
        
        // Open calculator
        fireEvent.click(screen.getByTestId('calculate-icon'));
        
        // Set dimensions (width = 10, height = 5)
        const widthInput = screen.getByTestId('number-input-width-input');
        const heightInput = screen.getByTestId('number-input-height-input');
        
        fireEvent.change(widthInput, { target: { value: '10' } });
        fireEvent.change(heightInput, { target: { value: '5' } });
        
        // Calculate
        const calculateButtons = screen.getAllByTestId('select-button');
        const calculateButton = calculateButtons.find(btn => !btn.getAttribute('color'));
        fireEvent.click(calculateButton);
        
        // Should have called onChange with area = width * height = 50
        expect(mockOnChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                name: 'area',
                value: 50
            })
        );
    });

    it('calculates circular duct area correctly', () => {
        const mockOnChange = jest.fn();
        render(
            <DuctAreaCalculator 
                dataObjList={mockDataObjList} 
                onChange={mockOnChange}
            />
        );
        
        // Open calculator
        fireEvent.click(screen.getByTestId('calculate-icon'));
        
        // Switch to circular duct type
        const ductTypeSelect = screen.getByTestId('duct-type-select-input');
        fireEvent.change(ductTypeSelect, { target: { value: '2' } });
        
        // Set radius = 5
        const radiusInput = screen.getByTestId('number-input-radius-input');
        fireEvent.change(radiusInput, { target: { value: '5' } });
        
        // Calculate
        const calculateButtons = screen.getAllByTestId('select-button');
        const calculateButton = calculateButtons.find(btn => !btn.getAttribute('color'));
        fireEvent.click(calculateButton);
        
        // Should have called onChange with area = π * r² ≈ 78.54
        const expectedArea = Math.PI * 25; // 5² * π
        expect(mockOnChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                name: 'area',
                value: expectedArea
            })
        );
    });

    it('updates area unit when dimension unit changes', () => {
        const mockOnChange = jest.fn();
        render(
            <DuctAreaCalculator 
                dataObjList={mockDataObjList} 
                onChange={mockOnChange}
            />
        );
        
        // Open calculator
        fireEvent.click(screen.getByTestId('calculate-icon'));
        
        // Change dimension unit to feet
        const unitSelect = screen.getByTestId('dimension-unit-dropdown-input');
        fireEvent.change(unitSelect, { target: { value: '2' } }); // 2 = Feet
        
        // Calculate with some values
        const widthInput = screen.getByTestId('number-input-width-input');
        const heightInput = screen.getByTestId('number-input-height-input');
        
        fireEvent.change(widthInput, { target: { value: '10' } });
        fireEvent.change(heightInput, { target: { value: '5' } });
        
        const calculateButtons = screen.getAllByTestId('select-button');
        const calculateButton = calculateButtons.find(btn => !btn.getAttribute('color'));
        fireEvent.click(calculateButton);
        
        // Should have called onChange with area calculation result
        expect(mockOnChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                name: 'area',
                value: 50 // 10 * 5 = 50 (area calculation)
            })
        );
    });

    it('closes modal when cancel is clicked', () => {
        render(<DuctAreaCalculator dataObjList={mockDataObjList} />);
        
        // Open calculator
        fireEvent.click(screen.getByTestId('calculate-icon'));
        expect(screen.getByTestId('modal-component')).toBeInTheDocument();
        
        // Click cancel (button with color="black")
        const modal = screen.getByTestId('modal-component');
        const cancelButtons = within(modal).getAllByTestId('select-button');
        const cancelButton = cancelButtons.find(btn => btn.getAttribute('color') === 'black');
        fireEvent.click(cancelButton);
        
        // Modal should be hidden (display: none) rather than removed from DOM
        const modalAfterCancel = screen.getByTestId('modal-component');
        expect(modalAfterCancel).toHaveStyle('display: none');
    });

    it('handles readonly mode correctly', () => {
        const readOnlyDataObjList = mockDataObjList.map(obj => ({
            ...obj,
            readOnly: true
        }));

        render(<DuctAreaCalculator dataObjList={readOnlyDataObjList} />);
        
        // The area input should be disabled
        expect(screen.getByTestId('number-input-area-input')).toBeDisabled();
        
        // The calculate icon should be disabled (opacity 0.5, pointer-events none)
        const calculateIcon = screen.getByTestId('calculate-icon');
        expect(calculateIcon).toHaveAttribute('disabled');
        
        // Modal should be visible (readonly mode opens it automatically)
        const modal = screen.getByTestId('modal-component');
        expect(modal).toBeInTheDocument();
        
        // All inputs in the modal should be disabled
        expect(screen.getByTestId('duct-type-select-input')).toBeDisabled();
        expect(screen.getByTestId('dimension-unit-dropdown-input')).toBeDisabled();
        expect(screen.getByTestId('number-input-width-input')).toBeDisabled();
        expect(screen.getByTestId('number-input-height-input')).toBeDisabled();
        
        // Calculate button in modal should be disabled
        const calculateButtons = within(modal).getAllByTestId('select-button');
        const calculateButton = calculateButtons.find(btn => !btn.getAttribute('color'));
        expect(calculateButton).toBeDisabled();
    });
});
