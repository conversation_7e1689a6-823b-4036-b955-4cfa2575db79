import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': require('../../../mocks/semantic-ui-react.mock'),
    'baja!': require('../../../mocks/bajaMock').default,
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ 
        label, 
        tooltip 
    }) => (
        <div data-testid="config-label">
            <label>{label}</label>
            {tooltip && <span title={tooltip}>{tooltip}</span>}
        </div>
    ),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel': ({ 
        label,
        disabled,
        tooltip,
        clearable,
        visible,
        options = [],
        value,
        onChange,
        placeholder 
    }) => {
        const handleChange = (e) => {
            if (onChange) {
                onChange(e, { 
                    value: e.target.value, 
                    name: 'airflowUnit', 
                    dataObj: null 
                });
            }
        };
        
        return (
            <div 
                className={`dropdown-float-label ${disabled ? 'disabled' : ''} ${visible === false ? 'hidden' : ''}`}
                data-testid="dropdown-float-label"
            >
                <label>{label}</label>
                {tooltip && <span title={tooltip}>{tooltip}</span>}
                <select 
                    disabled={disabled}
                    value={value || ''}
                    onChange={handleChange}
                    data-testid="dropdown-select"
                >
                    {placeholder && <option value="">{placeholder}</option>}
                    {options
                        .filter(option => 
                            option.visible === undefined || 
                            option.visible === true || 
                            option.value === value
                        )
                        .map((option, index) => (
                            <option 
                                key={index} 
                                value={option.value}
                                data-testid={`dropdown-option-${option.value}`}
                            >
                                {option.text}
                            </option>
                        ))
                    }
                </select>
            </div>
        );
    },
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('AirflowUnit', () => {
    let AirflowUnit;
    
    beforeAll(async () => {
        try {
            AirflowUnit = await extractAmdModule(
                'src/rc/honeywelldevice/components/AirflowUnit.js',
                mocks
            );
            console.log('Successfully extracted real AirflowUnit component for testing');
        } catch (error) {
            console.error('Failed to extract AirflowUnit component:', error);
            throw error;
        }
    });

    const defaultProps = {
        name: 'airflowUnit',
        label: 'Airflow Unit',
        tooltip: 'Select airflow unit',
        value: 'cfm',
        options: [
            { text: 'CFM', value: 'cfm', visible: true },
            { text: 'L/s', value: 'ls', visible: true },
            { text: 'M³/h', value: 'm3h', visible: false },
            { text: 'Hidden Option', value: 'hidden', visible: false }
        ],
        visible: true,
        disabled: false,
        clearable: true,
        readonly: false,
        onChange: jest.fn(),
        dataObj: { id: 'test-obj' }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Clear console.log mock
        jest.spyOn(console, 'log').mockImplementation(() => {});
    });

    afterEach(() => {
        if (console.log.mockRestore) {
            console.log.mockRestore();
        }
    });

    describe('Component Rendering', () => {
        it('should render with default props', () => {
            const component = render(<AirflowUnit {...defaultProps} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
            expect(screen.getByText('Airflow Unit')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-select')).toHaveValue('cfm');
        });

        it('should render with correct label and tooltip', () => {
            const component = render(<AirflowUnit {...defaultProps} />);
            
            expect(screen.getByText('Airflow Unit')).toBeInTheDocument();
            expect(screen.getByTitle('Select airflow unit')).toBeInTheDocument();
        });

        it('should render disabled state correctly', () => {
            const component = render(<AirflowUnit {...defaultProps} disabled={true} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            expect(screen.getByTestId('dropdown-select')).toBeDisabled();
        });

        it('should render readonly state as disabled', () => {
            const component = render(<AirflowUnit {...defaultProps} readonly={true} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            expect(screen.getByTestId('dropdown-select')).toBeDisabled();
        });

        it('should handle invisible state', () => {
            const component = render(<AirflowUnit {...defaultProps} visible={false} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('hidden');
        });
    });

    describe('Options Filtering', () => {
        it('should filter options based on visible property', () => {
            const component = render(<AirflowUnit {...defaultProps} />);
            
            // Should show visible options and current value option
            expect(screen.getByTestId('dropdown-option-cfm')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-ls')).toBeInTheDocument();
            
            // Should not show hidden options (unless they're the current value)
            expect(screen.queryByTestId('dropdown-option-hidden')).not.toBeInTheDocument();
        });

        it('should show hidden option if it is the current value', () => {
            const props = {
                ...defaultProps,
                value: 'hidden'
            };
            const component = render(<AirflowUnit {...props} />);
            
            // Hidden option should be visible when it's the current value
            expect(screen.getByTestId('dropdown-option-hidden')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-select')).toHaveValue('hidden');
        });

        it('should show options with undefined visible property', () => {
            const props = {
                ...defaultProps,
                options: [
                    { text: 'CFM', value: 'cfm' }, // undefined visible
                    { text: 'L/s', value: 'ls', visible: true },
                    { text: 'Hidden', value: 'hidden', visible: false }
                ]
            };
            const component = render(<AirflowUnit {...props} />);
            
            expect(screen.getByTestId('dropdown-option-cfm')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-ls')).toBeInTheDocument();
            expect(screen.queryByTestId('dropdown-option-hidden')).not.toBeInTheDocument();
        });
    });

    describe('Event Handling', () => {
        it('should call onChange when selection changes', () => {
            const mockOnChange = jest.fn();
            const props = { ...defaultProps, onChange: mockOnChange };
            
            const component = render(<AirflowUnit {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'ls' } });
            
            expect(mockOnChange).toHaveBeenCalledTimes(1);
            expect(mockOnChange).toHaveBeenCalledWith(
                expect.any(Object),
                expect.objectContaining({
                    value: 'ls',
                    name: 'airflowUnit',
                    dataObj: { id: 'test-obj' }
                })
            );
        });

        it('should log to console when handling change', () => {
            const mockOnChange = jest.fn();
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            const props = { ...defaultProps, onChange: mockOnChange };
            
            const component = render(<AirflowUnit {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'ls' } });
            
            expect(consoleSpy).toHaveBeenCalledWith(
                'AirflowUnit: handleChange: data: ',
                expect.objectContaining({
                    value: 'ls',
                    name: 'airflowUnit',
                    dataObj: { id: 'test-obj' }
                })
            );
            
            consoleSpy.mockRestore();
        });

        it('should not call onChange when onChange prop is not provided', () => {
            const props = { ...defaultProps };
            delete props.onChange;
            
            const component = render(<AirflowUnit {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            // Should not throw error when onChange is not provided
            expect(() => {
                fireEvent.change(select, { target: { value: 'ls' } });
            }).not.toThrow();
        });

        it('should handle change with different data types', () => {
            const mockOnChange = jest.fn();
            const props = {
                ...defaultProps,
                onChange: mockOnChange,
                dataObj: { complexData: { nested: 'value' } },
                options: [
                    { text: 'CFM', value: 'cfm', visible: true },
                    { text: 'L/s', value: 'ls', visible: true },
                    { text: 'M³/h', value: 'm3h', visible: true } // Make this visible for the test
                ]
            };
            
            const component = render(<AirflowUnit {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'm3h' } });
            
            expect(mockOnChange).toHaveBeenCalledWith(
                expect.any(Object),
                expect.objectContaining({
                    value: 'm3h',
                    name: 'airflowUnit',
                    dataObj: { complexData: { nested: 'value' } }
                })
            );
        });
    });

    describe('Props Validation', () => {
        it('should handle missing props gracefully', () => {
            const minimalProps = {
                options: []
            };
            
            expect(() => {
                render(<AirflowUnit {...minimalProps} />);
            }).not.toThrow();
        });

        it('should handle empty options array', () => {
            const props = {
                ...defaultProps,
                options: []
            };
            
            const component = render(<AirflowUnit {...props} />);
            
            expect(screen.getByTestId('dropdown-select')).toBeInTheDocument();
            expect(screen.getByText('Select One Item')).toBeInTheDocument();
        });

        it('should render placeholder text', () => {
            const component = render(<AirflowUnit {...defaultProps} />);
            
            expect(screen.getByText('Select One Item')).toBeInTheDocument();
        });
    });

    describe('Component Lifecycle', () => {
        it('should call componentDidMount', () => {
            const componentDidMountSpy = jest.spyOn(AirflowUnit.prototype, 'componentDidMount');
            
            render(<AirflowUnit {...defaultProps} />);
            
            expect(componentDidMountSpy).toHaveBeenCalledTimes(1);
            
            componentDidMountSpy.mockRestore();
        });

        it('should construct component with props', () => {
            // Component construction is tested implicitly by successful rendering
            const component = render(<AirflowUnit {...defaultProps} />);
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
        });
    });

    describe('Integration with DropdownFloatLabel', () => {
        it('should pass correct props to DropdownFloatLabel', () => {
            const component = render(<AirflowUnit {...defaultProps} />);
            
            const dropdownLabel = screen.getByTestId('dropdown-float-label');
            expect(dropdownLabel).toBeInTheDocument();
            expect(dropdownLabel).not.toHaveClass('disabled');
            expect(dropdownLabel).not.toHaveClass('hidden');
        });

        it('should pass clearable prop correctly', () => {
            const props = { ...defaultProps, clearable: false };
            const component = render(<AirflowUnit {...props} />);
            
            // Component should render without errors
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
        });

        it('should handle disabled and readonly states correctly', () => {
            const propsDisabled = { ...defaultProps, disabled: true };
            const { rerender } = render(<AirflowUnit {...propsDisabled} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            
            const propsReadonly = { ...defaultProps, readonly: true };
            rerender(<AirflowUnit {...propsReadonly} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
        });
    });
});
