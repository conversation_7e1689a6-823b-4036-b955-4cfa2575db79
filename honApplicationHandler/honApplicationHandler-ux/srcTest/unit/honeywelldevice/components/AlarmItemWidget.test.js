import React from 'react';
import { render, screen } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': require('../../../mocks/semantic-ui-react.mock'),
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('AlarmItemWidget Component', () => {
    let AlarmItemWidget;

    beforeEach(() => {
        // For code coverage: register the real component with the correct full path
        global.__AMD_MODULES__ = global.__AMD_MODULES__ || {};
        
        // Extract the AMD module - this is the key step that loads the actual component
        AlarmItemWidget = extractAmdModule(
            'nmodule/honApplicationHandler/rc/honeywelldevice/components/AlarmItemWidget',
            mocks
        );
    });

    // Helper function to set up data-testid attributes for easier testing
    const setupDataTestIds = (wrapper) => {
        if (!wrapper || !wrapper.querySelectorAll) return;

        // Add data-testid to the main container if not present
        if (wrapper.getAttribute('data-testid') !== 'alarm-item-widget') {
            wrapper.setAttribute('data-testid', 'alarm-item-widget');
        }

        // Add data-testid to labels for alarm name and value
        const labels = wrapper.querySelectorAll('label');
        if (labels.length >= 1 && !labels[0].hasAttribute('data-testid')) {
            labels[0].setAttribute('data-testid', 'alarm-name');
        }
        if (labels.length >= 2 && !labels[1].hasAttribute('data-testid')) {
            labels[1].setAttribute('data-testid', 'alarm-value');
        }

        // Add data-testid to svg icons
        const svgs = wrapper.querySelectorAll('svg');
        if (svgs.length > 0) {
            svgs.forEach(svg => {
                svg.setAttribute('data-testid', 'info-icon');
            });
        }

        // Add data-testid to tooltip content
        const popupContent = wrapper.querySelector('[data-testid="popup-content"]');
        if (popupContent && popupContent.firstChild) {
            popupContent.firstChild.setAttribute('data-testid', 'tooltip-content');
        }
    };

    it('renders correctly with Normal state', () => {
        const { container } = render(
            <AlarmItemWidget 
                alarmName="Temperature Alarm" 
                valueText="Normal" 
                tooltip="This is a normal state"
            />
        );

        // Set up data-testid attributes
        setupDataTestIds(container.firstChild);

        // Now we can use the data-testids to query elements
        expect(container.querySelector('[data-testid="alarm-item-widget"]')).toBeInTheDocument();
        
        // For the labels, we can check their content directly
        const labels = container.querySelectorAll('label');
        expect(labels[0].textContent).toContain('Temperature Alarm');
        expect(labels[1].textContent).toContain('Normal');
        
        // Make sure the value is not colored for normal state
        expect(labels[1].style.color).not.toBe('rgb(238, 49, 36)'); // Not red
        expect(labels[1].style.color).not.toBe('rgb(243, 112, 33)'); // Not orange
        
        // No info icon for normal state
        expect(container.querySelector('svg')).not.toBeInTheDocument();
    });

    it('renders correctly with Inactive state', () => {
        const { container } = render(
            <AlarmItemWidget 
                alarmName="Pressure Alarm" 
                valueText="Inactive" 
                tooltip="This is an inactive state"
            />
        );

        // Set up data-testid attributes
        setupDataTestIds(container.firstChild);

        // Check the labels
        const labels = container.querySelectorAll('label');
        expect(labels[0].textContent).toContain('Pressure Alarm');
        expect(labels[1].textContent).toContain('Inactive');
        
        // Make sure the value is not colored for inactive state
        expect(labels[1].style.color).not.toBe('rgb(238, 49, 36)'); // Not red
        expect(labels[1].style.color).not.toBe('rgb(243, 112, 33)'); // Not orange
        
        // No info icon for inactive state
        expect(container.querySelector('svg')).not.toBeInTheDocument();
    });

    it('renders correctly with Active(High) state', () => {
        const { container } = render(
            <AlarmItemWidget 
                alarmName="Critical Alarm" 
                valueText="Active(High)" 
                tooltip="This is a high priority alarm"
            />
        );

        // Set up data-testid attributes
        setupDataTestIds(container.firstChild);

        // Check the content
        const labels = container.querySelectorAll('label');
        expect(labels[0].textContent).toContain('Critical Alarm');
        expect(labels[1].textContent).toContain('Active(High)');
        
        // Check that the tooltip exists and contains the tooltip text
        expect(container.querySelector('[data-testid="popup-container"]')).toBeInTheDocument();
        
        // SVG icon should be present for active alarms
        expect(container.querySelector('svg')).toBeInTheDocument();
        
        // Red color for high priority (ee3124 is a red color)
        expect(labels[1].style.color).toBe('rgb(238, 49, 36)');
    });

    it('renders correctly with other active states', () => {
        const { container } = render(
            <AlarmItemWidget 
                alarmName="Warning Alarm" 
                valueText="Active(Low)" 
                tooltip="This is a low priority alarm"
            />
        );

        // Set up data-testid attributes
        setupDataTestIds(container.firstChild);

        // Check the content
        const labels = container.querySelectorAll('label');
        expect(labels[0].textContent).toContain('Warning Alarm');
        expect(labels[1].textContent).toContain('Active(Low)');
        
        // Check that the tooltip exists
        expect(container.querySelector('[data-testid="popup-container"]')).toBeInTheDocument();
        
        // SVG icon should be present for active alarms
        expect(container.querySelector('svg')).toBeInTheDocument();
        
        // Orange color for other states (f37021 is an orange color)
        expect(labels[1].style.color).toBe('rgb(243, 112, 33)');
    });

    it('does not render when visible is false', () => {
        const { container } = render(
            <AlarmItemWidget 
                alarmName="Hidden Alarm" 
                valueText="Normal" 
                visible={false}
            />
        );

        // The component should still render but with display:none
        expect(container.firstChild.style.display).toBe('none');
    });
});
