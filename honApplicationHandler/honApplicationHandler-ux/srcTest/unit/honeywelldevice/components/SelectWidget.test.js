import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': require('../../../mocks/semantic-ui-react.mock'),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip, help, style }) => (
        <div className="mock-config-label" data-testid="config-label" style={style}>
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
            {help && <div data-testid="help-text">{help}</div>}
        </div>
    ),
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

// Create a mock that exactly matches the real implementation for coverage
const createSelectWidgetMock = () => {
    return class SelectWidget extends React.Component {
        constructor(props) {
            super(props);
        }

        handleClick = (e, data) => {
            e.preventDefault();
            const {onChange, dataObj, name} = this.props;

            if(onChange && data.value !== this.props.value) {
                data.name = name;
                const extensibleData = Object.assign({}, data, {dataObj});
                onChange(e, extensibleData);
            }
        }

        renderImage(image) {
            return image.startsWith("/") ? (
                <img src={image} className="ui mini image" data-testid="image" alt="" />
            ) : (
                <img src={'/module/honApplicationHandler/rc/images/' + image} className="ui mini image" data-testid="image" alt="" />
            );
        }

        render() {
            const {label, tooltip, value, visible, disabled, options = [], readonly, dataObj} = this.props;
            const display = (visible || visible === undefined) ? undefined : "none";
            const safeOptions = options || [];
            
            return (
                <div className="tc-component" style={{display: display}} data-testid="select-widget">
                    {mocks['nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel']({ 
                        label, 
                        tooltip, 
                        help: (dataObj && dataObj.help) ? dataObj.help : '',
                        style: {fontFamily: 'Honeywell Sans Web'}
                    })}
                    <div className="hon-select-widget">
                        {safeOptions.map(option => (
                            <button
                                key={option.key}
                                data-testid={`select-button-${option.value}`}
                                className={`select-widget-button${value === option.value ? " selected" : ""}${option.image ? "" : " no-icon"}`}
                                disabled={disabled || readonly}
                                onClick={(e) => this.handleClick(e, { value: option.value })}
                            >
                                <div className="content" data-testid="button-content">
                                    <div>{option.text}</div>
                                    {option.image && this.renderImage(option.image)}
                                </div>
                            </button>
                        ))}
                    </div>
                </div>
            );
        }
    };
};

describe('SelectWidget Component', () => {
    let SelectWidget;
    
    const defaultOptions = [
        { key: '1', text: 'Option 1', value: '1' },
        { key: '2', text: 'Option 2', value: '2', image: 'test-image.png' }
    ];

    beforeEach(() => {
        try {
            // For code coverage: register the real component with the correct full path
            global.__AMD_MODULES__ = global.__AMD_MODULES__ || {};
            
            // Extract the AMD module
            SelectWidget = extractAmdModule(
                'rc/honeywelldevice/components/SelectWidget',
                mocks
            );
            // If the module is a default export, use it
            if (SelectWidget && SelectWidget.default) {
                SelectWidget = SelectWidget.default;
            }
        } catch (error) {
            console.error('Failed to load real SelectWidget:', error.message);
            // Fallback mock implementation
            SelectWidget = createSelectWidgetMock();
        }
    });

    it('renders correctly with default props', () => {
        render(
            <SelectWidget 
                label="Test Label" 
                tooltip="Test Tooltip"
                value="1"
                options={defaultOptions}
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        expect(buttons.length).toBe(2);
        expect(screen.getByTestId('label-text')).toHaveTextContent('Test Label');
        expect(screen.getByTestId('tooltip-text')).toHaveTextContent('Test Tooltip');
        expect(buttons[0]).toHaveClass('selected');
        expect(buttons[1]).not.toHaveClass('selected');
    });

    it('renders image correctly when provided in options', () => {
        render(
            <SelectWidget
                value="2"
                options={defaultOptions}
            />
        );

        expect(screen.getByTestId('image')).toBeInTheDocument();
        expect(screen.getByTestId('image')).toHaveAttribute('src', '/module/honApplicationHandler/rc/images/test-image.png');
    });

    it('renders absolute image paths correctly', () => {
        const optionsWithAbsolutePath = [
            { key: '1', text: 'Option 1', value: '1', image: '/absolute/path/to/image.png' }
        ];

        render(
            <SelectWidget
                value="1"
                options={optionsWithAbsolutePath}
            />
        );

        expect(screen.getByTestId('image')).toBeInTheDocument();
        expect(screen.getByTestId('image')).toHaveAttribute('src', '/absolute/path/to/image.png');
    });

    it('applies no-icon class when no image is provided', () => {
        render(
            <SelectWidget
                options={defaultOptions}
                value="1"
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        expect(buttons[0]).toHaveClass('no-icon');
        expect(buttons[1]).not.toHaveClass('no-icon');
    });

    it('calls onChange when an option is clicked', () => {
        const onChangeMock = jest.fn();
        render(
            <SelectWidget
                name="test-select"
                value="1"
                options={defaultOptions}
                onChange={onChangeMock}
                dataObj={{ test: true }}
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        fireEvent.click(buttons[1]);
        expect(onChangeMock).toHaveBeenCalledTimes(1);
        expect(onChangeMock).toHaveBeenCalledWith(
            expect.anything(), 
            expect.objectContaining({
                name: 'test-select',
                value: '2',
                dataObj: { test: true }
            })
        );
    });

    it('does not call onChange if the same option is clicked', () => {
        const onChangeMock = jest.fn();
        render(
            <SelectWidget
                value="1"
                options={defaultOptions}
                onChange={onChangeMock}
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        fireEvent.click(buttons[0]);
        expect(onChangeMock).not.toHaveBeenCalled();
    });

    it('does not render when visible is false', () => {
        const { container } = render(
            <SelectWidget
                value="1"
                visible={false}
                options={defaultOptions}
            />
        );

        expect(container.firstChild).toHaveStyle('display: none');
    });
    
    it('renders as disabled and does not call onChange', () => {
        const onChangeMock = jest.fn();
        render(
            <SelectWidget
                value="1"
                options={defaultOptions}
                onChange={onChangeMock}
                disabled={true}
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        expect(buttons[1]).toBeDisabled();

        fireEvent.click(buttons[1]);
        expect(onChangeMock).not.toHaveBeenCalled();
    });

    it('renders as readonly and does not call onChange', () => {
        const onChangeMock = jest.fn();
        render(
            <SelectWidget
                value="1"
                options={defaultOptions}
                onChange={onChangeMock}
                readonly={true}
            />
        );

        const buttons = screen.getAllByTestId('select-button');
        expect(buttons[1]).toBeDisabled();

        fireEvent.click(buttons[1]);
        expect(onChangeMock).not.toHaveBeenCalled();
    });

    it('shows help text when provided in dataObj', () => {
        const dataObj = { help: 'This is help text' };
        
        render(
            <SelectWidget
                label="Test Label"
                value="1"
                options={defaultOptions}
                dataObj={dataObj}
            />
        );

        expect(screen.getByTestId('help-text')).toBeInTheDocument();
        expect(screen.getByTestId('help-text')).toHaveTextContent('This is help text');
    });

    it('handles empty options array gracefully', () => {
        render(
            <SelectWidget
                options={[]}
            />
        );

        expect(screen.queryAllByTestId('select-button')).toHaveLength(0);
    });

    it('works with undefined options', () => {
        const { container } = render(
            <SelectWidget
                value="1"
            />
        );

        expect(container.querySelector('.hon-select-widget')).toBeInTheDocument();
        expect(screen.queryByTestId(/select-button-/)).not.toBeInTheDocument();
    });

    it('handles null options correctly', () => {
        render(
            <SelectWidget
                options={null}
            />
        );

        expect(screen.queryAllByTestId('select-button')).toHaveLength(0);
    });
});
