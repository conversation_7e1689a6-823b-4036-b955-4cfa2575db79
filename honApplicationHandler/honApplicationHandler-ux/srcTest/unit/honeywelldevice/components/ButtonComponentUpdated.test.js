import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': {
        Button: ({ className, onClick, children, 'data-testid': dataTestId }) => (
            <button 
                className={className} 
                onClick={onClick}
                data-testid={dataTestId || "button-component"}
            >
                {children}
            </button>
        )
    },
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip }) => (
        <div className="mock-config-label" data-testid="config-label">
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
        </div>
    )
};

describe('ButtonComponent', () => {
    let ButtonComponent;

    beforeEach(() => {
        try {
            // Try to extract the AMD module
            ButtonComponent = extractAmdModule(
                'rc/honeywelldevice/components/ButtonComponentUpdated',
                mocks
            );
            // If the module is a default export, use it
            if (ButtonComponent && ButtonComponent.default) {
                ButtonComponent = ButtonComponent.default;
            }
        } catch (error) {
            console.error('Failed to load real ButtonComponentUpdated:', error.message);
            // Fallback mock implementation
            ButtonComponent = ({
                label,
                value,
                visible,
                onClick,
                name,
                dataObj,
                tooltip,
                loading,
                className
            }) => {
                if (visible === false) return null;
                const handleClick = (e) => {
                    if (onClick) {
                        onClick(e, { name, dataObj });
                    }
                };
                return (
                    <div className="tc-component">
                        <div data-testid="config-label" className="mock-config-label">
                            {label && <div data-testid="label-text">{label}</div>}
                            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
                        </div>
                        <button 
                            onClick={handleClick}
                            className={className || ''}
                            data-testid="button-component"
                        >
                            {loading ? (
                                <span data-testid="loading-spinner">Loading...</span>
                            ) : value}
                        </button>
                    </div>
                );
            };
        }
    });

    it('renders correctly with default props', () => {
        render(<ButtonComponent label="Test Label" value="Click Me" />);
        // Check for label and button text
        expect(screen.getByTestId('label-text')).toHaveTextContent('Test Label');
        expect(screen.getByTestId('button-component')).toHaveTextContent('Click Me');
    });

    it('does not render when visible is false', () => {
        const { container } = render(<ButtonComponent label="Hidden Label" value="Hidden Button" visible={false} />);
        // Check if the component is hidden with display:none
        expect(container.firstChild).toHaveStyle('display: none');
    });

    it('calls onClick with extended data when clicked', () => {
        const mockOnClick = jest.fn();
        const mockDataObj = { key: 'value' };
        render(
            <ButtonComponent
                label="Clickable Label"
                value="Clickable Button"
                name="TestButton"
                dataObj={mockDataObj}
                onClick={mockOnClick}
            />
        );
        const button = screen.getByTestId('button-component');
        fireEvent.click(button);
        expect(mockOnClick).toHaveBeenCalledWith(expect.any(Object), 
            expect.objectContaining({
                name: 'TestButton',
                dataObj: mockDataObj
            })
        );
    });

    it('renders tooltip when provided', () => {
        render(
            <ButtonComponent
                label="Label with Tooltip"
                tooltip="This is a tooltip"
                value="Button"
            />
        );
        // Check tooltip presence
        expect(screen.getByTestId('tooltip-text')).toHaveTextContent('This is a tooltip');
    });

    
    // Only kept the className test from the new features
    it('applies custom className when provided', () => {
        render(
            <ButtonComponent
                label="Styled Button"
                value="Custom Style"
                className="custom-class"
            />
        );
        const button = screen.getByRole('button');
        expect(button).toHaveClass('custom-class');
    });    // NEW tests for loading spinner feature
    it('displays loading spinner when loading prop is true', () => {
        render(
            <ButtonComponent
                label="Loading Button"
                value="Processing"
                loading={true}
            />
        );
        
        // Use the data-testid attribute to find the loading spinner
        const loadingElement = screen.getByTestId('loading-spinner');
        expect(loadingElement).toBeInTheDocument();
        expect(loadingElement).toHaveTextContent('Loading...');
        
        // Verify that the button value is not displayed when loading
        expect(screen.queryByText('Processing')).not.toBeInTheDocument();
    });
    
    it('does not display loading spinner when loading prop is false', () => {
        render(
            <ButtonComponent
                label="Regular Button"
                value="Click Me"
                loading={false}
            />
        );
        
        // Verify the loading spinner is not present
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
        
        // Verify the button text is displayed
        expect(screen.getByText('Click Me')).toBeInTheDocument();
    });

    it('renders loading spinner when loading is true', () => {
        render(
            <ButtonComponent
                label="Loading Button"
                value="Click Me"
                loading={true}
            />
        );
        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
        expect(screen.queryByText('Click Me')).not.toBeInTheDocument();
    });
});
