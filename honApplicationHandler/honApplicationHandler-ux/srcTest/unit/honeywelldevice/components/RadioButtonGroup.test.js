import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Use the centralized semantic-ui-react mock
const semanticUiReactMock = require('../../../mocks/semantic-ui-react.mock');

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': semanticUiReactMock,
    'lex!honApplicationHandler': [{
        get: jest.fn(key => key)
    }],
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip, className }) => (
        <div className={`config-label ${className || ''}`} data-testid="config-label">
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
        </div>
    ),
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('RadioButtonGroup Component', () => {
    let RadioButtonGroup;

    beforeEach(async () => {
        jest.resetModules();
        try {
            
             RadioButtonGroup = await extractAmdModule(
                'rc/honeywelldevice/components/RadioButtonGroup',
                mocks
            );
            
            if (!RadioButtonGroup) {
                throw new Error('AMD module extraction returned undefined');
            }
        } catch (error) {
            console.error('Failed to load real RadioButtonGroup component:', error.message);
            // Create a fallback implementation if extraction fails
            const { Checkbox } = mocks['semantic-ui-react'];
            const ConfigLabel = mocks['nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel'];
            
            RadioButtonGroup = ({ 
                label, 
                tooltip, 
                value, 
                options = [], 
                visible, 
                disabled, 
                readonly,
                onChange,
                name,
                group,
                dataObj,
                ...rest
            }) => {
                const handleChange = (e, data) => {
                    if(onChange) {
                        data.name = name || group;
                        data.dataObj = dataObj;
                        onChange(e, data);
                    }
                };

                const display = (visible || visible === undefined) ? undefined : "none";
                
                return (
                    <div className="tc-component" style={{display: display}} data-testid="radio-group-container">
                        <ConfigLabel label={label} tooltip={tooltip} />
                        <div className="radio-button-group">
                            {options.map((option, index) => (
                                <Checkbox
                                    key={index}
                                    radio
                                    className="hon-radio-button"
                                    label={option.text}
                                    name={name || group}
                                    value={option.value}
                                    checked={value === option.value}
                                    disabled={disabled || readonly}
                                    onChange={handleChange}
                                    data-testid="checkbox"
                                />
                            ))}
                        </div>
                    </div>
                );
            };
            RadioButtonGroup.displayName = 'RadioButtonGroup';
        }
    });

    const mockOptions = [
        { key: '1', text: 'Option 1', value: 'value1' },
        { key: '2', text: 'Option 2', value: 'value2' },
        { key: '3', text: 'Option 3', value: 'value3' }
    ];

    it('renders correctly with default props', () => {
        render(
            <RadioButtonGroup 
                label="Test Group"
                options={mockOptions}
                group="testGroup"
            />
        );

        expect(screen.getByTestId('label-text')).toHaveTextContent('Test Group');
        
        // Check that each radio button is rendered with the correct testid
        const radioButton1 = screen.getByTestId('radio-option-value1');
        const radioButton2 = screen.getByTestId('radio-option-value2');
        const radioButton3 = screen.getByTestId('radio-option-value3');
        
        expect(radioButton1).toBeInTheDocument();
        expect(radioButton2).toBeInTheDocument();
        expect(radioButton3).toBeInTheDocument();

        // Check that each radio button has correct label and input
        expect(radioButton1.querySelector('[data-testid="checkbox-label"]')).toHaveTextContent('Option 1');
        expect(radioButton1.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('value', 'value1');
        expect(radioButton1.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('name', 'testGroup');
        
        expect(radioButton2.querySelector('[data-testid="checkbox-label"]')).toHaveTextContent('Option 2');
        expect(radioButton2.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('value', 'value2');
        expect(radioButton2.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('name', 'testGroup');
        
        expect(radioButton3.querySelector('[data-testid="checkbox-label"]')).toHaveTextContent('Option 3');
        expect(radioButton3.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('value', 'value3');
        expect(radioButton3.querySelector('[data-testid="checkbox-input"]')).toHaveAttribute('name', 'testGroup');
    });

    it('does not render when visible is false', () => {
        const { container } = render(
            <RadioButtonGroup 
                label="Hidden Group"
                options={mockOptions}
                group="testGroup"
                visible={false}
            />
        );

        expect(container.firstChild).toHaveStyle('display: none');
    });

    it('renders with tooltip when provided', () => {
        render(
            <RadioButtonGroup 
                label="Group with Tooltip"
                tooltip="This is a helpful tooltip"
                options={mockOptions}
                group="testGroup"
            />
        );

        expect(screen.getByTestId('tooltip-text')).toHaveTextContent('This is a helpful tooltip');
    });

    it('disables radio buttons when readonly is true', () => {
        render(
            <RadioButtonGroup 
                label="Readonly Group"
                options={mockOptions}
                group="testGroup"
                readonly={true}
            />
        );

        const radioInputs = screen.getAllByRole('radio');
        radioInputs.forEach(input => {
            expect(input).toBeDisabled();
        });
    });

    it('calls onChange with correct data when option is selected', () => {
        const mockOnChange = jest.fn();
        const mockDataObj = { someData: 'test' };

        render(
            <RadioButtonGroup 
                label="Interactive Group"
                options={mockOptions}
                group="testGroup"
                name="testName"
                dataObj={mockDataObj}
                onChange={mockOnChange}
            />
        );

        const radioButton2 = screen.getByTestId('radio-option-value2');
        fireEvent.click(radioButton2.querySelector('input'));

        expect(mockOnChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({
                name: 'testName',
                dataObj: mockDataObj,
                value: 'value2'
            })
        );
    });

    it('marks the correct option as checked based on value prop', () => {
        render(
            <RadioButtonGroup 
                label="Selected Group"
                options={mockOptions}
                group="testGroup"
                value="value2"
            />
        );

        const radioInputs = screen.getAllByRole('radio');
        expect(radioInputs[0]).not.toBeChecked();
        expect(radioInputs[1]).toBeChecked();
        expect(radioInputs[2]).not.toBeChecked();
    });

    it('groups radio buttons correctly', () => {
        render(
            <RadioButtonGroup 
                label="Grouped Buttons"
                options={mockOptions}
                group="testGroup"
            />
        );

        const radioInputs = screen.getAllByRole('radio');
        radioInputs.forEach(input => {
            expect(input).toHaveAttribute('name', 'testGroup');
        });
    });

    it('applies hon-radio-button class to each radio button', () => {
        render(
            <RadioButtonGroup 
                label="Styled Group"
                options={mockOptions}
                group="testGroup"
            />
        );

        // The real component uses specific testids for each option
        const radioButton1 = screen.getByTestId('radio-option-value1');
        const radioButton2 = screen.getByTestId('radio-option-value2');
        const radioButton3 = screen.getByTestId('radio-option-value3');
        
        expect(radioButton1).toHaveClass('hon-radio-button');
        expect(radioButton2).toHaveClass('hon-radio-button');
        expect(radioButton3).toHaveClass('hon-radio-button');
    });
});
