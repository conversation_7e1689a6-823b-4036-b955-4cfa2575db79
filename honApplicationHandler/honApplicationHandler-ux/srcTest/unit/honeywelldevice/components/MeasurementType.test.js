import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': require('../../../mocks/semantic-ui-react.mock'),
    'baja!': require('../../../mocks/bajaMock').default,
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ 
        label, 
        tooltip 
    }) => (
        <div data-testid="config-label">
            <label>{label}</label>
            {tooltip && <span title={tooltip}>{tooltip}</span>}
        </div>
    ),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/DropdownFloatLabel': ({ 
        label,
        disabled,
        tooltip,
        clearable,
        visible,
        options = [],
        value,
        onChange,
        placeholder 
    }) => {
        const handleChange = (e) => {
            if (onChange) {
                onChange(e, { 
                    value: e.target.value, 
                    name: 'measurementType', 
                    dataObj: null 
                });
            }
        };
        
        return (
            <div 
                className={`dropdown-float-label ${disabled ? 'disabled' : ''} ${visible === false ? 'hidden' : ''}`}
                data-testid="dropdown-float-label"
            >
                <label>{label}</label>
                {tooltip && <span title={tooltip}>{tooltip}</span>}
                <select 
                    disabled={disabled}
                    value={value || ''}
                    onChange={handleChange}
                    data-testid="dropdown-select"
                >
                    {placeholder && <option value="">{placeholder}</option>}
                    {options
                        .filter(option => 
                            option.visible === undefined || 
                            option.visible === true || 
                            option.value === value
                        )
                        .map((option, index) => (
                            <option 
                                key={index} 
                                value={option.value}
                                data-testid={`dropdown-option-${option.value}`}
                            >
                                {option.text}
                            </option>
                        ))
                    }
                </select>
            </div>
        );
    },
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('MeasurementType', () => {
    let MeasurementType;
    
    beforeAll(async () => {
        try {
            MeasurementType = await extractAmdModule(
                'src/rc/honeywelldevice/components/MeasurementType.js',
                mocks
            );
            console.log('Successfully extracted real MeasurementType component for testing');
        } catch (error) {
            console.error('Failed to extract MeasurementType component:', error);
            throw error;
        }
    });

    const defaultProps = {
        name: 'measurementType',
        label: 'Measurement Type',
        tooltip: 'Select measurement type',
        value: 'temperature',
        options: [
            { text: 'Temperature', value: 'temperature', visible: true },
            { text: 'Pressure', value: 'pressure', visible: true },
            { text: 'Flow', value: 'flow', visible: false },
            { text: 'Hidden Type', value: 'hidden', visible: false }
        ],
        visible: true,
        disabled: false,
        clearable: true,
        readonly: false,
        onChange: jest.fn(),
        dataObj: { id: 'measurement-obj' }
    };

    beforeEach(() => {
        jest.clearAllMocks();
        // Clear console.log mock
        jest.spyOn(console, 'log').mockImplementation(() => {});
    });

    afterEach(() => {
        if (console.log.mockRestore) {
            console.log.mockRestore();
        }
    });

    describe('Component Rendering', () => {
        it('should render with default props', () => {
            const component = render(<MeasurementType {...defaultProps} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
            expect(screen.getByText('Measurement Type')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-select')).toHaveValue('temperature');
        });

        it('should render with correct label and tooltip', () => {
            const component = render(<MeasurementType {...defaultProps} />);
            
            expect(screen.getByText('Measurement Type')).toBeInTheDocument();
            expect(screen.getByTitle('Select measurement type')).toBeInTheDocument();
        });

        it('should render disabled state correctly', () => {
            const component = render(<MeasurementType {...defaultProps} disabled={true} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            expect(screen.getByTestId('dropdown-select')).toBeDisabled();
        });

        it('should render readonly state as disabled', () => {
            const component = render(<MeasurementType {...defaultProps} readonly={true} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            expect(screen.getByTestId('dropdown-select')).toBeDisabled();
        });

        it('should handle invisible state', () => {
            const component = render(<MeasurementType {...defaultProps} visible={false} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('hidden');
        });
    });

    describe('Options Filtering', () => {
        it('should filter options based on visible property', () => {
            const component = render(<MeasurementType {...defaultProps} />);
            
            // Should show visible options and current value option
            expect(screen.getByTestId('dropdown-option-temperature')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-pressure')).toBeInTheDocument();
            
            // Should not show hidden options (unless they're the current value)
            expect(screen.queryByTestId('dropdown-option-hidden')).not.toBeInTheDocument();
        });

        it('should show hidden option if it is the current value', () => {
            const props = {
                ...defaultProps,
                value: 'hidden'
            };
            const component = render(<MeasurementType {...props} />);
            
            // Hidden option should be visible when it's the current value
            expect(screen.getByTestId('dropdown-option-hidden')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-select')).toHaveValue('hidden');
        });

        it('should show options with undefined visible property', () => {
            const props = {
                ...defaultProps,
                options: [
                    { text: 'Temperature', value: 'temperature' }, // undefined visible
                    { text: 'Pressure', value: 'pressure', visible: true },
                    { text: 'Hidden', value: 'hidden', visible: false }
                ]
            };
            const component = render(<MeasurementType {...props} />);
            
            expect(screen.getByTestId('dropdown-option-temperature')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-pressure')).toBeInTheDocument();
            expect(screen.queryByTestId('dropdown-option-hidden')).not.toBeInTheDocument();
        });

        it('should handle complex measurement types', () => {
            const props = {
                ...defaultProps,
                options: [
                    { text: 'Analog Input', value: 'analog', visible: true },
                    { text: 'Digital Input', value: 'digital', visible: true },
                    { text: 'Binary Output', value: 'binary', visible: false },
                    { text: 'Multistate', value: 'multistate', visible: true }
                ],
                value: 'analog'
            };
            const component = render(<MeasurementType {...props} />);
            
            expect(screen.getByTestId('dropdown-option-analog')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-digital')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-multistate')).toBeInTheDocument();
            expect(screen.queryByTestId('dropdown-option-binary')).not.toBeInTheDocument();
        });
    });

    describe('Event Handling', () => {
        it('should call onChange when selection changes', () => {
            const mockOnChange = jest.fn();
            const props = { ...defaultProps, onChange: mockOnChange };
            
            const component = render(<MeasurementType {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'pressure' } });
            
            expect(mockOnChange).toHaveBeenCalledTimes(1);
            expect(mockOnChange).toHaveBeenCalledWith(
                expect.any(Object),
                expect.objectContaining({
                    value: 'pressure',
                    name: 'measurementType',
                    dataObj: { id: 'measurement-obj' }
                })
            );
        });

        it('should log to console when handling change', () => {
            const mockOnChange = jest.fn();
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
            const props = { ...defaultProps, onChange: mockOnChange };
            
            const component = render(<MeasurementType {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'pressure' } });
            
            expect(consoleSpy).toHaveBeenCalledWith(
                'MeasurementType: handleChange: data: ',
                expect.objectContaining({
                    value: 'pressure',
                    name: 'measurementType',
                    dataObj: { id: 'measurement-obj' }
                })
            );
            
            consoleSpy.mockRestore();
        });

        it('should not call onChange when onChange prop is not provided', () => {
            const props = { ...defaultProps };
            delete props.onChange;
            
            const component = render(<MeasurementType {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            // Should not throw error when onChange is not provided
            expect(() => {
                fireEvent.change(select, { target: { value: 'pressure' } });
            }).not.toThrow();
        });

        it('should handle change with different measurement types', () => {
            const mockOnChange = jest.fn();
            const props = {
                ...defaultProps,
                onChange: mockOnChange,
                dataObj: { sensorType: 'thermistor', calibration: 'factory' },
                options: [
                    { text: 'Temperature', value: 'temperature', visible: true },
                    { text: 'Pressure', value: 'pressure', visible: true },
                    { text: 'Flow', value: 'flow', visible: true } // Make this visible for the test
                ]
            };
            
            const component = render(<MeasurementType {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'flow' } });
            
            expect(mockOnChange).toHaveBeenCalledWith(
                expect.any(Object),
                expect.objectContaining({
                    value: 'flow',
                    name: 'measurementType',
                    dataObj: { sensorType: 'thermistor', calibration: 'factory' }
                })
            );
        });

        it('should handle multiple rapid changes', () => {
            const mockOnChange = jest.fn();
            const props = { ...defaultProps, onChange: mockOnChange };
            
            const component = render(<MeasurementType {...props} />);
            const select = screen.getByTestId('dropdown-select');
            
            fireEvent.change(select, { target: { value: 'pressure' } });
            fireEvent.change(select, { target: { value: 'temperature' } });
            fireEvent.change(select, { target: { value: 'pressure' } });
            
            expect(mockOnChange).toHaveBeenCalledTimes(3);
            expect(mockOnChange).toHaveBeenLastCalledWith(
                expect.any(Object),
                expect.objectContaining({
                    value: 'pressure',
                    name: 'measurementType'
                })
            );
        });
    });

    describe('Props Validation', () => {
        it('should handle missing props gracefully', () => {
            const minimalProps = {
                options: []
            };
            
            expect(() => {
                render(<MeasurementType {...minimalProps} />);
            }).not.toThrow();
        });

        it('should handle empty options array', () => {
            const props = {
                ...defaultProps,
                options: []
            };
            
            const component = render(<MeasurementType {...props} />);
            
            expect(screen.getByTestId('dropdown-select')).toBeInTheDocument();
            expect(screen.getByText('Select One Item')).toBeInTheDocument();
        });

        it('should render placeholder text', () => {
            const component = render(<MeasurementType {...defaultProps} />);
            
            expect(screen.getByText('Select One Item')).toBeInTheDocument();
        });

        it('should handle null or undefined values', () => {
            const props = {
                ...defaultProps,
                value: null
            };
            
            expect(() => {
                render(<MeasurementType {...props} />);
            }).not.toThrow();
        });
    });

    describe('Component Lifecycle', () => {
        it('should call componentDidMount', () => {
            const componentDidMountSpy = jest.spyOn(MeasurementType.prototype, 'componentDidMount');
            
            render(<MeasurementType {...defaultProps} />);
            
            expect(componentDidMountSpy).toHaveBeenCalledTimes(1);
            
            componentDidMountSpy.mockRestore();
        });

        it('should construct component with props', () => {
            // Component construction is tested implicitly by successful rendering
            const component = render(<MeasurementType {...defaultProps} />);
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
        });
    });

    describe('Integration with DropdownFloatLabel', () => {
        it('should pass correct props to DropdownFloatLabel', () => {
            const component = render(<MeasurementType {...defaultProps} />);
            
            const dropdownLabel = screen.getByTestId('dropdown-float-label');
            expect(dropdownLabel).toBeInTheDocument();
            expect(dropdownLabel).not.toHaveClass('disabled');
            expect(dropdownLabel).not.toHaveClass('hidden');
        });

        it('should pass clearable prop correctly', () => {
            const props = { ...defaultProps, clearable: false };
            const component = render(<MeasurementType {...props} />);
            
            // Component should render without errors
            expect(screen.getByTestId('dropdown-float-label')).toBeInTheDocument();
        });

        it('should handle disabled and readonly states correctly', () => {
            const propsDisabled = { ...defaultProps, disabled: true };
            const { rerender } = render(<MeasurementType {...propsDisabled} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
            
            const propsReadonly = { ...defaultProps, readonly: true };
            rerender(<MeasurementType {...propsReadonly} />);
            
            expect(screen.getByTestId('dropdown-float-label')).toHaveClass('disabled');
        });

        it('should handle measurement-specific scenarios', () => {
            const props = {
                ...defaultProps,
                label: 'Point Type',
                tooltip: 'Select the point measurement type',
                options: [
                    { text: 'Temperature Sensor', value: 'temp_sensor', visible: true },
                    { text: 'Humidity Sensor', value: 'humidity_sensor', visible: true },
                    { text: 'CO2 Sensor', value: 'co2_sensor', visible: true },
                    { text: 'Advanced Sensor', value: 'advanced', visible: false }
                ],
                value: 'temp_sensor'
            };
            
            const component = render(<MeasurementType {...props} />);
            
            expect(screen.getByText('Point Type')).toBeInTheDocument();
            expect(screen.getByTitle('Select the point measurement type')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-temp_sensor')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-humidity_sensor')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-co2_sensor')).toBeInTheDocument();
            expect(screen.queryByTestId('dropdown-option-advanced')).not.toBeInTheDocument();
        });
    });

    describe('Edge Cases', () => {
        it('should handle options with special characters', () => {
            const props = {
                ...defaultProps,
                options: [
                    { text: 'Temperature (°C)', value: 'temp_c', visible: true },
                    { text: 'Temperature (°F)', value: 'temp_f', visible: true },
                    { text: 'Pressure (kPa)', value: 'pressure_kpa', visible: true }
                ],
                value: 'temp_c'
            };
            
            const component = render(<MeasurementType {...props} />);
            
            expect(screen.getByText('Temperature (°C)')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-temp_c')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-temp_f')).toBeInTheDocument();
            expect(screen.getByTestId('dropdown-option-pressure_kpa')).toBeInTheDocument();
        });

        it('should handle empty string values', () => {
            const props = {
                ...defaultProps,
                value: ''
            };
            
            expect(() => {
                render(<MeasurementType {...props} />);
            }).not.toThrow();
            
            expect(screen.getByTestId('dropdown-select')).toHaveValue('');
        });
    });
});
