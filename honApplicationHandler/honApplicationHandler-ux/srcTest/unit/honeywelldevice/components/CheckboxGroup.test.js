import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': require('../../../mocks/semantic-ui-react.mock'),
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip, help, style }) => (
        <div className="mock-config-label" data-testid="config-label" style={style}>
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
            {help && <div data-testid="help-text">{help}</div>}
        </div>
    ),
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('CheckboxGroup Component', () => {
    let CheckboxGroup;

    beforeEach(() => {
        // Extract the AMD module
        global.__AMD_MODULES__ = global.__AMD_MODULES__ || {};
        
        CheckboxGroup = extractAmdModule(
            'rc/honeywelldevice/components/CheckboxGroup',
            mocks
        );
    });

    it('renders correctly with default props', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2 },
            { key: '3', text: 'Option 3', value: 3 }
        ];
        
        render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                tooltip="Select multiple options"
                options={options}
                value={[1, 3]}
            />
        );

        // Check that the label and tooltip are rendered correctly
        expect(screen.getByTestId('label-text')).toHaveTextContent('Test Checkbox Group');
        expect(screen.getByTestId('tooltip-text')).toHaveTextContent('Select multiple options');

        // Check all checkboxes are rendered
        const checkboxes = screen.getAllByTestId('checkbox');
        expect(checkboxes).toHaveLength(3);

        // Check that the correct checkboxes are checked
        expect(checkboxes[0].querySelector('input')).toBeChecked();
        expect(checkboxes[1].querySelector('input')).not.toBeChecked();
        expect(checkboxes[2].querySelector('input')).toBeChecked();
    });

    it('handles checkbox selection changes', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2 },
            { key: '3', text: 'Option 3', value: 3 }
        ];
        
        const handleChange = jest.fn();
        
        // Initial render with [1] selected
        const { rerender } = render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={[1]}
                onChange={handleChange}
            />
        );

        // Click on the second checkbox to select it
        fireEvent.click(screen.getAllByTestId('checkbox')[1]);
        
        // Check that the onChange handler was called with the correct parameters
        expect(handleChange).toHaveBeenCalledTimes(1);
        
        // Get the updated value from the mock call
        const updatedValue = handleChange.mock.calls[0][1].value;
        expect(updatedValue).toContain(1); // First option should still be selected
        expect(updatedValue).toContain(2); // Second option should now be selected
        expect(updatedValue.length).toBe(2); // Only these two options should be in the array
        
        // Rerender with the updated value
        rerender(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={updatedValue}
                onChange={handleChange}
            />
        );

        // Click on the first checkbox to deselect it
        fireEvent.click(screen.getAllByTestId('checkbox')[0]);
        
        // Check that the onChange handler was called with the updated value
        expect(handleChange).toHaveBeenCalledTimes(2);
        
        // Get the final value from the mock call
        const finalValue = handleChange.mock.calls[1][1].value;
        expect(finalValue).not.toContain(1); // First option should be deselected
        expect(finalValue).toContain(2); // Second option should still be selected
        expect(finalValue.length).toBe(1); // Only the second option should remain
    });

    it('respects the disabled state of options', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2, disabled: true },
            { key: '3', text: 'Option 3', value: 3 }
        ];
        const handleChange = jest.fn();
        
        render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={[1]}
                onChange={handleChange}
            />
        );

        // Try to click on the disabled checkbox
        fireEvent.click(screen.getAllByTestId('checkbox')[1]);
        
        // The onChange handler should not be called
        expect(handleChange).not.toHaveBeenCalled();
        
        // Click on the third checkbox (which is enabled)
        fireEvent.click(screen.getAllByTestId('checkbox')[2]);
        
        // The onChange handler should be called now
        expect(handleChange).toHaveBeenCalledTimes(1);
        const updatedValue = handleChange.mock.calls[0][1].value;
        expect(updatedValue).toContain(1); // First option should still be selected
        expect(updatedValue).toContain(3); // Third option should now be selected
        expect(updatedValue.length).toBe(2); // Only these two options should be in the array
    });

    it('hides options that have visible set to false', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2, visible: false },
            { key: '3', text: 'Option 3', value: 3 }
        ];
        
        render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={[1]}
            />
        );

        // Only two checkboxes should be visible
        const checkboxes = screen.getAllByTestId('checkbox');
        expect(checkboxes).toHaveLength(2);
        
        // Verify the visible checkboxes have the expected content
        expect(screen.getAllByTestId('checkbox-label')[0]).toHaveTextContent('Option 1');
        expect(screen.getAllByTestId('checkbox-label')[1]).toHaveTextContent('Option 3');
    });

    it('does not render when visible is false', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2 }
        ];
        
        const { container } = render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={[1]}
                visible={false}
            />
        );

        // Component should have display: none
        expect(container.firstChild).toHaveStyle('display: none');
    });

    it('disables all checkboxes when readonly is true', () => {
        const options = [
            { key: '1', text: 'Option 1', value: 1 },
            { key: '2', text: 'Option 2', value: 2 }
        ];
        const handleChange = jest.fn();
        
        render(
            <CheckboxGroup 
                label="Test Checkbox Group" 
                options={options}
                value={[1]}
                onChange={handleChange}
                readonly={true}
            />
        );

        // All checkboxes should be disabled
        const checkboxes = screen.getAllByTestId('checkbox');
        checkboxes.forEach(checkbox => {
            expect(checkbox.querySelector('input')).toBeDisabled();
        });
        
        // Try to click on the first checkbox
        fireEvent.click(checkboxes[0]);
        
        // The onChange handler should not be called
        expect(handleChange).not.toHaveBeenCalled();
    });
});
