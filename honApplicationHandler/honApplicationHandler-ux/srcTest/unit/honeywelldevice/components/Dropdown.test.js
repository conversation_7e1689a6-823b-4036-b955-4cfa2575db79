import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';    

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': {
        Dropdown: ({ 
            className, 
            fluid, 
            disabled, 
            clearable, 
            selection, 
            options = [], 
            value, 
            onChange,
            placeholder,
            'data-testid': dataTestId 
        }) => (
            <div 
                className={`ui dropdown ${className || ''} ${disabled ? 'disabled' : ''}`} 
                data-testid={dataTestId || 'dropdown-component'}
            >
                <select 
                    disabled={disabled}
                    value={value || ''}
                    onChange={(e) => onChange && onChange(e, { value: e.target.value })}
                    data-testid="dropdown-select"
                >
                    {placeholder && <option value="">{placeholder}</option>}
                    {options && options
                        .filter(option => 
                            option.visible || 
                            option.visible === undefined || 
                            option.value === value
                        )
                        .map((option, index) => (
                            <option 
                                key={option.key || index} 
                                value={option.value}
                                data-testid={`dropdown-option-${option.value}`}
                            >
                                {option.text}
                            </option>
                        ))
                    }
                </select>
                {clearable && value && (
                    <button 
                        className="clear-button"
                        data-testid="dropdown-clear-btn"
                        onClick={(e) => onChange && onChange(e, { value: null })}
                    >
                        Clear
                    </button>
                )}
            </div>
        )
    },
    'lex!honApplicationHandler': [{
        get: jest.fn(key => key)
    }],
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip }) => (
        <div className="mock-config-label" data-testid="config-label">
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
        </div>
    ),
    'css!nmodule/honApplicationHandler/rc/css/HoneywellDeviceWizard': {},
    'css!nmodule/honApplicationHandler/rc/css/semantic/semantic.min': {}
};

describe('Dropdown Component', () => {
    let DropdownImpl;

    beforeEach(async () => {
        jest.resetModules();
        
        // Extract the AMD module - this is the key step that loads the actual component
        DropdownImpl = extractAmdModule(
            'nmodule/honApplicationHandler/rc/honeywelldevice/components/Dropdown',
            mocks
        );
    });

    const defaultOptions = [
        { key: '1', text: 'Option 1', value: '1' },
        { key: '2', text: 'Option 2', value: '2' },
        { key: '3', text: 'Option 3', value: '3' }
    ];

    it('renders correctly with default props', () => {
        render(
            <DropdownImpl 
                label="Test Label"
                options={defaultOptions}
            />
        );

        expect(screen.getByTestId('config-label')).toBeInTheDocument();
        expect(screen.getByTestId('dropdown-component')).toBeInTheDocument();
    });

    it('applies visibility styles when visible is false', () => {
        render(
            <DropdownImpl 
                label="Test Label" 
                value="1"
                visible={false}
                options={defaultOptions}
            />
        );

        // The actual component uses class "tc-component" for the container
        const container = document.querySelector('.tc-component');
        expect(container).toHaveStyle('display: none');
    });

    it('respects disabled and readonly props', () => {
        render(
            <DropdownImpl 
                label="Test Label" 
                value="1"
                disabled={true}
                options={defaultOptions}
            />
        );

        const dropdown = screen.getByTestId('dropdown-component');
        expect(dropdown).toHaveClass('disabled');

        const select = screen.getByTestId('dropdown-select');
        expect(select).toBeDisabled();
    });

    it('filters options based on visibility', () => {
        const optionsWithVisibility = [
            { key: '1', text: 'Option 1', value: '1', visible: true },
            { key: '2', text: 'Option 2', value: '2', visible: false },
            { key: '3', text: 'Option 3', value: '3' } // undefined visibility
        ];

        render(
            <DropdownImpl 
                label="Test Label" 
                options={optionsWithVisibility}
            />
        );

        const select = screen.getByTestId('dropdown-select');
        // Real component filters: visible=true OR undefined OR selected value
        // Expected: placeholder + Option 1 (visible=true) + Option 3 (undefined) = 3 total
        expect(select.children.length).toBe(3); // placeholder + 2 visible options
    });

    it('shows the invisible option if it is selected', () => {
        const optionsWithVisibility = [
            { key: '1', text: 'Option 1', value: '1', visible: true },
            { key: '2', text: 'Option 2', value: '2', visible: false },
            { key: '3', text: 'Option 3', value: '3' }
        ];

        render(
            <DropdownImpl 
                label="Test Label" 
                value="2"
                options={optionsWithVisibility}
            />
        );

        const select = screen.getByTestId('dropdown-select');
        expect(select.value).toBe('2');
    });

    it('calls onChange when value changes', () => {
        const handleChange = jest.fn();
        
        render(
            <DropdownImpl 
                label="Test Label" 
                options={defaultOptions}
                onChange={handleChange}
            />
        );

        const select = screen.getByTestId('dropdown-select');
        fireEvent.change(select, { target: { value: '1' } });
        
        expect(handleChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({ value: '1' })
        );
    });

    it('clears selection when clearable is true', () => {
        const handleChange = jest.fn();
        
        render(
            <DropdownImpl 
                label="Test Label" 
                value="1"
                options={defaultOptions}
                clearable={true}
                onChange={handleChange}
            />
        );

        const clearButton = screen.getByTestId('dropdown-clear-btn');
        fireEvent.click(clearButton);
        
        expect(handleChange).toHaveBeenCalledWith(
            expect.any(Object),
            expect.objectContaining({ value: null })
        );
    });
});
