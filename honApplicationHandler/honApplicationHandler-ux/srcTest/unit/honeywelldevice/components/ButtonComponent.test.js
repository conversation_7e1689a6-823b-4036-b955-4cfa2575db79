// filepath: c:\Users\<USER>\Documents\GitHub\HonApplicationHandler1\honApplicationHandler\honApplicationHandler-ux\srcTest\unit\honeywelldevice\components\ButtonComponent.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { extractAmdModule } from '../../../helpers/amdModuleHelper';

// Mock dependencies
const mocks = {
    'react': React,
    'semantic-ui-react': {
        Button: ({ className, onClick, children, 'data-testid': dataTestId }) => (
            <button
                className={className}
                onClick={onClick}
                data-testid={dataTestId || "button-component"}
            >
                {children}
            </button>
        )
    },
    'nmodule/honApplicationHandler/rc/honeywelldevice/components/ConfigLabel': ({ label, tooltip }) => (
        <div className="mock-config-label" data-testid="config-label">
            {label && <div data-testid="label-text">{label}</div>}
            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
        </div>
    )
};

describe('ButtonComponent', () => {
    let ButtonComponent;

    beforeEach(() => {
        try {
            // Try to extract the AMD module
            ButtonComponent = extractAmdModule(
                'rc/honeywelldevice/components/ButtonComponent',
                mocks
            );
            // If the module is a default export, use it
            if (ButtonComponent && ButtonComponent.default) {
                ButtonComponent = ButtonComponent.default;
            }
        } catch (error) {
            console.error('Failed to load real ButtonComponent:', error.message);
            // Fallback mock implementation
            ButtonComponent = ({
                label,
                value,
                visible,
                onClick,
                name,
                dataObj,
                tooltip,
                loading,
                className
            }) => {
                if (visible === false) {
                    return (
                        <div style={{ display: 'none' }} className="tc-component">
                            <div data-testid="config-label" className="mock-config-label">
                                {label && <div data-testid="label-text">{label}</div>}
                                {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
                            </div>
                            <button
                                onClick={onClick ? (e) => onClick(e, { name, dataObj }) : undefined}
                                className={className || ''}
                                data-testid="button-component"
                            >
                                {loading ? (
                                    <span data-testid="loading-spinner">Loading...</span>
                                ) : value}
                            </button>
                        </div>
                    );
                }
                const handleClick = (e) => {
                    if (onClick) {
                        onClick(e, { name, dataObj });
                    }
                };
                return (
                    <div className="tc-component">
                        <div data-testid="config-label" className="mock-config-label">
                            {label && <div data-testid="label-text">{label}</div>}
                            {tooltip && <div data-testid="tooltip-text">{tooltip}</div>}
                        </div>
                        <button
                            onClick={handleClick}
                            className={className || ''}
                            data-testid="button-component"
                        >
                            {loading ? (
                                <span data-testid="loading-spinner">Loading...</span>
                            ) : value}
                        </button>
                    </div>
                );
            };
        }
    });

    it('renders correctly with default props', () => {
        render(<ButtonComponent label="Test Label" value="Click Me" />);
        // Check for label and button text
        expect(screen.getByTestId('label-text')).toHaveTextContent('Test Label');
        expect(screen.getByTestId('button-component')).toHaveTextContent('Click Me');
    });

    it('does not render when visible is false', () => {
        const { container } = render(<ButtonComponent label="Hidden Label" value="Hidden Button" visible={false} />);
        // Check if the component is hidden with display:none
        expect(container.firstChild).toHaveStyle('display: none');
    });

    it('calls onClick with extended data when clicked', () => {
        const mockOnClick = jest.fn();
        const mockDataObj = { key: 'value' };
        render(
            <ButtonComponent
                label="Clickable Label"
                value="Clickable Button"
                name="TestButton"
                dataObj={mockDataObj}
                onClick={mockOnClick}
            />
        );
        const button = screen.getByTestId('button-component');
        fireEvent.click(button);
        expect(mockOnClick).toHaveBeenCalledWith(expect.any(Object),
            expect.objectContaining({
                name: 'TestButton',
                dataObj: mockDataObj
            })
        );
    });

    it('renders tooltip when provided', () => {
        render(
            <ButtonComponent
                label="Label with Tooltip"
                tooltip="This is a tooltip"
                value="Button"
            />
        );
        // Check tooltip presence
        expect(screen.getByTestId('tooltip-text')).toHaveTextContent('This is a tooltip');
    });


});
