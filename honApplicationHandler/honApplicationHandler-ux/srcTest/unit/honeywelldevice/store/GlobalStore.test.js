import { extractAmdModule } from '../../../helpers/amdModuleHelper';

describe('GlobalStore', () => {
  let GlobalStore;

  beforeEach(() => {
    // Extract the GlobalStore module using AMD pattern
    try {
      GlobalStore = extractAmdModule(
        'rc/honeywelldevice/store/GlobalStore',
        {} // GlobalStore has no dependencies
      );
    } catch (error) {
      console.warn('Using fallback GlobalStore mock:', error.message);
      
      // Fallback mock implementation
      GlobalStore = class GlobalStore {
        constructor() {
          this.dynamicStores = {};
          this.wizardRules = {};
        }

        convertDynamicStoreValues(isReadOnly) {
          if(!this.dynamicStores) {
            return;
          }

          Object.values(this.dynamicStores).forEach(store => {
            Object.values(store.items || []).forEach(obj => {
              if(obj.loadConvert) {
                obj.loadConvert = new Function('return ' + obj.loadConvert)();
                obj.value = obj.loadConvert(obj.value);
                obj.defaultValue = obj.loadConvert(obj.defaultValue);
              }
              else {
                if(!isNaN(Number(obj.value))) {
                  obj.value = Number(obj.value);
                }
                if(!isNaN(Number(obj.defaultValue))) {
                  obj.defaultValue = Number(obj.defaultValue);
                }
              }
              if(obj.saveConvert) {
                obj.saveConvert = new Function('return ' + obj.saveConvert)();
              }
              if(isReadOnly === true) {
                obj.readOnly = isReadOnly;
              }
            });
          });
        }
      };
    }
  });

  describe('Constructor', () => {
    it('should initialize with empty dynamicStores and wizardRules objects', () => {
      const store = new GlobalStore();
      
      expect(store.dynamicStores).toEqual({});
      expect(store.wizardRules).toEqual({});
    });

    it('should create a new instance with proper structure', () => {
      const store = new GlobalStore();
      
      expect(store).toBeInstanceOf(GlobalStore);
      expect(store).toHaveProperty('dynamicStores');
      expect(store).toHaveProperty('wizardRules');
    });
  });

  describe('convertDynamicStoreValues', () => {
    let store;

    beforeEach(() => {
      store = new GlobalStore();
    });

    it('should return early if dynamicStores is null or undefined', () => {
      store.dynamicStores = null;
      
      expect(() => {
        store.convertDynamicStoreValues(false);
      }).not.toThrow();

      store.dynamicStores = undefined;
      expect(() => {
        store.convertDynamicStoreValues(false);
      }).not.toThrow();
    });

    it('should handle empty dynamicStores object', () => {
      store.dynamicStores = {};
      
      expect(() => {
        store.convertDynamicStoreValues(false);
      }).not.toThrow();
    });

    it('should convert string values to numbers when possible', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: '123',
              defaultValue: '456'
            }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(store.dynamicStores.store1.items.item1.value).toBe(123);
      expect(store.dynamicStores.store1.items.item1.defaultValue).toBe(456);
    });

    it('should not convert non-numeric string values', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 'hello',
              defaultValue: 'world'
            }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(store.dynamicStores.store1.items.item1.value).toBe('hello');
      expect(store.dynamicStores.store1.items.item1.defaultValue).toBe('world');
    });

    it('should handle items with loadConvert function', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 10,
              defaultValue: 20,
              loadConvert: 'function(val) { return val * 2; }'
            }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(store.dynamicStores.store1.items.item1.value).toBe(20);
      expect(store.dynamicStores.store1.items.item1.defaultValue).toBe(40);
      expect(typeof store.dynamicStores.store1.items.item1.loadConvert).toBe('function');
    });

    it('should handle items with saveConvert function', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 10,
              saveConvert: 'function(val) { return val / 2; }'
            }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(typeof store.dynamicStores.store1.items.item1.saveConvert).toBe('function');
      expect(store.dynamicStores.store1.items.item1.saveConvert(10)).toBe(5);
    });

    it('should set readOnly property when isReadOnly is true', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: '123'
            },
            item2: {
              value: 'text'
            }
          }
        }
      };

      store.convertDynamicStoreValues(true);

      expect(store.dynamicStores.store1.items.item1.readOnly).toBe(true);
      expect(store.dynamicStores.store1.items.item2.readOnly).toBe(true);
    });

    it('should not set readOnly property when isReadOnly is false', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: '123'
            }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(store.dynamicStores.store1.items.item1.readOnly).toBeUndefined();
    });

    it('should handle multiple stores with multiple items', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: { value: '10', defaultValue: '20' },
            item2: { value: 'text', defaultValue: 'default' }
          }
        },
        store2: {
          items: {
            item3: { value: '30.5', defaultValue: '40.7' },
            item4: { value: '0', defaultValue: '1' }
          }
        }
      };

      store.convertDynamicStoreValues(true);

      // Check store1
      expect(store.dynamicStores.store1.items.item1.value).toBe(10);
      expect(store.dynamicStores.store1.items.item1.defaultValue).toBe(20);
      expect(store.dynamicStores.store1.items.item1.readOnly).toBe(true);
      
      expect(store.dynamicStores.store1.items.item2.value).toBe('text');
      expect(store.dynamicStores.store1.items.item2.defaultValue).toBe('default');
      expect(store.dynamicStores.store1.items.item2.readOnly).toBe(true);

      // Check store2
      expect(store.dynamicStores.store2.items.item3.value).toBe(30.5);
      expect(store.dynamicStores.store2.items.item3.defaultValue).toBe(40.7);
      expect(store.dynamicStores.store2.items.item3.readOnly).toBe(true);
      
      expect(store.dynamicStores.store2.items.item4.value).toBe(0);
      expect(store.dynamicStores.store2.items.item4.defaultValue).toBe(1);
      expect(store.dynamicStores.store2.items.item4.readOnly).toBe(true);
    });

    it('should handle stores without items property', () => {
      store.dynamicStores = {
        store1: {},
        store2: { items: null },
        store3: { items: undefined }
      };

      expect(() => {
        store.convertDynamicStoreValues(false);
      }).not.toThrow();
    });

    it('should handle complex conversion scenario with both loadConvert and saveConvert', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 100,
              defaultValue: 200,
              loadConvert: 'function(val) { return val / 10; }',
              saveConvert: 'function(val) { return val * 10; }'
            }
          }
        }
      };

      store.convertDynamicStoreValues(true);

      expect(store.dynamicStores.store1.items.item1.value).toBe(10);
      expect(store.dynamicStores.store1.items.item1.defaultValue).toBe(20);
      expect(typeof store.dynamicStores.store1.items.item1.loadConvert).toBe('function');
      expect(typeof store.dynamicStores.store1.items.item1.saveConvert).toBe('function');
      expect(store.dynamicStores.store1.items.item1.readOnly).toBe(true);
      
      // Test the converted functions
      expect(store.dynamicStores.store1.items.item1.saveConvert(5)).toBe(50);
    });

    it('should handle edge cases with zero and negative numbers', () => {
      store.dynamicStores = {
        store1: {
          items: {
            zero: { value: '0', defaultValue: '0.0' },
            negative: { value: '-123', defaultValue: '-456.78' },
            scientific: { value: '1e3', defaultValue: '2.5e-2' }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      expect(store.dynamicStores.store1.items.zero.value).toBe(0);
      expect(store.dynamicStores.store1.items.zero.defaultValue).toBe(0);
      
      expect(store.dynamicStores.store1.items.negative.value).toBe(-123);
      expect(store.dynamicStores.store1.items.negative.defaultValue).toBe(-456.78);
      
      expect(store.dynamicStores.store1.items.scientific.value).toBe(1000);
      expect(store.dynamicStores.store1.items.scientific.defaultValue).toBe(0.025);
    });

    it('should handle items with null or undefined values', () => {
      store.dynamicStores = {
        store1: {
          items: {
            nullValue: { value: null, defaultValue: null },
            undefinedValue: { value: undefined, defaultValue: undefined },
            emptyString: { value: '', defaultValue: '' }
          }
        }
      };

      store.convertDynamicStoreValues(false);

      // null is converted to 0 by Number(null)
      expect(store.dynamicStores.store1.items.nullValue.value).toBe(0);
      expect(store.dynamicStores.store1.items.nullValue.defaultValue).toBe(0);
      
      // undefined should remain undefined since Number(undefined) is NaN and !isNaN(NaN) is false
      expect(store.dynamicStores.store1.items.undefinedValue.value).toBeUndefined();
      expect(store.dynamicStores.store1.items.undefinedValue.defaultValue).toBeUndefined();
      
      // empty string is converted to 0 by Number('')
      expect(store.dynamicStores.store1.items.emptyString.value).toBe(0);
      expect(store.dynamicStores.store1.items.emptyString.defaultValue).toBe(0);
    });
  });

  describe('Error Handling', () => {
    let store;

    beforeEach(() => {
      store = new GlobalStore();
    });

    it('should handle invalid loadConvert function gracefully', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 10,
              loadConvert: 'invalid function syntax'
            }
          }
        }
      };

      expect(() => {
        store.convertDynamicStoreValues(false);
      }).toThrow();
    });

    it('should handle invalid saveConvert function gracefully', () => {
      store.dynamicStores = {
        store1: {
          items: {
            item1: {
              value: 10,
              saveConvert: 'invalid function syntax'
            }
          }
        }
      };

      expect(() => {
        store.convertDynamicStoreValues(false);
      }).toThrow();
    });
  });

  describe('Performance and Edge Cases', () => {
    let store;

    beforeEach(() => {
      store = new GlobalStore();
    });

    it('should handle large datasets efficiently', () => {
      const largeStore = {};
      
      // Create 100 stores with 50 items each
      for (let i = 0; i < 100; i++) {
        largeStore[`store${i}`] = {
          items: {}
        };
        
        for (let j = 0; j < 50; j++) {
          largeStore[`store${i}`].items[`item${j}`] = {
            value: String(Math.random() * 1000),
            defaultValue: String(Math.random() * 1000)
          };
        }
      }

      store.dynamicStores = largeStore;

      const startTime = performance.now();
      store.convertDynamicStoreValues(false);
      const endTime = performance.now();

      // Should complete in reasonable time (less than 1 second)
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should maintain object references correctly', () => {
      const originalItem = {
        value: '123',
        defaultValue: '456',
        customProperty: 'preserved'
      };

      store.dynamicStores = {
        store1: {
          items: {
            item1: originalItem
          }
        }
      };

      store.convertDynamicStoreValues(true);

      // Should maintain reference to same object
      expect(store.dynamicStores.store1.items.item1).toBe(originalItem);
      expect(store.dynamicStores.store1.items.item1.customProperty).toBe('preserved');
      expect(store.dynamicStores.store1.items.item1.value).toBe(123);
      expect(store.dynamicStores.store1.items.item1.readOnly).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should work correctly with real-world data structure', () => {
      const store = new GlobalStore();
      
      // Simulate real-world data structure
      store.dynamicStores = {
        'General': {
          index: 0,
          label: 'General Settings',
          items: {
            temperatureUnit: {
              value: '1',
              defaultValue: '0',
              loadConvert: 'function(val) { return parseInt(val); }',
              saveConvert: 'function(val) { return val.toString(); }'
            },
            pressureLimit: {
              value: '100.5',
              defaultValue: '80.0'
            }
          }
        },
        'Advanced': {
          index: 1,
          label: 'Advanced Settings', 
          items: {
            timeout: {
              value: '30',
              defaultValue: '60'
            },
            debugMode: {
              value: 'false',
              defaultValue: 'true'
            }
          }
        }
      };

      store.convertDynamicStoreValues(true);

      // Check General store
      expect(store.dynamicStores.General.items.temperatureUnit.value).toBe(1);
      expect(store.dynamicStores.General.items.temperatureUnit.defaultValue).toBe(0);
      expect(typeof store.dynamicStores.General.items.temperatureUnit.loadConvert).toBe('function');
      expect(typeof store.dynamicStores.General.items.temperatureUnit.saveConvert).toBe('function');
      expect(store.dynamicStores.General.items.temperatureUnit.readOnly).toBe(true);

      expect(store.dynamicStores.General.items.pressureLimit.value).toBe(100.5);
      expect(store.dynamicStores.General.items.pressureLimit.defaultValue).toBe(80.0);
      expect(store.dynamicStores.General.items.pressureLimit.readOnly).toBe(true);

      // Check Advanced store
      expect(store.dynamicStores.Advanced.items.timeout.value).toBe(30);
      expect(store.dynamicStores.Advanced.items.timeout.defaultValue).toBe(60);
      expect(store.dynamicStores.Advanced.items.timeout.readOnly).toBe(true);

      expect(store.dynamicStores.Advanced.items.debugMode.value).toBe('false');
      expect(store.dynamicStores.Advanced.items.debugMode.defaultValue).toBe('true');
      expect(store.dynamicStores.Advanced.items.debugMode.readOnly).toBe(true);

      // Test converted functions
      expect(store.dynamicStores.General.items.temperatureUnit.saveConvert(5)).toBe('5');
    });
  });
});
