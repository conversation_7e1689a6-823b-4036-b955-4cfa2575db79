/**
 * Mock for Baja
 */
const bajaMock = {
  // Basic Baja component functionality
  component: {
    obj: jest.fn(),
    array: jest.fn(),
  },
  // Ord functionality
  Ord: {
    make: jest.fn(value => value),
  },
  // Status values
  STATUS_OK: 0,
  STATUS_ERROR: 1,
  STATUS_DISABLED: 2,
  
  // Types
  Type: {
    fromString: jest.fn(str => ({ typeName: str })),
    fromNiagara: jest.fn(obj => ({ typeName: obj.type }))
  },
  
  // Additional functions you might need
  invoke: jest.fn(),
  get: jest.fn(),
  set: jest.fn()
};

export default bajaMock;