/**
 * WizardHomeProfile Mock for Integration Tests
 */

import React from 'react';

// Mock component
class WizardHomeProfileMock extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      activeIndex: 0,
      dynamicStores: {},
      changedDynamicStores: []
    };
  }

  componentDidMount() {
    // Simulate loading data and then updating state
    setTimeout(() => {
      this.setState({ loading: false });
    }, 50);
  }

  render() {
    return (
      <div data-testid="wizard-home-profile">
        {this.state.loading ? (
          <div data-testid="dimmer">
            <div data-testid="loader">Loading...</div>
          </div>
        ) : (
          <div data-testid="segment">
            <div data-testid="tab">
              <div data-testid="tab-menu">
                <div data-testid="tab-0" data-active={this.state.activeIndex === 0}>Terminal Assignment</div>
                <div data-testid="tab-1" data-active={this.state.activeIndex === 1}>Scheduling</div>
              </div>
              <div data-testid="tab-content">Tab Content</div>
            </div>
            <button 
              data-testid="button" 
              disabled={this.state.changedDynamicStores.length === 0}
            >
              Save
            </button>
          </div>
        )}
      </div>
    );
  }
}

// Export the mock component in different formats to ensure compatibility
export default WizardHomeProfileMock;
module.exports = WizardHomeProfileMock;
