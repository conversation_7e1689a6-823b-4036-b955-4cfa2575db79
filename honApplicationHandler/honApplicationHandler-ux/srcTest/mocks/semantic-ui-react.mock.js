/**
 * Consolidated mock for semantic-ui-react components used in tests
 * This file combines the best parts of all previous mock versions
 */
import React from 'react';

// Label component for StaticLabel
const Label = function(props) {
  const { children, size, ...rest } = props;
  return (
    <div
      data-testid="semantic-label"
      className={`mock-semantic-label ${props.className || ''}`}
      data-size={size}
      {...rest}
    >
      {children}
    </div>
  );
};

// Button component with Content subcomponent
const Button = function(props) {
  const { children, onClick, disabled, className, value, ...rest } = props;
  // Value mapping for test IDs
  let testId = '';
  if (rest['data-testid']) {
    testId = rest['data-testid'];
  } else if (rest.key) {
    testId = `select-button-${rest.key}`;
  } else if (value) {
    testId = `select-button-${value}`;
  } else {
    testId = 'select-button';
  }

  return (
    <button
      data-testid={testId}
      className={className}
      onClick={onClick}
      disabled={disabled}
      {...rest}
    >
      {children}
    </button>
  );
};

// Properly define Button.Content as a subcomponent
Button.Content = function Content(props) {
  const { children } = props;
  return <div>{children}</div>;
};

// Image component for help icons
const Image = function(props) {
  const { src, className, disabled, style, onClick, ...rest } = props;

  // Determine testid based on src
  let testId = 'image';
  if (src && src.includes('Calculate.svg')) {
    testId = 'calculate-icon';
  } else if (src && src.includes('Help.svg')) {
    testId = 'help-icon';
  } else if (className && className.includes('help-icon')) {
    testId = 'help-icon';
  }

  return (
    <img
      src={src}
      className={className}
      data-testid={testId}
      style={{
        ...style,
        opacity: disabled ? 0.5 : 1,
        pointerEvents: disabled ? 'none' : 'auto'
      }}
      onClick={onClick}
      disabled={disabled}
      {...rest}
    />
  );
};

// Popup component for tooltips
const Popup = function(props) {
  const { trigger, content, basic, ...rest } = props;
  return (
    <div data-testid="popup-container" className={basic ? 'basic' : ''} {...rest}>
      {trigger}
      <div data-testid="popup-content">{content}</div>
    </div>
  );
};

// Grid component for layout
const Grid = function(props) {
  const { children, className, ...rest } = props;
  return (
    <div
      className={`ui grid ${className || ''}`}
      data-testid="grid"
      {...rest}
    >
      {children}
    </div>
  );
};

// Grid.Row subcomponent
Grid.Row = function(props) {
  const { children, columns, className, ...rest } = props;
  return (
    <div
      className={`row ${className || ''}`}
      data-testid="grid-row"
      data-columns={columns}
      {...rest}
    >
      {children}
    </div>
  );
};

// Grid.Column subcomponent
Grid.Column = function(props) {
  const { children, className, ...rest } = props;
  return (
    <div
      className={`column ${className || ''}`}
      data-testid="grid-column"
      {...rest}
    >
      {children}
    </div>
  );
};

// Checkbox component for RadioButtonGroup
const Checkbox = function(props) {
  const {
    className,
    radio,
    label,
    name,
    value,
    disabled,
    checked,
    onChange,
    'data-testid': dataTestId,
    ...rest
  } = props;

  const handleChange = (e) => {
    // Don't call onChange if disabled
    if (disabled) {
      return;
    }

    if (onChange) {
      onChange(e, { value, checked: !checked });
    }
  };

  // Generate specific testid for radio buttons based on value
  let testId = dataTestId || "checkbox";
  if (radio && value) {
    testId = `radio-option-${value}`;
  }

  return (
    <label
      className={`ui checkbox ${className || ''} ${radio ? 'radio' : ''}`}
      data-testid={testId}
    >
      <input
        type={radio ? "radio" : "checkbox"}
        name={name}
        value={value}
        checked={checked}
        disabled={disabled}
        onChange={handleChange}
        data-testid="checkbox-input"
        {...rest}
      />
      <span className="slider" />
      <span data-testid="checkbox-label">{label}</span>
    </label>
  );
};

// Dropdown component
const Dropdown = function(props) {
  const {
    className,
    fluid,
    disabled,
    clearable,
    selection,
    options = [],
    value,
    onChange,
    placeholder,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      className={`ui dropdown ${className || ''} ${disabled ? 'disabled' : ''}`}
      data-testid={dataTestId || 'dropdown-component'}
    >
      <select
        disabled={disabled}
        value={value || ''}
        onChange={(e) => {
          if (onChange) {
            // Create a mock event object that mimics the structure expected by the component
            const mockEvent = {
              target: e.target,
              currentTarget: e.target,
              preventDefault: jest.fn(),
              stopPropagation: jest.fn()
            };
            const mockData = { value: e.target.value };
            onChange(mockEvent, mockData);
          }
        }}
        data-testid="dropdown-select"
        {...rest}
      >
        {placeholder && <option value="">{placeholder}</option>}
        {options && options
          .filter(option =>
            option.visible === undefined ||
            option.visible === true ||
            option.value === value
          )
          .map((option) => (
            <option
              key={option.key || option.value}
              value={option.value}
              data-testid={`dropdown-option-${option.value}`}
            >
              {option.text}
            </option>
          ))
        }
      </select>
      {clearable && value && (
        <button
          className="clear-button"
          data-testid="dropdown-clear-btn"
          onClick={(e) => {
            if (onChange) {
              const mockEvent = {
                target: e.target,
                currentTarget: e.target,
                preventDefault: jest.fn(),
                stopPropagation: jest.fn()
              };
              const mockData = { value: null };
              onChange(mockEvent, mockData);
            }
          }}
        >
          Clear
        </button>
      )}
    </div>
  );
};

// Modal component with sub-components
const Modal = function(props) {
  const {
    className,
    open,
    size,
    closeOnDimmerClick,
    closeOnDocumentClick,
    onClose,
    children,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      data-testid={dataTestId || "modal-component"}
      className={`ui modal ${className || ''} ${size || ''}`}
      style={{ display: open ? 'block' : 'none' }}
      {...rest}
    >
      {children}
    </div>
  );
};

// Modal.Header subcomponent
Modal.Header = function(props) {
  const { style, children, className, 'data-testid': dataTestId, ...rest } = props;
  return (
    <div
      data-testid={dataTestId || "modal-header"}
      className={`header ${className || ''}`}
      style={style}
      {...rest}
    >
      {children}
    </div>
  );
};

// Modal.Content subcomponent
Modal.Content = function(props) {
  const { style, children, className, 'data-testid': dataTestId, ...rest } = props;
  return (
    <div
      data-testid={dataTestId || "modal-content"}
      className={`content ${className || ''}`}
      style={style}
      {...rest}
    >
      {children}
    </div>
  );
};

// Modal.Actions subcomponent
Modal.Actions = function(props) {
  const { style, children, className, 'data-testid': dataTestId, ...rest } = props;
  return (
    <div
      data-testid={dataTestId || "modal-actions"}
      className={`actions ${className || ''}`}
      style={style}
      {...rest}
    >
      {children}
    </div>
  );
};

// Input component for forms
const Input = React.forwardRef((props, ref) => {
  const {
    style,
    value,
    disabled,
    size,
    onChange,
    onKeyPress,
    onBlur,
    placeholder,
    className,
    'data-testid': dataTestId,
    id,
    ...rest
  } = props;

  // Create testId based on id prop to match test expectations
  const testId = id ? `slider-input-${id}` : (dataTestId || "input-text");

  // Create a simple ref structure that matches what ReactSlider expects
  const inputElement = React.createElement('input', {
    'data-testid': testId,
    type: "text",
    value: value || '',
    disabled: disabled,
    placeholder: placeholder,
    id: id,
    onChange: onChange ? (e) => {
      // Create a synthetic event with similar structure to what semantic-ui would provide
      onChange(e, { value: e.target.value });
    } : undefined,
    onKeyPress: onKeyPress,
    onBlur: onBlur,
    ref: (element) => {
      if (ref && typeof ref === 'object') {
        // Create the expected nested ref structure for ReactSlider
        ref.current = {
          inputRef: {
            current: element
          },
          focus: () => {
            if (element && element.focus) {
              element.focus();
            }
          }
        };
      }
    },
    ...rest
  });

  return React.createElement('div', {
    className: `ui ${size || ''} input ${className || ''}`,
    'data-testid': dataTestId || "input-component",
    style: style
  }, inputElement);
});

// Container component for layout
const Container = function(props) {
  const {
    children,
    className,
    style,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      className={`ui container ${className || ''}`}
      data-testid={dataTestId || "container"}
      style={style}
      {...rest}
    >
      {children}
    </div>
  );
};

// Menu component for SelectButton
const MenuItem = function(props) {
  const {
    children,
    onClick,
    disabled,
    active,
    value,
    name,
    className,
    ...rest
  } = props;

  const handleClick = (e) => {
    if (disabled) return;
    if (onClick) {
      onClick(e, { value, name });
    }
  };

  return (
    <div
      className={`mock-menu-item ${active ? 'active' : ''} ${disabled ? 'disabled' : ''} ${className || ''}`}
      data-testid={`menu-item-${value}`}
      onClick={handleClick}
      {...rest}
    >
      {children || name}
    </div>
  );
};

const Menu = function(props) {
  const { children, widths, className, ...rest } = props;

  return (
    <div
      className={`mock-menu ${className || ''}`}
      data-testid="menu"
      data-widths={widths}
      {...rest}
    >
      {children}
    </div>
  );
};

// Attach Item as a subcomponent
Menu.Item = MenuItem;

// Tab component for DynamicPage
const Tab = function(props) {
  const {
    panes = [],
    onTabChange,
    activeIndex = 0,
    className,
    'data-testid': dataTestId,
    ...rest
  } = props;

  const handleTabClick = (index) => {
    if (onTabChange) {
      onTabChange(null, { activeIndex: index });
    }
  };

  // Filter out undefined/null panes
  const validPanes = panes.filter(pane => pane != null);

  return (
    <div
      className={`ui tab ${className || ''}`}
      data-testid={dataTestId || "tab-component"}
      {...rest}
    >
      <div className="tab-menu" data-testid="tab-menu">
        {validPanes.map((pane, index) => (
          <div
            key={index}
            className={`tab-item ${index === activeIndex ? 'active' : ''}`}
            data-testid={`tab-menu-item-${index}`}
            onClick={() => handleTabClick(index)}
          >
            {pane && pane.menuItem ? pane.menuItem : `Tab ${index}`}
          </div>
        ))}
      </div>
      <div className="tab-content" data-testid="tab-content">
       <div data-testid="tab-container">
         {validPanes[activeIndex] && validPanes[activeIndex].render && (
           <div data-testid={`tab-pane-${activeIndex}`}>
             {validPanes[activeIndex].render()}
           </div>
         )}
       </div>
      </div>
    </div>
  );
};

// TabPane component for DynamicPage
const TabPane = function(props) {
  const {
    children,
    className,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      className={`tab-pane ${className || ''}`}
      data-testid={dataTestId || "tab-pane"}
      {...rest}
    >
      {children}
    </div>
  );
};

// Attach Pane as a subcomponent of Tab
Tab.Pane = TabPane;

// Segment component for layout sections
const Segment = function(props) {
  const {
    children,
    className,
    style,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      className={`ui segment ${className || ''}`}
      data-testid={dataTestId || "segment"}
      style={style}
      {...rest}
    >
      {children}
    </div>
  );
};

// Dimmer component for loading overlays
const Dimmer = function(props) {
  const {
    children,
    active,
    inverted,
    className,
    'data-testid': dataTestId,
    ...rest
  } = props;

  if (!active) return null;

  return (
    <div
      className={`ui dimmer ${inverted ? 'inverted' : ''} ${className || ''}`}
      data-testid={dataTestId || "dimmer"}
      style={{ display: active ? 'block' : 'none' }}
      {...rest}
    >
      {children}
    </div>
  );
};

// Loader component for loading indicators
const Loader = function(props) {
  const {
    children,
    inverted,
    className,
    'data-testid': dataTestId,
    ...rest
  } = props;

  return (
    <div
      className={`ui loader ${inverted ? 'inverted' : ''} ${className || ''}`}
      data-testid={dataTestId || "loader"}
      {...rest}
    >
      {children || 'Loading...'}
    </div>
  );
};

// ModalHeader component (alias for Modal.Header)
const ModalHeader = Modal.Header;

// ModalContent component (alias for Modal.Content)
const ModalContent = Modal.Content;

// ModalActions component (alias for Modal.Actions)
const ModalActions = Modal.Actions;

// Add displayNames
Button.displayName = 'Button';
Label.displayName = 'Label';
Image.displayName = 'Image';
Popup.displayName = 'Popup';
Grid.displayName = 'Grid';
Dropdown.displayName = 'Dropdown';
Checkbox.displayName = 'Checkbox';
Modal.displayName = 'Modal';
Input.displayName = 'Input';
Container.displayName = 'Container';
Menu.displayName = 'Menu';
MenuItem.displayName = 'MenuItem';
Tab.displayName = 'Tab';
TabPane.displayName = 'TabPane';
Segment.displayName = 'Segment';
Dimmer.displayName = 'Dimmer';
Loader.displayName = 'Loader';
ModalHeader.displayName = 'ModalHeader';
ModalContent.displayName = 'ModalContent';
ModalActions.displayName = 'ModalActions';

// Export everything as named and default exports
export {
  Button,
  Label,
  Image,
  Popup,
  Grid,
  Dropdown,
  Checkbox,
  Modal,
  Input,
  Container,
  Menu,
  MenuItem,
  Tab,
  TabPane,
  Segment,
  Dimmer,
  Loader,
  ModalHeader,
  ModalContent,
  ModalActions
};

const exports = {
  Button,
  Label,
  Image,
  Popup,
  Grid,
  Dropdown,
  Checkbox,
  Modal,
  Input,
  Container,
  Menu,
  MenuItem,
  Tab,
  TabPane,
  Segment,
  Dimmer,
  Loader,
  ModalHeader,
  ModalContent,
  ModalActions
};

export default exports;