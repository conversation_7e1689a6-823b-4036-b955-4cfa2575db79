{"name": "hon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "version": "0.1.0", "homepage": "", "author": {"name": "E594553", "email": "<EMAIL>"}, "bugs": {"url": ""}, "engines": {"node": ">= 16.20.2"}, "scripts": {"test:coverage": "jest --coverage", "test:unit": "jest srcTest/unit", "test:integration": "jest srcTest/integration", "security-scan": "yarn audit", "outdated": "yarn outdated", "upgrade": "yarn upgrade-interactive --latest"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-transform-class-properties": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.5", "@babel/plugin-transform-react-jsx": "^7.24.7", "@babel/preset-env": "^7.24.5", "@testing-library/jest-dom": "6.4.2", "@testing-library/react": "14.2.1", "babel-jest": "^29.7.0", "babel-plugin-istanbul": "^6.1.1", "eslint-plugin-security": "^3.0.1", "grunt": "~1.6.1", "grunt-cli": "^1.5.0", "grunt-niagara": "^2.1.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.24.5", "@babel/preset-react": "^7.24.5", "html-react-parser": "5.2.2", "react": "18.2.0", "react-dom": "18.2.0", "resize-observer-polyfill": "1.5.1", "semantic-ui-react": "2.1.5"}, "resolutions": {"minimist": "^1.2.8", "glob-parent": "^6.0.2", "glob": "^10.3.10", "inflight": "^1.0.6", "semver": "^7.5.4", "json5": "^2.2.3", "word-wrap": "^1.2.5", "tough-cookie": "^4.1.3", "postcss": "^8.4.31", "browserify-sign": "^4.2.2", "eslint-plugin-security": "^3.0.1"}, "keywords": []}