<!-- Module Include File -->
<!-- Types -->
<types>
  <!-- Type Example:<type name="MyClass" class="com.acme.BMyClass"/> -->
  <!--com.honeywell.applicationhandler.ux-->
  <type class="com.honeywell.applicationhandler.ux.BHoneywellDeviceWizard" name="HoneywellDeviceWizard">
    <agent>
      <on type="honApplicationHandler:IHoneywellConfigurableDevice"/>
    </agent>
  </type>
  <type class="com.honeywell.applicationhandler.ux.BHoneywellDeviceWizardJsBuild" name="HoneywellDeviceWizardJsBuild"/>
  <type class="com.honeywell.applicationhandler.ux.BHoneywellDeviceWizardRPC" name="HoneywellDeviceWizardRPC"/>
</types>