/*
 * Copyright 2025 Honeywell. All Rights Reserved.
 */

import com.tridium.gradle.plugins.grunt.task.GruntBuildTask
import com.tridium.gradle.plugins.bajadoc.task.Bajadoc
import com.tridium.gradle.plugins.module.util.ModulePart.RuntimeProfile.*

plugins {
  // The Niagara Module plugin configures the "moduleManifest" extension and the
  // "jar" and "moduleTestJar" tasks.
  id("com.tridium.niagara-module")

  // The signing plugin configures the correct signing of modules. It requires
  // that the plugin also be applied to the root project.
  id("com.tridium.niagara-signing")

  // The bajadoc plugin configures the generation of Bajadoc for a module.
  id("com.tridium.bajadoc")

  // Configures JaCoCo for the "niagaraTest" task of this module.
  id("com.tridium.niagara-jacoco")

  // The Annotation processors plugin adds default dependencies on ":nre"
  // for the "annotationProcessor" and "moduleTestAnnotationProcessor"
  // configurations by creating a single "niagaraAnnotationProcessor"
  // configuration they extend from. This value can be overridden by explicitly
  // declaring a dependency for the "niagaraAnnotationProcessor" configuration.
  id("com.tridium.niagara-annotation-processors")

  // The niagara_home repositories convention plugin configures !bin/ext and
  // !modules as flat-file Maven repositories so that projects in this build can
  // depend on already-installed Niagara modules.
  id("com.tridium.convention.niagara-home-repositories")

  id("com.tridium.niagara-grunt")
}

description = "Niagara module to generate easy to use application configuration wizard for wiresheet strategy"

moduleManifest {
  moduleName.set("honApplicationHandler")
  preferredSymbol.set("hah")
  runtimeProfile.set(ux)
}

// See documentation at module://docDeveloper/doc/build.html#dependencies for the supported
// dependency types
dependencies {
  // NRE dependencies
  nre(":nre:4.10")

  // Niagara module dependencies
  api(":baja:4.10")
  //implementation(":workbench-wb:4.10")
  implementation(":web-rt:4.10")
  implementation(":webEditors-ux:4.10")
  implementation(":bajaScript-ux:4.10")
  implementation(":bajaui-ux:4.10")
  //implementation(":bajaui-wb:4.10")
  implementation(":bajaux-rt:4.10")
  implementation(":bajaux-ux:4.10")
  implementation(":js-ux:4.0.0")  
  implementation(":history-ux:4.10")
  implementation(project(":honApplicationHandler-rt"))

  // Test Niagara module dependencies
  moduleTestImplementation(":test-wb")
}

tasks.named<GruntBuildTask>("gruntBuild") {
  tasks("copy:dist", "requirejs", "appendJs")
}

tasks.named<Bajadoc>("bajadoc") {
  // Each of the packages you wish to include in your module's API documentation must be
  // enumerated below
  includePackage("com.honeywell.honApplicationHandler.ux")
}

tasks.named<Jar>("jar") {
    archiveBaseName.set(project.name)
    from("src") {
        include("rc/")
        include("res/*.*")
    }
}

// Task to run JavaScript tests with coverage generation
tasks.register<Exec>("runJsTests") {
    description = "Run Jest tests with coverage if dependencies are available"
    
    workingDir = projectDir
    
    // Only run if node_modules and srcTest directories exist
    onlyIf {
        file("node_modules").exists() && file("srcTest").exists()
    }
    
    // Use OS-specific command formatting with V8 coverage provider
    if (System.getProperty("os.name").toLowerCase().contains("windows")) {
        commandLine("cmd", "/c", "npx", "jest", "--coverage", "--coverageProvider=v8")
    } else {
        commandLine("npx", "jest", "--coverage", "--coverageProvider=v8")
    }
    
    isIgnoreExitValue = true
    
    doLast {
        logger.lifecycle("JavaScript tests completed. Coverage reports available at: ${projectDir}/coverage/lcov-report/index.html")
    }
}

// Connect JavaScript tests to the build lifecycle
tasks.named("build") {
    dependsOn("runJsTests")
}
