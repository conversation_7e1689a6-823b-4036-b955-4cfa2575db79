/*
 * Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.Test;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

import java.util.logging.Logger;

/**
 * Unit tests for BTimezoneEnum.
 *
 * Verifies default constant, factory methods by ordinal and tag, and basic Niagara type wiring.
 * Uses TestNG with Niagara BTestNg base as per project conventions.
 */
@NiagaraType
public class BTimezoneEnumTest extends BTestNg {
    /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
    /*@ $com.honeywell.applicationhandler.enums.BTimezoneEnumTest(000000001)1.0$ @*/
    /**
     * Niagara type metadata hook.
     * @return Type for this class
     */
    @Override
    public Type getType() { return TYPE; }
    public static final Type TYPE = Sys.loadType(BTimezoneEnumTest.class);
    /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final Logger LOGGER = Logger.getLogger("honApplicationHandler");

    /**
     * DEFAULT should be Etc_GMT as defined in the enum.
     */
    @Test
    public void testDefaultIsEtcGmt() {
        LOGGER.info("Verifying DEFAULT equals Etc_GMT");
        Assert.assertSame(BTimezoneEnum.DEFAULT, BTimezoneEnum.Etc_GMT, "DEFAULT must be Etc_GMT");
    }

    /**
     * make(int) should return the constant for a valid ordinal.
     * Uses two representative ordinals to avoid coupling to entire range.
     */
    @Test
    public void testMakeByOrdinal_validOrdinals() {
        // 0 -> Etc_GMT
        Assert.assertSame(BTimezoneEnum.make(BTimezoneEnum.ETC_GMT), BTimezoneEnum.Etc_GMT, "Ordinal 0 must map to Etc_GMT");
        // 67 -> Asia_Tokyo (per generated constants)
        Assert.assertSame(BTimezoneEnum.make(BTimezoneEnum.ASIA_TOKYO), BTimezoneEnum.Asia_Tokyo, "Ordinal 67 must map to Asia_Tokyo");
    }

    /**
     * make(String) should return the constant for a valid tag.
     */
    @Test
    public void testMakeByTag_validTags() {
        Assert.assertSame(BTimezoneEnum.make("Etc_GMT"), BTimezoneEnum.Etc_GMT, "Tag Etc_GMT must resolve");
        Assert.assertSame(BTimezoneEnum.make("Asia_Tokyo"), BTimezoneEnum.Asia_Tokyo, "Tag Asia_Tokyo must resolve");
    }

    /**
     * For out-of-range ordinals, Niagara BFrozenEnum Range.get(ordinal, false) returns null (unchecked lookup).
     * Validate null is returned rather than throwing.
     */
    @Test
    public void testMakeByOrdinal_invalidOrdinalsReturnNull() {
        Assert.assertNull(BTimezoneEnum.make(-1), "Negative ordinal should return null");
        Assert.assertNull(BTimezoneEnum.make(9999), "Out-of-range ordinal should return null");
    }

    /** 覆盖所有静态常量：按 tag 校验 make(tag) 返回对应实例。 */
    @Test
    public void testAllConstants_resolveByTag() throws Exception {
        for (Field f : BTimezoneEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers())
                    && f.getType() == BTimezoneEnum.class
                    && !"DEFAULT".equals(f.getName())) {
                BTimezoneEnum inst = (BTimezoneEnum) f.get(null);
                String tag = f.getName();
                Assert.assertSame(BTimezoneEnum.make(tag), inst, "Tag must resolve: " + tag);
            }
        }
    }

    /** 覆盖所有静态常量：按 ordinal 校验 make(ordinal) 返回对应实例。 */
    @Test
    public void testAllConstants_resolveByOrdinal() throws Exception {
        for (Field f : BTimezoneEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers())
                    && f.getType() == BTimezoneEnum.class
                    && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                Field ordField = BTimezoneEnum.class.getField(tag.toUpperCase());
                int ordinal = ordField.getInt(null);
                BTimezoneEnum inst = (BTimezoneEnum) f.get(null);
                Assert.assertSame(BTimezoneEnum.make(ordinal), inst, "Ordinal must resolve for: " + tag);
            }
        }
    }
}

