/*
 * Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Locale;
import java.util.logging.Logger;

/**
 * Unit tests for BMonthEnum.
 *
 * Covers DEFAULT mapping, full tag-based resolution for all constants, ordinal-based resolution
 * using robust tag->ordinal constant name conversion, and invalid ordinal behavior.
 */
@NiagaraType
public class BMonthEnumTest extends BTestNg {
    /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
    /*@ $com.honeywell.applicationhandler.enums.BMonthEnumTest(000000002)1.0$ @*/
    @Override public Type getType() { return TYPE; }
    public static final Type TYPE = Sys.loadType(BMonthEnumTest.class);
    /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final Logger LOGGER = Logger.getLogger("honApplicationHandler");

    @Test
    public void testDefault() {
        Assert.assertSame(BMonthEnum.DEFAULT, BMonthEnum.anyMonth, "DEFAULT must be anyMonth");
    }

    @Test
    public void testAllConstants_resolveByTag() throws Exception {
        for (Field f : BMonthEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BMonthEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                Assert.assertSame(BMonthEnum.make(tag), (BMonthEnum) f.get(null), "Tag must resolve: " + tag);
            }
        }
    }

    @Test
    public void testAllConstants_resolveByOrdinal() throws Exception {
        for (Field f : BMonthEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BMonthEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                String ordConst = toOrdinalConstName(tag);
                int ordinal = BMonthEnum.class.getField(ordConst).getInt(null);
                Assert.assertSame(BMonthEnum.make(ordinal), (BMonthEnum) f.get(null), "Ordinal must resolve for: " + tag);
            }
        }
    }

    @Test
    public void testInvalidOrdinalsReturnNull() throws Exception {
        int min = Integer.MAX_VALUE, max = Integer.MIN_VALUE;
        for (Field f : BMonthEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == int.class) {
                int v = f.getInt(null);
                min = Math.min(min, v);
                max = Math.max(max, v);
            }
        }
        int invalidLow = min - 100;
        int invalidHigh = max + 100;
        Assert.assertNull(BMonthEnum.make(invalidLow), "Invalid low ordinal should return null");
        Assert.assertNull(BMonthEnum.make(invalidHigh), "Invalid high ordinal should return null");
    }

    private static String toOrdinalConstName(String tag) {
        if (tag.contains("_")) return tag.toUpperCase(Locale.ROOT);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tag.length(); i++) {
            char c = tag.charAt(i);
            if (Character.isUpperCase(c) && i > 0) sb.append('_');
            sb.append(Character.toUpperCase(c));
        }
        return sb.toString();
    }
}

