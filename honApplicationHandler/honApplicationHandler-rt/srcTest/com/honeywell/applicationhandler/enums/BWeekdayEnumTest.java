/*
 * Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Locale;
import java.util.logging.Logger;

/**
 * Unit tests for BWeekdayEnum.
 */
@NiagaraType
public class BWeekdayEnumTest extends BTestNg {
    /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
    /*@ $com.honeywell.applicationhandler.enums.BWeekdayEnumTest(000000003)1.0$ @*/
    @Override public Type getType() { return TYPE; }
    public static final Type TYPE = Sys.loadType(BWeekdayEnumTest.class);
    /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

    private static final Logger LOGGER = Logger.getLogger("honApplicationHandler");

    @Test
    public void testDefault() {
        Assert.assertSame(BWeekdayEnum.DEFAULT, BWeekdayEnum.anyDay, "DEFAULT must be anyDay");
    }

    @Test
    public void testAllConstants_resolveByTag() throws Exception {
        for (Field f : BWeekdayEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BWeekdayEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                Assert.assertSame(BWeekdayEnum.make(tag), (BWeekdayEnum) f.get(null), "Tag must resolve: " + tag);
            }
        }
    }

    @Test
    public void testAllConstants_resolveByOrdinal() throws Exception {
        for (Field f : BWeekdayEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BWeekdayEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                String ordConst = toOrdinalConstName(tag);
                int ordinal = BWeekdayEnum.class.getField(ordConst).getInt(null);
                Assert.assertSame(BWeekdayEnum.make(ordinal), (BWeekdayEnum) f.get(null), "Ordinal must resolve for: " + tag);
            }
        }
    }

    @Test
    public void testInvalidOrdinalsReturnNull() throws Exception {
        int min = Integer.MAX_VALUE, max = Integer.MIN_VALUE;
        for (Field f : BWeekdayEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == int.class) {
                int v = f.getInt(null);
                min = Math.min(min, v);
                max = Math.max(max, v);
            }
        }
        int invalidLow = min - 100;
        int invalidHigh = max + 100;
        Assert.assertNull(BWeekdayEnum.make(invalidLow), "Invalid low ordinal should return null");
        Assert.assertNull(BWeekdayEnum.make(invalidHigh), "Invalid high ordinal should return null");
    }

    private static String toOrdinalConstName(String tag) {
        if (tag.contains("_")) return tag.toUpperCase(Locale.ROOT);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tag.length(); i++) {
            char c = tag.charAt(i);
            if (Character.isUpperCase(c) && i > 0) sb.append('_');
            sb.append(Character.toUpperCase(c));
        }
        return sb.toString();
    }
}

