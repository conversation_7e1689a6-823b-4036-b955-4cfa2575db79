/*
 * Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Locale;
import java.util.logging.Logger;

/**
 * Unit tests for BUnitGroupEnum.
 */
@NiagaraType
public class BUnitGroupEnumTest extends BTestNg {
    /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
    /*@ $com.honeywell.applicationhandler.enums.BUnitGroupEnumTest(000000005)1.0$ @*/
    @Override public Type getType() { return TYPE; }
    public static final Type TYPE = Sys.loadType(BUnitGroupEnumTest.class);
    /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/

    private static final Logger LOGGER = Logger.getLogger("honApplicationHandler");

    @Test
    public void testDefault() {
        Assert.assertSame(BUnitGroupEnum.DEFAULT, BUnitGroupEnum.none, "DEFAULT must be none");
    }

    @Test
    public void testAllConstants_resolveByTag() throws Exception {
        for (Field f : BUnitGroupEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BUnitGroupEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                Assert.assertSame(BUnitGroupEnum.make(tag), (BUnitGroupEnum) f.get(null), "Tag must resolve: " + tag);
            }
        }
    }

    @Test
    public void testAllConstants_resolveByOrdinal() throws Exception {
        for (Field f : BUnitGroupEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == BUnitGroupEnum.class && !"DEFAULT".equals(f.getName())) {
                String tag = f.getName();
                String ordConst = toOrdinalConstName(tag);
                int ordinal = BUnitGroupEnum.class.getField(ordConst).getInt(null);
                Assert.assertSame(BUnitGroupEnum.make(ordinal), (BUnitGroupEnum) f.get(null), "Ordinal must resolve for: " + tag);
            }
        }
    }

    @Test
    public void testInvalidOrdinalsReturnNull() throws Exception {
        int min = Integer.MAX_VALUE, max = Integer.MIN_VALUE;
        for (Field f : BUnitGroupEnum.class.getFields()) {
            if (Modifier.isStatic(f.getModifiers()) && f.getType() == int.class) {
                int v = f.getInt(null);
                min = Math.min(min, v);
                max = Math.max(max, v);
            }
        }
        int invalidLow = min - 100;
        int invalidHigh = max + 100;
        Assert.assertNull(BUnitGroupEnum.make(invalidLow), "Invalid low ordinal should return null");
        Assert.assertNull(BUnitGroupEnum.make(invalidHigh), "Invalid high ordinal should return null");
    }

    @Test
    public void testGetValidUnitGroupTagForTagMappingFile() {
        final String AIRFLOW_UNIT = "Airflow Unit";
        final String MEASUREMENT_TYPE = "Measurement Type";
        final String NONE = "None";
        final String UNKNOWN = "Unknown";

        Assert.assertEquals(BUnitGroupEnum.getValidUnitGroupTagForTagMappingFile(AIRFLOW_UNIT), "airflowUnit");
        Assert.assertEquals(BUnitGroupEnum.getValidUnitGroupTagForTagMappingFile(MEASUREMENT_TYPE), "measurementType");
        Assert.assertEquals(BUnitGroupEnum.getValidUnitGroupTagForTagMappingFile(NONE), "none");
        Assert.assertEquals(BUnitGroupEnum.getValidUnitGroupTagForTagMappingFile(UNKNOWN), "");
    }

    private static String toOrdinalConstName(String tag) {
        if (tag.contains("_")) return tag.toUpperCase(Locale.ROOT);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tag.length(); i++) {
            char c = tag.charAt(i);
            if (Character.isUpperCase(c) && i > 0) sb.append('_');
            sb.append(Character.toUpperCase(c));
        }
        return sb.toString();
    }
}

