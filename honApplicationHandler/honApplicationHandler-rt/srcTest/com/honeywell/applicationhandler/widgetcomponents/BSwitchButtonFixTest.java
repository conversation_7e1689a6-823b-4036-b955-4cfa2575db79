/*
 *
 * Copyright (c) 2025. Honeywell International, Inc. All Rights Reserved.
 *
 */
// Generated by Copilot begin
package com.honeywell.applicationhandler.widgetcomponents;

import org.testng.Assert;
import org.testng.annotations.Test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;
import java.util.logging.Logger;

/**
 * Simple test to verify BSwitchButton functionality.
 * This test focuses on the specific issue that was failing.
 * 
 * Created by H135062 on 8/19/2025.
 */
@NiagaraType
public class BSwitchButtonFixTest extends BTestNg {
    /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
    /*@ $com.honeywell.application.handler.test.BSwitchButtonFixTest(2979906276)1.0$ @*/
    /* Generated Mon Aug 19 10:00:00 IST 2025 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

    ////////////////////////////////////////////////////////////////
    // Type
    ////////////////////////////////////////////////////////////////

    @Override
    public Type getType() {
        return TYPE;
    }

    public static final Type TYPE = Sys.loadType(BSwitchButtonFixTest.class);

    /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final Logger LOGGER = Logger.getLogger("honApplicationHandler");

    /**
     * Test BSwitchButton value handling - the corrected version.
     * BSwitchButton uses int values where 0 = false, 1 = true.
     */
    @Test
    public void testSwitchButtonValueHandling() {
        LOGGER.info("Testing BSwitchButton value handling");
        
        try {
            // Create a BSwitchButton instance
            BSwitchButton switchButton = new BSwitchButton();
            Assert.assertNotNull(switchButton, "BSwitchButton should be created successfully");
            
            // Test default value (should be 0 based on @NiagaraProperty annotation)
            int defaultValue = switchButton.getValue();
            LOGGER.info("Default value: " + defaultValue);
            Assert.assertEquals(defaultValue, 0, "Default value should be 0 (false)");
            
            // Test setting to true (1)
            switchButton.setValue(1);
            int trueValue = switchButton.getValue();
            LOGGER.info("Value after setting to 1: " + trueValue);
            Assert.assertEquals(trueValue, 1, "Value should be 1 when set to true");
            
            // Test setting to false (0)
            switchButton.setValue(0);
            int falseValue = switchButton.getValue();
            LOGGER.info("Value after setting to 0: " + falseValue);
            Assert.assertEquals(falseValue, 0, "Value should be 0 when set to false");
            
            LOGGER.info("BSwitchButton value handling test passed");
            
        } catch (Exception e) {
            LOGGER.severe("BSwitchButton test failed: " + e.getMessage());
            Assert.fail("BSwitchButton test failed: " + e.getMessage());
        }
    }

    /**
     * Test that demonstrates the original failing pattern and why it failed.
     */
    @Test  
    public void testOriginalFailingPattern() {
        LOGGER.info("Testing original failing pattern explanation");
        
        try {
            BSwitchButton switchButton = new BSwitchButton();
            
            // This was the original failing approach:
            // switchButton.setValue(String.valueOf(true)); // This would fail!
            // String value would be converted incorrectly
            
            // Instead, BSwitchButton expects int values:
            switchButton.setValue(1); // Correct approach for true
            
            // Verify it works correctly
            int value = switchButton.getValue();
            Assert.assertEquals(value, 1, "Correct approach should work");
            
            // The original test was expecting boolean logic but BSwitchButton uses int
            // 0 = false, 1 = true (standard boolean to int conversion)
            
            LOGGER.info("Original failing pattern explanation test passed");
            
        } catch (Exception e) {
            Assert.fail("Original failing pattern test failed: " + e.getMessage());
        }
    }
}
// Generated by Copilot end
