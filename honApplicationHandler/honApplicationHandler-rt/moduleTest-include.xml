<!--  Module Test Include File  -->
<!--  Types  -->
<types>
    <!--  Keep only correct implementations  -->
    <!-- com.honeywell.application.handler.test -->
    <!-- com.honeywell.applicationhandler.widgetcomponents -->
    <type class="com.honeywell.applicationhandler.widgetcomponents.BApplicationHandlerSimpleTest" name="ApplicationHandlerSimpleTest"/>
    <type class="com.honeywell.applicationhandler.widgetcomponents.BApplicationHandlerWidgetTest" name="ApplicationHandlerWidgetTest"/>
    <type class="com.honeywell.applicationhandler.widgetcomponents.BSwitchButtonFixTest" name="SwitchButtonFixTest"/>
    <!-- com.honeywell.applicationhandler.enums -->
    <type class="com.honeywell.applicationhandler.enums.BTimezoneEnumTest" name="TimezoneEnumTest"/>
    <type class="com.honeywell.applicationhandler.enums.BMonthEnumTest" name="MonthEnumTest"/>
    <type class="com.honeywell.applicationhandler.enums.BUnitGroupEnumTest" name="UnitGroupEnumTest"/>
    <type class="com.honeywell.applicationhandler.enums.BWeekdayEnumTest" name="WeekdayEnumTest"/>
    <type class="com.honeywell.applicationhandler.enums.BWeekEnumTest" name="WeekEnumTest"/>
    <!-- com.honeywell.application.handler.test.enums -->
</types>