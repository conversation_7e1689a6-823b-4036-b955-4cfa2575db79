<?xml version="1.0" encoding="UTF-8"?>
<bajaObjectGraph version="4.0" reversibleEncodingKeySource="none" FIPSEnabled="false" reversibleEncodingValidator="[null.1]=">
	<p h="1" m="b=baja" t="b:UnrestrictedFolder">
		<!-- /Rule$20Utils -->
		<p n="Rule$20Utils" h="5" t="b:Folder">
			<p n="wsAnnotation" t="b:WsAnnotation" v="10,15,8" />
			<!-- /Rule$20Utils/Visibility$20Rule -->
			<p n="Visibility$20Rule" h="6" m="hah=honApplicationHandler" t="hah:VisibilityRuleShell">
			</p>
			<!-- /Rule$20Utils/Value$20Rule -->
			<p n="Value$20Rule" h="7" t="hah:ValueRuleShell">
			</p>
			<!-- /Rule$20Utils/Constraint$20Rule -->
			<p n="Constraint$20Rule" h="8" t="hah:ConstraintRuleShell">
			</p>
			<!-- /Rule$20Utils/TerminalAssignmentValue$20Rule -->
			<p n="TerminalAssignmentValue$20Rule" h="9" t="hah:TerminalAssignmentValueRuleShell">
			</p>
		</p>
		<!-- /honWizSelectors -->
		<p n="UI$20Widget$20Tags" h="10" t="b:UnrestrictedFolder">
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="AirflowUnit" h="11" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="AirflowUnit" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="DuctAreaCalculator" h="12" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="DuctAreaCalculator" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="Dropdown" h="13" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="Dropdown" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="MeasurementType" h="14" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="MeasurementType" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="NumberInput" h="15" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="NumberInput" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="RangeSlider" h="16" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="RangeSlider" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="SingleSlider" h="18" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="SingleSlider" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="SelectWidget" h="20" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="SelectWidget" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="SwitchButton" h="21" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="SwitchButton" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="Schedule" h="22" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="Schedule" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="TextInput" h="23" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="TextInput" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
			<!-- /honWizSelectors/honWidgetSelector -->
			<p n="Timezone" h="24" m="hah=honApplicationHandler" t="hah:HonWidgetSelector">
				<p n="widgetType" f="" t="b:String" v="Timezone" />
				<p n="wsAnnotation" t="b:WsAnnotation" v="31,21,8" />
			</p>
		</p>
	</p>
</bajaObjectGraph>
