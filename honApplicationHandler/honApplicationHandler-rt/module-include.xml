<!-- Module Include File -->
<!-- Types -->
<types>
  <!-- Type Example:<type name="MyClass" class="com.acme.BMyClass"/> -->
  <!--com.honeywell.applicationhandler.device-->
  <type class="com.honeywell.applicationhandler.device.BHonWizardGlobalStore" name="HonWizardGlobalStore"/>
  <type class="com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice" name="IHoneywellConfigurableDevice"/>
  <type class="com.honeywell.applicationhandler.device.BHonWizardRuleStore" name="HonWizardRuleStore"/>
  <type class="com.honeywell.applicationhandler.device.BHonWizardSlotDetails" name="HonWizardSlotDetails"/>
  <type class="com.honeywell.applicationhandler.device.BHonWizardMetaData" name="HonWizardMetaData"/>
  <!--com.honeywell.applicationhandler.device.points-->
  <type class="com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint" name="IHonWizardBooleanPoint"/>
  <type class="com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint" name="IHonWizardEnumPoint"/>
  <type class="com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint" name="IHonWizardNumericPoint"/>
  <type class="com.honeywell.applicationhandler.device.points.BIHonWizardPoint" name="IHonWizardPoint"/>
  <type class="com.honeywell.applicationhandler.device.points.BIHonWizardEnumSchedule" name="IHonWizardEnumSchedule"/>
  <!--com.honeywell.applicationhandler.ontology-->
  <type class="com.honeywell.applicationhandler.ontology.BHonWizardTag" name="HonWizardTag"/>
  <!--com.honeywell.applicationhandler.ontology.selector-->
  <type class="com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector" name="HonWizPointSelector"/>
  <type class="com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector" name="HonWizSlotSelector"/>
  <type class="com.honeywell.applicationhandler.ontology.selector.BHonWizSelector" name="HonWizSelector"/>
  <!--com.honeywell.applicationhandler.widgetcomponents-->
  <type class="com.honeywell.applicationhandler.widgetcomponents.BOption" name="Option"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BCheckbox" name="Checkbox"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BTimeZone" name="TimeZone"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BDropdown" name="Dropdown"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BNumberInput" name="NumberInput"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BRadioButton" name="RadioButton"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BSelectButton" name="SelectButton"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BSwitchButton" name="SwitchButton"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase" name="WidgetComponentBase"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BSingleSlider" name="SingleSlider"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BRangeSlider" name="RangeSlider"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BSelectWidget" name="SelectWidget"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BTextInput" name="TextInput"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase" name="DynamicWidgetComponentBase"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget" name="SchedulePageWidget"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget" name="TerminalAssignmentWidget"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator" name="DuctAreaCalculator"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetUnitSupportComponentBase" name="DynamicWidgetUnitSupportComponentBase"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BMeasurementType" name="MeasurementType"/>
  <type class="com.honeywell.applicationhandler.widgetcomponents.BAirflowUnit" name="AirflowUnit"/>
  <!--com.honeywell.applicationhandler.common-->
  <type class="com.honeywell.applicationhandler.common.BHonWizardJob" name="HonWizardJob"/>
  <!--com.honeywell.applicationhandler.jobs-->
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob" name="HonWizardRuleParserJob"/>
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardValueSaveJob" name="HonWizardValueSaveJob"/>
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardTagMappingFileGenJob" name="HonWizardTagMappingFileGenJob"/>
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardGlobalStoreGenJob" name="HonWizardGlobalStoreGenJob"/>
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardTagGenJob" name="HonWizardTagGenJob"/>
  <type class="com.honeywell.applicationhandler.jobs.BHonWizardTagValidationJob" name="HonWizardTagValidationJob"/>
  <!--com.honeywell.applicationhandler.utils-->
  <type class="com.honeywell.applicationhandler.utils.BHonWizardRuleShell" name="HonWizardRuleShell"/>
  <type class="com.honeywell.applicationhandler.utils.BValueRuleShell" name="ValueRuleShell"/>
  <type class="com.honeywell.applicationhandler.utils.BVisibilityRuleShell" name="VisibilityRuleShell"/>
  <type class="com.honeywell.applicationhandler.utils.BTerminalAssignmentValueRuleShell" name="TerminalAssignmentValueRuleShell"/>
  <type class="com.honeywell.applicationhandler.utils.BConstraintRuleShell" name="ConstraintRuleShell"/>
  <!--com.honeywell.applicationhandler.enums-->
  <type class="com.honeywell.applicationhandler.enums.BMonthEnum" name="MonthEnum"/>
  <type class="com.honeywell.applicationhandler.enums.BWeekdayEnum" name="WeekdayEnum"/>
  <type class="com.honeywell.applicationhandler.enums.BWeekEnum" name="WeekEnum"/>
  <type class="com.honeywell.applicationhandler.enums.BTimezoneEnum" name="TimezoneEnum"/>
  <type class="com.honeywell.applicationhandler.enums.BUnitGroupEnum" name="UnitGroupEnum"/>
  <!--com.honeywell.applicationhandler.palette-->
  <type class="com.honeywell.applicationhandler.palette.BHonWidgetSelector" name="HonWidgetSelector"/>
</types>