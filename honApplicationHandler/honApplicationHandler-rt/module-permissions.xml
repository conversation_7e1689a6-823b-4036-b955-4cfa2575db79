<permissions>
  <niagara-permission-groups type="all">
    <!-- Insert any global permissions here. -->
    <req-permission>
        <name>GET_ENVIRONMENT_VARIABLES</name>
        <purposeKey>Need access to get environment variables</purposeKey>
        <parameters>
            <parameter name="variables" value="SOURCE_DATE_EPOCH"/>
        </parameters>
    </req-permission>
  </niagara-permission-groups>
  <niagara-permission-groups type="workbench">
    <!-- Insert any workbench specific permissions here. -->
  </niagara-permission-groups>
  <niagara-permission-groups type="station">
    <!--<req-permission>-->
    <!--<name>NETWORK_COMMUNICATION</name>-->
    <!--<purposeKey>Outside access for Driver</purposeKey>-->
    <!--<parameters>-->
      <!--<parameter name="hosts" value="127.0.0.1"/>-->
      <!--<parameter name="ports" value="*"/>-->
      <!--<parameter name="type" value="all"/>-->
    <!--</parameters>-->
    <!--</req-permission>-->
      <req-permission>
          <name>MANAGE_EXECUTION</name>
          <purposeKey>This module needs to require modifyThread permission to shutdown scheduler.</purposeKey>

      </req-permission>

  </niagara-permission-groups>
</permissions>
