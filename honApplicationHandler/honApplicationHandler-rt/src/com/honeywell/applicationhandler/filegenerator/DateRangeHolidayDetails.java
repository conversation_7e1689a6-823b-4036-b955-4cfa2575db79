/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import com.tridium.json.JSONObject;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Feb 20, 2025
 */
public class DateRangeHolidayDetails extends HolidayDetails {

	private int fromDate;
	private String fromMonth;
	private int toDate;
	private String toMonth;
	
	public DateRangeHolidayDetails(String name, int fromDate, String fromMonth, int toDate, String toMonth) {
		super(name, DATE_RANGE);
		this.fromDate = fromDate;
		this.fromMonth = fromMonth;
		this.toDate = toDate;
		this.toMonth = toMonth;
	}
	
	public DateRangeHolidayDetails(JSONObject dateRangeHolidayJSON) {
		super(dateRangeHolidayJSON.getString("name"), dateRangeHolidayJSON.getString("holidayType"));
		this.fromDate = dateRangeHolidayJSON.getInt("fromDate");
		this.fromMonth = dateRangeHolidayJSON.getString("fromMonth");
		this.toDate = dateRangeHolidayJSON.getInt("toDate");
		this.toMonth = dateRangeHolidayJSON.getString("toMonth");
	}

	@Override
	public JSONObject toJSON() {
		JSONObject dateRangeJSON = new JSONObject();
		dateRangeJSON.put("name", getName());
		dateRangeJSON.put("holidayType", getHolidayType());
		dateRangeJSON.put("fromMonth", fromMonth);
		dateRangeJSON.put("fromDate", fromDate);
		dateRangeJSON.put("toMonth", toMonth);
		dateRangeJSON.put("toDate", toDate);
		
		return dateRangeJSON;
	}

	public int getFromDate() {
		return fromDate;
	}

	public String getFromMonth() {
		return fromMonth;
	}

	public int getToDate() {
		return toDate;
	}

	public String getToMonth() {
		return toMonth;
	}
	
	private static final String DATE_RANGE = "dateRangeEveryYear";

}
