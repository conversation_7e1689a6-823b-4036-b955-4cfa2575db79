/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import com.tridium.json.JSONObject;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Feb 20, 2025
 */
public abstract class HolidayDetails {
	
	private String name;
	private String holidayType;

	public String getName() {
		return name;
	}
	
	public String getHolidayType() {
		return holidayType;
	}

	protected HolidayDetails(String name, String holidayType) {
		super();
		this.name = name;
		this.holidayType = holidayType;
	}

	public abstract JSONObject toJSON();
	
}
