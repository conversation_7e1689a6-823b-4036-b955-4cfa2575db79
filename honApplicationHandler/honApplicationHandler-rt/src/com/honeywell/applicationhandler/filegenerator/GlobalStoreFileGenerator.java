/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.filegenerator;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.bacnet.enums.BBacnetObjectType;
import javax.baja.naming.SlotPath;
import javax.baja.schedule.BAbstractSchedule;
import javax.baja.schedule.BCompositeSchedule;
import javax.baja.schedule.BDailySchedule;
import javax.baja.schedule.BDateRangeSchedule;
import javax.baja.schedule.BDateSchedule;
import javax.baja.schedule.BDaySchedule;
import javax.baja.schedule.BTimeSchedule;
import javax.baja.schedule.BWeekAndDaySchedule;
import javax.baja.schedule.BWeekSchedule;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BInteger;
import javax.baja.sys.BString;
import javax.baja.sys.BTime;
import javax.baja.sys.BValue;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.units.BUnit;
import javax.baja.util.BFacetsMap;
import javax.baja.util.BUuid;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.generate.HonWizGlobalStoreGenerator;
import com.honeywell.applicationhandler.enums.BMonthEnum;
import com.honeywell.applicationhandler.enums.BWeekEnum;
import com.honeywell.applicationhandler.enums.BWeekdayEnum;
import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;

import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.HoneywellWidgetStaticDataUtil;
import com.honeywell.applicationhandler.widgetcomponents.BOption;
import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;
import com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;



public class GlobalStoreFileGenerator {
	private static final Lexicon lex = Lexicon.make(GlobalStoreFileGenerator.class);
    public static JSONObject generateJson(BHonWizardGlobalStore dynamicStoreComponent, BIHoneywellConfigurableDevice device) {
		BUuid globalStoreUuid = device.getGlobalStoreUuid(false);
		if(null == globalStoreUuid || globalStoreUuid.getMostSignificant() == 0){
            //if learn from controller, global store uuid is null, we need to regenerate global store with new UUID
			HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(device);
			BasicContext context = new BasicContext();
			try {
				generator.generate(context, true);
				globalStoreUuid = device.getGlobalStoreUuid(false);
			} catch (Exception e) {
				HoneywellDeviceWizardLogger.severe(lex.getText("BHonWizardGlobalStoreGenJob.fail", device.getDeviceName(),
						Arrays.toString(e.getStackTrace())));
			}
		}
		return generateJson(dynamicStoreComponent, false, globalStoreUuid);
    }

    public static JSONObject generateJson(BHonWizardGlobalStore dynamicStoreComponent, boolean writeToFile, BUuid globalStoreFileUuid) {
        JSONObject dynamicStore = new JSONObject();
        Map<String, Integer> pageOrderMap = dynamicStoreComponent.getPageOrderMap();
        List<ScheduleDetailsHolder> scheduleDetails = new ArrayList<>();
		String globalStoreStaticData = HoneywellWidgetStaticDataUtil.loadStaticDataFromJson(globalStoreFileUuid);
		if(null == globalStoreStaticData){
			return dynamicStore;
		}
		JSONObject globalStoreStaticJson = new JSONObject(globalStoreStaticData);
		for (Property tabPageStoreProperty : dynamicStoreComponent.getDynamicPropertiesArray()) {
			BValue propertyValue = dynamicStoreComponent.get(tabPageStoreProperty);

			if (!(propertyValue instanceof BComponent)) {
				continue;
			}
            JSONObject tabPageJsonObj = null;
            if(!skipGetDataFromStaticJsonFile(propertyValue)){
                tabPageJsonObj = globalStoreStaticJson.getJSONObject(tabPageStoreProperty.getName());
            }
			BComponent tabPageStoreComponent = (BComponent) propertyValue;
			String pageName = SlotPath.unescape(tabPageStoreComponent.getName());
			if (tabPageStoreComponent.getChildren(BSchedulePageWidget.class).length > 0) {
				// Handle schedule separately
				if (pageOrderMap.containsKey(pageName)) {
					ScheduleDetailsHolder holder = new ScheduleDetailsHolder(tabPageStoreComponent.getChildren(BSchedulePageWidget.class)[0], pageName, pageOrderMap.get(pageName));
					scheduleDetails.add(holder);
				} else {
					ScheduleDetailsHolder holder = new ScheduleDetailsHolder(tabPageStoreComponent.getChildren(BSchedulePageWidget.class)[0], pageName, BHonWizardTag.WIZ_PAGE_ORDER.size());
					scheduleDetails.add(holder);
				}
			} else {
				JSONObject tabPageJson = new JSONObject();
				tabPageJson.put("label", pageName);
				JSONObject itemsJson = new JSONObject();
				tabPageJson.put("items", itemsJson);

				if (pageOrderMap.containsKey(pageName)) {
					tabPageJson.put("index", pageOrderMap.get(pageName));
				} else {
					tabPageJson.put("index", BHonWizardTag.WIZ_PAGE_ORDER.size());
				}

				int configItemIndex = 0;
				for (Property configItemProperty : tabPageStoreComponent.getDynamicPropertiesArray()) {
					BComponent configItemComponent = (BComponent) tabPageStoreComponent.get(configItemProperty);
					JSONObject configItemJson = new JSONObject();
					configItemJson.put("index", configItemIndex);
                    buildStaticData(configItemJson, tabPageJsonObj, configItemComponent.getName());
                    buildDynamicData(configItemJson, configItemComponent);
					itemsJson.put(configItemComponent.getName(), configItemJson);
					configItemIndex++;
				}
				dynamicStore.put(tabPageStoreComponent.getName(), tabPageJson);
			}
		}
		
		updateAllScheduleDetailsTOJSON(scheduleDetails, dynamicStore);
        writeWizardConfigureToFile(writeToFile, dynamicStoreComponent, dynamicStore);
        return dynamicStore;
    }

    /**
     * build static data from static json file
     * @param configItemJson
     * @param tabPageJsonObj
     * @param configItemName
     */
    private static void buildStaticData(JSONObject configItemJson, JSONObject tabPageJsonObj, String configItemName){
        if(null != tabPageJsonObj) {
            JSONObject configItemJsonObj = tabPageJsonObj.getJSONObject(configItemName);
            for (String key : configItemJsonObj.keySet()) {
                configItemJson.put(key, configItemJsonObj.get(key));
            }
        }
    }

    /**
     * build dynamic data from wizard configure component
     * @param configItemJson
     * @param configItemComponent
     */
    private static void buildDynamicData(JSONObject configItemJson, BComponent configItemComponent){
        for (Property data : configItemComponent.getPropertiesArray()) {
            BValue propertyItemValue = configItemComponent.get(data);
			try {
				if (propertyItemValue instanceof BComponent) {
					String componentName = data.getName();
					BComponent itemComponent = (BComponent) propertyItemValue;
					if (componentName.equalsIgnoreCase("options")) {
						JSONArray options = new JSONArray();
						for (Property optionProperty : itemComponent.getDynamicPropertiesArray()) {
							if (itemComponent.get(optionProperty) instanceof BOption) {
								BOption option = (BOption) itemComponent.get(optionProperty);
								JSONObject optionJson = new JSONObject();

								optionJson.put("key", option.getKey());
								optionJson.put("text", option.getText());
								optionJson.put("value", option.getValue());
								if (!option.getImage().isEmpty()) {
									optionJson.put("image", option.getImage());
								}
								options.put(optionJson);
							}
						}
						configItemJson.put("options", options);
					}

				} else if (propertyItemValue instanceof BString) {
					configItemJson.put(data.getName(), propertyItemValue.toString());
				} else if (propertyItemValue instanceof BInteger) {
					configItemJson.put(data.getName(), ((BInteger) propertyItemValue).getInt());
				} else if (propertyItemValue instanceof BDouble) {
					configItemJson.put(data.getName(), ((BDouble) propertyItemValue).getFloat());
				} else if (propertyItemValue instanceof BBoolean) {
					configItemJson.put(data.getName(), ((BBoolean) propertyItemValue).getBoolean());
				} else if (propertyItemValue instanceof BBacnetObjectIdentifier) {
					BBacnetObjectType objectType = BBacnetObjectType.make(((BBacnetObjectIdentifier) propertyItemValue).getObjectType());
					int instanceNumber = ((BBacnetObjectIdentifier) propertyItemValue).getInstanceNumber();
					configItemJson.put(data.getName(), objectType.getTag() + ":" + instanceNumber);
				} else if (propertyItemValue instanceof BFacetsMap) {
					BFacetsMap facetsMap = (BFacetsMap) propertyItemValue;
					if (null != facetsMap.get("unit") && null != facetsMap.get("unit").get("units") &&
							facetsMap.get("unit").get("units") instanceof BUnit) {
						configItemJson.put("unitName", ((BUnit) facetsMap.get("unit").get("units")).getUnitName());
					} else if (null != facetsMap.get("unit") && null != facetsMap.get("unit").get("units")) {
						configItemJson.put("unitName", facetsMap.get("unit").get("units"));
					}
				} else if (propertyItemValue instanceof BEnum) {
					configItemJson.put(data.getName(), ((BEnum) propertyItemValue).getOrdinal());
				}
			} catch (Exception e) {
			    HoneywellDeviceWizardLogger.severe("Exception occurred while generating json for " + data.getName() + " of " + configItemComponent.getName(), e);
			    configItemJson.put(data.getName(), "");
			}
        }
    }

    /**
     * write wizard configure data to file for debug purpose
     * @param writeToFile
     * @param dynamicStoreComponent
     * @param dynamicStore
     */
    private static void writeWizardConfigureToFile(boolean writeToFile, BHonWizardGlobalStore dynamicStoreComponent, JSONObject dynamicStore) {
        if(writeToFile) {
            File userConfigData = new File(Sys.getStationHome(), "userConfigData");
            userConfigData.mkdir();
            String path = BHonWizardRuleParserJob.getFolderPath((BComponent) dynamicStoreComponent.getParent());
            File devicePath = new File(userConfigData, path);
            devicePath.mkdir();
            File userConfig = new File(devicePath, "userConfig.hfg");

            try (PrintWriter writer = new PrintWriter(new FileWriter(userConfig.getAbsolutePath()))) {
                writer.print(dynamicStore.toString());
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * terminal assignment widget and schedule component do not have static json file. So, we need to get data from the component itself.
     * @param propertyValue
     * @return
     */
    private static boolean skipGetDataFromStaticJsonFile(BValue propertyValue) {
        if (propertyValue instanceof BTerminalAssignmentWidget){
            return true;
        }
        BComponent comp = (BComponent) propertyValue;
        if(comp.getChildren(BSchedulePageWidget.class).length > 0){
            return true;
        }
        return false;

    }
    
	private static void updateAllScheduleDetailsTOJSON(List<ScheduleDetailsHolder> scheduleDetails, JSONObject dynamicStore) {
		JSONArray allSchedules = new JSONArray();
		for (ScheduleDetailsHolder scheduleDetail : scheduleDetails) {
			JSONObject tabPageJson = generateAndUpdateOneScheduleDetailToJSON(scheduleDetail.getSchedulePageWidget(), scheduleDetail.getPageName(),
					scheduleDetail.getIndex());
			if (null != tabPageJson) {
				allSchedules.put(tabPageJson);
			}
		}
		dynamicStore.put("Scheduling", allSchedules);
	}

	public static JSONObject generateAndUpdateOneScheduleDetailToJSON(BSchedulePageWidget scheduleComponent, String schedulePageName, int index) {
		JSONObject tabPageJson = new JSONObject();
    	tabPageJson.put("index", index);
		tabPageJson.put("label", schedulePageName);
		tabPageJson.put("componentType", "ScheduleWidget");
		JSONObject schdeulesJson = new JSONObject();
		JSONObject itemsJson = new JSONObject();
		JSONArray localSchTypeOpt = new JSONArray();
		JSONArray events =  new JSONArray();
		JSONObject holidaysJson = new JSONObject();
		
		if(!updateScheduleFacetDetails(scheduleComponent, localSchTypeOpt)) {
			// Failure occurred while updating Facet details
			return null;
		}
		
		schdeulesJson.put("localScheduleTypeOptions", localSchTypeOpt);
		BEnum defaultOut = ((BEnum) scheduleComponent.getEnumSchedule().getDefaultOutput().getValueValue());
		schdeulesJson.put("defaultLocalScheduleType", scheduleOrdinalToWizardOrdinalMap.get(defaultOut.getOrdinal()));
		
		if(!updateScheduleEventDetails(scheduleComponent, events)) {
			// Failure occurred while updating Facet details
			return null;
		}
		
		updateSpecialEvents(scheduleComponent, holidaysJson);
		
		// Reset the maps for next schedule page
		scheduleOrdinalToWizardOrdinalMap.clear();
		
		schdeulesJson.put("Events", events);
		itemsJson.put("Schedules", schdeulesJson);
		itemsJson.put("Holidays", holidaysJson);
		tabPageJson.put("items", itemsJson);
		return tabPageJson;
	}

	private static boolean updateScheduleFacetDetails(BSchedulePageWidget scheduleComponent, JSONArray localSchTypeOpt) {
		BEnumRange range = (BEnumRange) scheduleComponent.getEnumSchedule().getFacets().get(BFacets.RANGE);
		int i = 1;
		for (int ordinal : range.getOrdinals()) {
			JSONObject scheduleType = new JSONObject();
			String tag = range.getTag(ordinal);
			// Expected values : Occupied, Unoccupied, Standby
			if(!tag.equalsIgnoreCase("null")) {
					scheduleOrdinalToWizardOrdinalMap.put(ordinal, i);
					scheduleType.put("text", tag);
					scheduleType.put("value", i);
					scheduleType.put("key", i);
					localSchTypeOpt.put(scheduleType);
					i++;
			}
		}
		return true;
	}
	
	private static void updateSpecialEvents(BSchedulePageWidget scheduleComponent, JSONObject holidaysJson) {
		BCompositeSchedule compSchedule = scheduleComponent.getEnumSchedule().getSchedule();
		BCompositeSchedule[] specialSchedules = compSchedule.getChildren(BCompositeSchedule.class);
		JSONArray recurringJSON = new JSONArray();
		JSONArray specificDateJSON = new JSONArray();
		JSONArray dateRangeJSON = new JSONArray();
		for(BCompositeSchedule specialSchedule : specialSchedules) {
			for(BDailySchedule dailySch : specialSchedule.getChildren(BDailySchedule.class)) {
				BAbstractSchedule absSch = dailySch.getDays();
				if(absSch instanceof BWeekAndDaySchedule) {
					BWeekAndDaySchedule weekAndDay = (BWeekAndDaySchedule) absSch;
					String monthTag = BMonthEnum.make(weekAndDay.getMonth()).getTag();
					String weekDayTag = BWeekdayEnum.make(weekAndDay.getWeekday()).getTag();
					String weekTag = BWeekEnum.make(weekAndDay.getWeek()).getTag();
					RecurringHolidayDetails details = new RecurringHolidayDetails(SlotPath.unescape(dailySch.getName()),monthTag, weekTag, weekDayTag);
					recurringJSON.put(details.toJSON());
				} else if (absSch instanceof BDateSchedule) {
					BDateSchedule dateSchedule = (BDateSchedule) absSch;
					String monthTag = BMonthEnum.make(dateSchedule.getMonth()).getTag();
					int date = dateSchedule.getDay();
					SpecificDateHolidayDetails details = new SpecificDateHolidayDetails(SlotPath.unescape(dailySch.getName()), date, monthTag);
					specificDateJSON.put(details.toJSON());
				} else if (absSch instanceof BDateRangeSchedule) {
					BDateRangeSchedule dateRangeSchedule = (BDateRangeSchedule) absSch;
					BDateSchedule startDateSch = dateRangeSchedule.getStart();
					BDateSchedule endDateSch = dateRangeSchedule.getEnd();
					String fromMonthTag = BMonthEnum.make(startDateSch.getMonth()).getTag();
					int fromDate = startDateSch.getDay();
					String toMonthTag = BMonthEnum.make(endDateSch.getMonth()).getTag();
					int toDate = endDateSch.getDay();
					DateRangeHolidayDetails details = new DateRangeHolidayDetails(SlotPath.unescape(dailySch.getName()), fromDate, fromMonthTag, toDate, toMonthTag);
					dateRangeJSON.put(details.toJSON());
				}
			}
		}
		
		holidaysJson.put("recurringDaysEveryYear", recurringJSON);
		holidaysJson.put("specificDateEveryYear", specificDateJSON);
		holidaysJson.put("dateRangeEveryYear", dateRangeJSON);
	}
	
	private static boolean updateScheduleEventDetails(BSchedulePageWidget scheduleComponent, JSONArray events) {
		BCompositeSchedule compSchedule = scheduleComponent.getEnumSchedule().getSchedule();
		int eventId = 0;
		BWeekSchedule[] weekSchedules = compSchedule.getChildren(BWeekSchedule.class);
		for(BWeekSchedule weekSchedule : weekSchedules) {
			for(BDailySchedule dailySch : weekSchedule.getChildren(BDailySchedule.class)) {
				BDaySchedule daySchedule = dailySch.getDay();
				for(BTimeSchedule time : daySchedule.getChildren(BTimeSchedule.class)) {
					JSONObject indiEvent = new JSONObject();
					indiEvent.put("eventId", eventId);
					eventId++;
					// Only add first 3 characters of day name
					indiEvent.put("day", dailySch.getDisplayName(null).substring(0, 3));
					indiEvent.put("start", getConvertedTime(time.getStart(), true));
					indiEvent.put("end", getConvertedTime(time.getFinish(), false));
					if (null == time.getEffectiveValue()) {
						// If effective is not available, fall back to default
						// output.
						indiEvent.put("status", scheduleOrdinalToWizardOrdinalMap
								.get(((BEnum) scheduleComponent.getEnumSchedule().getDefaultOutput().getValueValue()).getOrdinal()));
					} else {
						indiEvent.put("status",
								scheduleOrdinalToWizardOrdinalMap.get(((BEnum) time.getEffectiveValue().getValueValue()).getOrdinal()));
					}
					events.put(indiEvent);
				}
			}
		}
		return true;
	}
	
	private static String getConvertedTime(BTime time, boolean isStartTime) {
		int hour = time.getHour();
		int min = time.getMinute();
		// Special handling for 00:00 - converting it to 24:00 for end time
		if(!isStartTime && hour == 0 && min == 0) {
			hour = 24;
		}
		String hr = Integer.toString(hour);
		String mn = Integer.toString(min);
		if(hour < 10) {
			hr = "0" + hour;
		}
		if(min < 10) {
			mn = "0" + min;
		}
		return hr + ":" + mn;
	}
	
	private static Map<Integer, Integer> scheduleOrdinalToWizardOrdinalMap = new HashMap<>();
    
}

