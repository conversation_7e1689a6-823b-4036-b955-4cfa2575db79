/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import java.util.List;
import java.util.ArrayList;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jan 30, 2025
 */
public class DayScheduleEvent {
	String dayOfEvent;
	List<ScheduleEventStartEndStatus> listOfEventsForDay = new ArrayList<>();
	
	public String getDayOfEvent() {
		return dayOfEvent;
	}
	public void setDayOfEvent(String dayOfEvent) {
		this.dayOfEvent = dayOfEvent;
	}
	public List<ScheduleEventStartEndStatus> getListOfEventsForDay() {
		return listOfEventsForDay;
	}
	public void setListOfEventsForDay(List<ScheduleEventStartEndStatus> listOfEventsForDay) {
		this.listOfEventsForDay = listOfEventsForDay;
	}
	
}
