/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jul 4, 2025
 */
public class ScheduleDetailsHolder {

	private BSchedulePageWidget schedulePageWidget;
	private String pageName;
	private int index;
	
	public ScheduleDetailsHolder(BSchedulePageWidget schedulePageWidget, String pageName, int index) {
		super();
		this.schedulePageWidget = schedulePageWidget;
		this.pageName = pageName;
		this.index = index;
	}

	public BSchedulePageWidget getSchedulePageWidget() {
		return schedulePageWidget;
	}

	public String getPageName() {
		return pageName;
	}

	public int getIndex() {
		return index;
	}
	
}
