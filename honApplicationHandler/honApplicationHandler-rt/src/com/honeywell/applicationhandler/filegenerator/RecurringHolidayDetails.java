/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import com.tridium.json.JSONObject;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Feb 20, 2025
 */
public class RecurringHolidayDetails extends HolidayDetails {

	private String month;
	private String week;
	private String weekday;
	
	public RecurringHolidayDetails(String name, String month, String week, String weekday) {
		super(name, RECURRING_DAYS);
		this.month = month;
		this.week = week;
		this.weekday = weekday;
	}
	
	public RecurringHolidayDetails(JSONObject recurringHolidayJSON) {
		super(recurringHolidayJSON.getString("name"), recurringHolidayJSON.getString("holidayType"));
		this.month = recurringHolidayJSON.getString("month");
		this.week = recurringHolidayJSON.getString("week");
		this.weekday = recurringHolidayJSON.getString("weekday");
	}

	@Override
	public JSONObject toJSON() {
		JSONObject recurringJSON = new JSONObject();
		recurringJSON.put("name", getName());
		recurringJSON.put("holidayType", getHolidayType());
		recurringJSON.put("month", month);
		recurringJSON.put("week", week);
		recurringJSON.put("weekday", weekday);
		
		return recurringJSON;
	}

	public String getMonth() {
		return month;
	}

	public String getWeek() {
		return week;
	}

	public String getWeekday() {
		return weekday;
	}
	
	private static final String RECURRING_DAYS = "recurringDaysEveryYear";

}
