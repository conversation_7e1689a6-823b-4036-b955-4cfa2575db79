/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.filegenerator;

import com.tridium.json.JSONObject;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Feb 20, 2025
 */
public class SpecificDateHolidayDetails extends HolidayDetails {

	private int date;
	private String month;
	
	public SpecificDateHolidayDetails(String name, int date, String month) {
		super(name, SPECIFIC_DATE);
		this.date = date;
		this.month = month;
	}
	
	public SpecificDateHolidayDetails(JSONObject specificDateHolidayJSON) {
		super(specificDateHolidayJSON.getString("name"), specificDateHolidayJSON.getString("holidayType"));
		this.month = specificDateHolidayJSON.getString("month");
		this.date = specificDateHolidayJSON.getInt("date");
	}

	@Override
	public JSONObject toJSON() {
		JSONObject specificDateJSON = new JSONObject();
		specificDateJSON.put("name", getName());
		specificDateJSON.put("holidayType", getHolidayType());
		specificDateJSON.put("month", month);
		specificDateJSON.put("date", date);
		
		return specificDateJSON;
	}

	public int getDate() {
		return date;
	}

	public String getMonth() {
		return month;
	}
	
	private static final String SPECIFIC_DATE = "specificDateEveryYear";
	
}
