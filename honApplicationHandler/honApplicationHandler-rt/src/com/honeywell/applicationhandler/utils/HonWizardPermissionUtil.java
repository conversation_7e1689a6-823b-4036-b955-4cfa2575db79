/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.utils;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import javax.baja.agent.AgentInfo;
import javax.baja.agent.AgentList;
import javax.baja.category.BCategory;
import javax.baja.category.BCategoryMask;
import javax.baja.category.BCategoryService;
import javax.baja.role.BRole;
import javax.baja.role.BRoleService;
import javax.baja.security.BPermissions;
import javax.baja.security.BPermissionsMap;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BInteger;
import javax.baja.sys.BString;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.user.BUser;
import javax.baja.util.Lexicon;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zhang
 */
public class HonWizardPermissionUtil {
    private HonWizardPermissionUtil(){

    }
    private static final Lexicon lex = Lexicon.make(HonWizardPermissionUtil.class);
    /**
     * get wizard related components permission mask, wizard view permission depends on global store component permission
     * if context is not null, the request is from web
     * if context is null, the request is from workbench, we will  call action to get permission mask
     * @param device honeywell device
     * @return wizard view permission mask
     */
    public static Map<String, Integer> getWizardPermissionMask(BIHoneywellConfigurableDevice device, Context context){
        Map<String, Integer> results = new HashMap<>();
        if(null == device){
            return results;
        }
        BHonWizardGlobalStore globalStore = device.getGlobalStore();

        if(null == globalStore){
            return results;
        }
        if(null != context) {
            setPermissionsForWeb(results, device, context);
        } else {
            setPermissionForWb(results, device);
        }
        return results;
    }

    /**
     * when open wizard view from web, we can get user information from context
     * @param results, save permission mask for device and global store component
     * @param device, honeywell device
     * @param context, context
     */
    private static void setPermissionsForWeb(Map<String, Integer> results, BIHoneywellConfigurableDevice device, Context context){
        BUser user = context.getUser();
        if(null != user) {
            BasicContext userContext = new BasicContext(user);
            BPermissions permissions = (device.getGlobalStore()).getPermissions(userContext);
            if(null != permissions) {
                results.put(GLOBAL_STORE_PERM, permissions.getMask());
            }
            BPermissions permissionDevice = ((BComponent)device).getPermissions(userContext);
            if(null != permissionDevice) {
                results.put(DEVICE_PERM, permissionDevice.getMask());
            }
        }

    }

    /**
     * set permission for wizard view when request is from workbench, as request is from workbench, the context is null
     * so for this case, we need to call RT action to retrieve permission mask from device
     * @param results, save permission mask for device and global store component
     * @param device, honeywell device
     */
    private static void setPermissionForWb(Map<String, Integer> results, BIHoneywellConfigurableDevice device){
        Action honWizardPermissionMaskAction = device.getHonWizardPermissionMaskAction();
        if(null != honWizardPermissionMaskAction){
            BString invokeResult = (BString) ((BComponent) device).invoke(honWizardPermissionMaskAction, null);
            if(null != invokeResult){
                String permString = invokeResult.getString();
                String[] split = permString.split(";");
                if(split.length == 2){
                    results.put(GLOBAL_STORE_PERM, Integer.parseInt(split[0]));
                    results.put(DEVICE_PERM, Integer.parseInt(split[1]));
                }

            }
        }else{
            //if getHonWizardPermissionMaskAction has not been implemented, it means permission is not required.
            results.put(GLOBAL_STORE_PERM, BPermissions.all.getMask());
            results.put(DEVICE_PERM, BPermissions.all.getMask());
        }
    }




    /**
     * check if wizard view is readonly
     * @param device, honeywell device
     * @return true or false
     */
    public static boolean isWizardViewReadonly(BIHoneywellConfigurableDevice device, Context context){
        if(null == device){
            return false;
        }
        Map<String, Integer> wizardPermissionMask = getWizardPermissionMask(device, context);
        if(!wizardPermissionMask.isEmpty()){
            int permissionMask = wizardPermissionMask.get(GLOBAL_STORE_PERM);
            return ((permissionMask & BPermissions.OPERATOR_READ) == BPermissions.OPERATOR_READ || (permissionMask & BPermissions.ADMIN_READ) == BPermissions.ADMIN_READ) && (permissionMask & BPermissions.ADMIN_WRITE) == 0 &&
                    (permissionMask & BPermissions.OPERATOR_WRITE) == 0;
        }
        return false;
    }

    /**
     * create hon wizard category if not exist
     * @return category index
     */
    public static int createHonWizardCategory(String categoryName){
        BCategoryService categoryService = (BCategoryService) Sys.getService(BCategoryService.TYPE);
        if(null == categoryService){
            return -1;
        }

        BCategory[] categories = categoryService.getChildren(BCategory.class);
        int index = 0;
        boolean isFound = false;
        if(null != categories && categories.length > 0) {
            for (int i = 0; i < categories.length; i++) {
                if(categories[i].getName().equalsIgnoreCase(categoryName)){
                    index = categories[i].getIndex();
                    isFound = true;
                    break;
                }
                if(index < categories[i].getIndex()){
                    index = categories[i].getIndex();
                }
            }
        }
        if(!isFound){
            BCategory wizardCategory = new BCategory();
            wizardCategory.setIndex(index + 1);
            categoryService.add(categoryName, wizardCategory, null);
            return index + 1;
        }
        return index;
    }


    /**
     * get hon wizard category index according honWizardCategory name
     * @return category index
     */
    public static int getHonWizardCategoryIndex(String categoryName){
        BCategoryService categoryService = (BCategoryService) Sys.getService(BCategoryService.TYPE);
        int index = -1;
        if(null != categoryService) {
            BCategory[] categories = categoryService.getChildren(BCategory.class);
            if(null != categories && categories.length > 0) {
                for (int i = 0; i < categories.length; i++) {
                    if(categories[i].getName().equalsIgnoreCase(categoryName)){
                        index = categories[i].getIndex();
                        break;
                    }
                }
            }
        }
        return index;
    }


    /**
     * set category mask for wizard related components, include global store, rule store and all hon selectors.
     * @param device, device
     * @param index, category index
     */
    public static void setCategoryForWizardRelatedComponentsWithDevice(BIHoneywellConfigurableDevice device, int index){
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        BHonWizardRuleStore ruleStore = device.getRuleStore();
        List<BHonWizSelector> allHonWizardSelectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) device);
        setCategoryMask(globalStore, index);
        setCategoryMask(ruleStore, index);
        if(null != allHonWizardSelectors && !allHonWizardSelectors.isEmpty()){
            allHonWizardSelectors.forEach(honWizSelector -> setCategoryMask(honWizSelector, index));
        }
    }

    public static void setCategoryForHonDeviceAndIrmConfig(BIHoneywellConfigurableDevice device, BComponent irmConfig, int index){
        setCategoryMask((BComponent) device, index);
        setCategoryMask(irmConfig, index);

    }

    /**
     * set category mask for a component
     * @param component
     * @param mask
     */
    private static void setCategoryMask(BComponent component,int mask){
        if(null != component){
            component.setCategoryMask(BCategoryMask.make(new int[]{mask}), null);
        }
    }

    /**
     * create roles for wizard related components if role does not exist
     * Roles include R, RWI and disable wizard role
     * @param categoryIndex, category index
     */
    public static void createRolesForWizard(int wizardCategoryIndex, int deviceCategoryIndex){
        BRoleService roleService = (BRoleService) Sys.getService(BRoleService.TYPE);
        try{
            if(null != roleService) {
                Property rwiProperty = roleService.getProperty(WIZARD_RWI_ROLE);
                if(null == rwiProperty) {
                    BRole wizardRwiRole = new BRole();
                    BPermissionsMap currentWizareRwiPM = wizardRwiRole.getPermissions();
                    String permissionMapString = getPermissionMapString(currentWizareRwiPM, wizardCategoryIndex, FULL_PERMISSION);
                    BPermissionsMap allPermissionMapForWizard = (BPermissionsMap) BPermissionsMap.DEFAULT.decodeFromString(permissionMapString);
                    wizardRwiRole.setPermissions(allPermissionMapForWizard);


                    currentWizareRwiPM = wizardRwiRole.getPermissions();
                    permissionMapString = getPermissionMapString(currentWizareRwiPM, deviceCategoryIndex, FULL_PERMISSION);
                    BPermissionsMap allPermissionMapForDevice = (BPermissionsMap) BPermissionsMap.DEFAULT.decodeFromString(permissionMapString);
                    wizardRwiRole.setPermissions(allPermissionMapForDevice);

                    roleService.add(WIZARD_RWI_ROLE, wizardRwiRole, null);
                }
                Property roProperty = roleService.getProperty(WIZARD_RO_ROLE);
                if(null == roProperty) {
                    BRole wizardRoRole = new BRole();
                    BPermissionsMap currentWizareRoPM = wizardRoRole.getPermissions();
                    String permissionMapString = getPermissionMapString(currentWizareRoPM, wizardCategoryIndex, READ_ONLY_PERMISSION);
                    BPermissionsMap rRPermissionMap = (BPermissionsMap) BPermissionsMap.DEFAULT.decodeFromString(permissionMapString);
                    wizardRoRole.setPermissions(rRPermissionMap);

                    currentWizareRoPM = wizardRoRole.getPermissions();
                    permissionMapString = getPermissionMapString(currentWizareRoPM, deviceCategoryIndex, FULL_PERMISSION);
                    BPermissionsMap allPermissionMapForDevice = (BPermissionsMap) BPermissionsMap.DEFAULT.decodeFromString(permissionMapString);
                    wizardRoRole.setPermissions(allPermissionMapForDevice);

                    roleService.add(WIZARD_RO_ROLE, wizardRoRole, null);
                }
            }
        } catch (Exception e){
            HoneywellDeviceWizardLogger.severe(lex.getText("error.permission.role.exception"), e);
        }

    }

    /**
     * hide wizard view if user has no permission to access wizard view
     * case 1: when device is readonly, wizard related components are readonly, do nothing. they will be controlled by niagara permission automatically
     * case 2: when device self is readonly, but global store are writable, device permission is top priority
     * - no implementation for this requirement, admin must make sure device is read-only and wizard related components is readonly as well
     * - wizard view will be hidden by Niagara automatically
     * case 3: when device is WI, global store is not R and W
     * - wizard view should be hidden by below code
     * - wizard related components, like global store, rule store and selectors will be disabled by niagara automatically.
     *
     * case 4: when device is WI, global store is not W
     * - wizard view won't be hidden, but wizard related components will be readonly
     * - actions on device will be disabled by below code, need to call hideHonWizardActionsOnDevice Action to disable them.
     * @param device, honeywell device
     * @param agentList, agent list
     */
    @SuppressWarnings("squid:S3776")
    public static void controlHonWizardComponentsBaseOnPermission(BIHoneywellConfigurableDevice device, AgentList agentList, Context context){
        if(null == agentList || agentList.list().length == 0){
            return;
        }
        Map<String, Integer> wizardPermissionMask = HonWizardPermissionUtil.getWizardPermissionMask(device, context);
        if(wizardPermissionMask.isEmpty() || !wizardPermissionMask.containsKey(HonWizardPermissionUtil.GLOBAL_STORE_PERM)){
            return;
        }
        int globalStorePermissionMask = wizardPermissionMask.get(HonWizardPermissionUtil.GLOBAL_STORE_PERM);
        if(globalStorePermissionMask == 0){
            if(null != device.getSetFlagForHonWizardAction()){
                ((BComponent)device).invoke(device.getSetFlagForHonWizardAction(), BInteger.make(Flags.HIDDEN));
            }
        }else if(isReadAndWriteDisable(globalStorePermissionMask)){
            if(null != device.getSetFlagForHonWizardAction()){
                ((BComponent)device).invoke(device.getSetFlagForHonWizardAction(), BInteger.make(Flags.READONLY));
            }
        }else{
            if(null != device.getRemoveFlagForHonWizardAction()){
                //only for expert mode, we need to remove read only
                //for other slot, it is controlled by niagara.
                ((BComponent)device).invoke(device.getRemoveFlagForHonWizardAction(), BInteger.make(Flags.READONLY));
            }
        }


    }

    private static boolean isDisableOrRWDisable(int globalStorePermissionMask) {
        return globalStorePermissionMask == 0 || ((globalStorePermissionMask & BPermissions.OPERATOR_READ) == 0 &&
                (globalStorePermissionMask & BPermissions.OPERATOR_WRITE) == 0 &&
                (globalStorePermissionMask & BPermissions.ADMIN_READ) == 0 &&
                (globalStorePermissionMask & BPermissions.ADMIN_WRITE) == 0
        );
    }
    private static boolean isReadAndWriteDisable(int globalStorePermissionMask) {
        return (globalStorePermissionMask & BPermissions.OPERATOR_WRITE) == 0 && (globalStorePermissionMask & BPermissions.ADMIN_WRITE) == 0 &&
                ((globalStorePermissionMask & BPermissions.OPERATOR_READ) == BPermissions.OPERATOR_READ || (globalStorePermissionMask & BPermissions.ADMIN_READ) == BPermissions.ADMIN_READ
                );
    }


    /**
     * get permission map string and replace permission string for specific category
     * @param permissionsMap, permission map
     * @param categoryIndex, category index
     * @param perm
     * @return
     */
    private static String getPermissionMapString(BPermissionsMap permissionsMap, int categoryIndex, String perm){
        String permissionStr = permissionsMap.encodeToString();
        String newPermStr = categoryIndex + "=" + perm;
        String matchStr = categoryIndex + "=";
        int startPos = permissionStr.indexOf(matchStr);
        if(startPos != -1){
            int endPos = permissionStr.indexOf(";", startPos);
            String replaceStr = permissionStr.substring(startPos, endPos);
            return permissionStr.replace(replaceStr, newPermStr);
        }else{
            if(!permissionStr.isEmpty()) {
                if (!permissionStr.endsWith(";")) {
                    permissionStr = permissionStr + ";" + newPermStr;
                } else {
                    permissionStr = permissionStr + newPermStr;
                }
            }else{
                permissionStr = newPermStr;
            }
            return permissionStr;
        }

    }

    private static final String HONEYWELL_DEVICE_WIZ = "honApplicationHandler:HoneywellDeviceWizard";
    private static final String WIZARD_RWI_ROLE = "honWizardRwiRole";

    private static final String WIZARD_RO_ROLE = "honWizardRoRole";

    private static final String FULL_PERMISSION = "rwiRWI";

    private static final String READ_ONLY_PERMISSION = "rR";

    public static final String GLOBAL_STORE_PERM = "globalStorePermMask";

    public static final String DEVICE_PERM = "devicePermMask";



}
