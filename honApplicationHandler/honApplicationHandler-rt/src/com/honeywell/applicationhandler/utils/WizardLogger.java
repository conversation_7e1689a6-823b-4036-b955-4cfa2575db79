package com.honeywell.applicationhandler.utils;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * WizardLogger provides logging functionality for encoding, decoding, and other operations.
 */
public class WizardLogger {
    private static final Logger LOGGER = Logger.getLogger("WizardMetaDataHandler");

    /**
     * Logs a message at the INFO level.
     * @param message The message to log.
     */
    public static void logInfo(String message) {
        LOGGER.log(Level.INFO, message);
    }

    /**
     * Logs a message at the FINER level.
     * @param message The message to log.
     */
    public static void logFiner(String message) {
        LOGGER.log(Level.FINER, message);
    }

    /**
     * Logs a message at the FINER level with parameters.
     * @param message The message to log.
     * @param params The parameters to include in the log.
     */
    public static void logFiner(String message, Object... params) {
        LOGGER.log(Level.FINER, message, params);
    }

    /**
     * Logs a message at the WARNING level.
     * @param message The message to log.
     */
    public static void logWarning(String message) {
        LOGGER.log(Level.WARNING, message);
    }

    /**
     * Logs a message at the SEVERE level.
     * @param message The message to log.
     */
    public static void logSevere(String message) {
        LOGGER.log(Level.SEVERE, message);
    }
}