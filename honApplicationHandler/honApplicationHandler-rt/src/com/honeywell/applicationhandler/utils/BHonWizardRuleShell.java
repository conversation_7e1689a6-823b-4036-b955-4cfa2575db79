package com.honeywell.applicationhandler.utils;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.applicationhandler.device.BHonWizardRuleStore;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jan 27, 2025
 */
@NiagaraType
public class BHonWizardRuleShell extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.utils.BHonWizardRuleShell(2979906276)1.0$ @*/
/* Generated Mon Jan 27 17:15:27 IST 2025 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardRuleShell.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public boolean isParentLegal(BComponent parent) {
		if(!Sys.isStation()) {
			return super.isParentLegal(parent);
		}
		return (parent instanceof BHonWizardRuleStore);
	}
}
