/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.utils;

import com.honeywell.applicationhandler.context.MasterSyncContext;
import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardMetaData;
import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.generate.HonWizGlobalStoreGenerator;
import com.honeywell.applicationhandler.exceptions.GlobalStoreGenerationExcpetion;
import com.honeywell.applicationhandler.exceptions.MasterSyncException;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget;

import javax.baja.collection.BITable;
import javax.baja.job.BSimpleJob;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BSimple;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Cursor;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.tag.Id;
import javax.baja.util.BUuid;
import javax.baja.util.Lexicon;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors;

/**
 * <AUTHOR> Zhang
 */
public class DeviceMasterSyncUtil {
    private DeviceMasterSyncUtil(){

    }

	public static final Context masterSyncContext = new MasterSyncContext();

    /**
     * do master sync in bacnet device manager, if validation failed, it will fail job and output error message
     * 1) validate all hon wizard selectors
     * 2) sync all hon wizard selectors
     * 3) add or update components in global store
     * 4) add or update components in rule store
     * 5) update common slot in global store
     * 6) update wizard component ord in points
     *
     * @param srcDevice,    source device
     * @param targetDevice, target device
     * @param job,          job
     * @throws Exception
     */
    public static void masterSyncDeviceWizard(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, BSimpleJob job) throws Exception {
        try {
            targetDevice.stopDynamicUpdatesOfGlobalStore("masterSync", masterSyncContext);
            List<String> errorMsgs = new ArrayList<>();
            validateWizardSelectors(srcDevice, targetDevice, errorMsgs);
            if (errorMsgs.isEmpty()) {
                syncHonWizSelectors(srcDevice, targetDevice, masterSyncContext);
                syncApplicationGuid(srcDevice, targetDevice, masterSyncContext);
                syncGlobalStoreUUID(srcDevice, targetDevice, masterSyncContext);
                syncRuleStore(srcDevice, targetDevice, masterSyncContext);
                copyMetaData(srcDevice, targetDevice, masterSyncContext);
                regenerateGlobalStore(targetDevice, masterSyncContext);
                copyPageOrder(srcDevice, targetDevice);

            } else {
                for (String errorMsg : errorMsgs) {
                    job.log().failed(errorMsg);
                }
                throw new MasterSyncException(lex.getText("error.mastersync.validation.failed"));
            }
        } catch (Exception e) {
            throw e;
        } finally {
        	targetDevice.resumeDynamicUpdatesOfGlobalStore(Const.FORCE_RESUME_DYNAMIC_UPDATE);
        }
    }

    /**
     * sync global store UUID from src device to target device
     * @param srcDevice
     * @param targetDevice
     * @param context
     */
    private static void syncGlobalStoreUUID(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, Context context){
        if (null == srcDevice || null == targetDevice) {
            return;
        }
        BUuid globalStoreUuid = srcDevice.getGlobalStoreUuid(false);
        if(null != globalStoreUuid){
            if(((BComponent) targetDevice).get(Const.BACK_UP_APPLICATION_UUID) != null) {
                ((BComponent) targetDevice).set(Const.BACK_UP_APPLICATION_UUID, globalStoreUuid, masterSyncContext);
            }else {
                ((BComponent) targetDevice).add(Const.BACK_UP_APPLICATION_UUID, globalStoreUuid, masterSyncContext);
            }
        }
    }

    /**
     *
     * @param srcDevice
     * @param targetDevice
     * @param context
     */
    private static void syncApplicationGuid(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, Context context){
        if (null == srcDevice || null == targetDevice) {
            return;
        }
        BUuid applicationGuid = srcDevice.getApplicationGuid();
        if(null != applicationGuid){
            ((BComponent) targetDevice).set(Const.APPLICATION_GUID, applicationGuid, masterSyncContext);
        }
    }

    /**
     * sync rule from source device to target device
     * @param srcDevice
     * @param targetDevice
     * @param context
     */
    private static void syncRuleStore(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, Context context){
        if (null == srcDevice || null == targetDevice) {
            return;
        }
        BHonWizardRuleStore srcRuleStore = srcDevice.getRuleStore();
        BHonWizardRuleStore targetRuleStore = targetDevice.getRuleStore();
        if(null != srcRuleStore && null != targetRuleStore){
            targetRuleStore.setSwappingOut(srcRuleStore.getSwappingOut());
            targetRuleStore.setAutoSwappingOutMinutesAfterChanged(srcRuleStore.getAutoSwappingOutMinutesAfterChanged());
            targetRuleStore.setAutoSwappingOutMinutesAfterStarted(srcRuleStore.getAutoSwappingOutMinutesAfterStarted());
            targetRuleStore.setNotes(srcRuleStore.getNotes());
            Property[] targetRuleStores = targetRuleStore.getDynamicPropertiesArray();
            for(int i = 0; i < targetRuleStores.length; i++){
                Property prop = targetRuleStores[i];
                if(targetRuleStore.get(prop) instanceof BString){
                    targetRuleStore.remove(prop.getName(), context);
                }
            }
            if(!srcRuleStore.getSwappingOut()) {
                Property[] dynamicPropertiesArray = srcRuleStore.getDynamicPropertiesArray();
                for(int i = 0; i < dynamicPropertiesArray.length; i++) {
                    Property prop = dynamicPropertiesArray[i];
                    if(srcRuleStore.get(prop) instanceof BString){
                        targetRuleStore.add(prop.getName(), srcRuleStore.get(prop), 0,
                                BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), context);
                    }
                }
                targetRuleStore.showHideActions(false);
            }else{
                targetRuleStore.showHideActions(true);
            }
        }
    }

    /**
     * sync hon wizard selectors
     *
     * @param srcDevice,    source device
     * @param targetDevice, target device
     * @param context       context
     */
    @SuppressWarnings("squid:S3776")
    private static void syncHonWizSelectors(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, Context context) {
        if (null == srcDevice || null == targetDevice) {
            return;
        }
        List<BHonWizSelector> srcHonWizardSelectors = getAllHonWizardSelectors((BComponent) srcDevice);
        if (srcHonWizardSelectors.isEmpty()) {
            return;
        }
        Map<String, BHonWizSelector> targetSelectorMap = buildSlotPathWithSelectors(targetDevice);
        SlotPath srcSlotPath = ((BComponent)srcDevice).getSlotPath();
        SlotPath targetSlotPath = ((BComponent)targetDevice).getSlotPath();
        Map<String, Map<String, BHonWizSlotSelector>> mapSlotPathWithSelector = new HashMap<>();
        for (BHonWizSelector srcHonWizardSelector : srcHonWizardSelectors) {
            SlotPath srcSelectorSlotPath = srcHonWizardSelector.getSlotPath();
            String targetSelectorSlotPath = srcSelectorSlotPath.toString().replace(srcSlotPath.toString(), targetSlotPath.toString());
            String parentCompOrdString = targetSelectorSlotPath.replace("/" + srcHonWizardSelector.getName(), "");
            BComponent targetParentComp = (BComponent) BOrd.make(parentCompOrdString).resolve(Sys.getStation()).get();

            if (srcHonWizardSelector instanceof BHonWizSlotSelector) {
                Map<String, BHonWizSlotSelector> slotMap = null;
                if(mapSlotPathWithSelector.containsKey(parentCompOrdString)) {
                    slotMap = mapSlotPathWithSelector.get(parentCompOrdString);
                } else {
                    slotMap = new HashMap<>();
                    mapSlotPathWithSelector.put(parentCompOrdString, slotMap);
                    BHonWizSlotSelector[] slotSelectors = targetParentComp.getChildren(BHonWizSlotSelector.class);
                    for (BHonWizSlotSelector slotSelector : slotSelectors) {
                        slotMap.put(slotSelector.getSourcePropertyNameString(), slotSelector);
                    }
                }
                BHonWizSlotSelector foundSelector = slotMap.get(((BHonWizSlotSelector) srcHonWizardSelector).getSourcePropertyNameString());
                addOrUpdateProperties(srcHonWizardSelector, foundSelector, targetParentComp, context);
                if(foundSelector != null) {
                    targetSelectorMap.remove(parentCompOrdString + "/" + ((BHonWizSlotSelector) srcHonWizardSelector).getSourcePropertyNameString());
                }
            } else if (srcHonWizardSelector instanceof BHonWizPointSelector) {
                BHonWizPointSelector[] pointSelectors = targetParentComp.getChildren(BHonWizPointSelector.class);
                if (pointSelectors.length == 0) {
                    addOrUpdateProperties(srcHonWizardSelector, null, targetParentComp, context);
                } else {
                    addOrUpdateProperties(srcHonWizardSelector, pointSelectors[0], targetParentComp, context);
                    targetSelectorMap.remove(parentCompOrdString);
                }
            }

        }

        for (Map.Entry<String, BHonWizSelector> honWizSelectorEntry : targetSelectorMap.entrySet()) {
            ((BComponent)honWizSelectorEntry.getValue().getParent()).remove(honWizSelectorEntry.getValue().getPropertyInParent());
        }

    }

    /**
     * build slot path map with selector for target device
     * @param targetDevice
     * @return
     */
    private static Map<String, BHonWizSelector> buildSlotPathWithSelectors(BIHoneywellConfigurableDevice targetDevice) {
        List<BHonWizSelector> targetHonWizardSelectors = getAllHonWizardSelectors((BComponent) targetDevice);
        HashMap<String, BHonWizSelector> result = new HashMap();
        if (targetHonWizardSelectors.isEmpty()) {
            return result;
        }

        for (BHonWizSelector targetHonWizardSelector : targetHonWizardSelectors) {
            String parentCompOrdString = targetHonWizardSelector.getSlotPath().toString().replace("/" + targetHonWizardSelector.getName(), "");
            if (targetHonWizardSelector instanceof BHonWizSlotSelector) {
                result.put(parentCompOrdString + "/" + ((BHonWizSlotSelector) targetHonWizardSelector).getSourcePropertyNameString(), targetHonWizardSelector);
            } else if (targetHonWizardSelector instanceof BHonWizPointSelector) {
                result.put(parentCompOrdString, targetHonWizardSelector);
            }
        }
        return result;

    }

    /**
     * add or update property with context in target component.
     * if property already exists, it will be updated, otherwise it will be added
     * if property is not in source component, it will be removed from target component
     *
     * @param srcHonWizardSelector,          src honeywell selector
     * @param targetHonWizardSelector,       target honeywell selector
     * @param targetWizardSelectorParentComp, parent component of honeywell selector component
     * @param context           context
     */
    private static void addOrUpdateProperties(BComponent srcHonWizardSelector, BComponent targetHonWizardSelector, BComponent targetWizardSelectorParentComp, Context context) {
        if (null == srcHonWizardSelector) {
            HoneywellDeviceWizardLogger.severe(lex.getText("error.mastersync.honwizardselector.notfound"));
            return;
        }
        if (null != targetHonWizardSelector) {
            Map<String, BValue> existingProperties = new HashMap<>();
            for (Property property : srcHonWizardSelector.getPropertiesArray()) {
                if (targetHonWizardSelector.get(property.getName()) != null) {
                    targetHonWizardSelector.set(property.getName(), srcHonWizardSelector.get(property), context);
                } else {
                    targetHonWizardSelector.add(property.getName(), srcHonWizardSelector.get(property), context);
                }
                existingProperties.put(property.getName(), BBoolean.TRUE);
            }
            for (Property property : targetHonWizardSelector.getPropertiesArray()) {
                if (!existingProperties.containsKey(property.getName())) {
                    targetHonWizardSelector.remove(property.getName(), context);
                }
            }

        } else {
            targetWizardSelectorParentComp.add(srcHonWizardSelector.getName() + "?", srcHonWizardSelector.newCopy(), context);
        }
    }

    /**
     * validate wizard slot selectors and point selectors
     * validation: wizard selectors should be in the same slot path as the source device
     * for slot selector, source property name should be in the target device
     *
     * @param srcDevice,    src device
     * @param targetDevice, target device
     * @param errorMsg,     error list
     */
    private static void validateWizardSelectors(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, List<String> errorMsg) {
        List<BHonWizSelector> srcSelectors = getAllHonWizardSelectors((BComponent) srcDevice);
        if (srcSelectors.isEmpty()) {
            return;
        }
        for (BHonWizSelector srcSelector : srcSelectors) {
            SlotPath srcSelectorSlotPath = srcSelector.getSlotPath();
            String targetSelectorSlotPath = srcSelectorSlotPath.toString().replace(((BComponent)srcDevice).getSlotPath().toString(), ((BComponent)targetDevice).getSlotPath().toString());
            String parentCompOrdString = targetSelectorSlotPath.replace("/" + srcSelector.getName(), "");
            BComponent targetParentComp = null;
            try {
                targetParentComp = (BComponent) BOrd.make(parentCompOrdString).resolve(Sys.getStation()).get();
            } catch (Exception ex) {
                errorMsg.add(lex.getText("error.mastersync.target.component.notfound", parentCompOrdString));
            }
            if (null != targetParentComp) {
                if (srcSelector instanceof BHonWizSlotSelector) {
                    BHonWizSlotSelector srcSlotSelector = (BHonWizSlotSelector) srcSelector;
                    String sourcePropertyName = srcSlotSelector.getSourcePropertyNameString();
                    if (null == targetParentComp.getProperty(sourcePropertyName)) {
                        errorMsg.add(lex.getText("error.mastersync.target.component.property.notfound", sourcePropertyName, parentCompOrdString));
                    }
                }
            }

        }
    }

    /**
     * add, update or remove components in global store and rule store
     * @param srcDevice, source device
     * @param targetDevice, target device
     * @param srcGlobalOrRuleStore, global or rule store component
     * @param context
     */
    private static void addOrUpdateComponents(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice,
                                              BComponent srcGlobalOrRuleStore, Context context) {
        HashMap<String, BComponent> cacheMap = new HashMap<>();
        deepCopyComponent((BComponent) srcDevice, (BComponent) targetDevice, srcGlobalOrRuleStore.getName(), context, cacheMap);

        BOrd selectorsOrd = BOrd.make(srcGlobalOrRuleStore.getNavOrd().toString() + "|bql:select * from baja:Component");
        BITable<?> result = (BITable<?>) selectorsOrd.resolve(Sys.getStation()).get();
        Cursor<?> c = result.cursor();
        HashMap<String, BComponent> removeMap = new HashMap<>();
        while (c.next()) {
            BComponent comp = (BComponent) c.get();
            if (!cacheMap.containsKey(comp.getSlotPath().toString())) {
                BComponent targetComp = (BComponent) BOrd.make(comp.getSlotPath().toString()).resolve(Sys.getStation()).get();
                if (targetComp.getParent() != null && !targetComp.getParent().getProperty(targetComp.getName()).isFrozen()) {
                    removeMap.put(targetComp.getName(), (BComponent) targetComp.getParent());
                }
            }
        }
        if (!removeMap.isEmpty()) {
            for (Map.Entry<String, BComponent> entry : removeMap.entrySet()) {
                BComponent comp = entry.getValue();
                if (comp != null) {
                    comp.remove(entry.getKey(), context);
                }
            }
        }

    }


    /**
     * deep copy children component and properties from source component to target component recursively
     *
     * @param srcComp, source component
     * @param targetComp, target component
     * @param propertyName, property name
     * @param context, context
     * @param doneCompMap, map to store copied components to remove components from target which is not in source component
     */
    @SuppressWarnings("squid:S3776")
    private static void deepCopyComponent(BComponent srcComp, BComponent targetComp, String propertyName, Context context, HashMap<String, BComponent> doneCompMap) {
        if (null == srcComp || null == targetComp) {
            return;
        }
        if (targetComp.get(propertyName) == null) {
            BValue childComp = srcComp.get(propertyName).newCopy();
            targetComp.add(propertyName, childComp, context);
            if (childComp instanceof BComponent) {
                doneCompMap.put(((BComponent)childComp).getSlotPath().toString(), targetComp);
                addChildrenCompToCache(targetComp, doneCompMap);
            }
        } else if (targetComp.get(propertyName) != null && srcComp.get(propertyName).isSimple()) {
            BSimple srcSimple = (BSimple) srcComp.get(propertyName);
            BSimple targetSimple = (BSimple) targetComp.get(propertyName);
            if (!srcSimple.equals(targetSimple)) {
                targetComp.set(propertyName, srcComp.get(propertyName), context);
            }
        } else if (targetComp.get(propertyName) != null && srcComp.get(propertyName).isStruct()) {
            targetComp.set(propertyName, srcComp.get(propertyName).newCopy(), context);
        } else if (targetComp.get(propertyName) != null && srcComp.get(propertyName).isComplex()) {
            //skip terminal assignment store in global store, we don't need to master/sync the component
            if(srcComp.get(propertyName) instanceof BTerminalAssignmentWidget){
                return;
            }
            BComponent childSrcComp = (BComponent) srcComp.get(propertyName);
            BComponent childtargetComp = (BComponent) targetComp.get(propertyName);
            doneCompMap.put(childtargetComp.getSlotPath().toString(), targetComp);
            Property[] properties = childSrcComp.getPropertiesArray();
            for (int i = 0; i < properties.length; i++) {
                Property prop = properties[i];
                deepCopyComponent(childSrcComp, childtargetComp, prop.getName(), context, doneCompMap);
            }
        }
    }

    /**
     * if it is new component, newCopy method will copy the component, all children components need to be added to cache
     * otherwise, it will be removed from target component
     * @param targetComp, target comp
     * @param doneCompMap
     */
    private static void addChildrenCompToCache(BComponent targetComp, HashMap<String, BComponent> doneCompMap){
        BOrd selectorsOrd = BOrd.make(targetComp.getNavOrd().toString() + "|bql:select * from baja:Component");
        BITable<?> result = (BITable<?>) selectorsOrd.resolve(Sys.getStation()).get();
        Cursor<?> c = result.cursor();
        while (c.next()) {
            BComponent comp = (BComponent) c.get();
            doneCompMap.put(comp.getSlotPath().toString(), targetComp);
        }
    }




    /**
     * for terminal assignment store, we don't need to master/sync from source device to target device
     * we need to regenerate the terminal assignment store based on IO Components of target device.
     * @param targetDevice, target device
     * @param context, context
     */
    private static void regenerateTerminalStore(BIHoneywellConfigurableDevice targetDevice, Context context) {
        if(targetDevice.isSupportTerminalAssignment()) {
            BHonWizardGlobalStore globalStore = targetDevice.getGlobalStore();
            if(null != globalStore) {
                Property terminalProp = globalStore.getProperty(SlotPath.escape(Const.TERMINAL_ASSIGNMENT_PAGE));
                if(null != terminalProp) {
                    BTerminalAssignmentWidget terminalAssignmentWidget = (BTerminalAssignmentWidget) globalStore.get(terminalProp);
                    terminalAssignmentWidget.removeAll();
                    globalStore.set(SlotPath.escape(Const.TERMINAL_ASSIGNMENT_PAGE), targetDevice.getTerminalAssignments(null), context);
                }else{
                    globalStore.add(SlotPath.escape(Const.TERMINAL_ASSIGNMENT_PAGE), targetDevice.getTerminalAssignments(null), context);
                }

            }
        }

    }

    /**
     * generate global store for target device
     * @param targetDevice, target device
     * @param context, context, reserved for the future
     * @throws GlobalStoreGenerationExcpetion
     */
    private static void regenerateGlobalStore(BIHoneywellConfigurableDevice targetDevice, Context context) throws GlobalStoreGenerationExcpetion {
        if(null == targetDevice){
            return;
        }
        HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(targetDevice);
        generator.generate(context, false);
    }

    /**
     * copy page order from source device to target device
     * @param srcDevice, source device
     * @param targetDevice, target device
     */
    private static void copyPageOrder(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice) {
        BHonWizardGlobalStore srcGlobalStore = srcDevice.getGlobalStore();
        BHonWizardGlobalStore targetGlobalStore = targetDevice.getGlobalStore();
        if (srcGlobalStore != null && targetGlobalStore != null) {
            Map<String, Integer> targetPageOrderMap = targetGlobalStore.getPageOrderMap();
            for (Map.Entry<String, Integer> entry : targetPageOrderMap.entrySet()) {
                String pageName = entry.getKey();
                Id idForPage = srcGlobalStore.getIdForPage(pageName);
                if(null != idForPage) {
                    targetGlobalStore.tags().set(idForPage, BString.make(pageName));
                }

            }

        }
    }


    /**
     * copy metadata from source device to target device, not include hon wizard selectors
     * @param srcDevice
     * @param targetDevice
     * @param context
     */
    private static void copyMetaData(BIHoneywellConfigurableDevice srcDevice, BIHoneywellConfigurableDevice targetDevice, Context context) {
        if (null == srcDevice || null == targetDevice) {
            return;
        }
        BHonWizardMetaData srcMetaDataComp = srcDevice.getMetaDataComp();
        BHonWizardMetaData targetMetaDataComp = targetDevice.getMetaDataComp();
        Property[] srcDynamicPropertiesArray = srcMetaDataComp.getDynamicPropertiesArray();
        for (Property property : srcDynamicPropertiesArray) {
            BValue srcValue = srcMetaDataComp.get(property);
            if(srcValue instanceof BSimple) {
                if(targetMetaDataComp.get(property.getName()) != null){
                    targetMetaDataComp.set(property.getName(), srcMetaDataComp.get(property), context);
                }else{
                    targetMetaDataComp.add(property.getName(), srcMetaDataComp.get(property), context);
                }
            } else if( srcValue instanceof BComplex && !(srcValue instanceof BHonWizSelector)) {
                if(targetMetaDataComp.get(property.getName()) != null){
                    targetMetaDataComp.set(property.getName(), srcMetaDataComp.get(property).newCopy(), context);
                }else{
                    targetMetaDataComp.add(property.getName(), srcMetaDataComp.get(property).newCopy(), context);
                }
            }

        }
    }



    private static final Lexicon lex = LexiconUtil.getLexicon();
}
