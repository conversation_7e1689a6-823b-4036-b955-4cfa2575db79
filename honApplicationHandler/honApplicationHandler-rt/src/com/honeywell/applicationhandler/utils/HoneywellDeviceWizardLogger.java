/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.utils;

import java.util.logging.Level;
import java.util.logging.Logger;

public class HoneywellDeviceWizardLogger {
    private static final Logger logger = Logger.getLogger("honeywellDeviceWizard");
    public static Logger getLogger() {
        return logger;
    }
    
    public static void finest(String msg) {
    	if(logger.isLoggable(Level.FINEST)) {
    		logger.log(Level.FINEST, msg);
    	}
    }
    
    public static void finer(String msg) {
    	if(logger.isLoggable(Level.FINER)) {
    		logger.log(Level.FINER, msg);
    	}
    }
    
    public static void fine(String msg) {
    	if(logger.isLoggable(Level.FINE)) {
    		logger.log(Level.FINE, msg);
    	}
    }
    
    public static void info(String msg) {
    	if(logger.isLoggable(Level.INFO)) {
    		logger.log(Level.INFO, msg);
    	}
    }
    
    public static void severe(String msg) {
    	if(logger.isLoggable(Level.SEVERE)) {
    		logger.log(Level.SEVERE, msg);
    	}
    }
    
    public static void severe(String msg, Exception ex) {
    	if(logger.isLoggable(Level.SEVERE)) {
    		logger.log(Level.SEVERE, msg, ex);
    	}
    }
    
    public static void warning(String msg) {
    	if(logger.isLoggable(Level.WARNING)) {
    		logger.log(Level.WARNING, msg);
    	}
    }
    
}
