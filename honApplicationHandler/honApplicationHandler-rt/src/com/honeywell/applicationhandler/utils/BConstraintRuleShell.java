/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.utils;

import com.honeywell.applicationhandler.device.BHonWizardRuleStore;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;


/**
 *
 * <AUTHOR> zhang
 */
@NiagaraType
public class BConstraintRuleShell extends BComponent {
/*+ ------------ B<PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.utils.BConstraintRuleShell(2979906276)1.0$ @*/
/* Generated Mon May 26 10:37:20 CST 2025 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BConstraintRuleShell.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
@Override
public boolean isParentLegal(BComponent parent) {
  if(!Sys.isStation()) {
    return super.isParentLegal(parent);
  }
  return (parent instanceof BHonWizardRuleStore);
}
}
