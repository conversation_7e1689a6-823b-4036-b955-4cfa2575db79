/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.utils;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> zhang
 * @since March 25, 2025
 */
@NiagaraType
public class BTerminalAssignmentValueRuleShell extends BHonWizardRuleShell {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.utils.BTerminalAssignmentValueRuleShell(2979906276)1.0$ @*/
/* Generated Tue Mar 25 14:31:32 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTerminalAssignmentValueRuleShell.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
