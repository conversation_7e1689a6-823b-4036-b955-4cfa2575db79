/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.utils;

import static com.honeywell.applicationhandler.common.Constants.AIRFLOW_UNIT;
import static com.honeywell.applicationhandler.common.Constants.CHECKBOX_GROUP;
import static com.honeywell.applicationhandler.common.Constants.DROPDOWN;
import static com.honeywell.applicationhandler.common.Constants.DUCT_AREA_CALCULATOR_TYPE;
import static com.honeywell.applicationhandler.common.Constants.MEASUREMENT_TYPE;
import static com.honeywell.applicationhandler.common.Constants.NO_VALID_SLOTS_AVAILABLE;
import static com.honeywell.applicationhandler.common.Constants.NUMBER_INPUT;
import static com.honeywell.applicationhandler.common.Constants.OPTIONAL;
import static com.honeywell.applicationhandler.common.Constants.RADIO_BUTTON_GROUP;
import static com.honeywell.applicationhandler.common.Constants.RANGE_SLIDER;
import static com.honeywell.applicationhandler.common.Constants.REQUIRED;
import static com.honeywell.applicationhandler.common.Constants.SCHEDULE;
import static com.honeywell.applicationhandler.common.Constants.SELECT_BUTTON;
import static com.honeywell.applicationhandler.common.Constants.SELECT_WIDGET;
import static com.honeywell.applicationhandler.common.Constants.SINGLE_SLIDER;
import static com.honeywell.applicationhandler.common.Constants.SOURCE_PROPERTY_NAME;
import static com.honeywell.applicationhandler.common.Constants.SWITCH_BUTTON;
import static com.honeywell.applicationhandler.common.Constants.TEXT_INPUT;
import static com.honeywell.applicationhandler.common.Constants.TIMEZONE_TYPE;
import static com.honeywell.applicationhandler.common.Constants.WIDGET_TYPE_LIST;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_BELONG_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_COLOR_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_DEADBAND_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_GRP_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_HELP_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_MAX_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_MIN_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_NAME_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_OPTIONS_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_ORDER_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_PAGE_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_READ_ONLY_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_ROLE_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_STEP_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_TAB_IN_PAGE_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_WDTTYPE_TAG;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.WGTTYPE_TAG;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BNumber;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Id;
import javax.baja.tag.Tag;
import javax.baja.tagdictionary.BTagDictionaryService;
import javax.baja.tagdictionary.BTagInfo;

import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumSchedule;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.tridium.nre.util.tuple.Pair;

/**
 * <AUTHOR> Zhang
 */
public class HoneywellWidgetTagUtil {
    private HoneywellWidgetTagUtil(){

    }
    private static final Map<Type, List<String>> pointTypeToWidgetTypeMap = new HashMap<>();

    private static final Map<String, List<Type>> widgetTypeToSourcePropertyTypeMap = new HashMap<>();

    private static final Map<String, List<String>> widgetTypeToTagMap = new HashMap<>();

    static {
        /**
         * Mapping of point value types to widget types.
         */
        pointTypeToWidgetTypeMap.put(BIHonWizardNumericPoint.TYPE,
                Arrays.asList(DUCT_AREA_CALCULATOR_TYPE, NUMBER_INPUT, RANGE_SLIDER, SINGLE_SLIDER));
        pointTypeToWidgetTypeMap.put(BIHonWizardBooleanPoint.TYPE,
                Arrays.asList(DROPDOWN, SELECT_WIDGET, SWITCH_BUTTON));
        pointTypeToWidgetTypeMap.put(BIHonWizardEnumPoint.TYPE,
                Arrays.asList(DROPDOWN, DUCT_AREA_CALCULATOR_TYPE, SELECT_WIDGET));
        pointTypeToWidgetTypeMap.put(BIHonWizardEnumSchedule.TYPE, Arrays.asList(SCHEDULE));

        /**
         * Mapping of widget types to source property types.
         */
        widgetTypeToSourcePropertyTypeMap.put(CHECKBOX_GROUP, Arrays.asList(BNumber.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(DUCT_AREA_CALCULATOR_TYPE, Arrays.asList(BNumber.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(NUMBER_INPUT, Arrays.asList(BNumber.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(RANGE_SLIDER, Arrays.asList(BNumber.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(SINGLE_SLIDER, Arrays.asList(BNumber.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(TEXT_INPUT, Arrays.asList(BString.TYPE, BNumber.TYPE, BBoolean.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(DROPDOWN, Arrays.asList(BBoolean.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(RADIO_BUTTON_GROUP, Arrays.asList(BBoolean.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(SELECT_BUTTON, Arrays.asList(BBoolean.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(SELECT_WIDGET, Arrays.asList(BBoolean.TYPE, BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(SWITCH_BUTTON, Arrays.asList(BBoolean.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(MEASUREMENT_TYPE, Arrays.asList(BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(AIRFLOW_UNIT, Arrays.asList(BEnum.TYPE));
        widgetTypeToSourcePropertyTypeMap.put(TIMEZONE_TYPE, Arrays.asList(BTimezoneEnum.TYPE));
        List<String> commonTags = Arrays.asList(HON_WIZ_PAGE_TAG, HON_WIZ_NAME_TAG, HON_WIZ_WDTTYPE_TAG,
                HON_WIZ_READ_ONLY_TAG, HON_WIZ_ORDER_TAG);
        List<String> optionTags = Arrays.asList(HON_WIZ_TAB_IN_PAGE_TAG, HON_WIZ_GRP_TAG, HON_WIZ_HELP_TAG);


        /**
         * Mapping of widget type and supported required and optional tags.
         */
        widgetTypeToTagMap.put(CHECKBOX_GROUP + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.get(CHECKBOX_GROUP + REQUIRED).addAll(Arrays.asList(HON_WIZ_OPTIONS_TAG));
        widgetTypeToTagMap.put(CHECKBOX_GROUP + OPTIONAL, optionTags);

        widgetTypeToTagMap.put(DUCT_AREA_CALCULATOR_TYPE + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.get(DUCT_AREA_CALCULATOR_TYPE + REQUIRED).addAll(Arrays.asList(HON_WIZ_ROLE_TAG, HON_WIZ_BELONG_TAG));
        widgetTypeToTagMap.put(DUCT_AREA_CALCULATOR_TYPE + OPTIONAL, new ArrayList<>(optionTags));
        widgetTypeToTagMap.get(DUCT_AREA_CALCULATOR_TYPE + OPTIONAL).addAll(Arrays.asList(HON_WIZ_OPTIONS_TAG));

        widgetTypeToTagMap.put(NUMBER_INPUT + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.get(NUMBER_INPUT + REQUIRED).addAll(Arrays.asList(HON_WIZ_MIN_TAG, HON_WIZ_MAX_TAG));
        widgetTypeToTagMap.put(NUMBER_INPUT + OPTIONAL, new ArrayList<>(optionTags));
        widgetTypeToTagMap.get(NUMBER_INPUT + OPTIONAL).addAll(Arrays.asList(HON_WIZ_STEP_TAG, HON_WIZ_UNIT_GROUP_TAG));

        widgetTypeToTagMap.put(RADIO_BUTTON_GROUP + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(RADIO_BUTTON_GROUP + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(RANGE_SLIDER + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.get(RANGE_SLIDER + REQUIRED).addAll(Arrays.asList(HON_WIZ_MIN_TAG, HON_WIZ_MAX_TAG, HON_WIZ_ROLE_TAG, HON_WIZ_BELONG_TAG));
        widgetTypeToTagMap.put(RANGE_SLIDER + OPTIONAL, new ArrayList<>(optionTags));
        widgetTypeToTagMap.get(RANGE_SLIDER + OPTIONAL).addAll(Arrays.asList(HON_WIZ_COLOR_TAG, HON_WIZ_STEP_TAG, HON_WIZ_UNIT_GROUP_TAG,
                HON_WIZ_DEADBAND_TAG));

        widgetTypeToTagMap.put(SINGLE_SLIDER + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.get(SINGLE_SLIDER + REQUIRED).addAll(Arrays.asList(HON_WIZ_MIN_TAG, HON_WIZ_MAX_TAG));
        widgetTypeToTagMap.put(SINGLE_SLIDER + OPTIONAL, new ArrayList<>(optionTags));
        widgetTypeToTagMap.get(SINGLE_SLIDER + OPTIONAL).addAll(Arrays.asList(HON_WIZ_COLOR_TAG, HON_WIZ_STEP_TAG, HON_WIZ_UNIT_GROUP_TAG));

        widgetTypeToTagMap.put(TEXT_INPUT + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(TEXT_INPUT + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(DROPDOWN + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(DROPDOWN + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(RADIO_BUTTON_GROUP + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(RADIO_BUTTON_GROUP + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(SELECT_BUTTON + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(SELECT_BUTTON + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(SELECT_WIDGET + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(SELECT_WIDGET + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(SWITCH_BUTTON + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(SWITCH_BUTTON + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(MEASUREMENT_TYPE + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(MEASUREMENT_TYPE + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(AIRFLOW_UNIT + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(AIRFLOW_UNIT + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(TIMEZONE_TYPE + REQUIRED, new ArrayList<>(commonTags));
        widgetTypeToTagMap.put(TIMEZONE_TYPE + OPTIONAL, new ArrayList<>(optionTags));

        widgetTypeToTagMap.put(SCHEDULE + REQUIRED, Arrays.asList(HON_WIZ_WDTTYPE_TAG, HON_WIZ_PAGE_TAG));
        widgetTypeToTagMap.put(SCHEDULE + OPTIONAL, new ArrayList<>());


    }

    /**
     * get index position and widget type value by widget type value
     *
     * @param widgetTypeTagValue
     * @return pair of index position and widget type value
     */
    private static Pair<Integer, String> getWidgetTypeMapping(String widgetTypeTagValue) {
        if (widgetTypeTagValue == null || widgetTypeTagValue.isEmpty()) {
            return null;
        }
        List<String> widgetTypes = Arrays.asList(WIDGET_TYPE_LIST);
        int index = widgetTypes.indexOf(widgetTypeTagValue);
        if (index == -1) {
            return null;
        }
        return new Pair<>(index, widgetTypeTagValue);


    }

    /**
     * get enum range for widget type based on point type
     *
     * @param point, source point
     * @return enum range
     */
    public static BEnumRange getPointWidgetEnumRange(BIHonWizardPoint point) {
        List<String> widgetTypeList = null;
        if (point instanceof BIHonWizardNumericPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardNumericPoint.TYPE);
        } else if (point instanceof BIHonWizardEnumPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardEnumPoint.TYPE);
        } else if (point instanceof BIHonWizardBooleanPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardBooleanPoint.TYPE);
        } else if (point instanceof BIHonWizardEnumSchedule) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardEnumSchedule.TYPE);
        }
        return getWidgetEnumRange(widgetTypeList);
    }

    /**
     * check if point selector supports the point type.
     *
     * @param point,      point
     * @param widgetType, widget type
     * @return true or false
     */
    public static boolean isPointSupportedSelector(BIHonWizardPoint point, String widgetType) {
        List<String> widgetTypeList = getPointSupportedWidgetTypes(point);
        return widgetTypeList.contains(widgetType);

    }

    /**
     * get supported widget types
     * @param point hon wizard point
     * @return widget type list
     */
    public static List<String> getPointSupportedWidgetTypes(BIHonWizardPoint point) {
        List<String> widgetTypeList = new ArrayList<>();
        if (point instanceof BIHonWizardNumericPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardNumericPoint.TYPE);
        } else if (point instanceof BIHonWizardBooleanPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardBooleanPoint.TYPE);
        } else if (point instanceof BIHonWizardEnumPoint) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardEnumPoint.TYPE);
        } else if (point instanceof BIHonWizardEnumSchedule) {
            widgetTypeList = pointTypeToWidgetTypeMap.get(BIHonWizardEnumSchedule.TYPE);
        }
        return widgetTypeList;
    }

    /**Check if widget type is supported by the component, the condition is below
     * at least one visible property value type is matched with the widget type.
     * @param parentComponent
     * @param widgetType
     * @return
     */
    public static boolean isSlotSupportedSelector(BComponent parentComponent, String widgetType) {
        List<Type> sourcePropertyTypes = widgetTypeToSourcePropertyTypeMap.get(widgetType);
        Property[] properties = parentComponent.getPropertiesArray();
        for (Property property : properties) {
            if ((parentComponent.getFlags(property) & Flags.HIDDEN) != Flags.HIDDEN) {
                BValue component = parentComponent.get(property);
                for (Type sourcePropertyType : sourcePropertyTypes) {
                    if (component.getType().is(sourcePropertyType)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * check all properties in parent component and get supported widget types for each property.
     * @param parentComponent, parent component
     * @return widget type list
     */
    public static Set<String> getSlotSupportedWidgetTypes(BComponent parentComponent){
        Property[] properties = parentComponent.getPropertiesArray();
        Set<String> supportedWidgetTypes = new HashSet<>();
        for (Property property : properties) {
            if ((parentComponent.getFlags(property) & Flags.HIDDEN) != Flags.HIDDEN) {
                BValue propValue = parentComponent.get(property);
                if(propValue instanceof BNumber){
                    supportedWidgetTypes.addAll(pointTypeToWidgetTypeMap.get(BIHonWizardNumericPoint.TYPE));
                } else if(propValue instanceof BTimezoneEnum){
                    supportedWidgetTypes.add(TIMEZONE_TYPE);
                } else if(propValue instanceof BBoolean) {
                    supportedWidgetTypes.addAll(pointTypeToWidgetTypeMap.get(BIHonWizardBooleanPoint.TYPE));
                } else if(propValue instanceof BEnum) {
                    supportedWidgetTypes.addAll(pointTypeToWidgetTypeMap.get(BIHonWizardEnumPoint.TYPE));
                }
            }
        }
        return supportedWidgetTypes;
    }

    public static boolean isPointSelectorAttached(BIHonWizardPoint point) {
        return ((BComponent) point).getChildren(BHonWizSelector.class).length > 0;

    }

    /**
     * get enum range for schedule widget type
     *
     * @return
     */
    public static BEnumRange getScheduleWidgetEnumRange() {
        Pair<Integer, String> widgetTypeMapping = getWidgetTypeMapping(SCHEDULE);
        if(null != widgetTypeMapping) {
            return BEnumRange.make(new int[]{widgetTypeMapping.getFirst()}, new String[]{widgetTypeMapping.getSecond()});
        }
        return null;
    }


    /**
     * get all supported widget type for slot selector
     *
     * @return widget enum range
     */
    public static BEnumRange getSlotWidgetTypeEnumRange() {
        return getWidgetEnumRange(Arrays.stream(WIDGET_TYPE_LIST).filter(o -> !o.equals(SCHEDULE)).collect(Collectors.toList()));
    }

    /**
     * build widget type enum range
     *
     * @param widgetTypeList, supported widget type list
     * @return widget type enum range
     */
    private static BEnumRange getWidgetEnumRange(List<String> widgetTypeList) {
        if (null == widgetTypeList) {
            return null;
        }
        List<Integer> ordinalList = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        for (String widgetType : widgetTypeList) {
            Pair<Integer, String> widgetTypeMapping = getWidgetTypeMapping(widgetType);
            if (null != widgetTypeMapping) {
                ordinalList.add(widgetTypeMapping.getFirst());
                tagList.add(widgetTypeMapping.getSecond());
            }
        }
        return BEnumRange.make(ordinalList.stream().mapToInt(Integer::intValue).toArray(),
                tagList.toArray(new String[0]));
    }

    /**
     * get widget type ordinal by widget type value
     *
     * @param widgetType
     * @return ordinal value of the widget type
     */
    public static Integer getWidgetTypeOrdinal(String widgetType) {
        Pair<Integer, String> widgetTypeMapping = getWidgetTypeMapping(widgetType);
        if (null != widgetTypeMapping) {
            return widgetTypeMapping.getFirst();
        }
        return null;
    }

    /**
     * set source property name for slot selector.
     * source property enum range is decided by widget type and parent component properties.
     *
     * @param widgetType,      widget type
     * @param slotSelector,    slot selector
     * @param parentComponent, parent of selector component
     * @param propertyName,    property name
     */
    @SuppressWarnings("squid:S3776")
    public static void setSourcePropertyName(String widgetType, BComponent slotSelector, BComponent parentComponent, String propertyName) {
        if (widgetType == null || propertyName == null) {
            return;
        }
        List<Type> sourcePropertyTypes = widgetTypeToSourcePropertyTypeMap.get(widgetType);
        Property[] properties = parentComponent.getPropertiesArray();
        int index = 0;
        int currentOrdinal = 0;
        List<String> tags = new ArrayList<>();
        for (Property property : properties) {
            if ((parentComponent.getFlags(property) & Flags.HIDDEN) != Flags.HIDDEN) {
                BValue component = parentComponent.get(property);
                for (Type sourcePropertyType : sourcePropertyTypes) {
                    if (component.getType().is(sourcePropertyType)) {
                        tags.add(property.getName());
                        if (property.getName().equals(propertyName)) {
                            currentOrdinal = index;
                        }
                        index++;
                        break;
                    }
                }
            }
        }
        if (!tags.isEmpty()) {
            BEnumRange enumRange = BEnumRange.make(tags.toArray(new String[0]));
            slotSelector.set(SOURCE_PROPERTY_NAME, BDynamicEnum.make(currentOrdinal, enumRange));
        } else {
            BEnumRange enumRange = BEnumRange.make(new String[]{NO_VALID_SLOTS_AVAILABLE});
            slotSelector.set(SOURCE_PROPERTY_NAME, BDynamicEnum.make(enumRange));
        }
    }

    /**
     * for slot selector, in slot selector started method, below two cases need to check and set source property name
     * 1) drag and drop a selector from palette. need to set source property name enum list based on current properties in component.
     * 2) station is started. maybe property in a component has some changes, need to reset source property name enum list.
     */
    public static void checkAndSetSourcePropertyName(BHonWizSlotSelector slotSelector) {
        if (null == slotSelector) {
            return;
        }
        BIDataValue widgetTypeTagValue = slotSelector.getTagValue(Const.WIDGET_TYPE_TAG);
        setSourcePropertyName(widgetTypeTagValue.toString(), slotSelector, (BComponent) slotSelector.getParent(), slotSelector.getSourcePropertyNameString());

    }

    /**
     * show/hide tags based on widget type
     *
     * @param widgetType, widget type
     * @param selector,   selector
     */
    public static void changeTagsByWidgetType(String widgetType, BHonWizSelector selector) {
        List<String> requiredTags = widgetTypeToTagMap.get(widgetType + REQUIRED);
        List<String> optionalTags = widgetTypeToTagMap.get(widgetType + OPTIONAL);
        Iterator<Tag> itr = selector.tags().iterator();
        List<String> existingTags = new ArrayList<>();
        BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
        List<Tag> tagsToRemove = new ArrayList<>(); // Collect tags to remove
        BHonWizardTag bHonWizardTag = (BHonWizardTag) tagDictionaryService.get("HonConfigureWizard");
        while (itr.hasNext()) {
            Tag t = itr.next();
            Id tagId = t.getId();
            if (!optionalTags.contains(tagId.getName()) && !requiredTags.contains(tagId.getName())) {
                tagsToRemove.add(t); // Mark for removal
            } else {
                existingTags.add(tagId.getName());
            }
        }
        // Remove tags after iteration
        for (Tag tag : tagsToRemove) {
            selector.tags().remove(tag);
        }
        List<Tag> newTagList = new ArrayList<>();
        for (String tag : requiredTags) {
            if (!existingTags.contains(tag)) {
                BValue bTagInfo = bHonWizardTag.getTagDefinitions().get(tag);
                newTagList.add(buildDefaultTag(bTagInfo, tag));
            }
        }
        for (String tag : optionalTags) {
            if (!existingTags.contains(tag)) {
                BValue bTagInfo = bHonWizardTag.getTagDefinitions().get(tag);
                newTagList.add(buildDefaultTag(bTagInfo, tag));
            }
        }
        for (Tag tag : newTagList) {
            selector.tags().set(tag);
        }
    }

    /**
     * build default tag based on tag value type
     *
     * @param bTagInfo,    tag info
     * @param realTagName, tag name
     * @return tag
     */
    private static Tag buildDefaultTag(BValue bTagInfo, String realTagName) {
        Tag tag = null;
        if (BHonWizardTag.ENUM_RANGE_TAGS.contains(realTagName)) {
        	if(realTagName.equals(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG)) { 
        		tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BUnitGroupEnum.measurementType);
			} else {
				tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BEnumRange.DEFAULT);
			}
        } else if (BHonWizardTag.BOOLEAN_TAGS.contains(realTagName)) {
            tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BBoolean.TRUE);
        } else {
            tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BString.make(""));
        }
        return tag;

    }

    /**
     * as showing all tag to user to configure, include optional tag and require tags, maybe user won't configure all tag
     * especially for optional tag, if user don't configure them, we will skip these tags validation.
     *
     * @param honWizSelector
     * @param tagValues
     * @return
     */
    public static Map<String, BIDataValue> filterNoConfigOptionalTagValues(BHonWizSelector honWizSelector, Map<String, BIDataValue> tagValues) {
        BIDataValue widgetTypeTagValue = honWizSelector.getTagValue(Const.WIDGET_TYPE_TAG);
        if (null != widgetTypeTagValue) {
            String widgetType = widgetTypeTagValue.toString();
            Iterator<Map.Entry<String, BIDataValue>> iterator = tagValues.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, BIDataValue> tag = iterator.next();
                if (shouldSkipTag(widgetType, tag.getKey(), tag.getValue())) {
                    iterator.remove();
                }
            }
        }
        return tagValues;

    }

    /**
     * check if skipping the tag
     * - the tag is optional tag, currently optional tag only includes string and enumrange.
     * - if the tag type is string, it should not be empty
     * - if the tag type is enumrange, it should not be null
     *
     * @param widgetType
     * @param tagName
     * @param tagValue
     * @return true or false
     */
    private static boolean shouldSkipTag(String widgetType, String tagName, BIDataValue tagValue) {
        if (widgetTypeToTagMap.containsKey(widgetType + OPTIONAL)  && widgetTypeToTagMap.get(widgetType + OPTIONAL).contains(tagName)) {
            if ((tagValue instanceof BEnumRange && ((BEnumRange) tagValue).isNull()) || tagValue.toString().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * add selector for point selector and slot selector based on widget type
     *
     * @param widgetName
     * @param widgetType
     * @param parentComp
     */
    public static void addSelectorByWidgetType(String widgetName, String widgetType, BComponent parentComp) {
        if (parentComp instanceof BIHonWizardPoint) {
            BHonWizPointSelector pointSelector = new BHonWizPointSelector();
            Integer widgetTypeOrdinal = getWidgetTypeOrdinal(widgetType);
            BDynamicEnum widgetTypeEnum = BDynamicEnum.make(widgetTypeOrdinal,
                    getPointWidgetEnumRange((BIHonWizardPoint) parentComp));
            pointSelector.tags().set(new Tag(WGTTYPE_TAG, widgetTypeEnum));
            changeTagsByWidgetType(widgetType, pointSelector);
            parentComp.add(widgetName + "?", pointSelector);
        } else {
            BHonWizSlotSelector slotSelector = new BHonWizSlotSelector();
            Integer widgetTypeOrdinal = getWidgetTypeOrdinal(widgetType);
            BDynamicEnum widgetTypeEnum = BDynamicEnum.make(widgetTypeOrdinal, getSlotWidgetTypeEnumRange());
            slotSelector.tags().set(new Tag(WGTTYPE_TAG, widgetTypeEnum));
            changeTagsByWidgetType(widgetType, slotSelector);
            parentComp.add(widgetName + "?", slotSelector);
            setSourcePropertyName(widgetType, slotSelector, parentComp, "");
        }
    }
}
