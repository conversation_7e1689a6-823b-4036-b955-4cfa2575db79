/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.utils;

import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>husud<PERSON>
 * @since Jan 27, 2025
 */
public final class LexiconUtil
{
	private static final Lexicon lex = Lexicon.make(BHonWizardJob.class);

	private LexiconUtil() {
		throw new IllegalStateException();
	}

	public static String getDisplayText(Enum<?> enumToLookup) {
		// textual info
		String key = enumToLookup.getClass().getSimpleName() + "." + enumToLookup.name();
		return lex.get(key, enumToLookup.name());
	}

	public static String getText(Enum<?> enumToLookup, Object[] args) {
		String key = enumToLookup.getClass().getSimpleName() + "." + enumToLookup.name();
		return lex.getText(key, args);
	}

	public static Lexicon getLexicon() {
		return lex;
	}
}
