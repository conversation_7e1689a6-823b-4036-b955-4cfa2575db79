/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.utils;

import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.Constants;
import com.honeywell.applicationhandler.widgetcomponents.BAirflowUnit;
import com.honeywell.applicationhandler.widgetcomponents.BCheckbox;
import com.honeywell.applicationhandler.widgetcomponents.BDropdown;
import com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BMeasurementType;
import com.honeywell.applicationhandler.widgetcomponents.BNumberInput;
import com.honeywell.applicationhandler.widgetcomponents.BOption;
import com.honeywell.applicationhandler.widgetcomponents.BRadioButton;
import com.honeywell.applicationhandler.widgetcomponents.BRangeSlider;
import com.honeywell.applicationhandler.widgetcomponents.BSelectButton;
import com.honeywell.applicationhandler.widgetcomponents.BSelectWidget;
import com.honeywell.applicationhandler.widgetcomponents.BSingleSlider;
import com.honeywell.applicationhandler.widgetcomponents.BSwitchButton;
import com.honeywell.applicationhandler.widgetcomponents.BTextInput;
import com.honeywell.applicationhandler.widgetcomponents.BTimeZone;
import com.honeywell.applicationhandler.widgetcomponents.json.AirflowUnit;
import com.honeywell.applicationhandler.widgetcomponents.json.Checkbox;
import com.honeywell.applicationhandler.widgetcomponents.json.Dropdown;
import com.honeywell.applicationhandler.widgetcomponents.json.DuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.json.DynamicWidgetBase;
import com.honeywell.applicationhandler.widgetcomponents.json.DynamicWidgetWithOptionBase;
import com.honeywell.applicationhandler.widgetcomponents.json.MeasurementType;
import com.honeywell.applicationhandler.widgetcomponents.json.NumberInput;
import com.honeywell.applicationhandler.widgetcomponents.json.PropertyTag;
import com.honeywell.applicationhandler.widgetcomponents.json.RadioButton;
import com.honeywell.applicationhandler.widgetcomponents.json.RangeSlider;
import com.honeywell.applicationhandler.widgetcomponents.json.SelectButton;
import com.honeywell.applicationhandler.widgetcomponents.json.SelectWidget;
import com.honeywell.applicationhandler.widgetcomponents.json.SingleSlider;
import com.honeywell.applicationhandler.widgetcomponents.json.SwitchButton;
import com.honeywell.applicationhandler.widgetcomponents.json.TextInput;
import com.honeywell.applicationhandler.widgetcomponents.json.Timezone;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

import javax.baja.sys.BBoolean;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BInteger;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BUuid;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zhang
 */
public class HoneywellWidgetStaticDataUtil {
    private static final String USER_CONFIG_DATA_FOLDER = "userConfigData";
    private static final String STATIC_DATA_FOLDER = "staticData";

    private static final Object RULE_STORE_LOCK = new Object();
    private static final Object STATIC_DATA_LOCK = new Object();
    
    /**
     * get static file of global store
     * @param globalStoreUuid
     * @return
     */
    public static String getGlobalStoreStaticFile(BUuid globalStoreUuid) {
        return globalStoreUuid.getMostSignificant() + "_globalStore.hfg";
    }
    public static String getGlobalStoreStaticTempFile(BUuid globalStoreUuid) {
        return globalStoreUuid.getMostSignificant() + "_globalStore.hfg.temp";
    }

    /**
     * get static file of rule store
     * @param globalStoreUuid
     * @return
     */
    public static String getRuleStoreFile(BUuid globalStoreUuid) {
        return globalStoreUuid.getMostSignificant() + "_ruleStore.hfg";
    }
    public static String getRuleStoreTempFile(BUuid globalStoreUuid) {
        return globalStoreUuid.getMostSignificant() + "_ruleStore.hfg.temp";
    }

    /**
     * load static data from Json file
     * @param globalStoreUuid
     * @return
     */
    public static String loadStaticDataFromJson(BUuid globalStoreUuid) {
        synchronized (STATIC_DATA_LOCK) {
            String globalStoreStaticFile = getGlobalStoreStaticFile(globalStoreUuid);
            File userConfigData = new File(Sys.getStationHome(), USER_CONFIG_DATA_FOLDER + File.separator + STATIC_DATA_FOLDER);
            if (!userConfigData.exists()) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.globalstore.noexist"));
                return null;
            }
            File globalStaticFile = new File(userConfigData, globalStoreStaticFile);
            if (!globalStaticFile.exists()) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.globalstore.noexist"));
                return null;
            }
            try (BufferedReader reader = new BufferedReader(new FileReader(globalStaticFile))) {
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append(System.lineSeparator());
                }
                return content.toString().trim();
            } catch (IOException e) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.globalstore.parse.error"), e);
                return null;
            }
        }
    }

    /**
     * load rule store component from json file
     * @param globalStoreUuid
     * @return
     */

    public static JSONObject loadRuleStoreFromJson(BUuid globalStoreUuid) {
        synchronized (RULE_STORE_LOCK) {
            String ruleStoreFile = getRuleStoreFile(globalStoreUuid);
            File userConfigData = new File(Sys.getStationHome(), USER_CONFIG_DATA_FOLDER + File.separator + STATIC_DATA_FOLDER);
            if (!userConfigData.exists()) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.rulestore.noexist"));
                return null;
            }
            File ruleStoreJsonFile = new File(userConfigData, ruleStoreFile);
            if (!ruleStoreJsonFile.exists()) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.rulestore.noexist"));
                return null;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(ruleStoreJsonFile))) {
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append(System.lineSeparator());
                }
                return new JSONObject(content.toString().trim());
            } catch (IOException e) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.rulestore.parse.error"), e);
                return null;
            }
        }

    }

    /**
     * save rule store to json file, copy global store file when rules are changed
     * @param ruleStore
     * @param globalStoreUuid
     */
    public static void saveRuleStoreToJson(BHonWizardRuleStore ruleStore, BIHoneywellConfigurableDevice device) {
        synchronized (RULE_STORE_LOCK) {
            if (ruleStore == null || device == null) return;
            BUuid oldGlobalStoreUuid = device.getGlobalStoreUuid(false);
            BUuid newGlobalStoreUuid = device.getGlobalStoreUuid(true);
            File ruleStoreBogFile = getFile(newGlobalStoreUuid, false);
            JSONObject ruleJson = new JSONObject();
            for (Property rule : ruleStore.getDynamicPropertiesArray()) {
                if(ruleStore.get(rule) instanceof BString) {
                    ruleJson.put(rule.getName(), ruleStore.getString(rule));
                }
            }
            try (PrintWriter writer = new PrintWriter(new FileWriter(ruleStoreBogFile.getAbsolutePath()))) {
                writer.print(ruleJson);
            } catch (IOException e) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.rulestore.save.error"), e);
            }
            copyGlobalStoreFile(oldGlobalStoreUuid, newGlobalStoreUuid);

        }
    }

    /**
     * get file according to global store uuid and type
     * @param globalStoreUuid
     * @param isGlobalStore
     * @return
     */
    private static File getFile(BUuid globalStoreUuid, boolean isGlobalStore) {
        String fileName = isGlobalStore?getGlobalStoreStaticFile(globalStoreUuid):getRuleStoreFile(globalStoreUuid);

        File userConfigData = new File(Sys.getStationHome(), USER_CONFIG_DATA_FOLDER);
        if(!userConfigData.exists()) {
            userConfigData.mkdir();
        }
        File staticData = new File(userConfigData, STATIC_DATA_FOLDER);
        if(!staticData.exists()){
            staticData.mkdir();
        }
        File file = new File(staticData, fileName);
        if(file.exists()){
            file.delete();
        }
        return file;
    }

    /**
     * write static data of global store to json file
     * @param globalStoreJson
     * @param globalStoreUuid
     */
    public static void generateStaticDataInFile(JSONObject globalStoreJson, BUuid globalStoreUuid) {
        synchronized (STATIC_DATA_LOCK) {
            if (globalStoreJson == null) return;
            File globalStaticFile = getFile(globalStoreUuid, true);
            try (PrintWriter writer = new PrintWriter(new FileWriter(globalStaticFile.getAbsolutePath()))) {
                writer.print(globalStoreJson);
            } catch (IOException e) {
                HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.file.save.error"), e);
            }
        }
    }


    /**
     * copy rule store file, when global store is changed, we only copy a new rule store file with new global store uuid
     * @param sourceGlobalStoreUuid
     * @param targetGlobalStoreUuid
     */
    public static void copyRuleStoreFile(BUuid sourceGlobalStoreUuid, BUuid targetGlobalStoreUuid) {
        if(sourceGlobalStoreUuid == null || targetGlobalStoreUuid == null) return;
        if(sourceGlobalStoreUuid.getMostSignificant() == targetGlobalStoreUuid.getMostSignificant()){
            return;
        }
        File userConfigData = new File(Sys.getStationHome(), USER_CONFIG_DATA_FOLDER + File.separator + STATIC_DATA_FOLDER);
        if(!userConfigData.exists()) {
            return;
        }

        String sourceRuleStoreFile = getRuleStoreFile(sourceGlobalStoreUuid);
        File sourceRuleStoreBogFile = new File(userConfigData, sourceRuleStoreFile);
        if(!sourceRuleStoreBogFile.exists()){
            return;
        }
        String targetRuleStoreFileName = getRuleStoreFile(targetGlobalStoreUuid);
        String targetRuleStoreTempFileName = getRuleStoreTempFile(targetGlobalStoreUuid);
        File targetRuleStoreTempFile = new File(userConfigData, targetRuleStoreTempFileName);
        try (BufferedReader reader = new BufferedReader(new FileReader(sourceRuleStoreBogFile));
            PrintWriter writer = new PrintWriter(new FileWriter(targetRuleStoreTempFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                writer.println(line);
            }
        } catch (IOException e) {
            HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.rulestore.copy.error"), e);
        }
        File targetFile = new File(userConfigData, targetRuleStoreFileName);
        if(targetFile.exists()){
            targetFile.delete();
        }
        targetRuleStoreTempFile.renameTo(targetFile);
    }

    /**
     * copy global store file, when rule store is changed, we only copy a new global store file with new global store uuid
     * @param sourceGlobalStoreUuid
     * @param targetGlobalStoreUuid
     */
    public static void copyGlobalStoreFile(BUuid sourceGlobalStoreUuid, BUuid targetGlobalStoreUuid) {
        if(sourceGlobalStoreUuid == null || targetGlobalStoreUuid == null) return;
        if(sourceGlobalStoreUuid.getMostSignificant() == targetGlobalStoreUuid.getMostSignificant()) {
            return;
        }
        File userConfigData = new File(Sys.getStationHome(), USER_CONFIG_DATA_FOLDER + File.separator + STATIC_DATA_FOLDER);
        if(!userConfigData.exists()) {
            return;
        }

        String sourceGlobalStoreFile = getGlobalStoreStaticFile(sourceGlobalStoreUuid);
        File sourceGlobalStoreBogFile = new File(userConfigData, sourceGlobalStoreFile);
        if(!sourceGlobalStoreBogFile.exists()){
            return;
        }
        String targetGlobalStoreFileName = getGlobalStoreStaticFile(targetGlobalStoreUuid);
        String targetGlobalStoreTempFileName = getGlobalStoreStaticTempFile(targetGlobalStoreUuid);
        File targetGlobalStoreTempFile = new File(userConfigData, targetGlobalStoreTempFileName);
        try (BufferedReader reader = new BufferedReader(new FileReader(sourceGlobalStoreBogFile));
            PrintWriter writer = new PrintWriter(new FileWriter(targetGlobalStoreTempFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                writer.println(line);
            }
        } catch (IOException e) {
            HoneywellDeviceWizardLogger.severe(LexiconUtil.getLexicon().getText("honWizard.static.globalstore.copy.error"), e);
        }
        File targetFile = new File(userConfigData, targetGlobalStoreFileName);
        if(targetFile.exists()){
            targetFile.delete();
        }
        targetGlobalStoreTempFile.renameTo(targetFile);

    }



    /**
     * update wizard property value in JSON file,immediate sync with wizard configuration when tag is changed
     * @param updates, key is page name, value is property list to be updated
     * @param device
     */
    public static void updateMultiplePropertiesInJsonFile(Map<String, List<PropertyTag>> updates, BIHoneywellConfigurableDevice device) {
        BUuid oldGlobalStoreUuid = device.getGlobalStoreUuid(false);
        BUuid globalStoreUuid = device.getGlobalStoreUuid(true);
        if (null == globalStoreUuid) {
            return;
        }
        String globalStoreStaticData = loadStaticDataFromJson(oldGlobalStoreUuid);
        if(null == globalStoreStaticData){
            return;
        }
        JSONObject globalStoreStaticJson = new JSONObject(globalStoreStaticData);
        for (Map.Entry<String, List<PropertyTag>> entry : updates.entrySet()) {
            String pageName = entry.getKey();
            List<PropertyTag> propUpdates = entry.getValue();
            if (globalStoreStaticJson.has(pageName)) {
                JSONObject pageJson = globalStoreStaticJson.getJSONObject(pageName);
                for (PropertyTag propUpdate : propUpdates) {
                    String compName = propUpdate.getComponentName();
                    String propertyName = propUpdate.getPropertyName();
                    Type type = propUpdate.getType();
                    BValue value = propUpdate.getValue();
                    BHonWizSelector selector = propUpdate.getSelector();
                    BDynamicWidgetComponentBase widgetComponentBase = propUpdate.getWidgetComponentBase();
                    DynamicWidgetBase widgetBase = getWidgetComp(widgetComponentBase);
                    JSONObject compJsonObj = pageJson.getJSONObject(compName);
                    if (propertyName.equals("color") && value != null && !value.toString().isEmpty()) {
                        compJsonObj.put(propertyName, BString.make(Constants.getColorCodeForColor(value.toString())));
                    } else if (propertyName.equals("options") && value != null) {
                        JSONArray jsonArray = getOptions((BEnumRange) value, selector, (DynamicWidgetWithOptionBase) widgetBase);
                        compJsonObj.put("options", jsonArray);
                    } else {
                        assignJsonValue(type, compJsonObj, propertyName, value);
                    }

                }
            }
        }
        generateStaticDataInFile(globalStoreStaticJson, globalStoreUuid);
        copyRuleStoreFile(oldGlobalStoreUuid, globalStoreUuid);
    }

    /**
     * remove wizard property value in JSON file,immediate sync with wizard configuration when tag is removed
     * @param updates, key is page name, value is property list to be removed
     * @param device
     */
    public static void resetMultiplePropertiesInJsonFile(Map<String, List<PropertyTag>> updates, BIHoneywellConfigurableDevice device) {
        BUuid oldGlobalStoreGuid = device.getGlobalStoreUuid(false);
        BUuid globalStoreUuid = device.getGlobalStoreUuid(true);
        if (null == globalStoreUuid) {
            return;
        }
        String globalStoreStaticData = loadStaticDataFromJson(oldGlobalStoreGuid);
        if(null == globalStoreStaticData){
            return;
        }
        JSONObject globalStoreStaticJson = new JSONObject(globalStoreStaticData);
        for (Map.Entry<String, List<PropertyTag>> entry : updates.entrySet()) {
            String pageName = entry.getKey();
            List<PropertyTag> propUpdates = entry.getValue();
            if (globalStoreStaticJson.has(pageName)) {
                JSONObject pageJson = globalStoreStaticJson.getJSONObject(pageName);
                for (PropertyTag propUpdate : propUpdates) {
                    String compName = propUpdate.getComponentName();
                    String propertyName = propUpdate.getPropertyName();
                    JSONObject compJsonObj = pageJson.getJSONObject(compName);
                    if (propertyName.equals("options")) {
                        compJsonObj.put("options", new JSONArray());
                    } else {
                        compJsonObj.remove(propertyName);
                    }

                }
            }
        }
        generateStaticDataInFile(globalStoreStaticJson, globalStoreUuid);
        copyRuleStoreFile(oldGlobalStoreGuid, globalStoreUuid);
    }

    /**
     * assign value to json object according to type
     * @param type
     * @param compJsonObj
     * @param propertyName
     * @param value
     */
    private static void assignJsonValue(Type type, JSONObject compJsonObj, String propertyName, BValue value) {
        if(type.equals(BInteger.TYPE)){
            compJsonObj.put(propertyName, ((BInteger) value).getInt());
        }else if(type.equals(BDouble.TYPE)){
            compJsonObj.put(propertyName, ((BDouble) value).getDouble());
        }else if(type.equals(BBoolean.TYPE)){
            compJsonObj.put(propertyName, ((BBoolean) value).getBoolean());
        }else{
            compJsonObj.put(propertyName, value.toString());
        }
    }

    /**
     * get new options
     * @param value
     * @param selector
     * @param widgetWithOptionBase
     * @return
     */
    private static JSONArray getOptions(BEnumRange value, BHonWizSelector selector, DynamicWidgetWithOptionBase widgetWithOptionBase) {
        BEnumRange newOptions = value;
        BEnumRange targetEnumRange = selector.getTargetEnumRange();
        List<BOption> options = widgetWithOptionBase.makeOptions(newOptions, targetEnumRange);
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < options.size(); i++) {
            BOption option = options.get(i);
            JSONObject optionJson = option.toJson();
            jsonArray.put(optionJson);
        }
        return jsonArray;
    }

    /**
     * get JSON widget component according to widget component
     * @param widgetComponentBase
     * @return
     */
    private static DynamicWidgetBase getWidgetComp(BDynamicWidgetComponentBase widgetComponentBase) {
        if(widgetComponentBase instanceof BAirflowUnit){
            return new AirflowUnit();
        } else if(widgetComponentBase instanceof BCheckbox){
            return new Checkbox();
        } else if(widgetComponentBase instanceof BDropdown) {
            return new Dropdown();
        } else if(widgetComponentBase instanceof BDuctAreaCalculator) {
            return new DuctAreaCalculator();
        } else if(widgetComponentBase instanceof BMeasurementType) {
            return new MeasurementType();
        } else if(widgetComponentBase instanceof BNumberInput) {
            return new NumberInput();
        } else if(widgetComponentBase instanceof BRadioButton) {
            return new RadioButton();
        } else if(widgetComponentBase instanceof BRangeSlider) {
            return new RangeSlider();
        } else if(widgetComponentBase instanceof BSelectButton) {
            return new SelectButton();
        } else if(widgetComponentBase instanceof BSelectWidget) {
            return new SelectWidget();
        } else if(widgetComponentBase instanceof BSingleSlider) {
            return new SingleSlider();
        } else if(widgetComponentBase instanceof BSwitchButton) {
            return new SwitchButton();
        } else if(widgetComponentBase instanceof BTextInput) {
            return new TextInput();
        } else if(widgetComponentBase instanceof BTimeZone) {
            return new Timezone();
        } else{
            throw new IllegalArgumentException("Unsupported widget component type: " + widgetComponentBase.getClass().getName());
        }

    }

    /**
     * update widget name, when component name is changed in irm folder or irm subfolder, it will be called.
     * @param device
     * @param oldName
     * @param newName
     */
    public static void updateWidgetNameInJsonFile(BIHoneywellConfigurableDevice device, String oldName, String newName) {
        BUuid oldGlobalStoreGuid = device.getGlobalStoreUuid(false);
        BUuid globalStoreUuid = device.getGlobalStoreUuid(true);
        if (null == globalStoreUuid) {
            return;
        }
        String globalStoreStaticData = loadStaticDataFromJson(oldGlobalStoreGuid);
        if(null == globalStoreStaticData){
            return;
        }
        JSONObject globalStoreStaticJson = new JSONObject(globalStoreStaticData);
        for (String pageName : globalStoreStaticJson.keySet()) {
            JSONObject pageJson = globalStoreStaticJson.getJSONObject(pageName);
            for(String widgetName : pageJson.keySet()) {
                if(widgetName.equals(oldName)){
                    JSONObject widgetJson = pageJson.getJSONObject(widgetName);
                    pageJson.remove(oldName);
                    pageJson.put(newName, widgetJson);
                    break;
                }
            }

        }
        generateStaticDataInFile(globalStoreStaticJson, globalStoreUuid);
        copyRuleStoreFile(oldGlobalStoreGuid, globalStoreUuid);

    }

}
