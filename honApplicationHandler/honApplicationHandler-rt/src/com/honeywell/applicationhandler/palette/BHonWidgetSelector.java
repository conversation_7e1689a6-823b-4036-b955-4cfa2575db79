/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.palette;

import static com.honeywell.applicationhandler.common.Constants.SCHEDULE;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BStation;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;

/**
 * <AUTHOR>
 */
@NiagaraType
@NiagaraProperty(
        name = "widgetType",
        type = "baja:String",
        defaultValue = "BString.DEFAULT",
        flags = Flags.HIDDEN
)
public class BHonWidgetSelector extends BComponent {
  /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
  /*@ $com.honeywell.applicationhandler.palette.BHonWidgetSelector(2083094570)1.0$ @*/
  /* Generated Tue Jul 08 16:32:41 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "widgetType"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code widgetType} property.
   * @see #getWidgetType
   * @see #setWidgetType
   */
  public static final Property widgetType = newProperty(Flags.HIDDEN, BString.DEFAULT, null);

  /**
   * Get the {@code widgetType} property.
   * @see #widgetType
   */
  public String getWidgetType() { return getString(widgetType); }

  /**
   * Set the {@code widgetType} property.
   * @see #widgetType
   */
  public void setWidgetType(String v) { setString(widgetType, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWidgetSelector.class);

  /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    @Override
    public void started() throws Exception {
        super.started();
        BComponent parent = (BComponent) this.getParent();
        String widgetName = this.getName();
        parent.remove(widgetName);
        HoneywellWidgetTagUtil.addSelectorByWidgetType(widgetName, getWidgetType(), parent);

    }

    @Override
    @SuppressWarnings("squid:S3516") // Niagara forces me to use an Exception instead of returning false
    public boolean isParentLegal(BComponent parent) {
        if (!Sys.isStation()) {
            return true;
        }
        BIHoneywellConfigurableDevice device = HoneywellConfigurableDeviceUtil.getDevice(parent);
        if(null != device && !device.getExpertMode()){
            throw new SelectorNotAllowedException("error.palette.slot.selector.no.support.expertmode.disabled");
        }
        if(parent instanceof BIHonWizardPoint){
            if(HoneywellWidgetTagUtil.isPointSelectorAttached((BIHonWizardPoint) parent)){
                throw new SelectorNotAllowedException("error.palette.point.selector.exist");
            }
            if(!HoneywellWidgetTagUtil.isPointSupportedSelector((BIHonWizardPoint) parent, getWidgetType())){
                throw new PointSelectorNoSupportException(getWidgetType(),
                        HoneywellWidgetTagUtil.getPointSupportedWidgetTypes((BIHonWizardPoint)parent));
            }
        }else{
            if(getWidgetType().equals(SCHEDULE)){
                throw new SelectorNotAllowedException("error.palette.slot.selector.schedule.no.support");
            }
           
            if(!device.isSlotSelectorSupportedInWizard(parent)) {
            	 throw new SlotSelectorNoSupportException(getWidgetType());
            }
            if(!HoneywellWidgetTagUtil.isSlotSupportedSelector(parent, getWidgetType())){
                throw new SlotSelectorNoSupportException(getWidgetType(), HoneywellWidgetTagUtil.getSlotSupportedWidgetTypes(parent));
            }
        }
        return true;
    }
}
