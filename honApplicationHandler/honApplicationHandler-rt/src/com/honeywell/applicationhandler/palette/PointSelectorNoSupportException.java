/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.palette;

import com.honeywell.applicationhandler.utils.LexiconUtil;

import javax.baja.sys.LocalizableRuntimeException;
import javax.baja.util.Lexicon;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PointSelectorNoSupportException extends LocalizableRuntimeException {
    private static final long serialVersionUID = 1;
    static final Lexicon lex = LexiconUtil.getLexicon();
    public PointSelectorNoSupportException(String widgetType, List<String> supportedWidgetTypes){
        super(BHonWidgetSelector.TYPE.getModule().getModuleName(),
                "error.palette.point.selector.no.support", new Object[]{widgetType,
                       String.join("\n", supportedWidgetTypes) });

    }

}
