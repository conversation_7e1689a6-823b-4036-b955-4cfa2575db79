/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.palette;

import com.honeywell.applicationhandler.utils.LexiconUtil;

import javax.baja.sys.LocalizableRuntimeException;
import javax.baja.util.Lexicon;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class SlotSelectorNoSupportException extends LocalizableRuntimeException{
    private static final long serialVersionUID = 1;
    static final Lexicon lex = LexiconUtil.getLexicon();
    public SlotSelectorNoSupportException(String widgetType, Set<String> supportedWidgetTypes){
        super(BHonWidgetSelector.TYPE.getModule().getModuleName(), "error.palette.slot.selector.no.support", new Object[]{widgetType,
                String.join("\n", supportedWidgetTypes) });
    }
    
    public SlotSelectorNoSupportException(String widgetType){
        super(BHonWidgetSelector.TYPE.getModule().getModuleName(), "error.palette.slot.selector.no.support.component");
    }

}
