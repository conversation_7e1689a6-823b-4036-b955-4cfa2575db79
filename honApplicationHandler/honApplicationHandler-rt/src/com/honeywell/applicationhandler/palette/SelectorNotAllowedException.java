/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.palette;

import javax.baja.sys.LocalizableRuntimeException;

/**
 * <AUTHOR>
 */
public class SelectorNotAllowedException extends LocalizableRuntimeException {
    private static final long serialVersionUID = 1;
    public SelectorNotAllowedException(String lexiconKey){
        super(BHonWidgetSelector.TYPE.getModule().getModuleName(), lexiconKey);

    }

}
