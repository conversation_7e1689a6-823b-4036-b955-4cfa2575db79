/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since Jun 23, 2025
 */
/**
 * The {@code ByteArrayBuilder} class provides a convenient and efficient way to construct a byte array
 * by sequentially appending various primitive data types and strings. This builder pattern is useful
 * for encoding data into a binary format for serialization, network transmission, or storage.
 * <p>
 * The class internally uses a {@link java.io.ByteArrayOutputStream} to accumulate bytes and provides
 * methods to add bytes, arrays of bytes, integers, doubles, booleans, and UTF-8 encoded strings.
 * Each method updates the internal offset to track the current position in the byte array.
 * <p>
 * Logging is performed using {@link WizardLogger} with message keys retrieved from a {@link Lexicon}
 * instance, which is initialized with {@link TagMapping}. This allows for internationalization and
 * consistent logging messages.
 * <p>
 * <b>Usage Example:</b>
 * <pre>
 *     ByteArrayBuilder builder = new ByteArrayBuilder();
 *     builder.addInt(42);
 *     builder.addString("Hello");
 *     builder.addBoolean(true);
 *     byte[] result = builder.toByteArray();
 * </pre>
 *
 * <p>
 * <b>Dependencies:</b>
 * <ul>
 *   <li>{@link java.io.ByteArrayOutputStream} - for byte accumulation</li>
 *   <li>{@link java.nio.ByteBuffer} - for primitive type to byte conversion</li>
 *   <li>{@link java.nio.charset.StandardCharsets} - for UTF-8 encoding</li>
 *   <li>{@link WizardLogger} - for logging operations</li>
 *   <li>{@link Lexicon} and {@link TagMapping} - for localized log messages</li>
 * </ul>
 *
 */
public class ByteArrayBuilder {
    private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private static final Lexicon lex = Lexicon.make(TagMapping.class);
    private int offset = 0;

    /**
     * Appends a single byte to the internal byte array.
     *
     * @param value the byte value to add (only the least significant 8 bits are used)
     * @see WizardLogger
     */
    public void addByte(int value) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addByte"), value);
        outputStream.write((byte) value);
        offset++;
    }

    /**
     * Appends a sequence of bytes from the specified array to the internal byte array.
     *
     * @param bytes  the source byte array
     * @param start  the starting offset in the array
     * @param length the number of bytes to write
     * @throws IndexOutOfBoundsException if start or length are invalid
     * @see WizardLogger
     */
    public void addBytes(byte[] bytes, int start, int length) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addBytes"), start, length);
        outputStream.write(bytes, start, length);
        offset += length;
    }

    /**
     * Appends a UTF-8 encoded string to the internal byte array.
     * The method first writes the length of the encoded byte array as a 4-byte integer,
     * followed by the UTF-8 bytes of the string.
     *
     * @param value the string to add
     * @see java.nio.charset.StandardCharsets
     * @see #addInt(int)
     * @see WizardLogger
     */
    public void addString(String value) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addString"), value);
		byte[] bytes = value.getBytes(StandardCharsets.UTF_8);
		addInt(bytes.length); // Add length of the string
		outputStream.write(bytes, 0, bytes.length);
		offset += bytes.length;
    }

    /**
     * Appends a 4-byte integer (big-endian) to the internal byte array.
     *
     * @param value the integer value to add
     * @see java.nio.ByteBuffer
     * @see WizardLogger
     */
    public void addInt(int value) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addInt"), value);
        byte[] bytes = ByteBuffer.allocate(4).putInt(value).array();
        outputStream.write(bytes, 0, bytes.length);
        offset += bytes.length;
    }

    /**
     * Appends an 8-byte double (IEEE 754, big-endian) to the internal byte array.
    *
    * @param value the double value to add
    * @see java.nio.ByteBuffer
    * @see WizardLogger
    */
    public void addDouble(double value) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addDouble"), value);
        byte[] bytes = ByteBuffer.allocate(8).putDouble(value).array();
        outputStream.write(bytes, 0, bytes.length);
        offset += bytes.length;
    }

    /**
     * Appends a boolean value to the internal byte array.
     * The value {@code true} is encoded as {@code 1}, and {@code false} as {@code 0}.
     *
     * @param value the boolean value to add
     * @see WizardLogger
     */
    public void addBoolean(boolean value) {
        WizardLogger.logFiner(lex.get("honWizardJob.byteBuilder.addBoolean"), value);
        outputStream.write(value ? 1 : 0);
        offset++;
    }

    /**
     * Returns a copy of the accumulated byte array.
     *
     * @return the byte array containing all appended data
     */
    public byte[] toByteArray() {
        return outputStream.toByteArray();
    }

    /**
     * Returns the current length of the accumulated byte array.
     *
     * @return the number of bytes written so far
     */
    public int getLength() {
        return outputStream.size();
    }

    /**
     * Returns the current offset, which is the total number of bytes written.
     * This is equivalent to {@link #getLength()}.
     *
     * @return the offset in the byte array
     */
    public int getOffset() {
        return offset;
    }
}