/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.Map;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
/**
 * <p>
 * The {@code GenericRepeatedValueCollector} class provides a generic implementation of the
 * {@link RepeatedValueCollector} interface for collecting the frequency of repeated values
 * from a given data source. This class is designed to work with any type of data by leveraging
 * Java generics, allowing it to be reused for various data types without code duplication.
 * </p>
 *
 * <p>
 * The primary use case for this class is to analyze a collection of values (such as sensor readings,
 * device messages, or any iterable data set) and determine how many times each unique value appears.
 * The results are stored in a {@code Map<Object, Integer>}, where the key is the value from the data
 * source and the value is the number of occurrences.
 * </p>
 *
 * <p>
 * <b>Usage Example:</b>
 * <pre>
 * {@code
 * List<String> deviceTags = Arrays.asList("A", "B", "A", "C", "B", "A");
 * Map<Object, Integer> occurrences = new HashMap<>();
 * RepeatedValueCollector collector = new GenericRepeatedValueCollector<>(deviceTags);
 * collector.collectRepeatedValues(occurrences);
 * // occurrences now contains: {"A"=3, "B"=2, "C"=1}
 * }
 * </pre>
 * </p>
 *
 * <p>
 * <b>Thread Safety:</b> This class is not thread-safe. If multiple threads access an instance
 * concurrently, external synchronization is required.
 * </p>
 *
 * @param <T> the type of elements in the data source to be analyzed for repeated values
 * @see RepeatedValueCollector
 */
public class GenericRepeatedValueCollector<T> implements RepeatedValueCollector {
    private final Iterable<T> dataSource;

    /**
     * Constructs a {@code GenericRepeatedValueCollector} with the specified data source.
     *
     * @param dataSource an {@link Iterable} collection of elements of type {@code T} to be analyzed.
     *                   This data source will be iterated over when collecting repeated values.
     * @throws NullPointerException if {@code dataSource} is {@code null}
     */
    public GenericRepeatedValueCollector(Iterable<T> dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * Collects the number of occurrences of each unique value in the data source and updates the provided map.
     *
     * <p>
     * For each element in the data source, this method increments the count in the {@code tagValueOccurrences}
     * map. If a value is encountered for the first time, it is added to the map with a count of 1.
     * </p>
     *
     * <p>
     * <b>Note:</b> The provided {@code tagValueOccurrences} map will be modified in-place. Existing counts
     * for values will be incremented if those values are present in the data source.
     * </p>
     *
     * @param tagValueOccurrences a {@link Map} where keys are values from the data source and values are their
     *                            respective occurrence counts. This map will be updated with the results.
     * @throws NullPointerException if {@code tagValueOccurrences} is {@code null}
     */
    @Override
    public void collectRepeatedValues(Map<Object, Integer> tagValueOccurrences) {
        for (T item : dataSource) {
            tagValueOccurrences.put(item, tagValueOccurrences.getOrDefault(item, 0) + 1);
        }
    }
}
