/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.Map;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
/**
 * The {@code RepeatedValueCollector} interface defines a contract for collecting and processing
 * repeated values within a dataset, typically represented as a mapping of tag values to their
 * occurrence counts. Implementations of this interface are responsible for handling the logic
 * required to identify, aggregate, or otherwise process values that appear multiple times in a
 * given context, such as during data parsing, validation, or transformation operations.
 * <p>
 * This interface is intended to be used in scenarios where it is necessary to analyze the frequency
 * of specific values (tags) within a collection, such as detecting duplicates, generating reports
 * on repeated entries, or enforcing business rules related to value repetition.
 * </p>
 * <p>
 * <b>Usage Example:</b>
 * <pre>
 * {@code
 * Map<Object, Integer> tagOccurrences = ...; // Populate with tag values and their counts
 * RepeatedValueCollector collector = new MyRepeatedValueCollectorImpl();
 * collector.collectRepeatedValues(tagOccurrences);
 * }
 * </pre>
 * </p>
 * <p>
 * <b>Thread Safety:</b> Implementations should document their thread-safety guarantees if used in
 * concurrent environments.
 * </p>
 *
 * @see java.util.Map
 */
public interface RepeatedValueCollector {
    /**
	 * Collects and processes repeated values from the provided mapping of tag values to their
	 * occurrence counts.
	 * <p>
	 * The input parameter {@code tagValueOccurrences} is a {@link java.util.Map} where each key
	 * represents a tag value (of type {@code Object}), and the corresponding value is an {@code Integer}
	 * indicating the number of times that tag value appears in the dataset.
	 * </p>
	 * <p>
	 * Implementations may use this method to perform actions such as:
	 * <ul>
	 *   <li>Identifying and storing values that occur more than once</li>
	 *   <li>Logging or reporting repeated values for auditing purposes</li>
	 *   <li>Triggering validation errors or warnings based on repetition rules</li>
	 *   <li>Aggregating statistics about value frequency</li>
	 * </ul>
	 * </p>
	 * <p>
	 * <b>Note:</b> The method does not specify how repeated values should be handled; this is left to
	 * the implementing class.
	 * </p>
	 *
	 * @param tagValueOccurrences a {@link java.util.Map} mapping tag values to their occurrence counts;
	 *                           must not be {@code null}. The map may contain any type of object as a key,
	 *                           depending on the application's domain model.
	 */
    void collectRepeatedValues(Map<Object, Integer> tagValueOccurrences);
}
