/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.io.ByteArrayInputStream;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since Jun 23, 2025
 */
/**
 * The {@code ByteArrayReader} class provides utility methods to sequentially read primitive data types and strings
 * from a byte array. It wraps a {@link ByteArrayInputStream} to facilitate reading operations and maintains
 * the current position within the byte array. This class is particularly useful for decoding binary protocols,
 * deserializing data, or reading structured byte streams.
 * <p>
 * Logging is performed for each read operation using {@link WizardLogger} and localized messages from {@link Lexicon}.
 * </p>
 *
 * <p>
 * <b>Usage Example:</b>
 * <pre>
 *     byte[] data = ...;
 *     ByteArrayReader reader = new ByteArrayReader(data);
 *     int value = reader.readInt();
 *     String str = reader.readString();
 *     // etc.
 * </pre>
 * </p>
 *
 * <p>
 * <b>Dependencies:</b>
 * <ul>
 *   <li>{@link ByteArrayInputStream} - for reading bytes from the array.</li>
 *   <li>{@link ByteBuffer} - for converting byte arrays to primitive types.</li>
 *   <li>{@link StandardCharsets#UTF_8} - for decoding strings.</li>
 *   <li>{@link WizardLogger} - for logging read operations.</li>
 *   <li>{@link Lexicon} - for localized log messages.</li>
 * </ul>
 * </p>
 */
public class ByteArrayReader {
    private final ByteArrayInputStream inputStream;
    private static final Lexicon lex = Lexicon.make(ByteArrayReader.class);
    private byte[] data;

    /**
     * Constructs a {@code ByteArrayReader} for the specified byte array.
     *
     * @param byteArray the source byte array to read from; must not be {@code null}
     */
    public ByteArrayReader(byte[] byteArray) {
    	this.data = byteArray;
        this.inputStream = new ByteArrayInputStream(byteArray);
    }

    /**
     * Reads a single byte from the current position in the byte array.
     * <p>
     * The value is logged using {@link WizardLogger}. If the end of the stream is reached, -1 is returned.
     * </p>
     *
     * @return the next byte as an integer in the range 0 to 255, or -1 if the end of the stream is reached
     */
    public int readByte() {
        int value = inputStream.read();
        WizardLogger.logFiner(lex.get("honWizardJob.byteReader.readByte"), value);
        return value;
    }
    
    /**
     * Reads a specified number of bytes from the current position in the byte array.
     * <p>
     * If fewer bytes than requested are available, the returned array will be zero-padded.
     * </p>
     *
     * @param length the number of bytes to read
     * @return a byte array containing the bytes read
     */
    public byte[] readBytes(int length) {
    	byte[] byteArrayData = new byte[length];
    	inputStream.read(byteArrayData, 0, length);
        return byteArrayData;
    }

    /**
     * Reads a UTF-8 encoded string from the byte array.
     * <p>
     * The method first reads a 4-byte integer indicating the length of the string in bytes,
     * then reads the specified number of bytes and decodes them as a UTF-8 string.
     * </p>
     * <p>
     * If the complete string cannot be read, an {@link IllegalStateException} is thrown.
     * </p>
     *
     * @return the decoded string
     * @throws IllegalStateException if the string cannot be fully read
     */
    public String readString() {
        int length = readInt(); // Read length of the string
        byte[] bytes = new byte[length];
        int bytesRead = inputStream.read(bytes, 0, length);
        if (bytesRead != length) {
            throw new IllegalStateException("Failed to read the complete string");
        }
        String value = new String(bytes, StandardCharsets.UTF_8);
        WizardLogger.logFiner(lex.get("honWizardJob.byteReader.readString"), value);
        return value;
    }

    /**
     * Reads a 4-byte integer from the current position in the byte array.
     * <p>
     * The bytes are interpreted in big-endian order.
     * </p>
     * <p>
     * If the complete integer cannot be read, an {@link IllegalStateException} is thrown.
     * </p>
     *
     * @return the integer value read
     * @throws IllegalStateException if the integer cannot be fully read
     */
    public int readInt() {
        byte[] bytes = new byte[4];
        int bytesRead = inputStream.read(bytes, 0, 4);
        if (bytesRead != 4) {
            throw new IllegalStateException("Failed to read the complete integer");
        }
        int value = ByteBuffer.wrap(bytes).getInt();
        WizardLogger.logFiner(lex.get("honWizardJob.byteReader.readInt"), value);
        return value;
    }

    /**
     * Reads an 8-byte double-precision floating point value from the current position in the byte array.
     * <p>
     * The bytes are interpreted in big-endian order.
     * </p>
     * <p>
     * If the complete double cannot be read, an {@link IllegalStateException} is thrown.
     * </p>
     *
     * @return the double value read
     * @throws IllegalStateException if the double cannot be fully read
     */
    public double readDouble() {
        byte[] bytes = new byte[8];
        int bytesRead = inputStream.read(bytes, 0, 8);
        if (bytesRead != 8) {
            throw new IllegalStateException("Failed to read the complete double");
        }
        double value = ByteBuffer.wrap(bytes).getDouble();
        WizardLogger.logFiner(lex.get("honWizardJob.byteReader.readDouble"), value);
        return value;
    }

    /**
     * Reads a boolean value from the current position in the byte array.
     * <p>
     * The value is interpreted as {@code true} if the byte is 1, and {@code false} otherwise.
     * </p>
     * <p>
     * If the end of the stream is reached, an {@link IllegalStateException} is thrown.
     * </p>
     *
     * @return the boolean value read
     * @throws IllegalStateException if the boolean value cannot be read
     */
    public boolean readBoolean() {
        int value = inputStream.read();
        if (value == -1) {
            throw new IllegalStateException("Failed to read the boolean value");
        }
        boolean value1 = value == 1;
        WizardLogger.logFiner(lex.get("honWizardJob.byteReader.readBoolean"), value1);
        return value1;
    }
    
    /**
     * Returns the current read position within the byte array.
     *
     * @return the number of bytes read from the start of the array
     */
    public int currentPos() {
    	return data.length - inputStream.available();
    }
    
    /**
     * Returns the original source byte array.
     *
     * @return the byte array provided at construction
     */
    public byte[] getSourceByteArray() {
		return this.data;
	}

    /**
     * Checks if there are more bytes available to read from the byte array.
     *
     * @return {@code true} if more bytes are available, {@code false} otherwise
     */
    public boolean hasNext() {
        return inputStream.available() > 0;
    }
}