/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
/**
 * The {@code VersionedStructure} class manages mappings between version numbers and their corresponding
 * {@link TagMapping} instances. This allows for version-specific handling of tag mappings, enabling
 * backward and forward compatibility when dealing with data structures that may evolve over time.
 * <p>
 * Typical use cases include:
 * <ul>
 *   <li>Retrieving the appropriate {@code TagMapping} for a given version of a data structure.</li>
 *   <li>Supporting multiple versions of encoded or decoded data formats within the same application.</li>
 *   <li>Centralizing version-to-mapping logic to simplify maintenance and extension as new versions are introduced.</li>
 * </ul>
 * <p>
 * The class is designed to be extensible: new versions and their mappings can be added by updating
 * the {@link #initializeVersionMappings()} method. If a requested version does not exist in the mapping,
 * an {@link IllegalArgumentException} is thrown to signal unsupported versions.
 * <p>
 * <b>Dependencies:</b>
 * <ul>
 *   <li>{@link TagMapping} - Represents the mapping logic or structure for a specific version.</li>
 * </ul>
 *
 * <pre>
 * Example usage:
 * VersionedStructure vs = new VersionedStructure();
 * TagMapping mapping = vs.getMappingForVersion(1);
 * </pre>
 *
 * @see TagMapping
 */
public class VersionedStructure {
    private final Map<Integer, TagMapping> versionMappings = new HashMap<>();

    public VersionedStructure() {
        initializeVersionMappings();
    }

    /**
     * Initializes the internal mapping between version numbers and their corresponding {@link TagMapping} instances.
     * <p>
     * This method is called during construction to set up the initial version-to-mapping relationships.
     * By default, it adds the mapping for version 1. To support additional versions, extend this method
     * by creating and registering new {@code TagMapping} instances for each version.
     * </p>
     */
    private void initializeVersionMappings() {
        // Version 1 mapping
        TagMapping version1Mapping = new TagMapping();
        versionMappings.put(1, version1Mapping);
    }

    /**
     * Retrieves the {@link TagMapping} associated with the specified version number.
     * <p>
     * This method allows clients to obtain the mapping logic or structure relevant to a particular version
     * of the data format. If the requested version is not supported (i.e., not present in the internal mapping),
     * an {@link IllegalArgumentException} is thrown.
     * </p>
     *
     * @param version the version number for which the {@code TagMapping} is requested
     * @return the {@code TagMapping} instance corresponding to the specified version
     * @throws IllegalArgumentException if the version is not supported or not present in the mapping
     */
    public TagMapping getMappingForVersion(int version) {
        if (!versionMappings.containsKey(version)) {
            throw new IllegalArgumentException("Unsupported version: " + version);
        }
        return versionMappings.get(version);
    }
}