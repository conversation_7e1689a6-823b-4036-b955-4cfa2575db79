/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.sys.BString;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON>undhar Madhusudhan\
 * @since Jul 2, 2025
 */
public class PageOrderHandler extends DataHandler {
	
    @Override
    public Map<Object, Integer> collectRepeatedValues(Object data) {
        Map<Object, Integer> tagValueOccurrences = new HashMap<>();
        if(data instanceof BHonWizardGlobalStore) {
        	RepeatedValueCollector collector = new GenericRepeatedValueCollector<>(((BHonWizardGlobalStore) data).getPageOrderMap().keySet());
        	collector.collectRepeatedValues(tagValueOccurrences);
        }

        return tagValueOccurrences;
    }

    @Override
	public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof BHonWizardGlobalStore) {
			BHonWizardGlobalStore globalStore = (BHonWizardGlobalStore) data;
			WizardLogger.logInfo(lex.get("honWizardJob.encode.pageOrder.start"));

			Map<String, Integer> pageOrderMap = globalStore.getPageOrderMap();
			// Add the size of the pageOrderMap
			byteArrayBuilder.addInt(pageOrderMap.size()); 
			List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(pageOrderMap.entrySet());
			sortedEntries.sort(Map.Entry.comparingByValue());

			for (Map.Entry<String, Integer> entry : sortedEntries) {
				String pageName = entry.getKey();
				Integer tagId = entry.getValue();

				// Check if the value exists in repeatedTagMappings
				Integer repeatedValueId = tagMapping.getRepeatedValueId(pageName);
				if (repeatedValueId != null) {
					// Type indicator for repeated value reference
					byteArrayBuilder.addByte(TYPE_REPEATED_VALUE_REFERENCE);
					byteArrayBuilder.addInt(repeatedValueId);
					WizardLogger.logFiner(lex.get("honWizardJob.encode.pageOrder.repeatedValue"), pageName, repeatedValueId);
				} else {
					encodeValue(byteArrayBuilder, BString.make(pageName));
					WizardLogger.logFiner(lex.get("honWizardJob.encode.pageOrder.fullValue"), pageName, tagId);
				}
			}

			WizardLogger.logInfo(lex.get("honWizardJob.encode.pageOrder.completed"));
		}
	}

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
        WizardLogger.logInfo(lex.get("honWizardJob.decode.pageOrder.start"));
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        
        // Read the size of the pageOrderMap
        int pageOrderCount = byteArrayReader.readInt(); 
        globalStore.clearAllPageOrders();
        for (int i = 0; i < pageOrderCount; i++) {
            int typeIndicator = byteArrayReader.readByte(); // Decode the type indicator

            BString pageName;
            if (typeIndicator == TYPE_REPEATED_VALUE_REFERENCE) {	
                // Decode repeated value reference
                int repeatedValueId = byteArrayReader.readInt();
                pageName = BString.make((String) repeatedTagMappings.get(repeatedValueId));
                WizardLogger.logFiner(lex.get("honWizardJob.decode.pageOrder.repeatedValue"), i, repeatedValueId, pageName);
            } else {
                // Decode full value
                pageName = (BString) decodeValue(byteArrayReader, typeIndicator);
                WizardLogger.logFiner(lex.get("honWizardJob.decode.pageOrder.fullValue"), i, pageName);
            }

            // Add the PageOrder tag to the GlobalStore
            if(null != pageName) {
				globalStore.addPageOrderForNewPage(pageName.getString());
            }
        }

        WizardLogger.logInfo(lex.get("honWizardJob.decode.pageOrder.completed"));
    }
}