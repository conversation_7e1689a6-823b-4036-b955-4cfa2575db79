/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
/**
 * Represents a functional interface for extracting a value from a given object.
 * <p>
 * This interface is intended to be used as a strategy for retrieving or computing
 * a value from an instance of type {@code T}. It is commonly used in scenarios
 * where you need to decouple the logic of value extraction from the processing logic,
 * such as in data transformation, filtering, or mapping operations.
 * </p>
 *
 * <p>
 * As a {@link FunctionalInterface}, {@code ValueExtractor} can be implemented using
 * lambda expressions or method references, making it suitable for use with Java Streams
 * and other functional programming constructs.
 * </p>
 *
 * <p>
 * <b>Example usage:</b>
 * <pre>{@code
 * // Extract the length of a string
 * ValueExtractor<String> lengthExtractor = str -> str.length();
 * int length = (int) lengthExtractor.extractValue("Hello");
 *
 * // Extract a property from a custom object
 * ValueExtractor<Person> nameExtractor = person -> person.getName();
 * String name = (String) nameExtractor.extractValue(personInstance);
 * }</pre>
 * </p>
 *
 * @param <T> the type of the input object from which the value is to be extracted
 *
 * @see java.util.function.Function
 */
 
@FunctionalInterface
public interface ValueExtractor<T> {
	/**
	 * Extracts a value from the specified item.
	 * <p>
	 * Implementations should define the logic for retrieving or computing a value
	 * from the provided {@code item} of type {@code T}. The returned value can be of any type,
	 * and it is the responsibility of the caller to cast or process the result as needed.
	 * </p>
	 *
	 * <p>
	 * This method is typically used in data processing pipelines, such as when mapping
	 * objects to their properties, extracting fields for display, or transforming data
	 * for serialization or storage.
	 * </p>
	 *
	 * @param item the input object from which the value is to be extracted; may be {@code null}
	 *             depending on the implementation
	 * @return the extracted value, which may be {@code null} if the extraction logic allows
	 */
	Object extractValue(T item);
}
