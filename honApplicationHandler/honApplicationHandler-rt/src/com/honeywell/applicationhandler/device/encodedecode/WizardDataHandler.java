/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.sys.BComponent;
import javax.baja.sys.Sys;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardMetaData;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
/**
 * Handles encoding and decoding of wizard data for Honeywell configurable devices.
 * <p>
 * The {@code WizardDataHandler} class is responsible for serializing and deserializing
 * wizard configuration data, including selectors, metadata, page order, and rules, to and from
 * a binary format. This enables persistent storage and retrieval of device configuration
 * in a versioned and extensible manner.
 * </p>
 *
 * <h2>Encoding</h2>
 * <ul>
 *   <li>Encodes selectors, metadata, page order, repeated tag mappings, and rules into a byte array.</li>
 *   <li>Supports versioning for backward and forward compatibility.</li>
 *   <li>Persists the encoded data to a device-specific file under the station's user configuration directory.</li>
 * </ul>
 *
 * <h2>Decoding</h2>
 * <ul>
 *   <li>Reads the versioned byte array and reconstructs the original wizard data structures.</li>
 *   <li>Populates device configuration with decoded selectors, metadata, page order, and rules.</li>
 * </ul>
 *
 * <h2>Dependencies</h2>
 * <ul>
 *   <li>{@link BIHoneywellConfigurableDevice} - The device whose configuration is being handled.</li>
 *   <li>{@link BHonWizSelector}, {@link BHonWizardGlobalStore}, {@link BHonWizardMetaData} - Data structures representing wizard configuration.</li>
 *   <li>{@link VersionedStructure}, {@link TagMapping} - Handle version-specific encoding/decoding logic.</li>
 *   <li>Handler classes: {@code RepeatedValueHandler}, {@code PageOrderHandler}, {@code MetaDataHandler}, {@code PointTagsHandler}, {@code SlotTagsHandler}, {@code RulesDataHandler} - Modular encoding/decoding of different data sections.</li>
 *   <li>{@link ByteArrayBuilder}, {@link ByteArrayReader} - Utilities for byte-level serialization.</li>
 *   <li>{@link WizardLogger}, {@link Lexicon} - Logging and localization support.</li>
 * </ul>
 *
 * <h2>Thread Safety</h2>
 * <p>
 * This class is not thread-safe. Each instance should be used by a single thread or properly synchronized.
 * </p>
 *
 * <h2>Usage Example</h2>
 * <pre>
 * BIHoneywellConfigurableDevice device = ...;
 * WizardDataHandler handler = new WizardDataHandler(device);
 * EncodedResult result = handler.encodeData(selectors, globalStore, metaData);
 * handler.decodeData(result.getByteArray());
 * </pre>
 */
public class WizardDataHandler {
    private final VersionedStructure versionedStructure = new VersionedStructure();
    private static final Lexicon lex = Lexicon.make(WizardDataHandler.class);
    private BIHoneywellConfigurableDevice device;

    // Constants for versioning
    private static final int ENCODE_VERSION = 1;

    // Constants for file paths
    private static final String DEFAULT_OUTPUT_FILE_PATH = "wizard_data_encoded.bin";
    
    /**
     * Constructs a new WizardDataHandler for the specified configurable device.
     *
     * @param device The {@link BIHoneywellConfigurableDevice} instance whose wizard data will be encoded/decoded.
     */
    public WizardDataHandler(BIHoneywellConfigurableDevice device) {
		super();
		this.device = device;
	}

    /**
     * Encodes the given wizard configuration data into a versioned byte array.
     * <p>
     * This method serializes the provided selectors, global store, and metadata into a binary format,
     * including repeated tag mappings, page order, and rules. The encoded data is versioned for
     * compatibility and persisted to a device-specific file under the station's user configuration directory.
     * </p>
     *
     * @param selectors   List of {@link BHonWizSelector} objects representing point selectors to encode.
     * @param globalStore The {@link BHonWizardGlobalStore} containing page order and global wizard data.
     * @param metaData    The {@link BHonWizardMetaData} containing metadata to encode.
     * @return EncodedResult containing the encoded byte array, its length, and offset.
     * @see RepeatedValueHandler
     * @see PageOrderHandler
     * @see MetaDataHandler
     * @see PointTagsHandler
     * @see SlotTagsHandler
     * @see RulesDataHandler
     */
	public EncodedResult encodeData(List<BHonWizSelector> selectors, BHonWizardGlobalStore globalStore, BHonWizardMetaData metaData) {
		WizardLogger.logInfo(lex.get("honWizardJob.encode.start"));

		ByteArrayBuilder byteArrayBuilder = new ByteArrayBuilder();
		byteArrayBuilder.addByte(ENCODE_VERSION); // Add versioning

		TagMapping tagMapping = versionedStructure.getMappingForVersion(ENCODE_VERSION);
		EncodedResult result = null;

		try {
			Map<Object, Integer> repeatedTagMappings = new HashMap<>();
			RepeatedValueHandler repeatedValueHandler = new RepeatedValueHandler();
			PageOrderHandler pageOrderHandler = new PageOrderHandler();
			MetaDataHandler metaDataHandler = new MetaDataHandler();
			PointTagsHandler pointTagsHandler = new PointTagsHandler();
			SlotTagsHandler slotTagsHandler = new SlotTagsHandler();
			RulesDataHandler rulesDataHandler = new RulesDataHandler();

			repeatedTagMappings.putAll(pageOrderHandler.collectRepeatedValues(globalStore));
			repeatedTagMappings.putAll(pointTagsHandler.collectRepeatedValues(selectors));
			repeatedTagMappings.putAll(slotTagsHandler.collectRepeatedValues(globalStore));

			repeatedValueHandler.encode(byteArrayBuilder, repeatedTagMappings, tagMapping);
			pageOrderHandler.encode(byteArrayBuilder, globalStore, tagMapping);
			metaDataHandler.encode(byteArrayBuilder, metaData, tagMapping);
			pointTagsHandler.encode(byteArrayBuilder, selectors, tagMapping);
			slotTagsHandler.encode(byteArrayBuilder, globalStore, tagMapping);
			rulesDataHandler.encode(byteArrayBuilder, device.getRuleStore(), tagMapping);

			byte[] byteArray = byteArrayBuilder.toByteArray();
			int overallSize = byteArray.length;
			result = new EncodedResult(byteArray, byteArrayBuilder.getLength(), byteArrayBuilder.getOffset());
			// Log overall size
			WizardLogger.logFiner(lex.get("honWizardJob.encode.size"), overallSize);

			saveToFile(byteArray);
			WizardLogger.logInfo(lex.get("honWizardJob.encode.completed"));
		} catch (Exception e) {
			WizardLogger.logSevere(lex.getText("honWizardJob.encode.failed", e.getMessage(), e));
		}

		return result;
	}

	/**
	 * Decodes the provided byte array into wizard configuration data structures.
	 * <p>
	 * This method reads the versioned binary data and reconstructs the original selectors, metadata,
	 * page order, repeated tag mappings, and rules, populating the device configuration accordingly.
	 * </p>
	 *
	 * @param byteArray The encoded byte array to decode.
	 * @see RepeatedValueHandler
	 * @see PageOrderHandler
	 * @see MetaDataHandler
	 * @see PointTagsHandler
	 * @see SlotTagsHandler
	 * @see RulesDataHandler
	 */
    public void decodeData(byte[] byteArray) {
        WizardLogger.logInfo(lex.get("honWizardJob.decode.start"));
        ByteArrayReader byteArrayReader = new ByteArrayReader(byteArray);
        int version = byteArrayReader.readByte(); // Read versioning
        WizardLogger.logFiner(lex.get("honWizardJob.decode.version"), version);

        TagMapping tagMapping = versionedStructure.getMappingForVersion(version);

        Map<Integer, Object> repeatedTagMappings = new HashMap<>();
		try {
	        RepeatedValueHandler repeatedValueHandler = new RepeatedValueHandler();
	        PageOrderHandler pageOrderHandler = new PageOrderHandler();
	        MetaDataHandler metaDataHandler = new MetaDataHandler();
	        PointTagsHandler pointTagsHandler = new PointTagsHandler();
	        SlotTagsHandler slotTagsHandler = new SlotTagsHandler();
	        RulesDataHandler rulesDataHandler = new RulesDataHandler();
	        
	        repeatedValueHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);
	        pageOrderHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);
	        metaDataHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);
	        pointTagsHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);
	        slotTagsHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);
	        rulesDataHandler.decode(byteArrayReader, repeatedTagMappings, tagMapping, device);

	        WizardLogger.logInfo(lex.get("honWizardJob.decode.completed"));
		} catch (Exception e) {
			WizardLogger.logSevere(lex.getText("honWizardJob.decode.failed", e.getMessage(), e));
		}
    }

    /**
     * Saves the provided byte array to a device-specific file under the station's user configuration directory.
     * <p>
     * The file is named {@code wizard_data_encoded.bin} and is stored in a subdirectory corresponding to the device.
     * </p>
     *
     * @param byteArray The byte array to persist to file.
     * @throws RuntimeException if an I/O error occurs during file writing.
     */
    private void saveToFile(byte[] byteArray) {
    	File userConfigData = new File(Sys.getStationHome(), "userConfigData");
		userConfigData.mkdir();
		String path = BHonWizardRuleParserJob.getFolderPath((BComponent) device);
		File devicePath = new File(userConfigData, path);
		devicePath.mkdir();
		File userConfig = new File(devicePath, DEFAULT_OUTPUT_FILE_PATH);

		 WizardLogger.logInfo(lex.getText("honWizardJob.file.save.start", userConfig));
        try (FileOutputStream fos = new FileOutputStream(new File(userConfig.getAbsolutePath()))) {
        	fos.write(byteArray);
        	WizardLogger.logFiner(lex.get("honWizardJob.file.save"), userConfig, byteArray.length);
        } catch (IOException e) {
        	 WizardLogger.logSevere(lex.getText("honWizardJob.file.save.error", e.getMessage()));
             throw new RuntimeException(lex.get("honWizardJob.file.save.error"), e);
        }

        WizardLogger.logInfo(lex.getText("honWizardJob.file.save.completed", DEFAULT_OUTPUT_FILE_PATH));
    }
}