/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.sys.BComponent;
import javax.baja.sys.Property;

import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;
import com.honeywell.applicationhandler.utils.WizardLogger;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 2, 2025
 */
public class SlotTagsHandler extends PointTagsHandler {

	@Override
	public Map<Object, Integer> collectRepeatedValues(Object data) {
		Map<Object, Integer> tagValueOccurrences = new HashMap<>();
		if (data instanceof BHonWizardGlobalStore) {
			Map<String, List<BHonWizSelector>> slotSelectors = collectParentSlotPathAndSlotSelectors((BHonWizardGlobalStore) data);
			for (List<BHonWizSelector> slotSelector : slotSelectors.values()) {
				for (BHonWizSelector selector : slotSelector) {
					RepeatedValueCollector collector = new GenericRepeatedValueCollector<>(selector.getTags().values());
					collector.collectRepeatedValues(tagValueOccurrences);
				}
			}
		}
		return tagValueOccurrences;
	}

    @Override
	public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof BHonWizardGlobalStore) {
			WizardLogger.logInfo(lex.get("honWizardJob.encode.slotTagMappings.start"));
			Map<String, List<BHonWizSelector>> parentSlotPathAndslotSelectors = collectParentSlotPathAndSlotSelectors((BHonWizardGlobalStore) data);
			// Encode the number of slot selectors
			byteArrayBuilder.addInt(parentSlotPathAndslotSelectors.size());
			ByteArrayBuilder selectorBuilder = new ByteArrayBuilder();
			for (Map.Entry<String, List<BHonWizSelector>> parentSlotNameAndSelectorEntry : parentSlotPathAndslotSelectors.entrySet()) {
				List<BHonWizSelector> slotSelectors = parentSlotNameAndSelectorEntry.getValue();

				// Encode commonSlot
				String commonSlotPath = parentSlotNameAndSelectorEntry.getKey();
				selectorBuilder.addString(commonSlotPath);
				super.encode(selectorBuilder, slotSelectors, tagMapping);
				// update the
				selectorBuilder.addInt(slotSelectors.size());
				for (BHonWizSelector slotSelector : slotSelectors) {
					// Encode sourcePropertyName
					selectorBuilder.addString(((BHonWizSlotSelector) slotSelector).getSourcePropertyNameString());
				}
			}
			// Add the selector's tags
			byteArrayBuilder.addBytes(selectorBuilder.toByteArray(), 0, selectorBuilder.getLength());

			WizardLogger.logInfo(lex.get("honWizardJob.encode.slotTagMappings.completed"));
		}
	}

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
        WizardLogger.logInfo(lex.get("honWizardJob.decode.slotTagMappings.start"));

        // Decode the number of slot selectors
        int selectorCount = byteArrayReader.readInt();

        for (int i = 0; i < selectorCount; i++) {
            // Decode commonSlot
            String parentSlotPath = byteArrayReader.readString();
            String completeParentSlotPath = ((BComponent) device).getSlotPath().toString() + parentSlotPath;
            BComponent parentComponent = (BComponent) BOrd.make(completeParentSlotPath).resolve(((BComponent) device)).get();
            // Decode tags using decodeTagMappings
            List<BHonWizSelector> decodedSelectors = decodeSelectors(byteArrayReader, repeatedTagMappings, tagMapping, true);
            
            // Read number sourceProps encoded
            int numberOfSelectors = byteArrayReader.readInt();
            
            for(int j = 0; j < numberOfSelectors; j++) {
				String sourcePropertyName = byteArrayReader.readString();
	            // Create and add BHonWizSlotSelector
	            BHonWizSlotSelector slotSelector = (BHonWizSlotSelector) decodedSelectors.get(j);
				BIDataValue widgetTypeTagValue = slotSelector.getTagValue(Const.WIDGET_TYPE_TAG);
	            parentComponent.add(Constants.HON_WIZ_SLOT_SELECTOR, slotSelector, DECODE_METADATA);
				HoneywellWidgetTagUtil.setSourcePropertyName(widgetTypeTagValue.toString(), slotSelector, parentComponent, sourcePropertyName);
	            WizardLogger.logFiner(lex.get("honWizardJob.decode.slotTagMappings.slotSelector"), parentSlotPath, slotSelector.getTags().size());
			}
        }

        WizardLogger.logInfo(lex.get("honWizardJob.decode.slotTagMappings.completed"));
    }

	private Map<String, List<BHonWizSelector>> collectParentSlotPathAndSlotSelectors(BHonWizardGlobalStore globalStore) {
		Map<String, List<BHonWizSelector>> parentSlotPathAndslotSelectors = new HashMap<>();

		BComponent device = (BComponent) globalStore.getParent();
		collectParentSlotPathAndSlotSelectorFromGlobalStoreComp(globalStore, parentSlotPathAndslotSelectors, device);

		return parentSlotPathAndslotSelectors;
	}

	private void collectParentSlotPathAndSlotSelectorFromGlobalStoreComp(BComponent gblStrComp,
			Map<String, List<BHonWizSelector>> parentSlotPathAndslotSelectors, BComponent device) {
		for (Property gblStoreProp : gblStrComp.getDynamicPropertiesArray()) {
			if (gblStrComp.get(gblStoreProp) instanceof BDynamicWidgetComponentBase) {
				resolveAndGetParentSlotPathAndSlotSelector(gblStrComp, parentSlotPathAndslotSelectors, gblStoreProp, device);
			} else if (gblStrComp.get(gblStoreProp) instanceof BComponent) {
				collectParentSlotPathAndSlotSelectorFromGlobalStoreComp((BComponent) gblStrComp.get(gblStoreProp), parentSlotPathAndslotSelectors,
						device);
			}
		}
	}

	private void resolveAndGetParentSlotPathAndSlotSelector(BComponent gblStrComp, Map<String, List<BHonWizSelector>> parentSlotPathAndslotSelectors,
			Property gblStoreProp, BComponent device) {
		BDynamicWidgetComponentBase widgetComponent = (BDynamicWidgetComponentBase) gblStrComp.get(gblStoreProp);
		BOrd commonSlot = widgetComponent.getCommonSlot();

		if (!commonSlot.equals(BOrd.DEFAULT) && !commonSlot.equals(BOrd.NULL)) {
			String commonSlotPath = commonSlot.encodeToString();
			String parentSlotPath = commonSlotPath.substring(0, commonSlotPath.lastIndexOf('/'));
			BComponent parentComponent = (BComponent) BOrd.make(parentSlotPath).resolve(device).get();

			BHonWizSlotSelector[] children = parentComponent.getChildren(BHonWizSlotSelector.class);
			for (BHonWizSlotSelector slotSelector : children) {
				if (commonSlotPath.endsWith(slotSelector.getSourcePropertyNameString())) {
					// Store only relative slotpath as the device slotpath is
					// already known. This will help save some space
					String relParentSlotPath = parentSlotPath.replace(device.getSlotPath().toString(), "");
					List<BHonWizSelector> slotSelectors;
					if (null == parentSlotPathAndslotSelectors.get(relParentSlotPath)) {
						slotSelectors = new ArrayList<>();
					} else {
						slotSelectors = parentSlotPathAndslotSelectors.get(relParentSlotPath);
					}
					slotSelectors.add(slotSelector);
					parentSlotPathAndslotSelectors.put(relParentSlotPath, slotSelectors);
				}
			}
		}
	}

}