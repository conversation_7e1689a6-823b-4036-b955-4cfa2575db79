/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

/**
 * EncodedResult encapsulates the result of encoding or inserting data.
 */
/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jun 23, 2025
 */
public class EncodedResult {
    private final byte[] byteArray;
    private final int length;
    private final int offset;

    public EncodedResult(byte[] byteArray, int length, int offset) {
        this.byteArray = byteArray;
        this.length = length;
        this.offset = offset;
    }

    public byte[] getByteArray() {
        return byteArray;
    }

    public int getLength() {
        return length;
    }

    public int getOffset() {
        return offset;
    }
}