/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.HashMap;
import java.util.Map;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - Shyams<PERSON>har Madhusudhan
 * @since Jul 2, 2025
 */
public class RepeatedValueHandler extends DataHandler {
	
    @Override
    public Map<Object, Integer> collectRepeatedValues(Object data) {
    	//Default Implementation when no value to collected
        return new HashMap<>();
    }

    @Override
    public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof Map<?, ?>) {
			WizardLogger.logInfo(lex.get("honWizardJob.encode.repeatedTagMappings.start"));
			@SuppressWarnings("unchecked")
			Map<Object, Integer> tagValueOccurrences = (Map<Object, Integer>) data;

			int repeatedValueIdCounter = 1;
			for (Map.Entry<Object, Integer> entry : tagValueOccurrences.entrySet()) {
				Object tagValue = entry.getKey();
				int occurrences = entry.getValue();

				if (occurrences > 1) { // Only add values that occur at least
										// twice
					Integer repeatedValueId = tagMapping.getRepeatedValueMappings().entrySet().stream()
							.filter(e -> e.getKey().toString().equals(tagValue.toString())).map(Map.Entry::getValue).findFirst().orElse(null);

					if (repeatedValueId == null) {
						repeatedValueId = repeatedValueIdCounter++;
						tagMapping.addRepeatedValueMapping(tagValue, repeatedValueId);
					}
				}
			}

			// Save repeatedTagMappings to the file
			byteArrayBuilder.addInt(tagMapping.getRepeatedValueMappings().size());
			for (Map.Entry<Object, Integer> entry : tagMapping.getRepeatedValueMappings().entrySet()) {
				byteArrayBuilder.addInt(entry.getValue());
				encodeValue(byteArrayBuilder, entry.getKey());
				WizardLogger.logFiner(lex.get("honWizardJob.encode.repeatedTagMappings"), entry.getValue(), entry.getKey());
			}

			WizardLogger.logInfo(lex.get("honWizardJob.encode.repeatedTagMappings.completed"));
		}
    }

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
        WizardLogger.logInfo(lex.get("honWizardJob.decode.repeatedTagMappings.start"));

        // Decode the repeatedTagMappings map
        int mapSize = byteArrayReader.readInt();
        for (int i = 0; i < mapSize; i++) {
            int repeatedValueId = byteArrayReader.readInt();
            Object value = decodeValue(byteArrayReader, byteArrayReader.readByte());
            repeatedTagMappings.put(repeatedValueId, value);
            WizardLogger.logFiner(lex.get("honWizardJob.decode.tagMappings.repeatedValue"), repeatedValueId, value);
        }
        WizardLogger.logInfo(lex.get("honWizardJob.decode.repeatedTagMappings.completed"));
    }

}