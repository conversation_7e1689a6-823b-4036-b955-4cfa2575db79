/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.HashMap;
import java.util.Map;

import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - Shya<PERSON>und<PERSON> Madhusudhan
 * @since Jun 23, 2025
 */
/**
 * The {@code TagMapping} class provides a bidirectional mapping between tag names and unique integer IDs,
 * as well as support for repeated value mappings used in encoding and decoding operations within the application.
 * <p>
 * This class is primarily responsible for:
 * <ul>
 *   <li>Mapping tag names (typically defined in {@link BHonWizardTag}) to unique integer identifiers for efficient lookup and serialization.</li>
 *   <li>Providing reverse lookup from tag IDs to tag names.</li>
 *   <li>Managing mappings for repeated values, which are used to optimize encoding/decoding by assigning unique IDs to frequently occurring values.</li>
 *   <li>Supporting logging and traceability via the {@link WizardLogger} and {@link Lexicon} classes.</li>
 * </ul>
 * <p>
 * The tag mappings are initialized using constants from the {@link BHonWizardTag} class, ensuring consistency across the application.
 * Repeated value mappings are intended for use cases where certain values occur multiple times and can be referenced by ID to reduce redundancy.
 * <p>
 * Typical use cases include:
 * <ul>
 *   <li>Encoding tag-based data structures for transmission or storage.</li>
 *   <li>Decoding received data by resolving tag IDs back to their string representations.</li>
 *   <li>Optimizing payload size by referencing repeated values via integer IDs.</li>
 * </ul>
 * <p>
 * Thread Safety: This class is not thread-safe. If multiple threads access an instance concurrently, external synchronization is required.
 *
 * @see BHonWizardTag
 * @see WizardLogger
 * @see Lexicon
 */
public class TagMapping {
    private final Map<String, Integer> tagToIdMap = new HashMap<>();
    private final Map<Integer, String> idToTagMap = new HashMap<>();
    private final Map<Object, Integer> repeatedValueMappings = new HashMap<>();
    private final Map<Integer, Object> reverseRepeatedValueMappings = new HashMap<>();
    private static final Lexicon lex = Lexicon.make(TagMapping.class);

    public TagMapping() {
        initializeTagMappings();
    }

    /**
     * Initializes the tag-to-ID and ID-to-tag mappings using tags from BHonWizardTag.
     */
    private void initializeTagMappings() {
        int id = 1;

        // Add all tags from BHonWizardTag
        addMapping(BHonWizardTag.HON_WIZ_PAGE_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_GRP_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_NAME_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_WDTTYPE_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_STEP_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_HELP_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_ROLE_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_BELONG_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_COLOR_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_TAB_IN_PAGE_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_DEF_VAL_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_READ_ONLY_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_MIN_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_MAX_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_DEADBAND_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_ORDER_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_OPTIONS_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG, id++);
        addMapping(BHonWizardTag.HON_WIZ_PAGE_ORDER, id++);
    }

    /**
     * Adds a tag-to-ID mapping and its reverse mapping.
     * @param tagName The tag name.
     * @param id The unique ID for the tag.
     */
    private void addMapping(String tagName, int id) {
        tagToIdMap.put(tagName, id);
        idToTagMap.put(id, tagName);
    }

    /**
     * Adds a repeated value mapping.
     * @param value The repeated value.
     * @param id The unique ID for the value.
     */
    public void addRepeatedValueMapping(Object value, int id) {
        WizardLogger.logFiner(lex.get("honWizardJob.tagMapping.add"), value, id);
        repeatedValueMappings.put(value, id);
        reverseRepeatedValueMappings.put(id, value);
    }

    /**
     * Gets the ID for a given tag name.
     * @param tagName The tag name.
     * @return The ID for the tag, or null if not found.
     */
    public Integer getTagId(String tagName) {
        return tagToIdMap.get(tagName);
    }

    /**
     * Gets the tag name for a given ID.
     * @param id The ID.
     * @return The tag name, or null if not found.
     */
    public String getTagName(int id) {
        return idToTagMap.get(id);
    }

    /**
     * Gets the ID for a repeated value.
     * @param value The repeated value.
     * @return The ID for the value, or null if not found.
     */
    public Integer getRepeatedValueId(Object value) {
        return repeatedValueMappings.get(value);
    }

    /**
     * Gets the repeated value for a given ID.
     * @param id The ID.
     * @return The repeated value, or null if not found.
     */
    public Object getRepeatedValue(int id) {
        return reverseRepeatedValueMappings.get(id);
    }

    /**
     * Retrieves all repeated value mappings for encoding or decoding.
     * @return Map of repeated value mappings.
     */
    public Map<Object, Integer> getRepeatedValueMappings() {
        return repeatedValueMappings;
    }

    /**
     * Retrieves all tag mappings for decoding.
     * @return Map of tag ID to tag name mappings.
     */
    public Map<Integer, String> getIdToTagMap() {
        return idToTagMap;
    }
}