/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.io.IOException;
import java.util.Map;

import javax.baja.naming.SlotPath;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BDouble;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.BNumber;
import javax.baja.sys.BString;
import javax.baja.sys.BasicContext;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.generate.TagGenerator;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jul 2, 2025
 */
/**
 * Abstract base class for handling encoding and decoding of various data types
 * used in the Honeywell Application Handler system. This class provides utility
 * methods for serializing and deserializing supported data types to and from
 * binary streams, as well as abstract methods for collecting repeated values
 * and performing encoding/decoding at a higher level.
 * <p>
 * Supported data types include:
 * <ul>
 *   <li>{@link BString} - Encoded as a string with a type indicator.</li>
 *   <li>{@link BNumber} (specifically {@link BDouble}) - Encoded as a double with a type indicator.</li>
 *   <li>{@link BBoolean} - Encoded as a boolean with a type indicator.</li>
 *   <li>{@link BEnumRange} - Encoded as a string representation of the enum range.</li>
 *   <li>{@link BFrozenEnum} - Encoded with class name and ordinal value for precise reconstruction.</li>
 *   <li>{@link BDynamicEnum} - Encoded with ordinal and optional range information.</li>
 * </ul>
 * <p>
 * The class uses {@link ByteArrayBuilder} for writing binary data and
 * {@link ByteArrayReader} for reading binary data. Logging is performed via
 * {@link WizardLogger} and localized messages are retrieved using {@link Lexicon}.
 * <p>
 * Subclasses must implement the abstract methods to provide specific logic for
 * collecting repeated values and handling encoding/decoding of complex data
 * structures.
 *
 * <h2>Encoding/Decoding Protocol</h2>
 * Each value is encoded with a type indicator byte, followed by the value's
 * binary representation. For enums, additional metadata (such as class name,
 * ordinal, and range) is included to support accurate reconstruction.
 *
 * <h2>Thread Safety</h2>
 * This class is not inherently thread-safe. Synchronization must be handled by
 * subclasses or calling code if used in a concurrent context.
 *
 * <h2>Dependencies</h2>
 * <ul>
 *   <li>{@link ByteArrayBuilder}, {@link ByteArrayReader} - For binary I/O.</li>
 *   <li>{@link TagMapping}, {@link TagGenerator} - For tag and enum range handling.</li>
 *   <li>{@link BIHoneywellConfigurableDevice} - Device context for decoding.</li>
 *   <li>{@link WizardLogger}, {@link Lexicon} - Logging and localization.</li>
 * </ul>
 *
 * @see ByteArrayBuilder
 * @see ByteArrayReader
 * @see TagMapping
 * @see BString
 * @see BNumber
 * @see BBoolean
 * @see BEnumRange
 * @see BFrozenEnum
 * @see BDynamicEnum
 */

public abstract class DataHandler {
	
	protected static final Lexicon lex = Lexicon.make(WizardDataHandler.class);
	
	// Constants for data type indicators in decodeValue and encodeValue
	protected static final int TYPE_BSTRING = 0;
	protected static final int TYPE_BNUMBER = 1;
	protected static final int TYPE_BBOOLEAN = 2;
	protected static final int TYPE_BENUM_RANGE = 3;
	protected static final int TYPE_REPEATED_VALUE_REFERENCE = 4;
	protected static final int TYPE_BFROZEN_ENUM = 5;
	protected static final int TYPE_BDYNAMIC_ENUM = 6;

	// Constants for encoding flags
	protected static final int HAS_RANGE_FLAG = 1;
	protected static final int NO_RANGE_FLAG = 0;
	
	public static final BasicContext DECODE_METADATA = new BasicContext();
	
	/**
	 * Collects repeated values from the provided data structure.
	 * 
	 * This method is intended to identify and map values that are repeated
	 * within the data structure, which can be used for efficient encoding by
	 * referencing repeated values instead of duplicating them.
	 *
	 * @param data
	 *            The data structure to analyze for repeated values.
	 * @return A map where the key is the repeated value and the value is an
	 *         integer reference or count, depending on the encoding strategy.
	 */
	abstract Map<Object, Integer> collectRepeatedValues(Object data);

	/**
	 * Encodes the provided data structure into a binary format using the
	 * specified {@link ByteArrayBuilder} and {@link TagMapping}.
	 * 
	 * Subclasses should implement this method to traverse the data structure
	 * and serialize its contents, using
	 * {@link #encodeValue(ByteArrayBuilder, Object)} for individual values.
	 *
	 * @param byteArrayBuilder
	 *            The builder to which encoded bytes are written.
	 * @param data
	 *            The data structure to encode.
	 * @param tagMapping
	 *            The mapping of tags used for encoding context.
	 */
	abstract void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping);

	/**
	 * Decodes a data structure from the provided {@link ByteArrayReader}, using
	 * repeated tag mappings and device context.
	 * 
	 * Subclasses should implement this method to reconstruct the data structure
	 * from the binary stream, using {@link #decodeValue(ByteArrayReader, int)}
	 * for individual values.
	 *
	 * @param byteArrayReader
	 *            The reader from which bytes are read.
	 * @param repeatedTagMappings
	 *            Map of repeated value references for decoding.
	 * @param tagMapping
	 *            The mapping of tags used for decoding context.
	 * @param device
	 *            The device context for decoding.
	 */
	abstract void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping,
			BIHoneywellConfigurableDevice device);

	/**
	 * Encodes a single value into the binary stream, using a type indicator and
	 * the appropriate binary representation for the value's type.
	 * 
	 * Supported types are: {@link BString}, {@link BNumber}, {@link BBoolean},
	 * {@link BEnumRange}, {@link BFrozenEnum}, and {@link BDynamicEnum}.
	 * 
	 * Throws {@link IllegalArgumentException} if the value type is unsupported.
	 *
	 * @param byteArrayBuilder
	 *            The builder to which encoded bytes are written.
	 * @param value
	 *            The value to encode.
	 */
	protected void encodeValue(ByteArrayBuilder byteArrayBuilder, Object value) {
		WizardLogger.logInfo(lex.get("honWizardJob.encode.value.start", value.getClass().getSimpleName()));
		if (value instanceof BString) {
			byteArrayBuilder.addByte(TYPE_BSTRING);
			byteArrayBuilder.addString(((BString) value).getString());
		} else if (value instanceof BNumber) {
			byteArrayBuilder.addByte(TYPE_BNUMBER);
			byteArrayBuilder.addDouble(((BNumber) value).getDouble());
		} else if (value instanceof BBoolean) {
			byteArrayBuilder.addByte(TYPE_BBOOLEAN);
			byteArrayBuilder.addBoolean(((BBoolean) value).getBoolean());
		} else if (value instanceof BEnumRange) {
			byteArrayBuilder.addByte(TYPE_BENUM_RANGE);
			byteArrayBuilder.addString(SlotPath.unescape(((BEnumRange) value).toString()));
		} else if (value instanceof BFrozenEnum) {
			byteArrayBuilder.addByte(TYPE_BFROZEN_ENUM); 
			byteArrayBuilder.addString(value.getClass().getName());
			byteArrayBuilder.addInt(((BFrozenEnum) value).getOrdinal());
			WizardLogger.logFiner(lex.get("honWizardJob.encode.value.frozenEnum"), value.getClass().getName(), ((BFrozenEnum) value).getOrdinal());
		} else if (value instanceof BDynamicEnum) {
			byteArrayBuilder.addByte(TYPE_BDYNAMIC_ENUM);
			byteArrayBuilder.addInt(((BDynamicEnum) value).getOrdinal());
			if (((BDynamicEnum) value).getRange() != null && !BEnumRange.NULL.equals(((BDynamicEnum) value).getRange())) {
				byteArrayBuilder.addByte(HAS_RANGE_FLAG); 
				byteArrayBuilder.addString(SlotPath.unescape(((BDynamicEnum) value).getRange().toString()));
			} else {
				byteArrayBuilder.addByte(NO_RANGE_FLAG);
			}
			WizardLogger.logFiner(lex.get("honWizardJob.encode.value.dynamicEnum"), ((BDynamicEnum) value).getOrdinal());
		} else {
			WizardLogger.logSevere(lex.get("honWizardJob.encode.value.unsupported", value.getClass().getSimpleName()));
			throw new IllegalArgumentException("Unsupported data type: " + value.getClass());
		}
		WizardLogger.logInfo(lex.get("honWizardJob.encode.value.completed", value.getClass().getSimpleName()));
	}
    
	/**
	 * Decodes a single value from the binary stream, using the provided type
	 * indicator to determine the value's type and binary representation.
	 * 
	 * Supported type indicators correspond to the constants defined in this
	 * class.
	 * 
	 * Throws {@link IllegalArgumentException} if the type indicator is
	 * unsupported.
	 *
	 * @param byteArrayReader
	 *            The reader from which bytes are read.
	 * @param typeIndicator
	 *            The type indicator byte read from the stream.
	 * @return The decoded value as an appropriate object.
	 */
	protected Object decodeValue(ByteArrayReader byteArrayReader, int typeIndicator) {
		WizardLogger.logInfo(lex.getText("honWizardJob.decode.value.start", typeIndicator));
		Object value = null;
		switch (typeIndicator) {
		case TYPE_BSTRING:
			value = BString.make(byteArrayReader.readString());
			break;
		case TYPE_BNUMBER:
			value = BDouble.make(byteArrayReader.readDouble());
			break;
		case TYPE_BBOOLEAN:
			value = BBoolean.make(byteArrayReader.readBoolean());
			break;
		case TYPE_BENUM_RANGE:
			try {
				value = BEnumRange.DEFAULT.decodeFromString(TagGenerator.getEnumRangeEncodingStringFromTagValue(byteArrayReader.readString()));
			} catch (IOException e) {
				WizardLogger.logSevere(lex.getText("honWizardJob.decode.value.error", e.getMessage(), e));
			}
			break;
		case TYPE_BFROZEN_ENUM:
			// Read the class name
			String className = byteArrayReader.readString();
			int ordinal = byteArrayReader.readInt(); // Read the ordinal value
			// Recreate the BFrozenEnum instance
			value = recreateFrozenEnum(className, ordinal);
			WizardLogger.logFiner(lex.get("honWizardJob.decode.value.frozenEnum"), className, ordinal);
			break;
		case TYPE_BDYNAMIC_ENUM:
			value = decodeDynamicEnum(byteArrayReader, value);
			break;
		default:
			WizardLogger.logSevere(lex.getText("honWizardJob.decode.value.unsupported", typeIndicator));
			throw new IllegalArgumentException("Unsupported type indicator: " + typeIndicator);
		}
		WizardLogger.logInfo(lex.getText("honWizardJob.decode.value.completed", typeIndicator, value));
		return value;
	}
	
	/**
	 * Decodes a {@link BDynamicEnum} value from the binary stream, including
	 * its ordinal and optional range information.
	 * 
	 * This method is typically called from
	 * {@link #decodeValue(ByteArrayReader, int)} when a dynamic enum type
	 * indicator is encountered.
	 *
	 * @param byteArrayReader
	 *            The reader from which bytes are read.
	 * @param value
	 *            The value object to populate (may be null).
	 * @return The decoded {@link BDynamicEnum} instance.
	 */
	public Object decodeDynamicEnum(ByteArrayReader byteArrayReader, Object value) {
		int dynOrdinal = byteArrayReader.readInt(); // Read the original value
		if (byteArrayReader.readByte() == HAS_RANGE_FLAG) { // Read the hasRange flag
			try {
				// Read the range
				String range = TagGenerator.getEnumRangeEncodingStringFromTagValue(byteArrayReader.readString());
				BEnumRange convRange = null;
				convRange = (BEnumRange) BEnumRange.DEFAULT.decodeFromString(range);

				if (null != range) {
					value = BDynamicEnum.make(dynOrdinal, convRange);
				} else {
					value = BDynamicEnum.make(dynOrdinal);
				}
			} catch (IOException e) {
				WizardLogger.logSevere(lex.getText("honWizardJob.decode.value.error", e.getMessage(), e));
			}
		} else {
			value = BDynamicEnum.make(dynOrdinal);
		}
		return value;
	}

	/**
	 * Recreates a {@link BFrozenEnum} instance from its class name and ordinal
	 * value, using reflection to invoke the static factory method.
	 * 
	 * Throws {@link RuntimeException} if the class cannot be loaded, is not a
	 * {@link BFrozenEnum}, or the factory method invocation fails.
	 *
	 * @param className
	 *            The fully qualified class name of the enum.
	 * @param ordinal
	 *            The ordinal value of the enum constant.
	 * @return The reconstructed {@link BFrozenEnum} instance.
	 */
	private BFrozenEnum recreateFrozenEnum(String className, int ordinal) {
		try {
			// Load the class dynamically
			Class<?> enumClass = Class.forName(className); 
			if (BFrozenEnum.class.isAssignableFrom(enumClass)) {
				// Use reflection to call the make(int ordinal) factory method
				return (BFrozenEnum) enumClass.getMethod("make", int.class).invoke(null, ordinal);
			} else {
				throw new IllegalArgumentException("Class " + className + " is not a BFrozenEnum.");
			}
		} catch (Exception e) {
			WizardLogger.logSevere(lex.getText("honWizardJob.decode.value.frozenEnum.error", className, ordinal, e.getMessage()));
			throw new RuntimeException("Error recreating BFrozenEnum: " + className, e);
		}
	}
    
}
