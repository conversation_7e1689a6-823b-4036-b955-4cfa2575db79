/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.HashMap;
import java.util.Map;

import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;

import com.honeywell.applicationhandler.device.BHonWizardMetaData;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 2, 2025
 */
public class MetaDataHandler extends DataHandler {
	
    @Override
    public Map<Object, Integer> collectRepeatedValues(Object data) {
    	//Default Implementation when no value to collected
        return new HashMap<>();
    }

    @Override
	public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof BHonWizardMetaData) {
			WizardLogger.logInfo(lex.get("honWizardJob.encode.metaData.start"));

			ByteArrayBuilder metaDataBuilder = new ByteArrayBuilder();

			for (String propertyName : BHonWizardMetaData.getMetadataPropertyToId().keySet()) {
				Integer propertyId = BHonWizardMetaData.getMetadataPropertyToId().get(propertyName);
				Object propertyValue = ((BHonWizardMetaData) data).get(propertyName);

				if (propertyValue != null) {
					// Add the unique identifier
					metaDataBuilder.addInt(propertyId); 
					// Encode the value
					encodeValue(metaDataBuilder, propertyValue); 
					WizardLogger.logFiner(lex.get("honWizardJob.encode.metaData"), propertyName, propertyValue);
				}
			}

			// Add the size of the metadata and the metadata itself
			byteArrayBuilder.addInt(metaDataBuilder.getLength());
			byteArrayBuilder.addBytes(metaDataBuilder.toByteArray(), 0, metaDataBuilder.getLength());

			WizardLogger.logInfo(lex.get("honWizardJob.encode.metaData.completed"));
		}
	}

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
        WizardLogger.logInfo(lex.get("honWizardJob.decode.metaData.start"));

        BHonWizardMetaData metaData = device.getMetaDataComp();
        int metaDataSize = byteArrayReader.readInt(); // Read the size of the metadata
        int currentPos = byteArrayReader.currentPos();

        while (byteArrayReader.currentPos() < (currentPos + metaDataSize)) {
            int propertyId = byteArrayReader.readInt(); // Decode the unique identifier
            String propertyName =  BHonWizardMetaData.getMetadataIdToProperty().get(propertyId);

            if (propertyName != null) {
                Object propertyValue = decodeValue(byteArrayReader, byteArrayReader.readByte()); // Decode the value
                if(metaData.get(SlotPath.escape(propertyName)) != null) {
					metaData.set(SlotPath.escape(propertyName), (BValue) propertyValue, DECODE_METADATA);
				} else {
					metaData.add(SlotPath.escape(propertyName), (BValue) propertyValue, DECODE_METADATA);
				}
                WizardLogger.logFiner(lex.get("honWizardJob.decode.metaData"), propertyName, propertyValue);
            } else {
                WizardLogger.logSevere(lex.getText("honWizardJob.decode.metaData.unknownProperty", propertyId));
                throw new IllegalArgumentException("Unknown metadata property ID: " + propertyId);
            }
        }

        WizardLogger.logInfo(lex.get("honWizardJob.decode.metaData.completed"));
    }

}