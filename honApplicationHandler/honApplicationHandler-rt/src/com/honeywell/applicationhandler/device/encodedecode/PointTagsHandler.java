/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.baja.sys.BComponent;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 2, 2025
 */
public class PointTagsHandler extends DataHandler {

	@Override
	public Map<Object, Integer> collectRepeatedValues(Object data) {
		Map<Object, Integer> tagValueOccurrences = new HashMap<>();
		if (data instanceof List<?>) {
			List<?> rawList = (List<?>) data;
			if (rawList.isEmpty() || rawList.get(0) instanceof BHonWizSelector) {
				@SuppressWarnings("unchecked")
				List<BHonWizSelector> selectors = (List<BHonWizSelector>) rawList;
				// Create a collector for the repeated values
				RepeatedValueCollector collector = new GenericRepeatedValueCollector<>(
						selectors.stream().flatMap(selector -> selector.getTags().values().stream()).collect(Collectors.toList()));
				collector.collectRepeatedValues(tagValueOccurrences);
			}
		}
		return tagValueOccurrences;
	}

    @Override
	public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof List<?>) {
			List<?> rawList = (List<?>) data;
			if (rawList.isEmpty() || rawList.get(0) instanceof BHonWizSelector) {
				@SuppressWarnings("unchecked")
				List<BHonWizSelector> selectors = (List<BHonWizSelector>) rawList;
				WizardLogger.logInfo(lex.get("honWizardJob.encode.tagMappings.start"));

				// Encode the number of selectors
				byteArrayBuilder.addInt(selectors.size());

				for (BHonWizSelector selector : selectors) {
					ByteArrayBuilder selectorBuilder = new ByteArrayBuilder();

					Map<String, Object> tags = selector.getTags();
					encodeTagsInASelector(tagMapping, selectorBuilder, tags);

					// Add the size of the selector's tags and the tags themselves
					byteArrayBuilder.addInt(selectorBuilder.getLength());
					byteArrayBuilder.addBytes(selectorBuilder.toByteArray(), 0, selectorBuilder.getLength());
				}

				WizardLogger.logInfo(lex.get("honWizardJob.encode.tagMappings.completed"));
			}
		}
	}

	protected void encodeTagsInASelector(TagMapping tagMapping, ByteArrayBuilder selectorBuilder, Map<String, Object> tags) {
		for (Map.Entry<String, Object> entry : tags.entrySet()) {
			String tagName = entry.getKey();
			Object tagValue = entry.getValue();
			Integer tagId = tagMapping.getTagId(tagName);

			if (tagId != null) {
				selectorBuilder.addByte(tagId);

				// Check if the value exists in repeatedTagMappings
				Integer repeatedValueId = tagMapping.getRepeatedValueId(tagValue);

				if (repeatedValueId != null) {
					// Use the repeated value ID instead of encoding the full value
					// Type indicator for repeated value reference
					selectorBuilder.addByte(TYPE_REPEATED_VALUE_REFERENCE);
					selectorBuilder.addInt(repeatedValueId);
					WizardLogger.logFiner(lex.get("honWizardJob.encode.tagMappings.repeatedValue"), tagName, repeatedValueId);
				} else {
					// Encode the full value if it's not in
					// repeatedTagMappings
					encodeValue(selectorBuilder, tagValue);
					WizardLogger.logFiner(lex.get("honWizardJob.encode.tagMappings.fullValue"), tagName, tagValue);
				}
			}
		}
	}

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
    	List<BHonWizSelector> pointSelectors = decodeSelectors(byteArrayReader, repeatedTagMappings, tagMapping, false);
		List<BComponent> ptsWithSelectors = device.getPointWithPointSelectors();
    	int i = 0;
    	for(BComponent pt : ptsWithSelectors) {
    		pt.add("honWizPointSelector?", pointSelectors.get(i), DECODE_METADATA);
    		++i;
    	}
    }

	protected List<BHonWizSelector> decodeSelectors(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, boolean isSlotSelector) {
		WizardLogger.logInfo(lex.get("honWizardJob.decode.tagMappings.start"));
        List<BHonWizSelector> selectors = new ArrayList<>();

        // Decode the number of selectors
        int selectorCount = byteArrayReader.readInt();

        for (int i = 0; i < selectorCount; i++) {
            Map<String, Object> tags = new HashMap<>();

            // Decode the size of the selector's tags
            int selectorSize = byteArrayReader.readInt();
            int currentPos = byteArrayReader.currentPos();

            while (byteArrayReader.currentPos() < (currentPos + selectorSize)) {
                int tagId = byteArrayReader.readByte();

                int typeIndicator = byteArrayReader.readByte();

                Object tagValue;
                if (typeIndicator == TYPE_REPEATED_VALUE_REFERENCE) {
                    // Decode repeated value reference
                    int repeatedValueId = byteArrayReader.readInt();
                    tagValue = repeatedTagMappings.get(repeatedValueId);
                    WizardLogger.logFiner(lex.get("honWizardJob.decode.tagMappings.repeatedValue"), tagId, repeatedValueId, tagValue);
                } else {
                    // Decode full value
                    tagValue = decodeValue(byteArrayReader, typeIndicator);
                    WizardLogger.logFiner(lex.get("honWizardJob.decode.tagMappings.fullValue"), tagId, tagValue);
                }

                String tagName = tagMapping.getTagName(tagId);
                if (tagName != null) {
                    tags.put(tagName, tagValue);
                }
            }

            BHonWizSelector selector;
            if(isSlotSelector) {
            	selector = new BHonWizSlotSelector();
            } else {
            	selector = new BHonWizPointSelector();
            }
            selector.handleTags(tags);
            selectors.add(selector);
            WizardLogger.logFiner(lex.get("honWizardJob.decode.tagMappings.selector"), tags);
        }

        WizardLogger.logInfo(lex.get("honWizardJob.decode.tagMappings.completed"));
        return selectors;
	}
}