/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device;

import java.io.File;
import java.io.FileInputStream;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.logging.Logger;

import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.util.ByteBuffer;
import javax.baja.schedule.BAbstractSchedule;
import javax.baja.schedule.BCompositeSchedule;
import javax.baja.schedule.BDailySchedule;
import javax.baja.schedule.BDateRangeSchedule;
import javax.baja.schedule.BDateSchedule;
import javax.baja.schedule.BDaySchedule;
import javax.baja.schedule.BEnumSchedule;
import javax.baja.schedule.BTimeSchedule;
import javax.baja.schedule.BWeekAndDaySchedule;
import javax.baja.schedule.BWeekSchedule;
import javax.baja.status.BStatus;
import javax.baja.status.BStatusEnum;
import javax.baja.sys.Action;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BStation;
import javax.baja.sys.BString;
import javax.baja.sys.BTime;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Id;
import javax.baja.tag.Tag;
import javax.baja.tagdictionary.BTagDictionaryService;
import javax.baja.tagdictionary.BTagInfo;
import javax.baja.util.BUuid;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.encodedecode.EncodedResult;
import com.honeywell.applicationhandler.device.encodedecode.WizardDataHandler;
import com.honeywell.applicationhandler.enums.BMonthEnum;
import com.honeywell.applicationhandler.enums.BWeekEnum;
import com.honeywell.applicationhandler.enums.BWeekdayEnum;
import com.honeywell.applicationhandler.filegenerator.DateRangeHolidayDetails;
import com.honeywell.applicationhandler.filegenerator.DayScheduleEvent;
import com.honeywell.applicationhandler.filegenerator.GlobalStoreFileGenerator;
import com.honeywell.applicationhandler.filegenerator.HolidayDetails;
import com.honeywell.applicationhandler.filegenerator.RecurringHolidayDetails;
import com.honeywell.applicationhandler.filegenerator.ScheduleEventStartEndStatus;
import com.honeywell.applicationhandler.filegenerator.SpecificDateHolidayDetails;
import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.generate.TagMappingFileGenerator;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.parser.RuleParser;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;
import com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget;
import com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase;
import com.tridium.json.JSONObject;

@NiagaraAction(name="generateGlobalStore", flags = Flags.HIDDEN)
@NiagaraAction(name="encodeWizardData")
@NiagaraAction(name="decodeWizardData")
@NiagaraAction (
		name = "ruleQuery",
		parameterType = "baja:String",
	    defaultValue = "BString.DEFAULT",
	    flags = Flags.HIDDEN)
@NiagaraType
public class BHonWizardGlobalStore extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.BHonWizardGlobalStore(894451972)1.0$ @*/
/* Generated Tue Jun 24 11:54:16 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Action "generateGlobalStore"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code generateGlobalStore} action.
   * @see #generateGlobalStore()
   */
  public static final Action generateGlobalStore = newAction(Flags.HIDDEN, null);
  
  /**
   * Invoke the {@code generateGlobalStore} action.
   * @see #generateGlobalStore
   */
  public void generateGlobalStore() { invoke(generateGlobalStore, null, null); }

////////////////////////////////////////////////////////////////
// Action "encodeWizardData"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code encodeWizardData} action.
   * @see #encodeWizardData()
   */
  public static final Action encodeWizardData = newAction(0, null);

  /**
   * Invoke the {@code encodeWizardData} action.
   * @see #encodeWizardData
   */
  public void encodeWizardData() { invoke(encodeWizardData, null, null); }

////////////////////////////////////////////////////////////////
// Action "decodeWizardData"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code decodeWizardData} action.
   * @see #decodeWizardData()
   */
  public static final Action decodeWizardData = newAction(0, null);

  /**
   * Invoke the {@code decodeWizardData} action.
   * @see #decodeWizardData
   */
  public void decodeWizardData() { invoke(decodeWizardData, null, null); }

////////////////////////////////////////////////////////////////
// Action "ruleQuery"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code ruleQuery} action.
   * @see #ruleQuery(BString parameter)
   */
  public static final Action ruleQuery = newAction(Flags.HIDDEN, BString.DEFAULT, null);
  
  /**
   * Invoke the {@code ruleQuery} action.
   * @see #ruleQuery
   */
  public void ruleQuery(BString parameter) { invoke(ruleQuery, parameter, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardGlobalStore.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();
    private static final Lexicon lex = Lexicon.make(BHonWizardGlobalStore.class);
    BIHoneywellConfigurableDevice selectorDevice = null;
    public void doGenerateGlobalStore() {
        try {
			BIHoneywellConfigurableDevice device = (BIHoneywellConfigurableDevice) this.getParent();
			BUuid globalStoreUuid = device.getGlobalStoreUuid(false);
			if(null == globalStoreUuid){
				return;
			}
			GlobalStoreFileGenerator.generateJson(this, true, globalStoreUuid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	@Override
	public void started() throws Exception {
		super.started();

		BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
		BValue bHonWizardTag = tagDictionaryService.get("HonConfigureWizard");
		if (bHonWizardTag == null) {
			tagDictionaryService.add("HonConfigureWizard", new BHonWizardTag());
		}
		selectorDevice = HoneywellConfigurableDeviceUtil.getDevice(this.getParent());
    }


	@Override
	public void descendantsStarted() throws Exception{
		super.descendantsStarted();
		//copy template file from module to station home if it does not exist
		TagMappingFileGenerator.copyTagMappingTemplateFileToStationHome();
	}
	
	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
		String name = SlotPath.unescape(property.getName());
    	if(name.startsWith(BHonWizardTag.HON_WIZ_PAGE_ORDER) && null != selectorDevice) {
    		selectorDevice.honWizardSelectorChanged(true, context);
    	}
	}
	
    public Map<String, Integer> getPageOrderMap() {
        Map<String, Integer> pageOrderMap = new HashMap<>();
        BHonWizardTag.WIZ_PAGE_ORDER.forEach(id -> {
            BValue value = get(SlotPath.escape(id.getQName()));
            if(value != null) {
                String pageName = value.toString();
                pageOrderMap.put(pageName, BHonWizardTag.WIZ_PAGE_ORDER.indexOf(id));
            }
        });

        return pageOrderMap;
    }

	public void addPageOrderForNewPage(String pageName) {
		if (pageName != null && !pageName.isEmpty()) {
			for(int i = 0; i<BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
				Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
				Optional<BIDataValue> pageOrderValue = tags().get(id);
				if(!pageOrderValue.isPresent()) {
					tags().set(id, BString.make(pageName));
					break;
				}
			}
		}
	}

	public Id getIdForPage(String pageName) {
		if (pageName != null && !pageName.isEmpty()) {
			for(int i = 0; i<BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
				Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
				Optional<BIDataValue> pageOrderValue = tags().get(id);
				if(pageOrderValue.isPresent() && pageOrderValue.get().toString().equals(pageName)) {
					return id;
				}
			}
		}
		return null;
	}

	public void removePageOrder(String pageName) {
		if(pageName != null && !pageName.isEmpty()) {
			for(int i = 0; i<BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
				Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
				Optional<BIDataValue> pageOrderValue = tags().get(id);
				if(pageOrderValue.isPresent() && pageOrderValue.get().toString().equals(pageName)) {
					tags().removeAll(id);
					break;
				}
			}
		}
	}

	public void clearAllPageOrders() {
		for (int i = 0; i < BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
			Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
			Optional<BIDataValue> pageOrderValue = tags().get(id);
			if (pageOrderValue.isPresent()) {
				tags().removeAll(id);
			}
		}
	}

    public boolean saveComponentValue(BWidgetComponentBase widgetComponent, String value) {
        try {
            if(!(widgetComponent instanceof BDynamicWidgetComponentBase)) {
                return false;
            }
            ((BDynamicWidgetComponentBase) widgetComponent).setValue(value);
            return true;
        }
        catch (Exception ex) {
        	HoneywellDeviceWizardLogger.severe("Failed saving value for component: " + widgetComponent.getName(), ex);
            return false;
        }
    }

    public BWidgetComponentBase getWidgetComponent(String storeName, String componentName) {
    	BComponent storeComponent=null;
    	if(SlotPath.isValidName(storeName)) {
        	storeComponent = (BComponent) get(storeName);
    	}
    	else {
    		storeComponent = (BComponent) get(SlotPath.escape(storeName));
		}
        if(storeComponent == null) {
        	HoneywellDeviceWizardLogger.severe("Page component not found: " + storeName);
            return null;
        }
        BComponent component =null;
        if(SlotPath.isValidName(componentName)) {
        	component = (BComponent) storeComponent.get(componentName);
    	}
    	else {
    		component = (BComponent) storeComponent.get(SlotPath.escape(componentName));
		}
        if(component == null) {
        	HoneywellDeviceWizardLogger.severe("Config component not found: " + componentName);
            return null;
        }

        if(!(component instanceof BWidgetComponentBase)) {
        	HoneywellDeviceWizardLogger.warning("Component is not a widget component: " + componentName);
            return null;
        }

        return (BWidgetComponentBase) component;
    }
    
	public boolean saveScheduleDetails(BWidgetComponentBase component, Map<String, DayScheduleEvent> eventDetails, int defaultLocalType,
			Map<String, HolidayDetails> holidayDetails) {
		if (!(component instanceof BSchedulePageWidget)) {
			return false;
		}
		BEnumSchedule globalStoreSchedule = ((BSchedulePageWidget) component).getEnumSchedule();
		globalStoreSchedule.clear();
		updateScheduleTagOrdinalMapping(globalStoreSchedule);
		globalStoreSchedule.setDefaultOutput(new BStatusEnum(((BEnumRange) globalStoreSchedule.getDefaultOutput().getStatusValueFacets().get(RANGE))
				.get(scheduleOrdinalToWizardOrdinalMap.get(defaultLocalType)), BStatus.ok));

		HoneywellDeviceWizardLogger.info("Default Local Schedule Type" + defaultLocalType);
		Iterator<Entry<String, DayScheduleEvent>> updatedScheduleDetails = eventDetails.entrySet().iterator();
		BCompositeSchedule compSch = globalStoreSchedule.getSchedule();
		while (updatedScheduleDetails.hasNext()) {
			Entry<String, DayScheduleEvent> entry = updatedScheduleDetails.next();
			DayScheduleEvent event = entry.getValue();
			String fullDay = dayNameMap.get(event.getDayOfEvent());

			BWeekSchedule[] weekSchs = compSch.getChildren(BWeekSchedule.class);
			BWeekSchedule weekSch;
			if (weekSchs.length == 0) {
				weekSch = new BWeekSchedule();
				compSch.add("week?", weekSch);
			} else {
				weekSch = weekSchs[0];
			}

			saveValueToScheduleComponent(globalStoreSchedule, event, fullDay, weekSch);
		}
		
		saveExceptionScheduleDetails(globalStoreSchedule, holidayDetails);
		
		//Reset the maps
		scheduleOrdinalToWizardOrdinalMap.clear();;
		scheduleTagToWizardOrdinalMap.clear();
		
		return true;
	}
	
	private void updateScheduleTagOrdinalMapping(BEnumSchedule globalStoreSchedule) {
		BEnumRange existingEnumRange = (BEnumRange) globalStoreSchedule.getDefaultOutput().getStatusValueFacets().get(RANGE);
		int wizardOrdinal = 1;
		for (int scheduleOrdinal : existingEnumRange.getOrdinals()) {
			scheduleTagToWizardOrdinalMap.put(wizardOrdinal, existingEnumRange.get(scheduleOrdinal).getTag());
			scheduleOrdinalToWizardOrdinalMap.put(wizardOrdinal, scheduleOrdinal);
			wizardOrdinal++;
		}
	}
	
	private boolean saveExceptionScheduleDetails(BEnumSchedule globalStoreSchedule, Map<String, HolidayDetails> holidayDetails) {
		BCompositeSchedule schToBeUpdated = globalStoreSchedule.getSchedule();
		BCompositeSchedule[] specialSchs = schToBeUpdated.getChildren(BCompositeSchedule.class);
		
		BCompositeSchedule specialSch;
		if (specialSchs.length == 0) {
			specialSch = new BCompositeSchedule();
			schToBeUpdated.add("specialEvents?", specialSch);
		} else {
			specialSch = specialSchs[0];
		}
		Iterator<Entry<String, HolidayDetails>> updatedExpScheduleDetails = holidayDetails.entrySet().iterator();
		List<String> namesOfEvent = new ArrayList<>();
		while (updatedExpScheduleDetails.hasNext()) {
			Entry<String, HolidayDetails> entry = updatedExpScheduleDetails.next();
			namesOfEvent.add(entry.getKey()); // Add the all events for Garbage cleaning unused
			HolidayDetails holDetails = entry.getValue();

			
			BDailySchedule splDailySch;
			if(null == specialSch.get(SlotPath.escape(entry.getKey()))) {
				splDailySch = new BDailySchedule();
				if(holDetails instanceof DateRangeHolidayDetails) {
					splDailySch.setDays(new BDateRangeSchedule());
				} else if (holDetails instanceof SpecificDateHolidayDetails) {
					splDailySch.setDays(new BDateSchedule());
				} else if (holDetails instanceof RecurringHolidayDetails) {
					splDailySch.setDays(new BWeekAndDaySchedule());
				}
				specialSch.add(SlotPath.escape(entry.getKey()), splDailySch);
			} else {
				splDailySch = (BDailySchedule) specialSch.get(SlotPath.escape(entry.getKey()));
				splDailySch.getDay().clear();
			}
			
			// Always unoccupied when a exception schedule is programmed from wizard
			BDaySchedule daySch = splDailySch.getDay();
			daySch.clear();
			
			BTimeSchedule timeSch = new BTimeSchedule();
			timeSch.setStart(BTime.MIDNIGHT);
			timeSch.setFinish(BTime.MIDNIGHT);
			timeSch.setEffectiveValue((new BStatusEnum(
					((BEnumRange) globalStoreSchedule.getEnumFacets().get(RANGE)).get(DEFAULT_EXCEPTION_SCH_OCC_STATUS), BStatus.ok)));

			daySch.add(timeSch);
			
			// Handle updates for exception schedule
			BAbstractSchedule absSch = splDailySch.getDays();
			if(absSch instanceof BWeekAndDaySchedule) {
				BWeekAndDaySchedule weekAndDaySch = (BWeekAndDaySchedule) absSch;
				int month = BMonthEnum.make(((RecurringHolidayDetails) holDetails).getMonth()).getOrdinal();
				int week = BWeekEnum.make(((RecurringHolidayDetails) holDetails).getWeek()).getOrdinal();
				int weekday = BWeekdayEnum.make(((RecurringHolidayDetails) holDetails).getWeekday()).getOrdinal();
				weekAndDaySch.setMonth(month);
				weekAndDaySch.setWeek(week);
				weekAndDaySch.setWeekday(weekday);
			} else if (absSch instanceof BDateSchedule) {
				BDateSchedule dateSch = (BDateSchedule) absSch;
				int month = BMonthEnum.make(((SpecificDateHolidayDetails) holDetails).getMonth()).getOrdinal();
				int date = ((SpecificDateHolidayDetails) holDetails).getDate();
				dateSch.setMonth(month);
				dateSch.setDay(date);
				dateSch.setYear(-1); // Always any year
				dateSch.getYearSchedule().setAlwaysEffective(true); // Always any year
			} else if (absSch instanceof BDateRangeSchedule) {
				BDateRangeSchedule dateRangeSch = (BDateRangeSchedule) absSch;
				int fromMonth = BMonthEnum.make(((DateRangeHolidayDetails) holDetails).getFromMonth()).getOrdinal();
				int fromDate = ((DateRangeHolidayDetails) holDetails).getFromDate();
				int toMonth = BMonthEnum.make(((DateRangeHolidayDetails) holDetails).getToMonth()).getOrdinal();
				int toDate = ((DateRangeHolidayDetails) holDetails).getToDate();
				dateRangeSch.getStart().setDay(fromDate);
				dateRangeSch.getStart().setMonth(fromMonth);
				dateRangeSch.getStart().setYear(-1); // Always any year
				dateRangeSch.getStart().getYearSchedule().setAlwaysEffective(true); // Always any year
				dateRangeSch.getEnd().setDay(toDate);
				dateRangeSch.getEnd().setMonth(toMonth);
				dateRangeSch.getEnd().setYear(-1); // Always any year
				dateRangeSch.getEnd().getYearSchedule().setAlwaysEffective(true); // Always any year
			}
			
		}
		
		// Check and remove the special events which are no more present
		for(BDailySchedule dailySch : specialSch.getChildren(BDailySchedule.class)) {
			String nameOfDailySch = SlotPath.unescape(dailySch.getName());
			if(!namesOfEvent.contains(nameOfDailySch)) {
				specialSch.remove(dailySch.getName());
			}
		}
		
		return true;
	}

	private void saveValueToScheduleComponent(BEnumSchedule sch, DayScheduleEvent event, String fullDay, BWeekSchedule weekSch) {
		BValue dailySchVal = weekSch.get(fullDay.toLowerCase());
		if (dailySchVal instanceof BDailySchedule) {
			BDaySchedule emptyDay = ((BDailySchedule) dailySchVal).getDay().clear();
			for (ScheduleEventStartEndStatus eventStartEnd : event.getListOfEventsForDay()) {
				BTimeSchedule timeSch = new BTimeSchedule();
				try {
					int eventId = eventStartEnd.getEventId();
					String startTime = eventStartEnd.getEventStart();
					String endTime = eventStartEnd.getEventEnd();
					int status = eventStartEnd.getEventStatus();
					int startTimeHr = Integer.parseInt(startTime.split(":")[0]);
					int startTimeMin = Integer.parseInt(startTime.split(":")[1]);
					int endTimeHr = Integer.parseInt(endTime.split(":")[0]);
					// Special handling if the received value is 24:00. Niagara
					// doesn't support 24:00. It should be replaced with 00:00
					if(endTimeHr == 24) {
						endTimeHr = 0;
					}
					int endTimeMin = Integer.parseInt(endTime.split(":")[1]);
					BTime startTimeFormat = BTime.make(startTimeHr, startTimeMin, SECOND_VALUE);
					BTime endTimeFormat = BTime.make(endTimeHr, endTimeMin, SECOND_VALUE);
					HoneywellDeviceWizardLogger.info(MessageFormat.format("Day: {0} EventId: {1} StartTime: {2} EndTime {3} Status: {4}", fullDay,
							eventId, startTime, endTime, status));
					timeSch.setEffectiveValue((new BStatusEnum(
							((BEnumRange) sch.getEnumFacets().get(RANGE)).get(scheduleTagToWizardOrdinalMap.get(status)), BStatus.ok)));
					timeSch.setStart(startTimeFormat);
					timeSch.setFinish(endTimeFormat);
				} catch (Exception e) {
					e.printStackTrace();
				}
				emptyDay.add("time?", timeSch);
			}
		}
	}

    /**
     * save terminal assignments in global store
     *
     * @param comp
     */
    public boolean updateTerminalAssignments(String storeName, BComponent comp, List<BOrd> changedOrdList) {
        BTerminalAssignmentWidget terminalStoreComponent = (BTerminalAssignmentWidget) get(storeName);
        if (terminalStoreComponent == null) {
            HoneywellDeviceWizardLogger.severe(lex.getText("BHonWizardValueSaveJob.terminalPageNotFound"));
            return false;
        }
        boolean isTerminalChanged = false;
        for (Slot slot : terminalStoreComponent.getSlots()) {
            String[] split = SlotPath.unescape(slot.getName()).split("~");
            if (split.length == 2) {
                String pinName = split[0];
                String fbName = split[1];
                BString assignedFbName = (BString) comp.get(SlotPath.escape(pinName));
                if (null != assignedFbName && !fbName.equals(assignedFbName.getString())) {
                    terminalStoreComponent.rename(slot.asProperty(), SlotPath.escape(pinName + "~" + assignedFbName.getString()));
                    isTerminalChanged = true;
                }
            }
        }
        if (isTerminalChanged) {
            changedOrdList.add(terminalStoreComponent.getSlotPathOrd());
        }
        return true;
    }
    
    public void doRuleQuery(BString query) {
        RuleParser parser = new RuleParser((BIHoneywellConfigurableDevice) this.getParent(), true);
        IRule rule = parser.parse(query.toString(), "Test");
        JSONObject json = parser.generateJson(rule);
        HoneywellDeviceWizardLogger.info(json.toString());
    }

    public void doEncodeWizardData() {
    	encodeAndGetEncodedResult();
	}

	public EncodedResult encodeAndGetEncodedResult() {
		BIHoneywellConfigurableDevice device = (BIHoneywellConfigurableDevice) this.getParent();
    	List<BComponent> ptsWithSelectors = device.getPointWithPointSelectors();
    	List<BHonWizSelector> pointSelectors = new ArrayList<>();
    	for(BComponent pt : ptsWithSelectors) {
			BHonWizPointSelector[] selectors = pt.getChildren(BHonWizPointSelector.class);
			if(selectors != null) {
				pointSelectors.addAll(Arrays.asList(selectors));
			}
		}
    	WizardDataHandler dataHandler = new WizardDataHandler(device);
    	return dataHandler.encodeData(pointSelectors, this, device.getMetaDataComp());
	}

    public void doDecodeWizardData() {
    	BIHoneywellConfigurableDevice device = (BIHoneywellConfigurableDevice) this.getParent();
    	WizardDataHandler dataHandler = new WizardDataHandler(device);
    	File userConfigData = new File(Sys.getStationHome(), "userConfigData");
		userConfigData.mkdir();
		String path = BHonWizardRuleParserJob.getFolderPath((BComponent) device);
		File devicePath = new File(userConfigData, path);
		devicePath.mkdir();
		File userConfig = new File(devicePath, "wizard_data_encoded.bin");

		try (FileInputStream fis = new FileInputStream(new File(userConfig.getAbsolutePath()))) {
			byte[] data = new byte[(int) userConfig.length()];
			fis.read(data);
	        device.stopDynamicUpdatesOfGlobalStore("doDecodeWizardData");
			dataHandler.decodeData(data);
			device.resumeDynamicUpdatesOfGlobalStore("doDecodeWizardData");
		} catch (Exception e) {
			HoneywellDeviceWizardLogger.severe("Error while decoding the wizard data", e);
		}
	}
    
    
    public void decodeWizardData(ByteBuffer buffer) {
    	BIHoneywellConfigurableDevice device = (BIHoneywellConfigurableDevice) this.getParent();
    	WizardDataHandler dataHandler = new WizardDataHandler(device);
    	try {
    		device.stopDynamicUpdatesOfGlobalStore("decodeWizardData");
    		byte[] data = buffer.getBytes();
			dataHandler.decodeData(data);
			device.resumeDynamicUpdatesOfGlobalStore("decodeWizardData");
		} catch (Exception e) {
			HoneywellDeviceWizardLogger.severe("Error while decoding the wizard data", e);
		}
    	
    }

    public void handleTags(Map<String, Object> tagData) {
        try {
            // Fetch the tag dictionary service
            BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
            if (tagDictionaryService == null) {
                logger.severe("TagDictionaryService not found.");
                return;
            }

            // Fetch the wizard tag definition
            BHonWizardTag wizardTag = (BHonWizardTag) tagDictionaryService.get("HonConfigureWizard");
            if (wizardTag == null) {
                logger.severe("HonConfigureWizard tag definition not found.");
                return;
            }

            // Iterate through the provided tag data and update tags
            for (Map.Entry<String, Object> entry : tagData.entrySet()) {
                String tagName = entry.getKey();
                Object tagValue = entry.getValue();

                	BTagInfo bTagInfo = (BTagInfo) wizardTag.getTagDefinitions().get(tagName); // Fetch tag info using tag name
                    Tag tag = createTag(bTagInfo, tagValue); // Create tag using TagGenerator logic
                    this.tags().set(tag); // Use `set` to update tags
                    logger.finest("Updated tag: Name=" + tagName + ", Value=" + tagValue);
            }
        } catch (Exception e) {
            logger.severe("Failed to update tags in BHonWizPointSelector: " + e.getMessage());
        }
    }

    /**
     * Creates a tag based on the provided tag info and value.
     * @param tagInfo The tag info object.
     * @param tagValue The value for the tag.
     * @return The created tag.
     */
    private Tag createTag(BTagInfo tagInfo, Object tagValue) {
        try {
            if (tagValue instanceof BIDataValue) {
                return new Tag(tagInfo.getTagId(), (BIDataValue) tagValue);
            }
        } catch (Exception e) {
            logger.severe("Failed to create tag: " + e.getMessage());
            return null;
        }
		return null;
    }

    private static final Map<String, String> dayNameMap = new HashMap<>();
    private static final int SECOND_VALUE = 0;
    private static final String RANGE = "range";
    private static final int DEFAULT_EXCEPTION_SCH_OCC_STATUS = 2;
	private Map<Integer, Integer> scheduleOrdinalToWizardOrdinalMap = new HashMap<>();
	private Map<Integer, String> scheduleTagToWizardOrdinalMap = new HashMap<>();
    
	static {
		for (DayOfWeek day : DayOfWeek.values()) {
			String shortName = day.getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
			String fullName = day.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
			dayNameMap.put(shortName, fullName);
		}
	}
   
}
