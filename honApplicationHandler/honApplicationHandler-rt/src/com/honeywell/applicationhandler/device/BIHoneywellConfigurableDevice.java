/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.device;

import static com.honeywell.applicationhandler.ontology.Const.HON_DEVICE_CATEGORY;
import static com.honeywell.applicationhandler.ontology.Const.HON_WIZARD_CATEGORY;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.baja.agent.AgentList;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.schedule.BEnumSchedule;
import javax.baja.security.BPermissions;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BInterface;
import javax.baja.sys.BValue;
import javax.baja.sys.BString;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Subscriber;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.user.BUser;
import javax.baja.user.BUserService;
import javax.baja.util.BUuid;

import com.honeywell.applicationhandler.jobs.BHonWizardGlobalStoreGenJob;
import com.honeywell.applicationhandler.jobs.BHonWizardTagGenJob;
import com.honeywell.applicationhandler.jobs.BHonWizardTagMappingFileGenJob;
import com.honeywell.applicationhandler.jobs.BHonWizardTagValidationJob;
import com.honeywell.applicationhandler.jobs.BHonWizardValueSaveJob;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.generate.TagGenerator;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.utils.HonWizardPermissionUtil;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget;

/**
 * The {@code BIHoneywellConfigurableDevice} interface represents a configurable device in the Honeywell application handler.
 * This interface extends {@code BInterface} and is annotated with {@code @NiagaraType}, indicating its integration with the Niagara framework.
 *
 * <p>This interface mandates the implementation of several methods to manage and synchronize device configurations, handle slot updates,
 * and generate necessary components and tags for the Honeywell wizard. The methods provided in this interface facilitate the interaction
 * with the global store, slot details, and tag generation for the device.</p>
 *
 * <h2>Methods:</h2>
 * <ul>
 *   <li>{@link #getGlobalStore()}: Retrieves the global store associated with the device.</li>
 *   <li>{@link #setGlobalStore(BHonWizardGlobalStore)}: Sets the global store for the device.</li>
 *   <li>{@link #getGlobalStoreProperty()}: Retrieves the property representing the global store.</li>
 *   <li>{@link #syncValueChangeFromHonConfigurableDevice(List)}: Synchronizes value changes from the configurable device.</li>
 *   <li>{@link #getDeviceName()}: Retrieves the name of the device.</li>
 *   <li>{@link #setHonWizardSlotDetails(BHonWizardSlotDetails)}: Sets the slot details for the Honeywell wizard.</li>
 *   <li>{@link #getHonWizardSlotDetails()}: Retrieves the slot details for the Honeywell wizard.</li>
 *   <li>{@link #getWizardSlotSubscriber()}: Retrieves the subscriber for wizard slot updates.</li>
 *   <li>{@link #processHonWizardSlotUpdates(Map)}: Processes updates to the Honeywell wizard slots.</li>
 *   <li>{@link #isScheduleToBeConfigured()}: Determines if the schedule needs to be configured (default implementation returns false).</li>
 *   <li>{@link #getConfiguredEnumSchedule()}: Retrieves the configured enum schedule (default implementation returns a new {@code BEnumSchedule}).</li>
 *   <li>{@link #generateGlobalStoreComponent()}: Generates the global store component using {@code HoneywellConfigurableDeviceUtil}.</li>
 *   <li>{@link #generateHoneywellWizardTags()}: Generates Honeywell wizard tags using {@code HoneywellConfigurableDeviceUtil} and {@code TagGenerator}.</li>
 *   <li>{@link #getTerminalAssignments()}: Retrieves the terminal assignment for the device.</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Implement this interface in classes that represent configurable Honeywell devices. Ensure that all required methods are properly implemented
 * to handle device configurations, slot updates, and tag generation. Utilize the provided default methods for common operations such as generating
 * global store components and Honeywell wizard tags.</p>
 *
 * <h2>Related Classes:</h2>
 * <ul>
 *   <li>{@code TagGenerator}: Utility class for generating tags for points and slots.</li>
 *   <li>{@code BHonWizSlotSelector}: Represents a selector for wizard slots.</li>
 *   <li>{@code BHonWizPointSelector}: Represents a selector for wizard points.</li>
 *   <li>{@code BHonWizardSlotDetails}: Encapsulates details about a wizard slot.</li>
 *   <li>{@code HoneywellConfigurableDeviceUtil}: Utility class for operations related to Honeywell configurable devices.</li>
 * </ul>
 *
 * <p>Note: This interface is auto-generated and may be subject to changes. Ensure to keep the implementation updated with the latest version.</p>
 *
 * @see BInterface
 * @see NiagaraType
 * @see BHonWizardGlobalStore
 * @see BHonWizardSlotDetails
 * @see TagGenerator
 * @see HoneywellConfigurableDeviceUtil
 */
@NiagaraType
public interface BIHoneywellConfigurableDevice extends BInterface {
  /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
  /*@ $com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice(2979906276)1.0$ @*/
  /* Generated Thu Sep 19 14:38:06 CST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  Type TYPE = Sys.loadType(BIHoneywellConfigurableDevice.class);

  /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  BHonWizardGlobalStore getGlobalStore();
  void setGlobalStore(BHonWizardGlobalStore v);

  BHonWizardRuleStore getRuleStore();
  void setRuleStore(BHonWizardRuleStore v);
  Property getGlobalStoreProperty();
  void syncValueChangeFromHonConfigurableDevice(List<BOrd> changedComponents, BHonWizardValueSaveJob saveJob,int wizardOptionSave);
  String getDeviceName();
  boolean canSaveToDatabase();
  boolean canUpdateValuesToController();
  Action getAttachHonWizardTagsAction();

  Action getAttachHonWizardTagsActionWithFileContent();
  Action getGenerateGlobalStoreAction();

  Action getGenerateTagMappingFileAction();
  Action getValidateHonWizardTagsAction();

  // Mandate the implementors to add these methods to Device so that slot
  // changes from wiresheet can be immediately propagated.
  void setHonWizardSlotDetails(BHonWizardSlotDetails slotDetails);
  BHonWizardSlotDetails getHonWizardSlotDetails();
  Subscriber getWizardSlotSubscriber();
  void processHonWizardSlotUpdates(Map<BOrd, String> updatedSlotDetails);
  void callSwapOutOnCloseOfWizard();
  void handleBackEndCallsDuringWizardLoad();


  default boolean isScheduleToBeConfigured() {
    return false;
  }

  default BEnumSchedule getConfiguredEnumSchedule(String name) {
    return new BEnumSchedule();
  }

  default void generateGlobalStoreComponent() {
    BHonWizardGlobalStoreGenJob globalStoreGenJob = new BHonWizardGlobalStoreGenJob(this);
    globalStoreGenJob.submit(null);
  }

  default void generateHoneywellWizardTags(String fileData) {
    BHonWizardTagGenJob tagGenJob = new BHonWizardTagGenJob(this, fileData);
    tagGenJob.submit(null);
  }

  default void generateHoneywellWizardTagsWithFileOrd(BOrd fileOrd) {
    String fileData = TagGenerator.parseTagMappingInfo(fileOrd);
    if(fileData != null) {
        BHonWizardTagGenJob tagGenJob = new BHonWizardTagGenJob(this, fileData);
        tagGenJob.submit(null);
    }
  }

  default void generateTagMappingExcel() {
    BHonWizardTagMappingFileGenJob tagMappingJob = new BHonWizardTagMappingFileGenJob(this);
    tagMappingJob.submit(null);
  }

  default void validateHoneywellWizardTags() {
    BHonWizardTagValidationJob tagValidationJob = new BHonWizardTagValidationJob(this);
    tagValidationJob.submit(null);
  }

  default BTerminalAssignmentWidget getTerminalAssignments(BTerminalAssignmentWidget terminalAssignmentWidget) {
    return terminalAssignmentWidget;
  }
  default boolean isSupportTerminalAssignment(){
    return true;
  }

  void setExpertMode(boolean expertMode);
  boolean getExpertMode();

  void setIsWizardInSyncWithConfiguration(boolean isWizardSync);

  boolean getIsWizardInSyncWithConfiguration();

  public int getDefaultTeachOption();

    /**
     * action to get the permission mask for Honeywell wizard.
     * if it is override, it means the device will support to control permission,
     * or else the device will not support to control permission.
      * @return
     */
  default Action getHonWizardPermissionMaskAction() {
        return null;
    }

    /**
     * action to Set the flag of honeywell wizard related components of device
     * @return
     */
    default Action getSetFlagForHonWizardAction() {
        return null;
    }
    default Action getRemoveFlagForHonWizardAction() {
        return null;
    }

    /**
     * Get device and global store permission mask  based on the user.
     * @param user, login user
     * @return permission mask, a string contains two parts, the first part is global store permission mask,
     * the second part is device permission mask.
     */
    default String getHonWizardPermissionMask(BUser user) {
        BUserService userService = (BUserService) Sys.getService(BUserService.TYPE);
        StringBuilder permsMaskString = new StringBuilder();
        if (null != userService) {
            if (null != user) {
                BasicContext userContext = new BasicContext(user);
                BPermissions permissions = (this.getGlobalStore()).getPermissions(userContext);
                if (null != permissions) {
                    permsMaskString.append(permissions.getMask());
                }
                permsMaskString.append(";");
                BPermissions devicePermissions = ((BComponent)this).getPermissions(userContext);
                if(null != permissions) {
                    permsMaskString.append(devicePermissions.getMask());
                }
            }
        }
        return permsMaskString.toString();
    }

    /**
     * control all wizard related components permission based on user permission.
     * includes wizard related components and wizard view
     * @param device, device
     * @param agentList, agent list on device
     * @param context, context
     */
    default void controlHonWizardComponentsBaseOnPermission(BIHoneywellConfigurableDevice device, AgentList agentList, Context context){
        HonWizardPermissionUtil.controlHonWizardComponentsBaseOnPermission(device, agentList, context);
    }

    /**
     * Builds Honeywell wizard permission components, including category and roles
     * category include wizard related cateogyr, device category
     * roles include honWizardRORole and honWizardRWIRole
     */
    default void buildHonWizardPermissionComponents() {
        int wizardCategoryIndex = HonWizardPermissionUtil.createHonWizardCategory(HON_WIZARD_CATEGORY);
        int deviceCategoryIndex = HonWizardPermissionUtil.createHonWizardCategory(HON_DEVICE_CATEGORY);
        if(wizardCategoryIndex >= 0 && deviceCategoryIndex >= 0) {
            HonWizardPermissionUtil.createRolesForWizard(wizardCategoryIndex, deviceCategoryIndex);
        }
    }

    /**
     * Adds honeywell wizard related components, like global store, rule store and selectors into wizard category
     * Add device and IRM config into device category
     * Wizard category is for controlling permission for wizard related components
     * Device category is for controlling permission for device and IRM config, IRM config is for setting expert mode permission
     * @param device, honeywell device
     * @param irmConfig, irm config component
     */
    default void addHonWizardPermissionForDevice(BIHoneywellConfigurableDevice device, BComponent irmConfig) {
        int wizardCategoryIndex = HonWizardPermissionUtil.getHonWizardCategoryIndex(HON_WIZARD_CATEGORY);
        int deviceCategoryIndex = HonWizardPermissionUtil.getHonWizardCategoryIndex(HON_DEVICE_CATEGORY);
        if(wizardCategoryIndex >= 0) {
            HonWizardPermissionUtil.setCategoryForWizardRelatedComponentsWithDevice(device, wizardCategoryIndex);
        }
        if(deviceCategoryIndex >= 0) {
            HonWizardPermissionUtil.setCategoryForHonDeviceAndIrmConfig(device, irmConfig, deviceCategoryIndex);
        }
    }

    String checkAndReturnWizardValidationMessage();
    
    default BHonWizardMetaData getMetaDataComp() {
	  return new BHonWizardMetaData();
	}
    
    default void setMetaDataComp(BHonWizardMetaData metaData) {
    	
    }
    
	default List<BComponent> getPointWithPointSelectors() {
		return new ArrayList<>();
	}
	
	/**
	 * Temporarily stops dynamic updates to the global store for the Honeywell wizard configuration.
	 * Adds a marker property to the device component to indicate updates should be ignored.
	 *
	 * @param source the identifier or reason for stopping updates (used as the marker value)
	 */
	default void stopDynamicUpdatesOfGlobalStore(String source) {
        if(null == ((BComponent) this).get(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE)) {
        	((BComponent) this).add(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE, BString.make(source));
        }
	}
	
	/**
	 * Temporarily stops dynamic updates to the global store for the Honeywell wizard configuration.
	 * Adds a marker property to the device component to indicate updates should be ignored, using the provided context.
	 *
	 * @param source the identifier or reason for stopping updates (used as the marker value)
	 * @param context the context in which the marker property should be added
	 */
	default void stopDynamicUpdatesOfGlobalStore(String source, Context context) {
        if(null == ((BComponent) this).get(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE)) {
        	((BComponent) this).add(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE, BString.make(source), context);
        }
	}

	/**
	 * Resumes dynamic updates to the global store for the Honeywell wizard configuration.
	 * Removes the marker property if the source matches or if a force resume is requested.
	 *
	 * @param source the identifier or reason for resuming updates; can be a force resume constant
	 */
	default void resumeDynamicUpdatesOfGlobalStore(String source) {
		if (null != ((BComponent) this).get(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE)
				&& (source.equals(((BComponent) this).get(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE).toString())
						|| Const.FORCE_RESUME_DYNAMIC_UPDATE.equals(source))) {
			((BComponent) this).remove(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE);
		}
	}
	
	default void clearAllWizardData() {
		stopDynamicUpdatesOfGlobalStore(this.getType().getTypeName());
		try {
			getRuleStore().clearAllRules();
			setGlobalStore(new BHonWizardGlobalStore());
			setMetaDataComp(new BHonWizardMetaData());

			// Clean up all the selectors
			List<BHonWizSelector> allHonWizardSelectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) this);
			for(BHonWizSelector selector : allHonWizardSelectors) {
				if(selector.getParent() != null) {
					((BComponent) selector.getParent()).remove(selector);
				}
			}
			// Reset the flag indicating the wizard is in sync with configuration
			setIsWizardInSyncWithConfiguration(true);
		} catch (Exception e) {
			resumeDynamicUpdatesOfGlobalStore(this.getType().getTypeName());
			throw e;
		}
		resumeDynamicUpdatesOfGlobalStore(this.getType().getTypeName());
	}
	
	default void honWizardSelectorChanged(boolean value, Context context) {}

	default boolean isSlotSelectorSupportedInWizard(BComponent parent) {
		return true;
	}

    /**
     * generate new UUID for global store
     * @param isUseNewGlobalStoreUuid, true means that we will replace global store uuid with application guid
     *                                 false means that we will keep the existing global store uuid
     * @return UUID of global store
     */
    default BUuid getGlobalStoreUuid(boolean isUseNewGlobalStoreUuid) {
        BValue bValue = null;
        if(!isUseNewGlobalStoreUuid){
            bValue = ((BComponent) this).get(Const.BACK_UP_APPLICATION_UUID);
        } else {
            bValue = ((BComponent) this).get(Const.APPLICATION_GUID);
            if(((BComponent) this).get(Const.BACK_UP_APPLICATION_UUID) != null) {
                ((BComponent) this).set(Const.BACK_UP_APPLICATION_UUID, bValue);
            }else {
                ((BComponent) this).add(Const.BACK_UP_APPLICATION_UUID, bValue, Flags.HIDDEN);
            }
        }
        return (BUuid) bValue;
    }

    default BUuid getApplicationGuid() {
        return null;
    }




}
