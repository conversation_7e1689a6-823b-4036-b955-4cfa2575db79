package com.honeywell.applicationhandler.device;

import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BIcon;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.BUuid;

import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.utils.BConstraintRuleShell;
import com.honeywell.applicationhandler.utils.BHonWizardRuleShell;
import com.honeywell.applicationhandler.utils.BTerminalAssignmentValueRuleShell;
import com.honeywell.applicationhandler.utils.BValueRuleShell;
import com.honeywell.applicationhandler.utils.BVisibilityRuleShell;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellWidgetStaticDataUtil;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.tridium.json.JSONObject;

@NiagaraType
@NiagaraAction(name="generateRuleConfig", flags = Flags.HIDDEN)
@NiagaraAction(name="validateRuleConfig")
@NiagaraAction(name="addValueRule")
@NiagaraAction(name="addVisibilityRule")
@NiagaraAction(name="addTerminalAssignmentValueRule")
@NiagaraAction(name="addConstraintRule")
@NiagaraAction(name="clearAllRules")
@NiagaraAction(name="swapOut")
@NiagaraAction(name="swapIn")
@NiagaraProperty(name="swappingOut", type="boolean", defaultValue = "false", flags = Flags.HIDDEN|Flags.READONLY)
@NiagaraProperty(name = "autoSwappingOutMinutesAfterStarted", type = "int", defaultValue = "1", flags = Flags.HIDDEN)
@NiagaraProperty(name = "autoSwappingOutMinutesAfterChanged", type = "int", defaultValue = "60", flags = Flags.HIDDEN)
@NiagaraProperty(name = "notes", type = "BString", defaultValue = "BString.make(\"\")", facets = {
		@Facet(name = "BFacets.FIELD_WIDTH", value = "135"),
		@Facet(name = "BFacets.MULTI_LINE", value = "true")
}, flags = Flags.READONLY)
@NiagaraProperty(
		name = "icon",
		type = "baja:Icon",
		defaultValue = "BIcon.std(\"ghost.png\")",
		flags = Flags.HIDDEN | Flags.TRANSIENT | Flags.READONLY,
		override = true
)
public class BHonWizardRuleStore extends BComponent {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.honeywell.applicationhandler.device.BHonWizardRuleStore(4196205457)1.0$ @*/
/* Generated Tue Sep 09 09:44:33 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "swappingOut"

  /**
   * Slot for the {@code swappingOut} property.
   * @see #getSwappingOut
   * @see #setSwappingOut
   */
  public static final Property swappingOut = newProperty(Flags.HIDDEN | Flags.READONLY, false, null);

  /**
   * Get the {@code swappingOut} property.
   * @see #swappingOut
   */
  public boolean getSwappingOut() { return getBoolean(swappingOut); }

  /**
   * Set the {@code swappingOut} property.
   * @see #swappingOut
   */
  public void setSwappingOut(boolean v) { setBoolean(swappingOut, v, null); }

  //endregion Property "swappingOut"

  //region Property "autoSwappingOutMinutesAfterStarted"

  /**
   * Slot for the {@code autoSwappingOutMinutesAfterStarted} property.
   * @see #getAutoSwappingOutMinutesAfterStarted
   * @see #setAutoSwappingOutMinutesAfterStarted
   */
  public static final Property autoSwappingOutMinutesAfterStarted = newProperty(Flags.HIDDEN, 1, null);

  /**
   * Get the {@code autoSwappingOutMinutesAfterStarted} property.
   * @see #autoSwappingOutMinutesAfterStarted
   */
  public int getAutoSwappingOutMinutesAfterStarted() { return getInt(autoSwappingOutMinutesAfterStarted); }

  /**
   * Set the {@code autoSwappingOutMinutesAfterStarted} property.
   * @see #autoSwappingOutMinutesAfterStarted
   */
  public void setAutoSwappingOutMinutesAfterStarted(int v) { setInt(autoSwappingOutMinutesAfterStarted, v, null); }

  //endregion Property "autoSwappingOutMinutesAfterStarted"

  //region Property "autoSwappingOutMinutesAfterChanged"

  /**
   * Slot for the {@code autoSwappingOutMinutesAfterChanged} property.
   * @see #getAutoSwappingOutMinutesAfterChanged
   * @see #setAutoSwappingOutMinutesAfterChanged
   */
  public static final Property autoSwappingOutMinutesAfterChanged = newProperty(Flags.HIDDEN, 60, null);

  /**
   * Get the {@code autoSwappingOutMinutesAfterChanged} property.
   * @see #autoSwappingOutMinutesAfterChanged
   */
  public int getAutoSwappingOutMinutesAfterChanged() { return getInt(autoSwappingOutMinutesAfterChanged); }

  /**
   * Set the {@code autoSwappingOutMinutesAfterChanged} property.
   * @see #autoSwappingOutMinutesAfterChanged
   */
  public void setAutoSwappingOutMinutesAfterChanged(int v) { setInt(autoSwappingOutMinutesAfterChanged, v, null); }

  //endregion Property "autoSwappingOutMinutesAfterChanged"

  //region Property "notes"

  /**
   * Slot for the {@code notes} property.
   * @see #getNotes
   * @see #setNotes
   */
  public static final Property notes = newProperty(Flags.READONLY, BString.make(""), BFacets.make(BFacets.make(BFacets.FIELD_WIDTH, 135), BFacets.make(BFacets.MULTI_LINE, true)));

  /**
   * Get the {@code notes} property.
   * @see #notes
   */
  public String getNotes() { return getString(notes); }

  /**
   * Set the {@code notes} property.
   * @see #notes
   */
  public void setNotes(String v) { setString(notes, v, null); }

  //endregion Property "notes"

  //region Property "icon"

  /**
   * Slot for the {@code icon} property.
   * @see #getIcon
   * @see #setIcon
   */
  public static final Property icon = newProperty(Flags.HIDDEN | Flags.TRANSIENT | Flags.READONLY, BIcon.std("ghost.png"), null);
  public void setIcon(BIcon v) { set(icon, v, null); }
  //endregion Property "icon"

  //region Action "generateRuleConfig"

  /**
   * Slot for the {@code generateRuleConfig} action.
   * @see #generateRuleConfig()
   */
  public static final Action generateRuleConfig = newAction(Flags.HIDDEN, null);

  /**
   * Invoke the {@code generateRuleConfig} action.
   * @see #generateRuleConfig
   */
  public void generateRuleConfig() { invoke(generateRuleConfig, null, null); }

  //endregion Action "generateRuleConfig"

  //region Action "validateRuleConfig"

  /**
   * Slot for the {@code validateRuleConfig} action.
   * @see #validateRuleConfig()
   */
  public static final Action validateRuleConfig = newAction(0, null);

  /**
   * Invoke the {@code validateRuleConfig} action.
   * @see #validateRuleConfig
   */
  public void validateRuleConfig() { invoke(validateRuleConfig, null, null); }

  //endregion Action "validateRuleConfig"

  //region Action "addValueRule"

  /**
   * Slot for the {@code addValueRule} action.
   * @see #addValueRule()
   */
  public static final Action addValueRule = newAction(0, null);

  /**
   * Invoke the {@code addValueRule} action.
   * @see #addValueRule
   */
  public void addValueRule() { invoke(addValueRule, null, null); }

  //endregion Action "addValueRule"

  //region Action "addVisibilityRule"

  /**
   * Slot for the {@code addVisibilityRule} action.
   * @see #addVisibilityRule()
   */
  public static final Action addVisibilityRule = newAction(0, null);

  /**
   * Invoke the {@code addVisibilityRule} action.
   * @see #addVisibilityRule
   */
  public void addVisibilityRule() { invoke(addVisibilityRule, null, null); }

  //endregion Action "addVisibilityRule"

  //region Action "addTerminalAssignmentValueRule"

  /**
   * Slot for the {@code addTerminalAssignmentValueRule} action.
   * @see #addTerminalAssignmentValueRule()
   */
  public static final Action addTerminalAssignmentValueRule = newAction(0, null);

  /**
   * Invoke the {@code addTerminalAssignmentValueRule} action.
   * @see #addTerminalAssignmentValueRule
   */
  public void addTerminalAssignmentValueRule() { invoke(addTerminalAssignmentValueRule, null, null); }

  //endregion Action "addTerminalAssignmentValueRule"

  //region Action "addConstraintRule"

  /**
   * Slot for the {@code addConstraintRule} action.
   * @see #addConstraintRule()
   */
  public static final Action addConstraintRule = newAction(0, null);

  /**
   * Invoke the {@code addConstraintRule} action.
   * @see #addConstraintRule
   */
  public void addConstraintRule() { invoke(addConstraintRule, null, null); }

  //endregion Action "addConstraintRule"

  //region Action "clearAllRules"

  /**
   * Slot for the {@code clearAllRules} action.
   * @see #clearAllRules()
   */
  public static final Action clearAllRules = newAction(0, null);

  /**
   * Invoke the {@code clearAllRules} action.
   * @see #clearAllRules
   */
  public void clearAllRules() { invoke(clearAllRules, null, null); }

  //endregion Action "clearAllRules"

  //region Action "swapOut"

  /**
   * Slot for the {@code swapOut} action.
   * @see #swapOut()
   */
  public static final Action swapOut = newAction(0, null);

  /**
   * Invoke the {@code swapOut} action.
   * @see #swapOut
   */
  public void swapOut() { invoke(swapOut, null, null); }

  //endregion Action "swapOut"

  //region Action "swapIn"

  /**
   * Slot for the {@code swapIn} action.
   * @see #swapIn()
   */
  public static final Action swapIn = newAction(0, null);

  /**
   * Invoke the {@code swapIn} action.
   * @see #swapIn
   */
  public void swapIn() { invoke(swapIn, null, null); }

  //endregion Action "swapIn"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardRuleStore.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
	private ScheduledFuture<?> scheduledTask;

    @Override
	public boolean isParentLegal(BComponent parent) {
		return (parent instanceof BIHoneywellConfigurableDevice);
	}
  
	@Override
	public boolean isChildLegal(BComponent child) {
		return (child instanceof BHonWizardRuleShell);
	}
	
	@Override
	public void added(Property property, Context context) {
		super.added(property, context);
		if (this.get(property) instanceof BVisibilityRuleShell) {
			BVisibilityRuleShell visibilityRuleShell = (BVisibilityRuleShell) this.get(property);
			BString visibilityRule = BString.make(VISIBILITY_RULE);
			String visibilityRuleShellName = visibilityRuleShell.getName();
			this.remove(property);
			this.add(visibilityRuleShellName, visibilityRule, 0,
					BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);

		} else if (this.get(property) instanceof BValueRuleShell) {
			BValueRuleShell valueRuleShell = (BValueRuleShell) this.get(property);
			BString valueRule = BString.make(VALUE_RULE);
			String valueRuleShellName = valueRuleShell.getName();
			this.remove(property);
			this.add(valueRuleShellName, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)),
					null);
		} else if (this.get(property) instanceof BTerminalAssignmentValueRuleShell) {
			BTerminalAssignmentValueRuleShell terminalValueRuleShell = (BTerminalAssignmentValueRuleShell) this.get(property);
			BString valueRule = BString.make(TERMINAL_ASSIGNMENT_VALUE_RULE);
			String valueRuleShellName = terminalValueRuleShell.getName();
			this.remove(property);
			this.add(valueRuleShellName, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)),
					null);
		} else if(this.get(property) instanceof BConstraintRuleShell) {
			BConstraintRuleShell constraintRuleShell = (BConstraintRuleShell) this.get(property);
			BString constraintRule = BString.make(CONSTRAINT_RULE);
			String constraintRuleShellName = constraintRuleShell.getName();
			this.remove(property);
			this.add(constraintRuleShellName, constraintRule, 0,
					BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
		}
	}
  
	public void doGenerateRuleConfig() {
		BHonWizardRuleParserJob job = new BHonWizardRuleParserJob((BIHoneywellConfigurableDevice) this.getParent(), this, false);
		job.submit(null);
	}

	public void doValidateRuleConfig() {
		BHonWizardRuleParserJob job = new BHonWizardRuleParserJob((BIHoneywellConfigurableDevice) this.getParent(), this, true);
		job.submit(null);
	}


	public void doAddValueRule() {
		BString valueRule = BString.make(VALUE_RULE);
		this.add(VALUE_RULE_NAME, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}

	public void doAddVisibilityRule() {
		BString visibilityRule = BString.make(VISIBILITY_RULE);
		this.add(VISIBILITY_RULE_NAME, visibilityRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}

	public void doAddTerminalAssignmentValueRule() {
		BString terminalAssignmentValueRule = BString.make(TERMINAL_ASSIGNMENT_VALUE_RULE);
		this.add(TERMINAL_ASSIGNMENT_VALUE_RULE_NAME, terminalAssignmentValueRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}
	
	public void doAddConstraintRule() {
		BString constraintRule = BString.make(CONSTRAINT_RULE);
		this.add(CONSTRAINT_RULE_NAME, constraintRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}
	
	public void doClearAllRules() {
        for (Property rule : this.getDynamicPropertiesArray()) {
        	if(this.get(rule) instanceof BString) {
        		this.remove(rule);
        	}
        }
	}


	public void doSwapOut() {
		if(this.getSwappingOut()){
			return;
		}
		HoneywellWidgetStaticDataUtil.saveRuleStoreToJson(this, selectorDevice);
		Property[] dynamicPropertiesArray = this.getDynamicPropertiesArray();
		for (Property rule : dynamicPropertiesArray) {
			if (this.get(rule) instanceof BString) {
				this.remove(rule);
			}
		}
		setSwappingOut(true);
	}


	/**
	 * show/hide actions based on swapped out status
	 * @param isHidden
	 */
	public void showHideActions(boolean isHidden){
	    if(isHidden){
			this.setFlags(generateRuleConfig, this.getFlags(generateRuleConfig) | Flags.HIDDEN);
			this.setFlags(validateRuleConfig, this.getFlags(validateRuleConfig) | Flags.HIDDEN);
			this.setFlags(addValueRule, this.getFlags(addValueRule) | Flags.HIDDEN);
			this.setFlags(addVisibilityRule, this.getFlags(addVisibilityRule) | Flags.HIDDEN);
			this.setFlags(addTerminalAssignmentValueRule, this.getFlags(addTerminalAssignmentValueRule) | Flags.HIDDEN);
			this.setFlags(addConstraintRule, this.getFlags(addConstraintRule) | Flags.HIDDEN);
			this.setFlags(clearAllRules, this.getFlags(clearAllRules) | Flags.HIDDEN);
			this.setFlags(swapOut, this.getFlags(swapOut) | Flags.HIDDEN);
			this.setFlags(swapIn, this.getFlags(swapIn) & ~Flags.HIDDEN);
			setIcon(BIcon.std("ghost.png"));

		}else{
			this.setFlags(generateRuleConfig, this.getFlags(generateRuleConfig) & ~Flags.HIDDEN);
			this.setFlags(validateRuleConfig, this.getFlags(validateRuleConfig) & ~Flags.HIDDEN);
			this.setFlags(addValueRule, this.getFlags(addValueRule) & ~Flags.HIDDEN);
			this.setFlags(addVisibilityRule, this.getFlags(addVisibilityRule) & ~Flags.HIDDEN);
			this.setFlags(addTerminalAssignmentValueRule, this.getFlags(addTerminalAssignmentValueRule) & ~Flags.HIDDEN);
			this.setFlags(addConstraintRule, this.getFlags(addConstraintRule) & ~Flags.HIDDEN);
			this.setFlags(clearAllRules, this.getFlags(clearAllRules) & ~Flags.HIDDEN);
			this.setFlags(swapOut, this.getFlags(swapOut) & ~Flags.HIDDEN);
			this.setFlags(swapIn, this.getFlags(swapIn)| Flags.HIDDEN);
			setIcon(BIcon.std("objectFolder.png"));
		}
	}
	public void doSwapIn() {
		if(!this.getSwappingOut()){
			return;
		}
		BUuid globalStoreUuid = selectorDevice.getGlobalStoreUuid(false);
		if(null == globalStoreUuid){
			return;
		}
		JSONObject wizardRuleStoreJsonObj = HoneywellWidgetStaticDataUtil.loadRuleStoreFromJson(globalStoreUuid);
		if(null != wizardRuleStoreJsonObj) {
			for (String key : wizardRuleStoreJsonObj.keySet()) {
				BString ruleValue = BString.make(wizardRuleStoreJsonObj.getString(key));
				this.add(key, ruleValue, 0,
						BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
			}
		}
		setSwappingOut(false);
	}
	
    public Map<String, String> getAllRules() {
        Map<String, String> rules = new HashMap<>();
		if(getSwappingOut()) {
			BUuid globalStoreUuid = selectorDevice.getGlobalStoreUuid(false);
			if(null == globalStoreUuid) {
				return rules;
			}
			JSONObject wizardRuleStoreJson = HoneywellWidgetStaticDataUtil.loadRuleStoreFromJson(globalStoreUuid);
			if(null != wizardRuleStoreJson) {
				for (String key : wizardRuleStoreJson.keySet()) {
					rules.put(key, wizardRuleStoreJson.getString(key));
				}
			}
		} else {
			for (Property rule : this.getDynamicPropertiesArray()) {
				if (this.get(rule) instanceof BString) {
					rules.put(rule.getName(), ((BString) this.get(rule)).getString());
				}
			}
		}
        return rules;
    }

	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
		if (null != selectorDevice) {
			//only change rules, we will trigger scheduler to swap out rule store automatically
			if((this.get(property) instanceof BString) && !property.equals(notes)) {
				selectorDevice.honWizardSelectorChanged(true, context);
				if (scheduledTask != null && !scheduledTask.isDone()) {
					scheduledTask.cancel(false);
				}
				scheduledTask = scheduler.schedule(this::doSwapOut, getAutoSwappingOutMinutesAfterChanged(), TimeUnit.MINUTES);
			}
			if(property.equals(autoSwappingOutMinutesAfterChanged)){
				String notes = LexiconUtil.getLexicon().getText("honWizard.rulestore.swapout.notes",
						this.getAutoSwappingOutMinutesAfterChanged());
				setNotes(notes);
			}
			if(property.equals(swappingOut)){
				showHideActions(getSwappingOut());
			}

		}
	}

	@Override
	public void started() throws Exception {
		super.started();
		selectorDevice = HoneywellConfigurableDeviceUtil.getDevice(this.getParent());
		if(getSwappingOut()){
			showHideActions(true);
		}else{
			showHideActions(false);
		}
		scheduledTask = scheduler.schedule(this::doSwapOut, getAutoSwappingOutMinutesAfterStarted(), TimeUnit.MINUTES);
		String notes = LexiconUtil.getLexicon().getText("honWizard.rulestore.swapout.notes",
				this.getAutoSwappingOutMinutesAfterChanged());
		setNotes(notes);

	}
	@Override
	public void stopped() throws Exception {
		super.stopped();
		if(scheduler != null){
			AccessController.doPrivileged((PrivilegedAction<Void>) () -> {
				scheduler.shutdown();
				return null;
			});
		}
	}




    
	private static final String VISIBILITY_RULE 		= "ADD VISIBILITY RULE TO: <PrimaryTab>: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String VALUE_RULE 				= "ADD VALUE RULE TO: <PrimaryTab>: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String VISIBILITY_RULE_NAME 	= "VisibilityRule?";
	private static final String VALUE_RULE_NAME 		= "ValueRule?";
	private static final String TERMINAL_ASSIGNMENT_VALUE_RULE 		= "ADD TERMINAL_ASSIGNMENT_VALUE RULE TO: Terminal Assignment: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String TERMINAL_ASSIGNMENT_VALUE_RULE_NAME 	= "TerminalAssignmentValueRule?";

	private static final String CONSTRAINT_RULE 		= "ADD CONSTRAINT RULE TO: <PrimaryTab>: WITH DEADBAND AS: <deadband object> \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<limit action>";
	private static final String CONSTRAINT_RULE_NAME 	= "ConstraintRule?";


	BIHoneywellConfigurableDevice selectorDevice = null;

}
