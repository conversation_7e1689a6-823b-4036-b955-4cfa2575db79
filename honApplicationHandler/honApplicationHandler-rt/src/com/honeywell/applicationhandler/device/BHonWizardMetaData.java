/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BStation;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>hus<PERSON>
 * @since Jun 10, 2025
 */
@NiagaraType
public class BHonWizardMetaData extends BComponent {
/*+ ------------ B<PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.BHonWizardMetaData(2979906276)1.0$ @*/
/* Generated Tue Jun 10 15:57:05 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardMetaData.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public void descendantsStarted() throws Exception {
		super.descendantsStarted();
		if (!Sys.isStation()) {
			return;
		}
	
		// Always hide the wizard meta data component
		BComponent parent = (BComponent) getParent();
		parent.setFlags(getPropertyInParent(), Flags.HIDDEN);
	}
	
	@Override
	public void started() throws Exception {
		super.started();
		selectorDevice = HoneywellConfigurableDeviceUtil.getDevice(this.getParent());
	}

	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
		if (null != selectorDevice) {
			selectorDevice.honWizardSelectorChanged(true, context);
		}
	}
	
	
	static {
		Map<String, Integer> propertyToId = new HashMap<>();
		propertyToId.put("Timezone", 1);
		propertyToId.put("DuctAreaCalculator_height", 2);
		propertyToId.put("DuctAreaCalculator_width", 3);
		propertyToId.put("DuctAreaCalculator_radius", 4);
		propertyToId.put("DuctAreaCalculator_area", 5);
		propertyToId.put("DuctAreaCalculator_ducttype", 6);
		propertyToId.put("DuctAreaCalculator_dimensionunit", 7);
		METADATA_PROPERTY_TO_ID = Collections.unmodifiableMap(propertyToId);

		Map<Integer, String> idToProperty = new HashMap<>();
		idToProperty.put(1, "Timezone");
		idToProperty.put(2, "DuctAreaCalculator_height");
		idToProperty.put(3, "DuctAreaCalculator_width");
		idToProperty.put(4, "DuctAreaCalculator_radius");
		idToProperty.put(5, "DuctAreaCalculator_area");
		idToProperty.put(6, "DuctAreaCalculator_ducttype");
		idToProperty.put(7, "DuctAreaCalculator_dimensionunit");
		METADATA_ID_TO_PROPERTY = Collections.unmodifiableMap(idToProperty);
	}

	public static Map<String, Integer> getMetadataPropertyToId() {
		return METADATA_PROPERTY_TO_ID;
	}

	public static Map<Integer, String> getMetadataIdToProperty() {
		return METADATA_ID_TO_PROPERTY;
	}
	
	private static final Map<String, Integer> METADATA_PROPERTY_TO_ID;
	private static final Map<Integer, String> METADATA_ID_TO_PROPERTY;
	BIHoneywellConfigurableDevice selectorDevice = null;
  
}
