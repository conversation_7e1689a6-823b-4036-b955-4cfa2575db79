/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.points;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jan 29, 2025
 */
@NiagaraType
public interface BIHonWizardEnumSchedule extends BIHonWizardPoint{
/*+ ------------ B<PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.points.BIHonWizardEnumSchedule(2979906276)1.0$ @*/
/* Generated Wed Jan 29 07:47:56 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  Type TYPE = Sys.loadType(BIHonWizardEnumSchedule.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
