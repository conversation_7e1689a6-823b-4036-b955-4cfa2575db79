/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.device.points;

import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BInterface;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
public interface BIHonWizardPoint extends BInterface {
  /*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
  /*@ $com.honeywell.applicationhandler.device.points.BIHonWizardPoint(2979906276)1.0$ @*/
  /* Generated Wed May 22 14:12:44 IST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  Type TYPE = Sys.loadType(BIHonWizardPoint.class);

  /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  BBacnetObjectIdentifier getBacnetObjectId();
  String getName();
  void setWizardComponentOrd(BOrd ord);
  BOrd getWizardComponentOrd();
  String updateValueToPropSheet(BValue val,Context cx) throws Exception;

    /**
     * check if the ord target is a dynamic widget component base. it will be called in honIrmControl
      * @return
     */
  default boolean isDynamicWidgetComponentBase() {
        if (getWizardComponentOrd() == null || getWizardComponentOrd().isNull()) {
            return false;
        }
        try{
            return getWizardComponentOrd().resolve(Sys.getStation()).get() instanceof BDynamicWidgetComponentBase;
        }catch (Exception e) {
            return false;
        }
    }

    /**
     * check if the ord target is a schedule page widget. it will be called in honIrmControl
     * @return
     */
    default boolean isSchedulePageWidget() {
        if (getWizardComponentOrd() == null || getWizardComponentOrd().isNull()) {
            return false;
        }
        try{
            return getWizardComponentOrd().resolve(Sys.getStation()).get() instanceof BSchedulePageWidget;
        }catch (Exception e) {
            return false;
        }
    }
}
