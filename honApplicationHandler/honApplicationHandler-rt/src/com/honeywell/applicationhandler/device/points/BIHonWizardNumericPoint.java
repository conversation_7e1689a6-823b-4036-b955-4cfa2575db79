/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.device.points;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

@NiagaraType
public interface BIHonWizardNumericPoint extends BIHonWizardPoint {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.points.BIHonWizardPoint(2979906276)1.0$ @*/
/* Generated Wed May 22 14:12:44 IST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  Type TYPE = Sys.loadType(BIHonWizardNumericPoint.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  double getValue();
  double getDefault();
  BUnit getUnit();
  String setUnitToPropSheet(BUnit unit,Context cx) throws Exception;
}
