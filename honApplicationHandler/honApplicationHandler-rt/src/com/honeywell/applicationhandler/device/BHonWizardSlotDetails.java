/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device;

import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.Collection;
import java.util.Map;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>hus<PERSON>
 * @since Feb 8, 2025
 */
@NiagaraType
public class BHonWizardSlotDetails extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.BHonWizardSlotDetails(2979906276)1.0$ @*/
/* Generated Sat Feb 08 16:09:13 IST 2025 by Slot-o-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardSlotDetails.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	@Override
	public boolean isParentLegal(BComponent parent) {
		return (parent instanceof BIHoneywellConfigurableDevice);
	}

    public void updateSlotDetails(Map<BOrd, String> updatedSlotDetails) {
        if(updatedSlotDetails.isEmpty()) {
            return;
        }

        Property[] existingProps = getDynamicPropertiesArray();
        Collection<String> newSourceNames = updatedSlotDetails.values();
        for (Property prop : existingProps) {
            String sourcePropertyName = get(prop).toString();
            if(newSourceNames.contains(sourcePropertyName)) {
                remove(prop);
            }
        }
        updatedSlotDetails.forEach((ord, sourceName) -> add(SlotPath.escape(ord.toString()), BString.make(sourceName)));
    }
}
