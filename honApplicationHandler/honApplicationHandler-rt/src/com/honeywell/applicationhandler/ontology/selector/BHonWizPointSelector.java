/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ontology.selector;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Tag;
import javax.baja.tag.TagInfo;
import javax.baja.tagdictionary.BTagDictionaryService;
import javax.baja.tagdictionary.BTagInfo;

import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;

/**
 * <AUTHOR> Sun
 */
@NiagaraType
@SuppressWarnings({
        "squid:MaximumInheritanceDepth",
        "squid:S2160",
        "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizPointSelector extends BHonWizSelector {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ontology.BHonWizSelector(2979906276)1.0$ @*/
/* Generated Mon Jan 13 19:00:34 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizPointSelector.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  static Logger logger = HoneywellDeviceWizardLogger.getLogger();
  
    @Override
    public boolean isParentLegal(BComponent parent) {
        if(!Sys.isStation()) {
            return super.isParentLegal(parent);
        }

        return parent instanceof BIHonWizardPoint;
    }

    @Override
    protected BDynamicWidgetComponentBase getWidgetComponent() {
        BComponent device = (BComponent) selectorDevice;
        if(device == null) {
            return null;
        }
        BIHonWizardPoint parent = (BIHonWizardPoint) selectorParent;
        BOrd wizardComponentOrd = parent.getWizardComponentOrd();
        if(wizardComponentOrd.equals(BOrd.make("")) || wizardComponentOrd.equals(BOrd.DEFAULT)) {
            return null;
        }

        try {
        	return (BDynamicWidgetComponentBase) wizardComponentOrd.resolve(device).get();
        } catch (Exception e) {
        	logger.finest("Error resolving wizard component: " + wizardComponentOrd.toString());
		}
        
        return null;
    }

    @Override
    public BEnumRange getTargetEnumRange() {
        BIHonWizardPoint parent = (BIHonWizardPoint) selectorParent;
        if(parent instanceof BIHonWizardBooleanPoint) {
            return ((BIHonWizardBooleanPoint)parent).getEnumRange();
        }
        else if(parent instanceof BIHonWizardEnumPoint) {
            return ((BIHonWizardEnumPoint)parent).getEnumRange();
        }

        return null;
    }
    
	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
		// Propagate the change to the parent component
	}
    
}
