/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ontology.selector;

import static com.honeywell.applicationhandler.common.Constants.DUCT_AREA_CALCULATOR_TYPE;
import static com.honeywell.applicationhandler.common.Constants.HEADING_LABEL;
import static com.honeywell.applicationhandler.common.Constants.TIMEZONE_TYPE;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.TAG_NAME_TO_PROPERTY_MAP;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDouble;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BStation;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.IPropertyValidator;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Id;
import javax.baja.tag.Tag;
import javax.baja.tag.TagInfo;
import javax.baja.tagdictionary.BTagDictionaryService;
import javax.baja.tagdictionary.BTagInfo;

import com.honeywell.applicationhandler.context.MasterSyncContext;
import com.honeywell.applicationhandler.device.BHonWizardSlotDetails;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.generate.HonWizGlobalStoreGenerator;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.exceptions.GlobalStoreGenerationExcpetion;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.palette.SelectorNotAllowedException;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.HoneywellWidgetStaticDataUtil;
import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;
import com.honeywell.applicationhandler.validation.Constants;
import com.honeywell.applicationhandler.validation.tags.TagValidation;
import com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BTimeZone;
import com.honeywell.applicationhandler.widgetcomponents.json.PropertyTag;

/**
 * <AUTHOR> Sun
 */
@NiagaraType
public abstract class BHonWizSelector extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ontology.selector.BHonWizSelector(2979906276)1.0$ @*/
/* Generated Tue Jan 14 19:39:42 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizSelector.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();
    public static final BasicContext TAGS_UPDATED_IN_SELCTOR = new BasicContext();

    boolean isMultiPropertiesChange = false;

    /**
     * this variable for checking if the context is dynamic sync enabled or not.
     * if the selector is created because of below operations, isContextDynamicSyncEnabled value will be set to false
     * 1) perform duplicate device or copy/paste device
     * 2) perform master/sync device
     * 3) perform duplicate selector or copy/paste selector
     * once user update, remove or add a tag in the selector, isContextDynamicSyncEnabled value will be set to true, sync thread will be allowed to start.
     */
    boolean isDynamicSyncEnabledAtSelectorStart = true;

    boolean isDynamicSyncEnabledAtTagChanges = true;


    boolean isChangedAsUnitConversion = false;

    GlobalStoreDynamicSyncTask addedTimerTask = null;
    GlobalStoreDynamicSyncTask removedTimerTask = null;
    BComponent selectorParent = null;
    BIHoneywellConfigurableDevice selectorDevice = null;

    static Timer addedTimer = new Timer();
    static Timer removedTimer = new Timer();


    protected abstract BDynamicWidgetComponentBase getWidgetComponent();
    public abstract BEnumRange getTargetEnumRange();

    private boolean shouldDynamicallyUpdateWidgetComponent() {
        if(selectorDevice == null) {
            return false;
        }
        if(!((BComponent)selectorDevice).isRunning()){
            return false;
        }

        boolean isRunningInStation = Sys.isStation() && Sys.isStationStarted() && Sys.getStation().isRunning();
        if(!isRunningInStation) {
            return false;
        }

        // if this device has IGNORE_WIZARD_CONFIGURATION_UPDATE flag, we should not dynamically update the widget component
        BValue igoreWizardConfigurationUpdate = ((BComponent)selectorDevice).get(Const.IGNORE_WIZARD_CONFIGURATION_UPDATE);
        return !(igoreWizardConfigurationUpdate instanceof BString) && selectorDevice.getExpertMode();
    }

    @Override
    public IPropertyValidator getPropertyValidator(Property property, Context context){
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            throw new SelectorNotAllowedException("error.edit.tag.not.allowed.expertmode.disable");
        }
        return super.getPropertyValidator(property, context);
    }

    @Override
    public IPropertyValidator getPropertyValidator(Property[] properties, Context context) {
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
           throw new SelectorNotAllowedException("error.edit.tag.not.allowed.expertmode.disable");
        }
        if(!(context instanceof MasterSyncContext)) {
            isMultiPropertiesChange = true;
        }
        return super.getPropertyValidator(properties, context);
    }

    @Override
	public void started() {
		selectorParent = (BComponent) getParent();
		selectorDevice = HoneywellConfigurableDeviceUtil.getDevice(selectorParent);
		if(this instanceof BHonWizSlotSelector) {
			if (null == selectorParent.get(TIMEZONE_TYPE)
					&& TIMEZONE_TYPE.equals(this.get(SlotPath.escape(BHonWizardTag.WGTTYPE_TAG.toString())).toString())) {
				selectorParent.add(TIMEZONE_TYPE, BTimezoneEnum.make(0), Flags.READONLY);
			}
		}
		if (Sys.isStation() && Sys.isStationStarted() && Sys.getStation().isRunning() && shouldDynamicallyUpdateWidgetComponent() && isDynamicSyncEnabledAtSelectorStart) {
			logger.finest("started called ------------------");
			// if added selector is in an existing page, then we should
			// refresh the page component itself
			// if added selector is in a new page, then we should create a
			// new page component
			// if added selector is a slot selector, then we should call
			// device.processHonWizardSlotUpdates
			String threadName = "startedSyncThread_" + getHandleOrd().toString();
			startNewGlobalStoreDynamicSyncTask(new Property[0], threadName, SyncType.STARTED);
		}
	}

    @Override
	public void stopped() {
		if (Sys.isStation() && Sys.isStationStarted() && Sys.getStation().isRunning() && shouldDynamicallyUpdateWidgetComponent()) {
			logger.finest("stopped called ------------------");

			// if removed selector is a RangeSlider then we should remove
			// both widget components of RangeSlider
			// if removed selector is a DuctAreaCalculator then we should
			// check if its role is area, if yes we should remove all
			// components of DuctAreaCalculator,
			// otherwise we should remove this widget component and then
			// create a default one with same role.
			// if removed selector is some other type then we should remove
			// this widget component directly
			String threadName = "stoppedSyncThread_" + getHandleOrd().toString();
			startNewGlobalStoreDynamicSyncTask(new Property[0], threadName, SyncType.STOPPED);
		}

		if (Sys.isStation() && Sys.isStationStarted() && !Sys.getStation().isRunning()) {
			addedTimer.cancel();
			removedTimer.cancel();
		}
	}

    @Override
    public void changed(Property property, Context context) {
		generateUuidForSelectorChange(property, context);
        setDynamicSyncEnabledByContext(context);
        // When performing unit conversion, the min and max values in tags are updated, which triggers the change detection mechanism.
        // In this case, the synchronization thread should not be started, as the min and max values are already handled within the unit conversion logic.
        // Furthermore, triggering the sync thread would cause validation errors, since it validates all tags.
        // This happens because the current value is updated after the min and max values, so it may temporarily fall outside the new min and max range during validation.
        if(isChangedAsUnitConversion){
            return;
        }
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            return;
        }
        BIDataValue widgetTypeTagValue = checkAndGetWidgetTypeTagValue(property);
        if(null != widgetTypeTagValue) {
            HoneywellWidgetTagUtil.changeTagsByWidgetType(widgetTypeTagValue.toString(), this);
        }

        if(shouldDynamicallyUpdateWidgetComponent() && !isMultiPropertiesChange && isDynamicSyncEnabledAtTagChanges) {
            logger.finest("changed called ------------------");

            String threadName = "changedSyncThread_" + getHandleOrd().toString() + "_" + property.getName();
            startNewGlobalStoreDynamicSyncTask(new Property[]{property}, threadName, SyncType.CHANGED);
        }
        if(Sys.isStation() && Sys.isStationStarted() && Sys.getStation().isRunning() && this.getParent() != null) {
			((BComponent) this.getParent()).changed(this.getPropertyInParent(), TAGS_UPDATED_IN_SELCTOR);
		}
    }
    @Override
    public void checkRemove(Property property, Context context) {
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            throw new SelectorNotAllowedException("error.delete.tag.not.allowed.expertmode.disable");
        }
        super.checkRemove(property, context);

    }

    @Override
    public void checkAdd(String name, BValue value, int flags, BFacets facets, Context context)  {
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            throw new SelectorNotAllowedException("error.add.tag.not.allowed.expertmode.disable");
        }
        super.checkAdd(name, value, flags, facets, context);

    }
    @Override
    public void added(Property property, Context context) {
        generateUuidForSelectorChange(property, context);
        setDynamicSyncEnabledByContext(context);
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            return;
        }
        if(shouldDynamicallyUpdateWidgetComponent() && isDynamicSyncEnabledAtTagChanges) {
            logger.finest("added called ------------------");


            addedTimerTask = refreshTask(property, addedTimerTask, "addedSyncThread_", SyncType.ADDED);
            addedTimer.schedule(addedTimerTask, 500);
        }
    }

    @Override
    public void removed(Property property, BValue oldValue, Context context) {
        generateUuidForSelectorChange(property, context);
        setDynamicSyncEnabledByContext(context);
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            return;
        }
        if(shouldDynamicallyUpdateWidgetComponent() && isDynamicSyncEnabledAtTagChanges) {
            logger.finest("removed called ------------------");

            removedTimerTask = refreshTask(property, removedTimerTask, "removedSyncThread_", SyncType.REMOVED);
            removedTimer.schedule(removedTimerTask, 500);
        }
    }
    
  
	public void generateUuidForSelectorChange(Property property, Context context) {
		if (Sys.isStation() && Sys.isStationStarted() && Sys.getStation().isRunning() && null != selectorDevice) {
            String name = SlotPath.unescape(property.getName());
            if (name.startsWith(Const.HON_WIZARD_TAG_NAMESPACE + ":")) {
                selectorDevice.honWizardSelectorChanged(true, context);
            }
		}
	}
    
    
    @Override
    public void batchChanged(Property[] properties, Context context) {
        isMultiPropertiesChange = false;
        setDynamicSyncEnabledByContext(context);
        if(isChangedAsUnitConversion){
            return;
        }
        if(null != selectorDevice && !selectorDevice.getExpertMode()){
            return;
        }
        if(shouldDynamicallyUpdateWidgetComponent() && isDynamicSyncEnabledAtTagChanges) {
            logger.finest("batchChanged called ------------------");

            StringBuilder threadName = new StringBuilder("changedSyncThread_" + getHandleOrd().toString() + "_");
            for (Property property : properties) {
                threadName.append(property.getName()).append("_");
            }

            startNewGlobalStoreDynamicSyncTask(properties, threadName.toString(), SyncType.CHANGED);
        }
    }

    /**
     * dynamic sync is enabled in below context, context is from added and changed method.
     * copying context is because duplicate or copy/paste
     * master sync context is because master/sync device
     * decoding context is because start station
     * if duplicate or copy/paste selector, as added method will be triggered for each tag, consider to sync issue, we won't start sync thread for each tag
     * in started method, we will trigger sync thread in start.
     * if tag changes, like add, remove, change tags, it will trigger sync thread in separate method, because started of selector won't be called
     * if it is because master/sync, decoding, in started of selector, sync thread won't be triggered
     * @param context
     * @return
     */
    private void setDynamicSyncEnabledByContext(Context context) {
        isDynamicSyncEnabledAtSelectorStart = context != null && context.equals(Context.copying);
        isDynamicSyncEnabledAtTagChanges =  (context == null) || (!(context instanceof MasterSyncContext) && !context.equals(Context.decoding) && !context.equals(Context.copying));
    }


    public void setChangedAsUnitConversion(boolean changedAsUnitConversion) {
        isChangedAsUnitConversion = changedAsUnitConversion;
    }


    private GlobalStoreDynamicSyncTask refreshTask(Property property, GlobalStoreDynamicSyncTask task, String threadPrefix, SyncType syncType) {
        Property[] properties = new Property[]{property};
        if(task != null) {
            task.cancel();

            Property[] previousProperties = task.getChangedProperties();
            if(previousProperties.length > 0) {
                Property[] newProperties = new Property[previousProperties.length + 1];
                System.arraycopy(previousProperties, 0, newProperties, 0, previousProperties.length);
                newProperties[newProperties.length - 1] = property;
                properties = newProperties;
            }
        }
        String threadName = threadPrefix + getHandleOrd().toString() + "_" + property.getName();
        return new GlobalStoreDynamicSyncTask(selectorDevice, this, properties, syncType, threadName);
    }

    private void startNewGlobalStoreDynamicSyncTask(Property[] properties, String threadName, SyncType syncType) {
        GlobalStoreDynamicSyncTask task = new GlobalStoreDynamicSyncTask(selectorDevice, this, properties, syncType, threadName);
        Thread changedSyncThread = new Thread(task, threadName);
        changedSyncThread.start();
    }

    static boolean isHonWizTag(Property property) {
        int defaultFlag = property.getDefaultFlags();
        if(((defaultFlag & Flags.METADATA) == 0) || !property.isDynamic()) {
            return false;
        }
        String name = SlotPath.unescape(property.getName());
        if(!name.startsWith(Const.HON_WIZARD_TAG_NAMESPACE+":")) {
            return false;
        }

        return true;
    }

    void updateWidgetComponentByTag(BDynamicWidgetComponentBase widgetComponent, String tagName, BValue tagValue) {
        if(TAG_NAME_TO_PROPERTY_MAP.containsKey(tagName)) {
            PropertyTag properTag = TAG_NAME_TO_PROPERTY_MAP.get(tagName);
            if((properTag.getFromStaticJson() != null && properTag.getFromStaticJson()) || (properTag.getFromStaticJson() == null && !(widgetComponent instanceof BDuctAreaCalculator))){
                Map<String, List<PropertyTag>> updates = new HashMap<>();
                properTag.setWidgetComponentBase(widgetComponent);
                properTag.setComponentName(widgetComponent.getName());
                properTag.setSelector(this);
                properTag.setValue(tagValue);
                updates.put(widgetComponent.getParent().getName(), Arrays.asList(properTag));
                HoneywellWidgetStaticDataUtil.updateMultiplePropertiesInJsonFile(updates, selectorDevice);
            } else {
                setWidgetComponentProperty(widgetComponent, properTag.getPropertyName(), tagValue);
            }
        }
    }

    void resetWidgetComponentByTag(BDynamicWidgetComponentBase widgetComponent, String tagName) {
        if(TAG_NAME_TO_PROPERTY_MAP.containsKey(tagName)) {
            PropertyTag propertyTag = TAG_NAME_TO_PROPERTY_MAP.get(tagName);
            if((propertyTag.getFromStaticJson() != null && propertyTag.getFromStaticJson()) || (propertyTag.getFromStaticJson() == null && !(widgetComponent instanceof BDuctAreaCalculator))){
                Map<String, List<PropertyTag>> updates = new HashMap<>();
                propertyTag.setWidgetComponentBase(widgetComponent);
                propertyTag.setComponentName(widgetComponent.getName());
                updates.put(widgetComponent.getParent().getName(), Arrays.asList(propertyTag));
                HoneywellWidgetStaticDataUtil.resetMultiplePropertiesInJsonFile(updates, selectorDevice);

            } else {
                resetWidgetComponentProperty(widgetComponent, propertyTag.getPropertyName());
            }
        }
    }

    private void updateOptions(BDynamicWidgetComponentBase widgetComponent, BEnumRange newOptions) {
        BEnumRange targetEnumRange = getTargetEnumRange();
        widgetComponent.makeOptions(newOptions, targetEnumRange);
    }

    private void setWidgetComponentProperty(BDynamicWidgetComponentBase widgetComponent, String propertyName, BValue tagValue) {
        Property property = widgetComponent.getProperty(propertyName);
        if(property == null) {
            return;
        }
        if(property.getType().is(BDouble.TYPE)) {
            widgetComponent.set(property, BDouble.make(tagValue.toString()));
        }
        else if(propertyName.equals("color")) {
            widgetComponent.set(property, BString.make(Constants.getColorCodeForColor(tagValue.toString())));
        }
        else if(propertyName.equals("options")) {
            BEnumRange newOptions = (BEnumRange) tagValue;
            updateOptions(widgetComponent, newOptions);
        }
        else {
            widgetComponent.set(property, tagValue);
        }
    }

    private void resetWidgetComponentProperty(BDynamicWidgetComponentBase widgetComponent, String propertyName) {
        Property property = widgetComponent.getProperty(propertyName);
        if(property == null) {
            return;
        }
        if(property.isDynamic()) {
            widgetComponent.remove(property);
        }
        else {
            if(propertyName.equals("options")) {
                updateOptions(widgetComponent, null);
            }
            else {
                widgetComponent.set(property, property.getDefaultValue());
            }
        }
    }

	public Map<String, Object> getTags() {
        Map<String, Object> tags = new HashMap<>();
        try {
            // Fetch the tag dictionary service
            BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
            if (tagDictionaryService == null) {
                logger.severe("TagDictionaryService not found.");
                return tags;
            }

            // Fetch the wizard tag definition
            BHonWizardTag wizardTag = (BHonWizardTag) tagDictionaryService.get("HonConfigureWizard");
            if (wizardTag == null) {
                logger.severe("HonConfigureWizard tag definition not found.");
                return tags;
            }

            // Iterate through all tags and fetch their names and values
            for (Tag tag : this.tags().getAll()) {
                java.util.Optional<TagInfo> tagInfoOptional = wizardTag.getTagDefinitions().getTag(tag.getId());
                if (tagInfoOptional.isPresent()) {
                    String tagName = tagInfoOptional.get().getName();
                    BValue tagValue = (BValue) tag.getValue(); // Typecast to BValue
                    tags.put(tagName, tagValue);
                    logger.finest("Fetched tag: Name=" + tagName + ", Value=" + tagValue);
                }
            }
        } catch (Exception e) {
            logger.severe("Failed to fetch tags from BHonWizPointSelector: " + e.getMessage());
        }
        return tags;
	}

    public void handleTags(Map<String, Object> tagData) {
        try {
            // Fetch the tag dictionary service
            BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
            if (tagDictionaryService == null) {
                logger.severe("TagDictionaryService not found.");
                return;
            }

            // Fetch the wizard tag definition
            BHonWizardTag wizardTag = (BHonWizardTag) tagDictionaryService.get("HonConfigureWizard");
            if (wizardTag == null) {
                logger.severe("HonConfigureWizard tag definition not found.");
                return;
            }

            // Iterate through the provided tag data and update tags
            for (Map.Entry<String, Object> entry : tagData.entrySet()) {
                String tagName = entry.getKey();
                Object tagValue = entry.getValue();

                	BTagInfo bTagInfo = (BTagInfo) wizardTag.getTagDefinitions().get(tagName); // Fetch tag info using tag name
                    Tag tag = createTag(bTagInfo, tagValue); // Create tag using TagGenerator logic
                    this.tags().set(tag); // Use `set` to update tags
                    logger.finest("Updated tag: Name=" + tagName + ", Value=" + tagValue);
            }
        } catch (Exception e) {
            logger.severe("Failed to update tags in BHonWizPointSelector: " + e.getMessage());
        }
    }

    /**
     * Creates a tag based on the provided tag info and value.
     * @param tagInfo The tag info object.
     * @param tagValue The value for the tag.
     * @return The created tag.
     */
    private Tag createTag(BTagInfo tagInfo, Object tagValue) {
        try {
            if (tagValue instanceof BIDataValue) {
                return new Tag(tagInfo.getTagId(), (BIDataValue) tagValue);
            }
        } catch (Exception e) {
            logger.severe("Failed to create tag: " + e.getMessage());
            return null;
        }
		return null;
    }

    /**
     * get tag value by tag name
     * @param tagName, tag name
     * @return tag value
     */
    public BIDataValue getTagValue(String tagName){
        Iterator<Tag> itr = this.tags().iterator();
        while (itr.hasNext()) {
            Tag t = itr.next();
            Id tagId = t.getId();
            if(tagId.getName().equals(tagName)) {
                return t.getValue();
            }
        }
        return null;
    }

    /**
     * Check if widget type tag changed, if changed, get widget type value
     * @param property, changed property
     * @return widget type tag value
     */
    protected BIDataValue checkAndGetWidgetTypeTagValue(Property property) {
        String name = SlotPath.unescape(property.getName());
        if(name.equals(Const.HON_WIZARD_TAG_NAMESPACE + ":" + Const.WIDGET_TYPE_TAG)) {
            BIDataValue widgetTypeTagValue = getTagValue(Const.WIDGET_TYPE_TAG);
            if (null != widgetTypeTagValue) {
                return widgetTypeTagValue;
            }
        }
        return null;
    }

    static class GlobalStoreDynamicSyncTask extends TimerTask {
        private BIHoneywellConfigurableDevice device;
        private BHonWizSelector changedSelector;
        private Property[] changedProperties;
        private SyncType syncType;
        private String threadName;

        public GlobalStoreDynamicSyncTask(BIHoneywellConfigurableDevice device, BHonWizSelector changedSelector, Property[] changedProperties, SyncType syncType, String threadName) {
            this.changedSelector = changedSelector;
            this.changedProperties = changedProperties;
            this.syncType = syncType;
            this.device = device;
            this.threadName = threadName;
        }

        public Property[] getChangedProperties() {
            return changedProperties;
        }

        @Override
        public void run() {
            List<BHonWizSelector> selectors = null;
            try {
                // Handle special scenario where area component is removed, then remove all the associated selectors
                BDynamicWidgetComponentBase widgetComponent = changedSelector.getWidgetComponent();
                if (widgetComponent != null) {
                    BComponent page = (BComponent) widgetComponent.getParent();
                    if (widgetComponent instanceof BDuctAreaCalculator) {
                        BDuctAreaCalculator ductAreaCalculator = (BDuctAreaCalculator) widgetComponent;
                        String belong = ductAreaCalculator.getBelong();
                        String role = ductAreaCalculator.getRole();

                        if (role.equals("area")) {
                            handleAreaComponentRemoved(page, belong);
                            HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(device);
                            BasicContext context = new BasicContext();
                            generator.generate(context, true);
                            return;
                        }else{
                            //we only consider duct area calculator with area role in immediate tag sync. other roles are not valid behaviour
                            return;
                        }
                    }
                }

                Property[] properties = changedProperties;
                changedProperties = new Property[0];

                logger.finest("GlobalStoreDynamicSyncTask started in thread " + threadName + " ------------------");
                selectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) device)
                        .stream().filter(BComponent::isRunning).collect(Collectors.toList());

                // disable all tags
                for (BHonWizSelector selector : selectors) {
                    setReadOnlyForTags(selector, true);
                }

                if (!TagValidation.validateAll(selectors, device.getDeviceName())) {
                    logger.finest("GlobalStoreDynamicSyncTask validation failed in thread " + threadName + " ------------------");
                    device.setIsWizardInSyncWithConfiguration(false);
                    return;
                }

                switch (syncType) {
                    case CHANGED:
                    case ADDED:
                    case REMOVED:
                        tagEventSync(properties);
                        break;
                    case STARTED:
                        startedSync();
                        break;
                    case STOPPED:
                        stoppedSync();
                        break;
                }

                device.setIsWizardInSyncWithConfiguration(true);
            }
            catch (Exception ex) {
                logger.log(Level.SEVERE, "GlobalStoreDynamicSyncTask exception in thread " + threadName + " ------------------", ex);
                device.setIsWizardInSyncWithConfiguration(false);
            }
            finally {
                // enable all tags
                if(selectors != null) {
                    for (BHonWizSelector selector : selectors) {
                        setReadOnlyForTags(selector, false);
                    }
                }
            }
            logger.finest("GlobalStoreDynamicSyncTask finished in thread " + threadName + " ------------------");
        }

        private void tagEventSync(Property[] properties) throws GlobalStoreGenerationExcpetion{
            if(isFullRefreshNeeded(properties)) {
                HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(device);
                BasicContext context = new BasicContext();
                //reserve context to extend it in the future
                generator.generate(context, true);
            }
            else {
                BDynamicWidgetComponentBase widgetComponent = changedSelector.getWidgetComponent();
                for(Property property : properties) {
                    if(!isHonWizTag(property)) {
                        continue;
                    }
                    if(widgetComponent != null) {
                        switch(syncType) {
                            case CHANGED:
                            case ADDED:
                                BValue tagValue = changedSelector.get(property);
                                changedSelector.updateWidgetComponentByTag(widgetComponent, SlotPath.unescape(property.getName()), tagValue);
                                break;
                            case REMOVED:
                                changedSelector.resetWidgetComponentByTag(widgetComponent, SlotPath.unescape(property.getName()));
                                break;
                            default:
                            	break;
                        }
                    }
                }
            }
        }

        private void startedSync() throws GlobalStoreGenerationExcpetion {
            // get page tag of new added selector
            // check the page is existing or not
            // if existing then we should remove old one and create a new one
            // if not then we should create a new page component
            // if added selector is a slot selector, then we should call device.processHonWizardSlotUpdates finally
            String newPageName = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(changedSelector, BHonWizardTag.PAGE_TAG);
            BComponent existingPageComponent = (BComponent) device.getGlobalStore().get(SlotPath.escape(newPageName));
            if(existingPageComponent != null) {
                device.getGlobalStore().remove(existingPageComponent);
            }

            HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(device);
            generator.generatePageComponent(newPageName);

            if(existingPageComponent == null) {
                // set page order for new page component
                device.getGlobalStore().addPageOrderForNewPage(newPageName);
            }

            if(changedSelector instanceof BHonWizSlotSelector) {
                Map<BOrd, String> updatedSlotDetails = generator.getUpdatedSlotDetails();
                if(null != updatedSlotDetails) {
                    device.getHonWizardSlotDetails().updateSlotDetails(updatedSlotDetails);
                    device.processHonWizardSlotUpdates(updatedSlotDetails);
                }
            }
        }

        private void stoppedSync() {
            // if removed selector is a DuctAreaCalculator then we should check if its role is area, if yes we should remove all components of DuctAreaCalculator,
            //                otherwise we should remove this widget component and then create a default one with same role.
            // if removed selector is some other type then we should remove this widget component directly
            // finally the widget component should be removed from the slot details
            BDynamicWidgetComponentBase widgetComponent = changedSelector.getWidgetComponent();
            if(widgetComponent != null) {
                BOrd widgetComponentOrd = widgetComponent.getHandleOrd();
                BComponent page = (BComponent) widgetComponent.getParent();
                if(widgetComponent instanceof BDuctAreaCalculator) {
                    handleDuctAreaWidgetRemoved(widgetComponent, page);
                }
                else {
                	if(widgetComponent instanceof BTimeZone) {
                		device.getMetaDataComp().remove(TIMEZONE_TYPE);
                	}
                    page.remove(widgetComponent);
                    removeGroupComponent(page, Collections.singletonList(widgetComponent.getName()));
                }

                // remove page if nothing left in the page
                if(page.getSlotCount() == 0) {
                    device.getGlobalStore().removePageOrder(SlotPath.unescape(page.getName()));
                    device.getGlobalStore().remove(page);
                }

                if(changedSelector instanceof BHonWizSlotSelector) {
                    removeFromSlotDetails(widgetComponentOrd);
                }
                else if(changedSelector instanceof BHonWizPointSelector) {
                    resetWizardComponentOrdOfHonWizardPoint((BHonWizPointSelector) changedSelector);
                }
            }
        }

		private void handleDuctAreaWidgetRemoved(BDynamicWidgetComponentBase widgetComponent, BComponent page) {
			BDuctAreaCalculator ductAreaCalculator = (BDuctAreaCalculator) widgetComponent;
			String belong = ductAreaCalculator.getBelong();
			String role = ductAreaCalculator.getRole();

			if (role.equals("area")) {
				handleAreaComponentRemoved(page, belong);
			}
            //we don't consider removing duct area calculator related selectors in metadata. because it is not a valid behaviour.

		}

		private void handleAreaComponentRemoved(BComponent page, String belong) {
			BDuctAreaCalculator[] allCalculator = page.getChildren(BDuctAreaCalculator.class);
			List<String> removedComponents = new ArrayList<>();
			for (BDuctAreaCalculator calculator : allCalculator) {
				if (calculator.getBelong().equals(belong)) {
					if (null != device.getMetaDataComp().get(DUCT_AREA_CALCULATOR_TYPE + "_" + calculator.getRole())) {
						device.getMetaDataComp().remove(DUCT_AREA_CALCULATOR_TYPE + "_" + calculator.getRole());
					}
					for (BHonWizSlotSelector selector : device.getMetaDataComp().getChildren(BHonWizSlotSelector.class)) {
						// Remove all the selectors related to
						// DuctAreaCalculator.
						if (selector.getSourcePropertyNameString().contains(DUCT_AREA_CALCULATOR_TYPE)) {
							device.getMetaDataComp().remove(selector);
						}
					}
					removedComponents.add(calculator.getName());
                    page.remove(calculator);
				}
			}
			if (!removedComponents.isEmpty()) {
				removeGroupComponent(page, removedComponents);
			}
		}

        private void removeFromSlotDetails(BOrd handleOrd) {
            String handleOrdString = SlotPath.escape(handleOrd.toString());
            BHonWizardSlotDetails wizardSlotDetails = device.getHonWizardSlotDetails();
            if(wizardSlotDetails != null && wizardSlotDetails.getProperty(handleOrdString) != null) {
                wizardSlotDetails.remove(handleOrdString);
            }
        }

		private void resetWizardComponentOrdOfHonWizardPoint(BHonWizPointSelector selector) {
			BIHonWizardPoint parentPoint = (BIHonWizardPoint) selector.selectorParent;
			parentPoint.setWizardComponentOrd(BOrd.DEFAULT);
			HoneywellDeviceWizardLogger.info("wizPoint.getWizardComponentOrd() is "
					+ parentPoint.getWizardComponentOrd() + "for point " + ((BComponent) parentPoint).getSlotPathOrd());
		}

        private void removeGroupComponent(BComponent parent, List<String> originalComponents) {
            Property[] properties = parent.getPropertiesArray();
            for(Property property : properties) {
                BValue component = parent.get(property);
                if(component instanceof BComponent) {
                    BValue componentType = ((BComponent)component).get("componentType");

                    if(componentType == null || !componentType.toString().equals(HEADING_LABEL)) {
                        continue;
                    }

                    BValue itemsInGroup = ((BComponent)component).get("itemsInGroup");
                    if(itemsInGroup == null || itemsInGroup.toString().isEmpty()) {
                        parent.remove(property);
                        continue;
                    }
                    String itemNames = itemsInGroup.toString();
                    String[] items = itemNames.split(",");
                    if(!Arrays.stream(items).anyMatch(item -> originalComponents.contains(item))) {
                        continue;
                    }
                    List<String> newItemList = new ArrayList<>();
                    for(String item : items) {
                        if(!originalComponents.contains(item)) {
                            newItemList.add(item);
                        }
                    }

                    if(newItemList.isEmpty()) {
                        parent.remove(property);
                    }
                    else {
                        String newItemNames = String.join(",", newItemList);
                        ((BComponent) component).set("itemsInGroup", BString.make(newItemNames));
                    }
                }
            }
        }

        private void setReadOnlyForTags(BHonWizSelector selector, boolean readOnly) {
            Property[] properties = selector.getPropertiesArray();
            for (Property property : properties) {
                if (isHonWizTag(property)) {
                    int flags = selector.getFlags(property);
                    if(readOnly) {
                        flags = flags | Flags.READONLY;
                    } else {
                        flags = flags & ~Flags.READONLY;
                    }
                    selector.setFlags(property, flags);
                }
            }
        }

        private boolean isFullRefreshNeeded(Property[] properties) {
            // check sync flag and whether Page/Group/WidgetType/TabInPage/Order are included in properties
            return !device.getIsWizardInSyncWithConfiguration() || Arrays.stream(properties).anyMatch(property -> {
                String propertyName = SlotPath.unescape(property.getName());
                return propertyName.equals(BHonWizardTag.PAGE_TAG.getQName()) ||
                        propertyName.equals(BHonWizardTag.GRP_TAG.getQName()) ||
                        propertyName.equals(BHonWizardTag.WGTTYPE_TAG.getQName()) ||
                        propertyName.equals(BHonWizardTag.TAB_IN_PAGE_TAG.getQName()) ||
                        propertyName.equals(BHonWizardTag.WIZ_ORDER_TAG.getQName());
            });
        }
    }

    enum SyncType {
        CHANGED,
        ADDED,
        REMOVED,
        STARTED,
        STOPPED
    }
}
