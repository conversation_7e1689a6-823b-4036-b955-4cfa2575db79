/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.ontology.generate;

import static com.honeywell.applicationhandler.common.Constants.SCHEDULE;
import static com.honeywell.applicationhandler.common.Constants.TIMEZONE_TYPE;
import static com.honeywell.applicationhandler.ontology.BHonWizardTag.HON_WIZ_WDTTYPE_TAG;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.security.AccessController;
import java.security.PrivilegedExceptionAction;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.baja.collection.BITable;
import javax.baja.file.BDataFile;
import javax.baja.file.BIFile;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BObject;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Cursor;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.tag.Tag;
import javax.baja.tagdictionary.BTagDictionaryService;
import javax.baja.tagdictionary.BTagInfo;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.jobs.BHonWizardTagGenJob;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;

/**
 * The TagGenerator class is responsible for generating and managing tags for points and slots within the Honeywell application.
 * It utilizes tag mapping information loaded from an Excel file to generate tags based on naming conventions and slot paths.
 * The class provides methods to generate tags for points, renew point selectors, renew slot selectors, and load tag mapping information.
 * 
 * <p>
 * The main functionalities of this class include:
 * </p>
 * <ul>
 *   <li>Generating tags for points based on naming conventions.</li>
 *   <li>Generating tags for slots based on slot paths.</li>
 *   <li>Renewing point selectors by cleaning up existing tags and adding new ones.</li>
 *   <li>Renewing slot selectors by cleaning up existing tags and adding new ones.</li>
 *   <li>Loading tag mapping information from an Excel file.</li>
 * </ul>
 * 
 * <p>
 * The class interacts with various other classes and services, including:
 * </p>
 * <ul>
 *   <li>{@link BTagDictionaryService} - Service to retrieve tag definitions.</li>
 *   <li>{@link BHonWizardTag} - Class representing the Honeywell wizard tag.</li>
 *   <li>{@link BHonWizPointSelector} - Class representing the point selector for Honeywell wizard.</li>
 *   <li>{@link BHonWizSlotSelector} - Class representing the slot selector for Honeywell wizard.</li>
 *   <li>{@link TagMappingInfo} - Class representing the tag mapping information loaded from the Excel file.</li>
 *   <li>{@link Tag} - Class representing a tag.</li>
 *   <li>{@link BComponent} - Class representing a component in the system.</li>
 *   <li>{@link BOrd} - Class representing an ordered reference to a component or property.</li>
 *   <li>{@link BITable} - Interface representing a table of results.</li>
 *   <li>{@link Cursor} - Interface representing a cursor to iterate over table results.</li>
 *   <li>{@link Property} - Class representing a property of a component.</li>
 *   <li>{@link BValue} - Class representing a value in the system.</li>
 *   <li>{@link BEnumRange} - Class representing an enumeration range.</li>
 *   <li>{@link BBoolean} - Class representing a boolean value.</li>
 *   <li>{@link BString} - Class representing a string value.</li>
 *   <li>{@link XSSFWorkbook} - Class representing an Excel workbook.</li>
 *   <li>{@link XSSFSheet} - Class representing an Excel sheet.</li>
 *   <li>{@link Row} - Class representing a row in an Excel sheet.</li>
 *   <li>{@link Cell} - Class representing a cell in an Excel sheet.</li>
 * </ul>
 * 
 * <p>
 * The class also includes several private helper methods to handle specific tasks, such as:
 * </p>
 * <ul>
 *   <li>{@code generateTags} - Generates tags based on the provided tag mapping information.</li>
 *   <li>{@code handleTaggingForSchedule} - Handles tagging for schedule components.</li>
 *   <li>{@code addGeneratedTagsForPoints} - Adds generated tags to a point component.</li>
 *   <li>{@code cleanUpExistingTagsInPointSelector} - Cleans up existing tags in a point selector.</li>
 *   <li>{@code cleanUpExistingHonSlotSelectorTags} - Cleans up existing tags in a slot selector.</li>
 *   <li>{@code generateAndAddTagsForSlotParents} - Generates and adds tags for slot parent components.</li>
 *   <li>{@code getEnumRangeEncodingStringFromTagValue} - Retrieves the enum range encoding string from a tag value.</li>
 *   <li>{@code loadTagMappingInfo} - Loads tag mapping information from an Excel file.</li>
 *   <li>{@code getTagMappingInfosFormWorkSheet} - Retrieves tag mapping information from an Excel sheet.</li>
 *   <li>{@code copyTagMappingFileToStationHome} - Copies the tag mapping file to the station home directory.</li>
 *   <li>{@code getHeaderNames} - Retrieves header names from an Excel sheet.</li>
 *   <li>{@code getNamingConventions} - Retrieves naming conventions from a cell value.</li>
 *   <li>{@code getCellValue} - Retrieves the value of a cell.</li>
 * </ul>
 * 
 * <p>
 * The class uses a logger to log messages and exceptions, and it handles various exceptions that may occur during the execution of its methods.
 * </p>
 * 
 */
public class TagGenerator {

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    private List<TagMappingInfo> pointTagMappingInfos = new ArrayList<>();
    private List<TagMappingInfo> slotTagMappingInfos = new ArrayList<>();

    private Map<Integer, String> headerNames = new HashMap<>();
    private BIHoneywellConfigurableDevice device;

    public List<TagMappingInfo> getPointTagMappingInfos() {
        return pointTagMappingInfos;
    }

    public List<TagMappingInfo> getSlotTagMappingInfos() {
        return slotTagMappingInfos;
    }

    public List<Tag> generateTagsForPoint(BIHonWizardPoint point, BIHoneywellConfigurableDevice device) {
    	this.device = device;
        String pointName = point.getName();
        for(TagMappingInfo tagMappingInfo : pointTagMappingInfos) {
            // Generate tags based on tag mapping info
            boolean isMatched = tagMappingInfo.isMatched(pointName);

            if(isMatched) {
                BEnumRange widgetTypeEnumRange = HoneywellWidgetTagUtil.getPointWidgetEnumRange(point);
                return generateTags(tagMappingInfo, widgetTypeEnumRange);
            }
        }

        return null;
    }



    private List<Tag> generateTags(TagMappingInfo tagMappingInfo, BEnumRange widgetTypeEnumRange) {
        BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
        BHonWizardTag bHonWizardTag = (BHonWizardTag)tagDictionaryService.get("HonConfigureWizard");
        List<Tag> tags = new ArrayList<>();
        if(bHonWizardTag != null) {
        	if(tagMappingInfo.isSchedule) {
        		handleTaggingForSchedule(tagMappingInfo, bHonWizardTag, tags, widgetTypeEnumRange);
        	} else {
				tagMappingInfo.tagValues.forEach((tagName, tagValue) -> {
					// remove 'Tag' from the tail of tagName
					String realTagName = tagName.substring(0, tagName.length() - 3);
					BValue bTagInfo = bHonWizardTag.getTagDefinitions().get(realTagName);
					if (bTagInfo != null && tagValue != null && !tagValue.toString().isEmpty()) {
						if (BHonWizardTag.ENUM_RANGE_TAGS.contains(realTagName)) {
							try {
                                if(realTagName.equals(HON_WIZ_WDTTYPE_TAG)){
                                    Integer widgetTypeOrdinal = HoneywellWidgetTagUtil.getWidgetTypeOrdinal(tagValue.toString());
                                    BDynamicEnum widgetType = null;
                                    if(null != widgetTypeOrdinal) {
                                        widgetType = BDynamicEnum.make(widgetTypeOrdinal, widgetTypeEnumRange);
                                    } else {
                                        widgetType = BDynamicEnum.make( widgetTypeEnumRange);
                                    }
                                    Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(), widgetType);
                                    tags.add(tag);

                                } else if(realTagName.equals(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG)) { 
                                	BUnitGroupEnum unitGroupEnum = BUnitGroupEnum.make(tagValue.toString());
									Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(), unitGroupEnum);
									tags.add(tag);
                                } else {
                                    String enumRangeEncodingString = getEnumRangeEncodingStringFromTagValue(tagValue.toString());
                                    Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(),
                                            (BEnumRange) BEnumRange.DEFAULT.decodeFromString(enumRangeEncodingString));
                                    tags.add(tag);
                                }
							} catch (IOException ex) {
								logger.warning("Invalid enum range value in TagMapping excel file: " + tagValue);
							}
						} else if (BHonWizardTag.BOOLEAN_TAGS.contains(realTagName)) {
							String cellValue = tagValue.toString();
							boolean boolValue = cellValue.equals(Const.BOOLEAN_TAG_TRUE);
							Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BBoolean.make(boolValue));
							tags.add(tag);
						} else {
							Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BString.make(tagValue.toString()));
							tags.add(tag);
						}
					}
				});
			}
        }
        return tags;
    }

	private void handleTaggingForSchedule(TagMappingInfo tagMappingInfo, BHonWizardTag bHonWizardTag, List<Tag> tags, BEnumRange widgetTypeEnumRange) {
		BValue bTagInfo = bHonWizardTag.getTagDefinitions().get(Const.WIDGET_TYPE_TAG);
		Tag tag = new Tag(((BTagInfo) bTagInfo).getTagId(), BDynamicEnum.make(widgetTypeEnumRange));
		tags.add(tag);
		BValue bPageTagInfo = bHonWizardTag.getTagDefinitions().get(Const.PAGE_TAG);
		Object tagValue = tagMappingInfo.tagValues.get(Const.PAGE_HEADER);
		if (bPageTagInfo != null && tagValue != null && !tagValue.toString().isEmpty()) {
			Tag pageTag = new Tag(((BTagInfo) bPageTagInfo).getTagId(), BString.make(tagValue.toString()));
			tags.add(pageTag);
		}
	}


    public void renewPointSelector(BComponent point, List<Tag> tags) {
        cleanUpExistingTagsInPointSelector(point);

        addGeneratedTagsForPoints(point, tags);
    }

	private void addGeneratedTagsForPoints(BComponent point, List<Tag> tags) {
		BHonWizPointSelector pointSelector = new BHonWizPointSelector();
        for(Tag tag : tags) {
            pointSelector.tags().set(tag);
        }

        point.add("honWizPointSelector", pointSelector);
	}

	private void cleanUpExistingTagsInPointSelector(BComponent point) {
		List<String> pointProperties = new ArrayList<>();
        BOrd pointsOrd = BOrd.make((point).getNavOrd().toString() + "|bql:select from honApplicationHandler:HonWizPointSelector" );
        BITable<?> result = (BITable<?>) pointsOrd.resolve(Sys.getStation()).get();

        Cursor<?> c = result.cursor();
        while(c.next()) {
            BComponent pointSelector = (BComponent) c.get();
            Property pointProperty = pointSelector.getPropertyInParent();
            pointProperties.add(pointProperty.getName());
        }
        for (String pointProperty : pointProperties) {
            point.remove(pointProperty);
        }
	}

    public void renewSlotSelector(String baseSlotPath, BComponent job) {
        slotTagMappingInfos.forEach(slotTagMappingInfo -> {
            if((slotTagMappingInfo.slotPath == null || slotTagMappingInfo.slotPath.isEmpty()) && !slotTagMappingInfo.isTimezone) {
                return;
            }
            
            if(slotTagMappingInfo.isTimezone) {
            	if(device.getMetaDataComp().get(TIMEZONE_TYPE) == null) {
    				device.getMetaDataComp().add(TIMEZONE_TYPE, BTimezoneEnum.make(0), Flags.READONLY);
    			}
            	String fullSlotPath = device.getMetaDataComp().getSlotPath().toString() + "/Timezone";
            	slotTagMappingInfo.slotPath = fullSlotPath.replace(baseSlotPath, "");
			}
            
			BOrd slotPath = slotTagMappingInfo.slotPath.startsWith("/")
					? BOrd.make(Const.STATION_ORD_BASE + baseSlotPath + slotTagMappingInfo.slotPath)
					: BOrd.make(Const.STATION_ORD_BASE + baseSlotPath + "/" + slotTagMappingInfo.slotPath);
			try {
				if (!slotTagMappingInfo.isSchedule) {
					Property slotProperty = slotPath.resolve().getPropertyInParent();
					BComponent parentComponent = (BComponent) slotPath.getParent().get();
					if(slotPath.resolve().get() instanceof BIHonWizardPoint) {
						BComponent pointComp = (BComponent) slotPath.resolve().get();
                        BEnumRange widgetTypeEnumRange = HoneywellWidgetTagUtil.getPointWidgetEnumRange((BIHonWizardPoint) slotPath.resolve().get());

						List<Tag> tags = generateTags(slotTagMappingInfo, widgetTypeEnumRange);
						
						cleanUpExistingTagsInPointSelector(pointComp);
						addGeneratedTagsForPoints(pointComp, tags);
					} else {
						cleanUpExistingHonSlotSelectorTags(slotProperty, parentComponent);
						if (device.isSlotSelectorSupportedInWizard(parentComponent)) {
							generateAndAddTagsForSlotParents(slotTagMappingInfo, slotProperty, parentComponent);
						}else {
							((BHonWizardTagGenJob)job).setHasFailItemsInLog(true);
							((BHonWizardTagGenJob)job).updateFailLog( "Slot selector is not supported for slot: " + slotTagMappingInfo.slotPath
									+ " in device: " + device + ". Please check the slot path.");
						}
					}
				} else {
					BComponent scheduleComp = (BComponent) slotPath.resolve().get();
					
					List<Tag> tags = generateTags(slotTagMappingInfo, HoneywellWidgetTagUtil.getScheduleWidgetEnumRange());
					
					cleanUpExistingTagsInPointSelector(scheduleComp);
					addGeneratedTagsForPoints(scheduleComp, tags);
				}
			} catch (Exception ex) {
				String message = "Failed to create selector for slot " + slotTagMappingInfo.slotPath;
				logger.log(Level.INFO, message, ex);
			}
        });
    }

	private void cleanUpExistingHonSlotSelectorTags(Property slotProperty, BComponent parentComponent) {
		BOrd slotSelectorOrd = BOrd
				.make((parentComponent).getNavOrd().toString() + "|bql:select from honApplicationHandler:HonWizSlotSelector");
		BITable<?> slotSelectors = (BITable<?>) slotSelectorOrd.resolve(Sys.getStation()).get();

		Cursor<?> c = slotSelectors.cursor();
		while (c.next()) {
			BHonWizSlotSelector slotSelector = (BHonWizSlotSelector) c.get();
			if (slotSelector.getSourcePropertyNameString().equals(slotProperty.getName()) && slotSelector.getParent().equals(parentComponent)) {
				parentComponent.remove(slotSelector);
				break;
			}
		}
	}

	private void generateAndAddTagsForSlotParents(TagMappingInfo slotTagMappingInfo, Property slotProperty, BComponent parentComponent) {
		BHonWizSlotSelector slotSelector = new BHonWizSlotSelector();

        BEnumRange slotWidgetEnumRange = HoneywellWidgetTagUtil.getSlotWidgetTypeEnumRange();
        List<Tag> tags = generateTags(slotTagMappingInfo, slotWidgetEnumRange);
        BTagDictionaryService tagDictionaryService = (BTagDictionaryService) Sys.getService(BTagDictionaryService.TYPE);
        BHonWizardTag bHonWizardTag = (BHonWizardTag)tagDictionaryService.get("HonConfigureWizard");
        BValue bTagInfo = bHonWizardTag.getTagDefinitions().get(HON_WIZ_WDTTYPE_TAG);
        String widgetType = "";
		for (Tag tag : tags) {
            if(tag.getId().equals(((BTagInfo)bTagInfo).getTagId())){
                widgetType = tag.getValue().toString();
            }
			slotSelector.tags().set(tag);
		}
        HoneywellWidgetTagUtil.setSourcePropertyName(widgetType, slotSelector,  parentComponent, slotProperty.getName());
		parentComponent.add("honWizSlotSelector?", slotSelector);
	}

    public static String getEnumRangeEncodingStringFromTagValue(String tagValue) throws IOException {
        String[] splitted = tagValue.split("[,{}]");
        String result = "{";
        for(String s : splitted) {
            s = s.trim();
            if(!s.isEmpty()) {
                if(!s.matches("^.+=\\d+$")) {
                    throw new IOException();
                }

                int lastIndexOfEqual = s.lastIndexOf("=");
                String enumName = SlotPath.escape(s.substring(0, lastIndexOfEqual));
                String enumOrdinal = s.substring(lastIndexOfEqual + 1);
                result += enumName + "=" + enumOrdinal + ",";
            }
        }

        if(result.endsWith(",")) {
            result = result.substring(0, result.length() - 1);
        }

        return result + "}";
    }

	public void loadTagMappingInfo(String fileName) {
		// Load tag mapping info from excel file
		File userConfigData = new File(Sys.getStationHome(), "userConfigData");
		File tagMappingExcelFile = new File(userConfigData, fileName);
		try {
			AccessController.doPrivileged((PrivilegedExceptionAction<Void>) () -> {
				copyTagMappingFileToStationHome(tagMappingExcelFile);

				try (XSSFWorkbook workbook = new XSSFWorkbook(tagMappingExcelFile)) {
					getTagMappingInfosFormWorkSheet(workbook.getSheetAt(0));
				} catch (Exception e) {
					logger.log(Level.SEVERE, "Failed to load tag mapping info from excel file.", e);
				}
				return null;
			});
		} catch (Exception e) {
			logger.log(Level.SEVERE, "Failed to load tag mapping info from excel file.", e);
		}
	}

	public void loadTagMappingInfoFromFileData(String fileData) {
		char[] convertedChars = new char[fileData.length()];
		fileData.getChars(0, fileData.length(), convertedChars, 0);
		byte[] convertedBytes = new byte[convertedChars.length];
		for (int index = 0; index < convertedChars.length; index++) {
			convertedBytes[index] = (byte) convertedChars[index];
		}
		try {
			AccessController.doPrivileged((PrivilegedExceptionAction<Void>) () -> {
				try (XSSFWorkbook workbook = new XSSFWorkbook(new ByteArrayInputStream(convertedBytes))) {
					getTagMappingInfosFormWorkSheet(workbook.getSheetAt(0));
				} catch (Exception e) {
					logger.log(Level.SEVERE, "Failed to load tag mapping info from file data.", e);
				}
				return null;
			});
		} catch (Exception e) {
			logger.log(Level.SEVERE, "Failed to load tag mapping info from file data.", e);
		}
	}

    public static String parseTagMappingInfo(BOrd fileOrd) {
        BObject file = fileOrd.get();
        if(file instanceof BIFile) {
            BIFile bFile = (BIFile) file;

            try(InputStream is = bFile.getInputStream()) {
                long fileSize = bFile.getSize();
                if(fileSize <= 0) {
                    logger.log(Level.SEVERE, "Tag mapping file is empty.");
                    return null;
                }
                byte[] fileBytes = new byte[(int) fileSize + 10];

                int readBytes = is.read(fileBytes);
                if(readBytes != fileSize) {
                    logger.log(Level.SEVERE, "Failed to read tag mapping file.");
                    return null;
                }

                char[] fileChars = new char[fileBytes.length];
                for(int index = 0; index < fileBytes.length; index++) {
                    fileChars[index] = (char)(fileBytes[index] & 0xff);
                }

                String convertedString = new String(fileChars);
                logger.info("Converted tag mapping file data string length: " + convertedString.length());
                return convertedString;
            }
            catch (IOException ex) {
                logger.log(Level.SEVERE, "Failed to load tag mapping info from excel file.", ex);
            }
        }

        return null;
    }

    private void getTagMappingInfosFormWorkSheet(XSSFSheet sheet) {
        headerNames = getHeaderNames(sheet.getRow(0));
        for (Row row : sheet) {
            // Skip header row
            if (row.getRowNum() == 0) {
                continue;
            }
            Iterator<Cell> cellIterator = row.cellIterator();

            TagMappingInfo tagMappingInfo = new TagMappingInfo();
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                Integer cellIndex = cell.getColumnIndex();
                Object cellValue = getCellValue(cell);
                if (!headerNames.containsKey(cellIndex) || cellValue == null) {
                    continue;
                }
                String headerName = headerNames.get(cellIndex);
                if (headerName.equals(Const.NAMING_CONVENTION_HEADER)) {
                    if(!cellValue.toString().isEmpty()) {
                        tagMappingInfo.namingConventions = getNamingConventions(cellValue.toString());
                        if(tagMappingInfo.namingConventions.isEmpty()) {
                            tagMappingInfo.fullPointName = cellValue.toString();
                        }
                    }
                } else if (headerName.equals(Const.SLOT_PATH_HEADER)) {
                    tagMappingInfo.slotPath = cellValue.toString();
                } else if (headerName.equals(Const.WIDGET_TYPE_HEADER)) {
                	if(cellValue.toString().trim().equalsIgnoreCase(SCHEDULE)) {
                		tagMappingInfo.isSchedule = true;
                	} else if (cellValue.toString().trim().equalsIgnoreCase(TIMEZONE_TYPE)) {
                		tagMappingInfo.isTimezone = true;
                	}
                	tagMappingInfo.tagValues.put(headerName, cellValue);
                    tagMappingInfo.widgetType = cellValue.toString();
                } else {
                    tagMappingInfo.tagValues.put(headerName, cellValue);
                }
            }

            if((tagMappingInfo.slotPath != null && !tagMappingInfo.slotPath.isEmpty()) || tagMappingInfo.isTimezone) {
                this.slotTagMappingInfos.add(tagMappingInfo);
            }
            else if(!tagMappingInfo.namingConventions.isEmpty() || tagMappingInfo.fullPointName != null) {
                this.pointTagMappingInfos.add(tagMappingInfo);
            }
        }
    }

    private void copyTagMappingFileToStationHome(File tagMappingExcelFile) {
        if(tagMappingExcelFile.exists()) {
            return;
        }

        try (OutputStream outputStream = Files.newOutputStream(tagMappingExcelFile.toPath())) {
            BOrd xmlOrd = BOrd.make(Const.TAG_MAPPING_FILE_PATH);
            BDataFile dataFile = (BDataFile) xmlOrd.get();
            byte[] fileData = dataFile.read();
            outputStream.write(fileData);
        }
        catch (IOException ex) {
            logger.log(Level.SEVERE, "Failed to copy tag mapping file to station home.", ex);
        }
    }

    private Map<Integer, String> getHeaderNames(Row headerRow) {
        Iterator<Cell> cellIterator = headerRow.cellIterator();
        Map<Integer, String> headerNames = new HashMap<>();
        while (cellIterator.hasNext()) {
            Cell cell = cellIterator.next();
            if(cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue();
                if(cellValue != null && !cellValue.isEmpty()) {
                    headerNames.put(cell.getColumnIndex(), cellValue);
                }
            }
        }

        return headerNames;
    }

    private List<List<String>> getNamingConventions(String cellValue) {
        List<List<String>> namingConventions = new ArrayList<>();

        Pattern pattern = Pattern.compile("\\((.*?)\\)");
        Matcher matcher = pattern.matcher(cellValue);

        while(matcher.find()) {
            String namingConventionGroup = matcher.group(1);
            if(namingConventionGroup != null && !namingConventionGroup.isEmpty()) {
                List<String> namingConventionStrings =
                        Arrays.stream(namingConventionGroup.split(",")).collect(Collectors.toList());
                namingConventions.add(namingConventionStrings);
            }
        }

        return namingConventions;
    }

    private Object getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return cell.getNumericCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    public Map<Integer, String> getHeaderNames() {
        return headerNames;
    }
}
