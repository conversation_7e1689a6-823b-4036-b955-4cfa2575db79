/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ontology.generate;

import static com.honeywell.applicationhandler.ontology.Const.TEMPLATE_FILE_PATH;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.AccessController;
import java.security.PrivilegedActionException;
import java.security.PrivilegedExceptionAction;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.baja.collection.BITable;
import javax.baja.data.BIDataValue;
import javax.baja.file.BDataFile;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BComponent;
import javax.baja.sys.Cursor;
import javax.baja.sys.Sys;
import javax.baja.tag.Id;
import javax.baja.util.Lexicon;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.jobs.BHonWizardTagMappingFileGenJob;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.honeywell.applicationhandler.validation.Constants;

/**
 * Generates the Tag Mapping File for Honeywell Configurable Devices.
 * 
 * @since Feb 18, 2025
 */
public class TagMappingFileGenerator {

    private BIHoneywellConfigurableDevice device;
    private BHonWizardTagMappingFileGenJob fileGenJob;
    private String fileName;
    private static final String DELIM = "/";
    private static final Lexicon lex = LexiconUtil.getLexicon();
    private String logMessage = "";
    
    public TagMappingFileGenerator(BIHoneywellConfigurableDevice device, BHonWizardTagMappingFileGenJob fileGenJob, String fileName) {
        this.device = device;
        this.fileGenJob = fileGenJob;
        this.fileName = fileName;
    }

    public void generateTagMappingExcel() {
        if(null == this.fileGenJob) {
        	fileGenJob = new BHonWizardTagMappingFileGenJob(device);
        	fileGenJob.submit(null);
        	return;
        }
        	
        String logMessage = "Starting to generate Tag Mapping Excel file for device: ";
        HoneywellDeviceWizardLogger.finest(logMessage + device.getDeviceName());
        List<TagDetail> tagDetails = collectTagDetails();
        if (fileName == null) {
            fileName = Sys.getStationHome().toPath() + "/userConfigData/TagMapping_" + BAbsTime.now().getMillis() + ".xlsx";
        }
        createExcelFile(tagDetails, fileName);
        logMessage = "Tag Mapping Excel file generation completed.";
        HoneywellDeviceWizardLogger.finest(logMessage);
    }

    private List<TagDetail> collectTagDetails() {
        String logMessage = "Collecting tag details for device: " + device.getDeviceName();
        fileGenJob.updateMessageLog(logMessage);
        HoneywellDeviceWizardLogger.finest(logMessage);
        List<TagDetail> tagDetails = new ArrayList<>();

        int currentJobProgress = fileGenJob.getProgress();
        int progressForTagCollection = fileGenJob.getTotalAvailableProgress() / 2;

        // Collect tags from BHonWizSlotSelector
        try {
            BOrd slotSelectorOrd = BOrd.make(((BComponent) device).getNavOrd() + "|bql:select from honApplicationHandler:HonWizSlotSelector");
            BITable<?> slotSelectors = (BITable<?>) slotSelectorOrd.resolve(Sys.getStation()).get();
            Cursor<?> slotCursor = slotSelectors.cursor();

            while (slotCursor.next()) {
                BHonWizSlotSelector slotSelector = (BHonWizSlotSelector) slotCursor.get();
                String compSlotPath = ((BComponent) slotSelector.getParent()).getSlotPathOrd().toString();
                String deviceSlotPath = ((BComponent) device).getSlotPathOrd().toString();
                String relativeSlotPath = compSlotPath.replace(deviceSlotPath, "");
                relativeSlotPath = relativeSlotPath + DELIM + slotSelector.getSourcePropertyNameString();
                tagDetails.add(extractTags(slotSelector, relativeSlotPath, ""));
                logMessage = MessageFormat.format("Collected tags from BHonWizSlotSelector: {0}", slotSelector.getSlotPath());
                HoneywellDeviceWizardLogger.finest(logMessage);
            }
        } catch (Exception e) {
            logMessage = MessageFormat.format("{0}: {1}", lex.get("error.collecting.tags.slotselector"), e);
            HoneywellDeviceWizardLogger.severe(logMessage);
            fileGenJob.updateFailLog(logMessage);
        }
        
        currentJobProgress += (progressForTagCollection / 2);
        fileGenJob.progress(currentJobProgress);

        // Collect tags from BHonWizPointSelector
        try {
            BOrd pointSelectorOrd = BOrd.make(((BComponent) device).getNavOrd() + "|bql:select from honApplicationHandler:HonWizPointSelector");
            BITable<?> pointSelectors = (BITable<?>) pointSelectorOrd.resolve(Sys.getStation()).get();
            Cursor<?> pointCursor = pointSelectors.cursor();
            while (pointCursor.next()) {
                BHonWizPointSelector pointSelector = (BHonWizPointSelector) pointCursor.get();
                String compSlotPath = ((BComponent) pointSelector.getParent()).getSlotPathOrd().toString();
                String deviceSlotPath = ((BComponent) device).getSlotPathOrd().toString();
                String relativeSlotPath = compSlotPath.replace(deviceSlotPath, "");
                tagDetails.add(extractTags(pointSelector, relativeSlotPath, pointSelector.getParent().getName()));
                logMessage = MessageFormat.format("Collected tags from BHonWizPointSelector: {0}", pointSelector.getSlotPath());
                HoneywellDeviceWizardLogger.finest(logMessage);
            }
        } catch (Exception e) {
            logMessage = MessageFormat.format("{0}: {1}", lex.get("error.collecting.tags.pointselector"), e);
            HoneywellDeviceWizardLogger.severe(logMessage);
            fileGenJob.updateFailLog(logMessage);
        }

        currentJobProgress += (progressForTagCollection / 2);
        fileGenJob.progress(currentJobProgress);
        
        logMessage = "Tag details collection completed for device: " + device.getDeviceName();
        HoneywellDeviceWizardLogger.finest(logMessage);
        fileGenJob.updateMessageLog(logMessage);
        return tagDetails;
    }

    private TagDetail extractTags(BComponent component, String relativeSlotPath, String namingConventions) {
        String logMessage = MessageFormat.format("Extracting tags for component: {0}", component.getSlotPath());
        HoneywellDeviceWizardLogger.finest(logMessage);
        return new TagDetail(
            getTagValueOfComponent(component, BHonWizardTag.PAGE_TAG),
            getTagValueOfComponent(component, BHonWizardTag.TAB_IN_PAGE_TAG),
            getTagValueOfComponent(component, BHonWizardTag.GRP_TAG),
            getTagValueOfComponent(component, BHonWizardTag.NAME_TAG),
            getTagValueOfComponent(component, BHonWizardTag.WGTTYPE_TAG),
            getTagValueOfComponent(component, BHonWizardTag.UNITGRP_TAG),
            getTagValueOfComponent(component, BHonWizardTag.HELP_TAG),
            getTagValueOfComponent(component, BHonWizardTag.STEP_TAG),
            getTagValueOfComponent(component, BHonWizardTag.COLOR_TAG),
            getTagValueOfComponent(component, BHonWizardTag.BELONG_TAG),
            getTagValueOfComponent(component, BHonWizardTag.ROLE_TAG),
            getTagValueOfComponent(component, BHonWizardTag.MIN_TAG),
            getTagValueOfComponent(component, BHonWizardTag.MAX_TAG),
            getTagValueOfComponent(component, BHonWizardTag.DEADBAND_TAG),
            SlotPath.unescape(getTagValueOfComponent(component, BHonWizardTag.WIZ_OPTIONS_TAG)),
            getTagValueOfComponent(component, BHonWizardTag.READ_ONLY_TAG),
            getTagValueOfComponent(component, BHonWizardTag.WIZ_ORDER_TAG),
            namingConventions,
            relativeSlotPath
        );
    }

    private String getTagValueOfComponent(BComponent component, Id tagId) {
        Optional<BIDataValue> tag = component.tags().get(tagId);
        if (tag.isPresent()) {
        	
        	if(tagId.equals(BHonWizardTag.COLOR_TAG)) {
        		return Constants.getColorForColorCode(tag.get().toString());
        	}
        	
            return tag.get().toString();
        } else {
            String logMessage = MessageFormat.format("{0}: {1} for component: {2}", lex.get("warning.tag.not.found"), tagId, component.getSlotPath());
            HoneywellDeviceWizardLogger.finest(logMessage);
            return "";
        }
    }

    /**
     * copy template file from module to station home if it does not exist.
     */
    public static void copyTagMappingTemplateFileToStationHome() {
		try {
			AccessController.doPrivileged((PrivilegedExceptionAction<Void>) () -> {
				File tagMappingTemplateExcelFile = new File(TEMPLATE_FILE_PATH);
				if (tagMappingTemplateExcelFile.exists()) {
					return null;
				}
				File userConfigDataDir = new File(Sys.getStationHome().toPath().resolve("userConfigData").toString());
				if (!userConfigDataDir.exists()) {
					if (!userConfigDataDir.mkdirs()) {
						HoneywellDeviceWizardLogger.severe(lex.getText("error.creating.userconfigdata.folder"));
						return null;
					}
				}
				try (OutputStream outputStream = Files.newOutputStream(tagMappingTemplateExcelFile.toPath())) {
					BOrd xmlOrd = BOrd.make(Const.TAG_MAPPING_TEMPLATE_FILE_PATH);
					BDataFile dataFile = (BDataFile) xmlOrd.get();
					byte[] fileData = dataFile.read();
					outputStream.write(fileData);
				} catch (IOException ex) {
					HoneywellDeviceWizardLogger.severe(lex.getText("error.copy.excel.template.file"), ex);
				}
				return null;
			});
		} catch (PrivilegedActionException e) {
			HoneywellDeviceWizardLogger.severe(lex.getText("error.copy.excel.template.file"), e);
		}
    	 
    }

    private void createExcelFile(List<TagDetail> tagDetails, String fileName) {
        logMessage = MessageFormat.format("Creating Excel file: {0}", fileName);
        HoneywellDeviceWizardLogger.finest(logMessage);
        fileGenJob.updateMessageLog(logMessage);
		try {
			AccessController.doPrivileged((PrivilegedExceptionAction<Void>) () -> {
				// Copy the template file to the new file
				Files.copy(Paths.get(TEMPLATE_FILE_PATH), Paths.get(fileName));

				// Open the copied file
				try (FileInputStream fileInputStream = new FileInputStream(fileName); Workbook workbook = new XSSFWorkbook(fileInputStream)) {

					// Rename the sheet to the device name
					Sheet sheet = workbook.getSheetAt(0);
					workbook.setSheetName(workbook.getSheetIndex(sheet), device.getDeviceName());

					// Populate data rows
					int rowNum = 1;
					for (TagDetail tagDetail : tagDetails) {
						rowNum = updateRowsOfExcel(logMessage, sheet, rowNum, tagDetail);
					}

					// Write the output to the file
					try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
						workbook.write(fileOut);
					}
				}
				return null;
			});
		} catch (PrivilegedActionException e) {
			logMessage = MessageFormat.format("{0}: {1}", lex.get("error.creating.excel.file"), e.getMessage());
			HoneywellDeviceWizardLogger.finest(logMessage);
			fileGenJob.updateFailLog(logMessage);
		} 
    	 
        logMessage = "Excel file creation completed for device: " + device.getDeviceName();
        HoneywellDeviceWizardLogger.finest(logMessage);
        fileGenJob.updateMessageLog(logMessage);
    }

	private int updateRowsOfExcel(String logMessage, Sheet sheet, int rowNum, TagDetail tagDetail) {
		try {
			Row row = sheet.createRow(rowNum++);
			row.createCell(getColumnIndex("PageTag", sheet)).setCellValue(tagDetail.getPageTag());
			row.createCell(getColumnIndex("TabInPageTag", sheet)).setCellValue(tagDetail.getTabInPageTag());
			row.createCell(getColumnIndex("GroupTag", sheet)).setCellValue(tagDetail.getGroupTag());
			row.createCell(getColumnIndex("NameTag", sheet)).setCellValue(tagDetail.getNameTag());
			row.createCell(getColumnIndex("WidgetTypeTag", sheet)).setCellValue(tagDetail.getWidgetTypeTag());
			row.createCell(getColumnIndex("UnitGroupTag", sheet))
					.setCellValue(BUnitGroupEnum.getValidUnitGroupTagForTagMappingFile(tagDetail.getUnitGroupTag()));
			if (tagDetail.getStepTag().equals("") || Double.parseDouble(tagDetail.getStepTag()) == 0) {
				row.createCell(getColumnIndex("StepTag", sheet)).setCellValue("");
			} else {
				row.createCell(getColumnIndex("StepTag", sheet)).setCellValue(Double.parseDouble(tagDetail.getStepTag()));
			}
			row.createCell(getColumnIndex("HelpTag", sheet)).setCellValue(tagDetail.getHelpTag());
			row.createCell(getColumnIndex("ColorTag", sheet)).setCellValue(tagDetail.getColorTag());
			row.createCell(getColumnIndex("BelongTag", sheet)).setCellValue(tagDetail.getBelongTag());
			row.createCell(getColumnIndex("RoleTag", sheet)).setCellValue(tagDetail.getRoleTag());
			if (tagDetail.getMinTag().equals("")) {
				row.createCell(getColumnIndex("MinTag", sheet)).setCellValue("");
			} else {
				row.createCell(getColumnIndex("MinTag", sheet)).setCellValue(Double.parseDouble(tagDetail.getMinTag()));
			}
			if (tagDetail.getMaxTag().equals("")) {
				row.createCell(getColumnIndex("MaxTag", sheet)).setCellValue("");
			} else {
				row.createCell(getColumnIndex("MaxTag", sheet)).setCellValue(Double.parseDouble(tagDetail.getMaxTag()));
			}
			if (tagDetail.getDeadbandTag().equals("")) {
				row.createCell(getColumnIndex("DeadbandTag", sheet)).setCellValue("");
			} else {
				row.createCell(getColumnIndex("DeadbandTag", sheet)).setCellValue(Double.parseDouble(tagDetail.getDeadbandTag()));
			}
			row.createCell(getColumnIndex("OptionsTag", sheet)).setCellValue(tagDetail.getOptionsTag());
			row.createCell(getColumnIndex("ReadOnlyTag", sheet)).setCellValue(tagDetail.getReadOnlyTag());
			if (tagDetail.getOrderTag().equals("") || Double.parseDouble(tagDetail.getOrderTag()) == 0) {
				row.createCell(getColumnIndex("OrderTag", sheet)).setCellValue("");
			} else {
				row.createCell(getColumnIndex("OrderTag", sheet)).setCellValue(Double.parseDouble(tagDetail.getOrderTag()));
			}
			row.createCell(getColumnIndex("NamingConventions", sheet)).setCellValue(tagDetail.getNamingConventions());
			row.createCell(getColumnIndex("RelativeSlotPath", sheet)).setCellValue(tagDetail.getRelativeSlotPath());
			logMessage = MessageFormat.format("Added row for TagDetail: {0}", tagDetail);
		} catch (Exception e) {
			 String error = lex.getText("error.tagdetail.update", tagDetail, e);
			 HoneywellDeviceWizardLogger.finest(logMessage);
			 fileGenJob.updateFailLog(error);
		}
		HoneywellDeviceWizardLogger.finest(logMessage);
		return rowNum;
	}

    private int getColumnIndex(String columnName, Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        for (Cell cell : headerRow) {
            if (cell.getStringCellValue().equals(columnName)) {
                return cell.getColumnIndex();
            }
        }
        String logMessage = MessageFormat.format("{0}: {1}", lex.get("error.invalid.column.name"), columnName);
        HoneywellDeviceWizardLogger.severe(logMessage);
        throw new IllegalArgumentException("Invalid column name: " + columnName);
    }

    private static class TagDetail {
        private final String pageTag;
        private final String tabInPageTag;
        private final String groupTag;
        private final String nameTag;
        private final String widgetTypeTag;

        private final String unitGroup;
        private final String colorTag;
        private final String belongTag;
        private final String roleTag;
        private final String minTag;
        private final String maxTag;
        private final String deadbandTag;
        private final String optionsTag;
        private final String readOnlyTag;
        private final String orderTag;

        private final String helpTag;

        private final String stepTag;
        private final String namingConventions;
        private final String relativeSlotPath;

        public TagDetail(String pageTag, String tabInPageTag, String groupTag, String nameTag, String widgetTypeTag, String unitGroup, String helpTag, String stepTag, String colorTag,
                         String belongTag, String roleTag, String minTag, String maxTag, String deadbandTag, String optionsTag,
                         String readOnlyTag, String orderTag, String namingConventions, String relativeSlotPath) {
            this.pageTag = pageTag;
            this.tabInPageTag = tabInPageTag;
            this.groupTag = groupTag;
            this.nameTag = nameTag;
            this.widgetTypeTag = widgetTypeTag;
            this.colorTag = colorTag;
            this.belongTag = belongTag;
            this.roleTag = roleTag;
            this.minTag = minTag;
            this.maxTag = maxTag;
            this.deadbandTag = deadbandTag;
            this.optionsTag = optionsTag;
            this.readOnlyTag = readOnlyTag;
            this.orderTag = orderTag;
            this.namingConventions = namingConventions;
            this.relativeSlotPath = relativeSlotPath;
            this.unitGroup = unitGroup;
            this.helpTag = helpTag;
            this.stepTag = stepTag;
        }

        public String getPageTag() {
            return pageTag;
        }

        public String getTabInPageTag() {
            return tabInPageTag;
        }

        public String getGroupTag() {
            return groupTag;
        }

        public String getNameTag() {
            return nameTag;
        }

        public String getWidgetTypeTag() {
            return widgetTypeTag;
        }

        public String getColorTag() {
            return colorTag;
        }

        public String getBelongTag() {
            return belongTag;
        }

        public String getRoleTag() {
            return roleTag;
        }

        public String getMinTag() {
            return minTag;
        }

        public String getMaxTag() {
            return maxTag;
        }

        public String getDeadbandTag() {
            return deadbandTag;
        }

        public String getOptionsTag() {
            return optionsTag;
        }

        public String getReadOnlyTag() {
            return readOnlyTag;
        }

        public String getOrderTag() {
            return orderTag;
        }

        public String getNamingConventions() {
            return namingConventions;
        }

        public String getRelativeSlotPath() {
            return relativeSlotPath;
        }

        public String getUnitGroupTag() { return unitGroup;}
        public String getHelpTag() {return helpTag;}
        public String getStepTag() {return stepTag;}

        @Override
        public String toString() {
            return new StringBuilder()
                .append("TagDetail{")
                .append("pageTag='").append(pageTag).append('\'')
                .append(", tabInPageTag='").append(tabInPageTag).append('\'')
                .append(", groupTag='").append(groupTag).append('\'')
                .append(", nameTag='").append(nameTag).append('\'')
                .append(", widgetTypeTag='").append(widgetTypeTag).append('\'')
                .append(", colorTag='").append(colorTag).append('\'')
                .append(", belongTag='").append(belongTag).append('\'')
                .append(", roleTag='").append(roleTag).append('\'')
                .append(", minTag='").append(minTag).append('\'')
                .append(", maxTag='").append(maxTag).append('\'')
                .append(", deadbandTag='").append(deadbandTag).append('\'')
                .append(", optionsTag='").append(optionsTag).append('\'')
                .append(", readOnlyTag='").append(readOnlyTag).append('\'')
                .append(", orderTag='").append(orderTag).append('\'')
                .append(", namingConventions='").append(namingConventions).append('\'')
                .append(", relativeSlotPath='").append(relativeSlotPath).append('\'')
                .append(", unitGroup='").append(unitGroup).append('\'')
                .append(", helpTag='").append(helpTag).append('\'')
                .append(", stepTag='").append(stepTag).append('\'')
                .append('}')
                .toString();
        }
    }
}