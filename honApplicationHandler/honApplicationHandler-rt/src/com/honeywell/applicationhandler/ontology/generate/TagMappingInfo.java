/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.ontology.generate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TagMappingInfo {
    public Map<String, Object> tagValues = new HashMap<>();

    public List<List<String>> namingConventions = new ArrayList<>();

    public String slotPath = null;

    public String fullPointName = null;
    
    public boolean isSchedule;
    
    public boolean isTimezone;

    public String widgetType;

    public boolean isMatched(String pointName) {
        if(fullPointName != null) {
            return fullPointName.trim().equalsIgnoreCase(pointName);
        }
        else {
            if(namingConventions.isEmpty()) {
                return false;
            }

            boolean isMatched = true;
            for(List<String> namingConvention : namingConventions) {
                boolean anyMatched = false;
                for(String namingConventionString : namingConvention) {
                    if(pointName.toLowerCase().contains(namingConventionString.toLowerCase().trim())) {
                        anyMatched = true;
                        break;
                    }
                }
                if(!anyMatched) {
                    isMatched = false;
                    break;
                }
            }

            return isMatched;
        }
    }

    /**
     * Get the display string of naming convention, the format is like ((a, b, c), (d, e)) or fullPointName
     * It's same as the naming convention string in tagMapping excel file
     */
    public String getNamingConventionDisplayString() {
        if(fullPointName != null) {
            return fullPointName;
        }
        else {
            List<String> sections = new ArrayList<>();
            for(List<String> namingConvention : namingConventions) {
                sections.add("(" + String.join(",", namingConvention) + ")");
            }
            return String.join(",", sections);
        }
    }
}
