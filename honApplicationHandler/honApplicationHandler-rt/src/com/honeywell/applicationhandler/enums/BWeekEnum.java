/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - Shya<PERSON>undhar Madhusudhan
 * @since Feb 20, 2025
 */
@NiagaraEnum(
	    range = {
	    		@Range(ordinal=-1, value="anyWeek"),
	    		@Range(ordinal=1, value="first"),
	    		@Range(ordinal=2, value="second"),
	    		@Range(ordinal=3, value="third"),
	    		@Range(ordinal=4, value="fourth"),
	    		@Range(ordinal=5, value="fifth"),
	    		@Range(ordinal=6, value="lastWeek")
	    }
	)

@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S1845",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public final class BWeekEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.enums.BWeekEnum(2629708126)1.0$ @*/
/* Generated Thu Feb 20 20:39:45 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for anyWeek. */
  public static final int ANY_WEEK = -1;
  /** Ordinal value for first. */
  public static final int FIRST = 1;
  /** Ordinal value for second. */
  public static final int SECOND = 2;
  /** Ordinal value for third. */
  public static final int THIRD = 3;
  /** Ordinal value for fourth. */
  public static final int FOURTH = 4;
  /** Ordinal value for fifth. */
  public static final int FIFTH = 5;
  /** Ordinal value for lastWeek. */
  public static final int LAST_WEEK = 6;
  
  /** BWeekEnum constant for anyWeek. */
  public static final BWeekEnum anyWeek = new BWeekEnum(ANY_WEEK);
  /** BWeekEnum constant for first. */
  public static final BWeekEnum first = new BWeekEnum(FIRST);
  /** BWeekEnum constant for second. */
  public static final BWeekEnum second = new BWeekEnum(SECOND);
  /** BWeekEnum constant for third. */
  public static final BWeekEnum third = new BWeekEnum(THIRD);
  /** BWeekEnum constant for fourth. */
  public static final BWeekEnum fourth = new BWeekEnum(FOURTH);
  /** BWeekEnum constant for fifth. */
  public static final BWeekEnum fifth = new BWeekEnum(FIFTH);
  /** BWeekEnum constant for lastWeek. */
  public static final BWeekEnum lastWeek = new BWeekEnum(LAST_WEEK);
  
  /** Factory method with ordinal. */
  public static BWeekEnum make(int ordinal)
  {
    return (BWeekEnum)anyWeek.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BWeekEnum make(String tag)
  {
    return (BWeekEnum)anyWeek.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BWeekEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BWeekEnum DEFAULT = anyWeek;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BWeekEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
