/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;


// using lexicon for display name of the tags (getDisplayTag)
/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since Feb 20, 2025
 */
@NiagaraEnum(
    range = {
    	@Range(ordinal=-1, value="anyMonth"),
        @Range("January"),
        @Range("February"),
        @Range("March"),
        @Range("April"),
        @Range("May"),
        @Range("June"),
        @Range("July"),
        @Range("August"),
        @Range("September"),
        @Range("October"),
        @Range("November"),
        @Range("December")
    }
)
@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S1845",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public final class BMonthEnum
    extends BFrozenEnum
{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.enums.BMonthEnum(1162711927)1.0$ @*/
/* Generated Thu Feb 20 21:04:34 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for anyMonth. */
  public static final int ANY_MONTH = -1;
  /** Ordinal value for January. */
  public static final int JANUARY = 0;
  /** Ordinal value for February. */
  public static final int FEBRUARY = 1;
  /** Ordinal value for March. */
  public static final int MARCH = 2;
  /** Ordinal value for April. */
  public static final int APRIL = 3;
  /** Ordinal value for May. */
  public static final int MAY = 4;
  /** Ordinal value for June. */
  public static final int JUNE = 5;
  /** Ordinal value for July. */
  public static final int JULY = 6;
  /** Ordinal value for August. */
  public static final int AUGUST = 7;
  /** Ordinal value for September. */
  public static final int SEPTEMBER = 8;
  /** Ordinal value for October. */
  public static final int OCTOBER = 9;
  /** Ordinal value for November. */
  public static final int NOVEMBER = 10;
  /** Ordinal value for December. */
  public static final int DECEMBER = 11;
  
  /** BMonthEnum constant for anyMonth. */
  public static final BMonthEnum anyMonth = new BMonthEnum(ANY_MONTH);
  /** BMonthEnum constant for January. */
  public static final BMonthEnum January = new BMonthEnum(JANUARY);
  /** BMonthEnum constant for February. */
  public static final BMonthEnum February = new BMonthEnum(FEBRUARY);
  /** BMonthEnum constant for March. */
  public static final BMonthEnum March = new BMonthEnum(MARCH);
  /** BMonthEnum constant for April. */
  public static final BMonthEnum April = new BMonthEnum(APRIL);
  /** BMonthEnum constant for May. */
  public static final BMonthEnum May = new BMonthEnum(MAY);
  /** BMonthEnum constant for June. */
  public static final BMonthEnum June = new BMonthEnum(JUNE);
  /** BMonthEnum constant for July. */
  public static final BMonthEnum July = new BMonthEnum(JULY);
  /** BMonthEnum constant for August. */
  public static final BMonthEnum August = new BMonthEnum(AUGUST);
  /** BMonthEnum constant for September. */
  public static final BMonthEnum September = new BMonthEnum(SEPTEMBER);
  /** BMonthEnum constant for October. */
  public static final BMonthEnum October = new BMonthEnum(OCTOBER);
  /** BMonthEnum constant for November. */
  public static final BMonthEnum November = new BMonthEnum(NOVEMBER);
  /** BMonthEnum constant for December. */
  public static final BMonthEnum December = new BMonthEnum(DECEMBER);
  
  /** Factory method with ordinal. */
  public static BMonthEnum make(int ordinal)
  {
    return (BMonthEnum)anyMonth.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BMonthEnum make(String tag)
  {
    return (BMonthEnum)anyMonth.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BMonthEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BMonthEnum DEFAULT = anyMonth;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BMonthEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
 
}
