/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON>har Madhusudhan
 * @since Feb 20, 2025
 */
@NiagaraEnum(
	    range = {
	    		@Range(ordinal=-1, value="anyDay"),
	    		@Range(ordinal=0, value="sunday"),
	    		@Range(ordinal=1, value="monday"),
	    		@Range(ordinal=2, value="tuesday"),
	    		@Range(ordinal=3, value="wednesday"),
	    		@Range(ordinal=4, value="thursday"),
	    		@Range(ordinal=5, value="friday"),
	    		@Range(ordinal=6, value="saturday"),
	    }
	)

@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S1845",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public final class BWeekdayEnum extends BFrozenEnum{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.enums.BWeekdayEnum(984199894)1.0$ @*/
/* Generated Thu Feb 20 20:39:45 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for anyDay. */
  public static final int ANY_DAY = -1;
  /** Ordinal value for sunday. */
  public static final int SUNDAY = 0;
  /** Ordinal value for monday. */
  public static final int MONDAY = 1;
  /** Ordinal value for tuesday. */
  public static final int TUESDAY = 2;
  /** Ordinal value for wednesday. */
  public static final int WEDNESDAY = 3;
  /** Ordinal value for thursday. */
  public static final int THURSDAY = 4;
  /** Ordinal value for friday. */
  public static final int FRIDAY = 5;
  /** Ordinal value for saturday. */
  public static final int SATURDAY = 6;
  
  /** BWeekdayEnum constant for anyDay. */
  public static final BWeekdayEnum anyDay = new BWeekdayEnum(ANY_DAY);
  /** BWeekdayEnum constant for sunday. */
  public static final BWeekdayEnum sunday = new BWeekdayEnum(SUNDAY);
  /** BWeekdayEnum constant for monday. */
  public static final BWeekdayEnum monday = new BWeekdayEnum(MONDAY);
  /** BWeekdayEnum constant for tuesday. */
  public static final BWeekdayEnum tuesday = new BWeekdayEnum(TUESDAY);
  /** BWeekdayEnum constant for wednesday. */
  public static final BWeekdayEnum wednesday = new BWeekdayEnum(WEDNESDAY);
  /** BWeekdayEnum constant for thursday. */
  public static final BWeekdayEnum thursday = new BWeekdayEnum(THURSDAY);
  /** BWeekdayEnum constant for friday. */
  public static final BWeekdayEnum friday = new BWeekdayEnum(FRIDAY);
  /** BWeekdayEnum constant for saturday. */
  public static final BWeekdayEnum saturday = new BWeekdayEnum(SATURDAY);
  
  /** Factory method with ordinal. */
  public static BWeekdayEnum make(int ordinal)
  {
    return (BWeekdayEnum)anyDay.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BWeekdayEnum make(String tag)
  {
    return (BWeekdayEnum)anyDay.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BWeekdayEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BWeekdayEnum DEFAULT = anyDay;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BWeekdayEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

}
