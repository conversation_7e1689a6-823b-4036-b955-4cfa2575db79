/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.enums;

import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;


// using lexicon for display name of the tags (getDisplayTag)

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>hus<PERSON>
 * @since Jun 10, 2025
 */
@NiagaraEnum(
    range = {
            @Range("Etc_GMT"),
            @Range("Pacific_Midway"),
            @Range("Pacific_Honolulu"),
            @Range("US_Alaska"),
            @Range("America_Los_Angeles"),
            @Range("America_Tijuana"),
            @Range("US_Arizona"),
            @Range("America_Chihuahua"),
            @Range("US_Mountain"),
            @Range("America_Managua"),
            @Range("US_Central"),
            @Range("America_Mexico_City"),
            @Range("Canada_Saskatchewan"),
            @Range("America_Bogota"),
            @Range("US_Eastern"),
            @Range("US_East_Indiana"),
            @Range("Canada_Atlantic"),
            @Range("America_Caracas"),
            @Range("America_Manaus"),
            @Range("America_Santiago"),
            @Range("Canada_Newfoundland"),
            @Range("America_Sao_Paulo"),
            @Range("America_Argentina_Buenos_Aires"),
            @Range("America_Godthab"),
            @Range("America_Montevideo"),
            @Range("America_Noronha"),
            @Range("Atlantic_Cape_Verde"),
            @Range("Atlantic_Azores"),
            @Range("Africa_Casablanca"),
            @Range("Etc_Greenwich"),
            @Range("Europe_Amsterdam"),
            @Range("Europe_Belgrade"),
            @Range("Europe_Brussels"),
            @Range("Europe_Sarajevo"),
            @Range("Africa_Lagos"),
            @Range("Asia_Amman"),
            @Range("Europe_Athens"),
            @Range("Asia_Beirut"),
            @Range("Africa_Cairo"),
            @Range("Africa_Harare"),
            @Range("Europe_Helsinki"),
            @Range("Asia_Jerusalem"),
            @Range("Europe_Minsk"),
            @Range("Africa_Windhoek"),
            @Range("Asia_Kuwait"),
            @Range("Europe_Moscow"),
            @Range("Africa_Nairobi"),
            @Range("Asia_Tbilisi"),
            @Range("Asia_Tehran"),
            @Range("Asia_Muscat"),
            @Range("Asia_Baku"),
            @Range("Asia_Yerevan"),
            @Range("Asia_Kabul"),
            @Range("Asia_Yekaterinburg"),
            @Range("Asia_Karachi"),
            @Range("Asia_Calcutta"),
            @Range("Asia_Katmandu"),
            @Range("Asia_Almaty"),
            @Range("Asia_Dhaka"),
            @Range("Asia_Rangoon"),
            @Range("Asia_Bangkok"),
            @Range("Asia_Krasnoyarsk"),
            @Range("Asia_Hong_Kong"),
            @Range("Asia_Kuala_Lumpur"),
            @Range("Asia_Irkutsk"),
            @Range("Australia_Perth"),
            @Range("Asia_Taipei"),
            @Range("Asia_Tokyo"),
            @Range("Asia_Seoul"),
            @Range("Asia_Yakutsk"),
            @Range("Australia_Adelaide"),
            @Range("Australia_Darwin"),
            @Range("Australia_Brisbane"),
            @Range("Australia_Canberra"),
            @Range("Australia_Hobart"),
            @Range("Pacific_Guam"),
            @Range("Asia_Vladivostok"),
            @Range("Asia_Magadan"),
            @Range("Pacific_Auckland"),
            @Range("Pacific_Fiji"),
            @Range("Pacific_Tongatapu")
    }
)
@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S1845",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public final class BTimezoneEnum
    extends BFrozenEnum
{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.enums.BTimezoneEnum(1439076642)1.0$ @*/
/* Generated Tue Jun 10 20:10:49 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for Etc_GMT. */
  public static final int ETC_GMT = 0;
  /** Ordinal value for Pacific_Midway. */
  public static final int PACIFIC_MIDWAY = 1;
  /** Ordinal value for Pacific_Honolulu. */
  public static final int PACIFIC_HONOLULU = 2;
  /** Ordinal value for US_Alaska. */
  public static final int US_ALASKA = 3;
  /** Ordinal value for America_Los_Angeles. */
  public static final int AMERICA_LOS_ANGELES = 4;
  /** Ordinal value for America_Tijuana. */
  public static final int AMERICA_TIJUANA = 5;
  /** Ordinal value for US_Arizona. */
  public static final int US_ARIZONA = 6;
  /** Ordinal value for America_Chihuahua. */
  public static final int AMERICA_CHIHUAHUA = 7;
  /** Ordinal value for US_Mountain. */
  public static final int US_MOUNTAIN = 8;
  /** Ordinal value for America_Managua. */
  public static final int AMERICA_MANAGUA = 9;
  /** Ordinal value for US_Central. */
  public static final int US_CENTRAL = 10;
  /** Ordinal value for America_Mexico_City. */
  public static final int AMERICA_MEXICO_CITY = 11;
  /** Ordinal value for Canada_Saskatchewan. */
  public static final int CANADA_SASKATCHEWAN = 12;
  /** Ordinal value for America_Bogota. */
  public static final int AMERICA_BOGOTA = 13;
  /** Ordinal value for US_Eastern. */
  public static final int US_EASTERN = 14;
  /** Ordinal value for US_East_Indiana. */
  public static final int US_EAST_INDIANA = 15;
  /** Ordinal value for Canada_Atlantic. */
  public static final int CANADA_ATLANTIC = 16;
  /** Ordinal value for America_Caracas. */
  public static final int AMERICA_CARACAS = 17;
  /** Ordinal value for America_Manaus. */
  public static final int AMERICA_MANAUS = 18;
  /** Ordinal value for America_Santiago. */
  public static final int AMERICA_SANTIAGO = 19;
  /** Ordinal value for Canada_Newfoundland. */
  public static final int CANADA_NEWFOUNDLAND = 20;
  /** Ordinal value for America_Sao_Paulo. */
  public static final int AMERICA_SAO_PAULO = 21;
  /** Ordinal value for America_Argentina_Buenos_Aires. */
  public static final int AMERICA_ARGENTINA_BUENOS_AIRES = 22;
  /** Ordinal value for America_Godthab. */
  public static final int AMERICA_GODTHAB = 23;
  /** Ordinal value for America_Montevideo. */
  public static final int AMERICA_MONTEVIDEO = 24;
  /** Ordinal value for America_Noronha. */
  public static final int AMERICA_NORONHA = 25;
  /** Ordinal value for Atlantic_Cape_Verde. */
  public static final int ATLANTIC_CAPE_VERDE = 26;
  /** Ordinal value for Atlantic_Azores. */
  public static final int ATLANTIC_AZORES = 27;
  /** Ordinal value for Africa_Casablanca. */
  public static final int AFRICA_CASABLANCA = 28;
  /** Ordinal value for Etc_Greenwich. */
  public static final int ETC_GREENWICH = 29;
  /** Ordinal value for Europe_Amsterdam. */
  public static final int EUROPE_AMSTERDAM = 30;
  /** Ordinal value for Europe_Belgrade. */
  public static final int EUROPE_BELGRADE = 31;
  /** Ordinal value for Europe_Brussels. */
  public static final int EUROPE_BRUSSELS = 32;
  /** Ordinal value for Europe_Sarajevo. */
  public static final int EUROPE_SARAJEVO = 33;
  /** Ordinal value for Africa_Lagos. */
  public static final int AFRICA_LAGOS = 34;
  /** Ordinal value for Asia_Amman. */
  public static final int ASIA_AMMAN = 35;
  /** Ordinal value for Europe_Athens. */
  public static final int EUROPE_ATHENS = 36;
  /** Ordinal value for Asia_Beirut. */
  public static final int ASIA_BEIRUT = 37;
  /** Ordinal value for Africa_Cairo. */
  public static final int AFRICA_CAIRO = 38;
  /** Ordinal value for Africa_Harare. */
  public static final int AFRICA_HARARE = 39;
  /** Ordinal value for Europe_Helsinki. */
  public static final int EUROPE_HELSINKI = 40;
  /** Ordinal value for Asia_Jerusalem. */
  public static final int ASIA_JERUSALEM = 41;
  /** Ordinal value for Europe_Minsk. */
  public static final int EUROPE_MINSK = 42;
  /** Ordinal value for Africa_Windhoek. */
  public static final int AFRICA_WINDHOEK = 43;
  /** Ordinal value for Asia_Kuwait. */
  public static final int ASIA_KUWAIT = 44;
  /** Ordinal value for Europe_Moscow. */
  public static final int EUROPE_MOSCOW = 45;
  /** Ordinal value for Africa_Nairobi. */
  public static final int AFRICA_NAIROBI = 46;
  /** Ordinal value for Asia_Tbilisi. */
  public static final int ASIA_TBILISI = 47;
  /** Ordinal value for Asia_Tehran. */
  public static final int ASIA_TEHRAN = 48;
  /** Ordinal value for Asia_Muscat. */
  public static final int ASIA_MUSCAT = 49;
  /** Ordinal value for Asia_Baku. */
  public static final int ASIA_BAKU = 50;
  /** Ordinal value for Asia_Yerevan. */
  public static final int ASIA_YEREVAN = 51;
  /** Ordinal value for Asia_Kabul. */
  public static final int ASIA_KABUL = 52;
  /** Ordinal value for Asia_Yekaterinburg. */
  public static final int ASIA_YEKATERINBURG = 53;
  /** Ordinal value for Asia_Karachi. */
  public static final int ASIA_KARACHI = 54;
  /** Ordinal value for Asia_Calcutta. */
  public static final int ASIA_CALCUTTA = 55;
  /** Ordinal value for Asia_Katmandu. */
  public static final int ASIA_KATMANDU = 56;
  /** Ordinal value for Asia_Almaty. */
  public static final int ASIA_ALMATY = 57;
  /** Ordinal value for Asia_Dhaka. */
  public static final int ASIA_DHAKA = 58;
  /** Ordinal value for Asia_Rangoon. */
  public static final int ASIA_RANGOON = 59;
  /** Ordinal value for Asia_Bangkok. */
  public static final int ASIA_BANGKOK = 60;
  /** Ordinal value for Asia_Krasnoyarsk. */
  public static final int ASIA_KRASNOYARSK = 61;
  /** Ordinal value for Asia_Hong_Kong. */
  public static final int ASIA_HONG_KONG = 62;
  /** Ordinal value for Asia_Kuala_Lumpur. */
  public static final int ASIA_KUALA_LUMPUR = 63;
  /** Ordinal value for Asia_Irkutsk. */
  public static final int ASIA_IRKUTSK = 64;
  /** Ordinal value for Australia_Perth. */
  public static final int AUSTRALIA_PERTH = 65;
  /** Ordinal value for Asia_Taipei. */
  public static final int ASIA_TAIPEI = 66;
  /** Ordinal value for Asia_Tokyo. */
  public static final int ASIA_TOKYO = 67;
  /** Ordinal value for Asia_Seoul. */
  public static final int ASIA_SEOUL = 68;
  /** Ordinal value for Asia_Yakutsk. */
  public static final int ASIA_YAKUTSK = 69;
  /** Ordinal value for Australia_Adelaide. */
  public static final int AUSTRALIA_ADELAIDE = 70;
  /** Ordinal value for Australia_Darwin. */
  public static final int AUSTRALIA_DARWIN = 71;
  /** Ordinal value for Australia_Brisbane. */
  public static final int AUSTRALIA_BRISBANE = 72;
  /** Ordinal value for Australia_Canberra. */
  public static final int AUSTRALIA_CANBERRA = 73;
  /** Ordinal value for Australia_Hobart. */
  public static final int AUSTRALIA_HOBART = 74;
  /** Ordinal value for Pacific_Guam. */
  public static final int PACIFIC_GUAM = 75;
  /** Ordinal value for Asia_Vladivostok. */
  public static final int ASIA_VLADIVOSTOK = 76;
  /** Ordinal value for Asia_Magadan. */
  public static final int ASIA_MAGADAN = 77;
  /** Ordinal value for Pacific_Auckland. */
  public static final int PACIFIC_AUCKLAND = 78;
  /** Ordinal value for Pacific_Fiji. */
  public static final int PACIFIC_FIJI = 79;
  /** Ordinal value for Pacific_Tongatapu. */
  public static final int PACIFIC_TONGATAPU = 80;
  
  /** BTimezoneEnum constant for Etc_GMT. */
  public static final BTimezoneEnum Etc_GMT = new BTimezoneEnum(ETC_GMT);
  /** BTimezoneEnum constant for Pacific_Midway. */
  public static final BTimezoneEnum Pacific_Midway = new BTimezoneEnum(PACIFIC_MIDWAY);
  /** BTimezoneEnum constant for Pacific_Honolulu. */
  public static final BTimezoneEnum Pacific_Honolulu = new BTimezoneEnum(PACIFIC_HONOLULU);
  /** BTimezoneEnum constant for US_Alaska. */
  public static final BTimezoneEnum US_Alaska = new BTimezoneEnum(US_ALASKA);
  /** BTimezoneEnum constant for America_Los_Angeles. */
  public static final BTimezoneEnum America_Los_Angeles = new BTimezoneEnum(AMERICA_LOS_ANGELES);
  /** BTimezoneEnum constant for America_Tijuana. */
  public static final BTimezoneEnum America_Tijuana = new BTimezoneEnum(AMERICA_TIJUANA);
  /** BTimezoneEnum constant for US_Arizona. */
  public static final BTimezoneEnum US_Arizona = new BTimezoneEnum(US_ARIZONA);
  /** BTimezoneEnum constant for America_Chihuahua. */
  public static final BTimezoneEnum America_Chihuahua = new BTimezoneEnum(AMERICA_CHIHUAHUA);
  /** BTimezoneEnum constant for US_Mountain. */
  public static final BTimezoneEnum US_Mountain = new BTimezoneEnum(US_MOUNTAIN);
  /** BTimezoneEnum constant for America_Managua. */
  public static final BTimezoneEnum America_Managua = new BTimezoneEnum(AMERICA_MANAGUA);
  /** BTimezoneEnum constant for US_Central. */
  public static final BTimezoneEnum US_Central = new BTimezoneEnum(US_CENTRAL);
  /** BTimezoneEnum constant for America_Mexico_City. */
  public static final BTimezoneEnum America_Mexico_City = new BTimezoneEnum(AMERICA_MEXICO_CITY);
  /** BTimezoneEnum constant for Canada_Saskatchewan. */
  public static final BTimezoneEnum Canada_Saskatchewan = new BTimezoneEnum(CANADA_SASKATCHEWAN);
  /** BTimezoneEnum constant for America_Bogota. */
  public static final BTimezoneEnum America_Bogota = new BTimezoneEnum(AMERICA_BOGOTA);
  /** BTimezoneEnum constant for US_Eastern. */
  public static final BTimezoneEnum US_Eastern = new BTimezoneEnum(US_EASTERN);
  /** BTimezoneEnum constant for US_East_Indiana. */
  public static final BTimezoneEnum US_East_Indiana = new BTimezoneEnum(US_EAST_INDIANA);
  /** BTimezoneEnum constant for Canada_Atlantic. */
  public static final BTimezoneEnum Canada_Atlantic = new BTimezoneEnum(CANADA_ATLANTIC);
  /** BTimezoneEnum constant for America_Caracas. */
  public static final BTimezoneEnum America_Caracas = new BTimezoneEnum(AMERICA_CARACAS);
  /** BTimezoneEnum constant for America_Manaus. */
  public static final BTimezoneEnum America_Manaus = new BTimezoneEnum(AMERICA_MANAUS);
  /** BTimezoneEnum constant for America_Santiago. */
  public static final BTimezoneEnum America_Santiago = new BTimezoneEnum(AMERICA_SANTIAGO);
  /** BTimezoneEnum constant for Canada_Newfoundland. */
  public static final BTimezoneEnum Canada_Newfoundland = new BTimezoneEnum(CANADA_NEWFOUNDLAND);
  /** BTimezoneEnum constant for America_Sao_Paulo. */
  public static final BTimezoneEnum America_Sao_Paulo = new BTimezoneEnum(AMERICA_SAO_PAULO);
  /** BTimezoneEnum constant for America_Argentina_Buenos_Aires. */
  public static final BTimezoneEnum America_Argentina_Buenos_Aires = new BTimezoneEnum(AMERICA_ARGENTINA_BUENOS_AIRES);
  /** BTimezoneEnum constant for America_Godthab. */
  public static final BTimezoneEnum America_Godthab = new BTimezoneEnum(AMERICA_GODTHAB);
  /** BTimezoneEnum constant for America_Montevideo. */
  public static final BTimezoneEnum America_Montevideo = new BTimezoneEnum(AMERICA_MONTEVIDEO);
  /** BTimezoneEnum constant for America_Noronha. */
  public static final BTimezoneEnum America_Noronha = new BTimezoneEnum(AMERICA_NORONHA);
  /** BTimezoneEnum constant for Atlantic_Cape_Verde. */
  public static final BTimezoneEnum Atlantic_Cape_Verde = new BTimezoneEnum(ATLANTIC_CAPE_VERDE);
  /** BTimezoneEnum constant for Atlantic_Azores. */
  public static final BTimezoneEnum Atlantic_Azores = new BTimezoneEnum(ATLANTIC_AZORES);
  /** BTimezoneEnum constant for Africa_Casablanca. */
  public static final BTimezoneEnum Africa_Casablanca = new BTimezoneEnum(AFRICA_CASABLANCA);
  /** BTimezoneEnum constant for Etc_Greenwich. */
  public static final BTimezoneEnum Etc_Greenwich = new BTimezoneEnum(ETC_GREENWICH);
  /** BTimezoneEnum constant for Europe_Amsterdam. */
  public static final BTimezoneEnum Europe_Amsterdam = new BTimezoneEnum(EUROPE_AMSTERDAM);
  /** BTimezoneEnum constant for Europe_Belgrade. */
  public static final BTimezoneEnum Europe_Belgrade = new BTimezoneEnum(EUROPE_BELGRADE);
  /** BTimezoneEnum constant for Europe_Brussels. */
  public static final BTimezoneEnum Europe_Brussels = new BTimezoneEnum(EUROPE_BRUSSELS);
  /** BTimezoneEnum constant for Europe_Sarajevo. */
  public static final BTimezoneEnum Europe_Sarajevo = new BTimezoneEnum(EUROPE_SARAJEVO);
  /** BTimezoneEnum constant for Africa_Lagos. */
  public static final BTimezoneEnum Africa_Lagos = new BTimezoneEnum(AFRICA_LAGOS);
  /** BTimezoneEnum constant for Asia_Amman. */
  public static final BTimezoneEnum Asia_Amman = new BTimezoneEnum(ASIA_AMMAN);
  /** BTimezoneEnum constant for Europe_Athens. */
  public static final BTimezoneEnum Europe_Athens = new BTimezoneEnum(EUROPE_ATHENS);
  /** BTimezoneEnum constant for Asia_Beirut. */
  public static final BTimezoneEnum Asia_Beirut = new BTimezoneEnum(ASIA_BEIRUT);
  /** BTimezoneEnum constant for Africa_Cairo. */
  public static final BTimezoneEnum Africa_Cairo = new BTimezoneEnum(AFRICA_CAIRO);
  /** BTimezoneEnum constant for Africa_Harare. */
  public static final BTimezoneEnum Africa_Harare = new BTimezoneEnum(AFRICA_HARARE);
  /** BTimezoneEnum constant for Europe_Helsinki. */
  public static final BTimezoneEnum Europe_Helsinki = new BTimezoneEnum(EUROPE_HELSINKI);
  /** BTimezoneEnum constant for Asia_Jerusalem. */
  public static final BTimezoneEnum Asia_Jerusalem = new BTimezoneEnum(ASIA_JERUSALEM);
  /** BTimezoneEnum constant for Europe_Minsk. */
  public static final BTimezoneEnum Europe_Minsk = new BTimezoneEnum(EUROPE_MINSK);
  /** BTimezoneEnum constant for Africa_Windhoek. */
  public static final BTimezoneEnum Africa_Windhoek = new BTimezoneEnum(AFRICA_WINDHOEK);
  /** BTimezoneEnum constant for Asia_Kuwait. */
  public static final BTimezoneEnum Asia_Kuwait = new BTimezoneEnum(ASIA_KUWAIT);
  /** BTimezoneEnum constant for Europe_Moscow. */
  public static final BTimezoneEnum Europe_Moscow = new BTimezoneEnum(EUROPE_MOSCOW);
  /** BTimezoneEnum constant for Africa_Nairobi. */
  public static final BTimezoneEnum Africa_Nairobi = new BTimezoneEnum(AFRICA_NAIROBI);
  /** BTimezoneEnum constant for Asia_Tbilisi. */
  public static final BTimezoneEnum Asia_Tbilisi = new BTimezoneEnum(ASIA_TBILISI);
  /** BTimezoneEnum constant for Asia_Tehran. */
  public static final BTimezoneEnum Asia_Tehran = new BTimezoneEnum(ASIA_TEHRAN);
  /** BTimezoneEnum constant for Asia_Muscat. */
  public static final BTimezoneEnum Asia_Muscat = new BTimezoneEnum(ASIA_MUSCAT);
  /** BTimezoneEnum constant for Asia_Baku. */
  public static final BTimezoneEnum Asia_Baku = new BTimezoneEnum(ASIA_BAKU);
  /** BTimezoneEnum constant for Asia_Yerevan. */
  public static final BTimezoneEnum Asia_Yerevan = new BTimezoneEnum(ASIA_YEREVAN);
  /** BTimezoneEnum constant for Asia_Kabul. */
  public static final BTimezoneEnum Asia_Kabul = new BTimezoneEnum(ASIA_KABUL);
  /** BTimezoneEnum constant for Asia_Yekaterinburg. */
  public static final BTimezoneEnum Asia_Yekaterinburg = new BTimezoneEnum(ASIA_YEKATERINBURG);
  /** BTimezoneEnum constant for Asia_Karachi. */
  public static final BTimezoneEnum Asia_Karachi = new BTimezoneEnum(ASIA_KARACHI);
  /** BTimezoneEnum constant for Asia_Calcutta. */
  public static final BTimezoneEnum Asia_Calcutta = new BTimezoneEnum(ASIA_CALCUTTA);
  /** BTimezoneEnum constant for Asia_Katmandu. */
  public static final BTimezoneEnum Asia_Katmandu = new BTimezoneEnum(ASIA_KATMANDU);
  /** BTimezoneEnum constant for Asia_Almaty. */
  public static final BTimezoneEnum Asia_Almaty = new BTimezoneEnum(ASIA_ALMATY);
  /** BTimezoneEnum constant for Asia_Dhaka. */
  public static final BTimezoneEnum Asia_Dhaka = new BTimezoneEnum(ASIA_DHAKA);
  /** BTimezoneEnum constant for Asia_Rangoon. */
  public static final BTimezoneEnum Asia_Rangoon = new BTimezoneEnum(ASIA_RANGOON);
  /** BTimezoneEnum constant for Asia_Bangkok. */
  public static final BTimezoneEnum Asia_Bangkok = new BTimezoneEnum(ASIA_BANGKOK);
  /** BTimezoneEnum constant for Asia_Krasnoyarsk. */
  public static final BTimezoneEnum Asia_Krasnoyarsk = new BTimezoneEnum(ASIA_KRASNOYARSK);
  /** BTimezoneEnum constant for Asia_Hong_Kong. */
  public static final BTimezoneEnum Asia_Hong_Kong = new BTimezoneEnum(ASIA_HONG_KONG);
  /** BTimezoneEnum constant for Asia_Kuala_Lumpur. */
  public static final BTimezoneEnum Asia_Kuala_Lumpur = new BTimezoneEnum(ASIA_KUALA_LUMPUR);
  /** BTimezoneEnum constant for Asia_Irkutsk. */
  public static final BTimezoneEnum Asia_Irkutsk = new BTimezoneEnum(ASIA_IRKUTSK);
  /** BTimezoneEnum constant for Australia_Perth. */
  public static final BTimezoneEnum Australia_Perth = new BTimezoneEnum(AUSTRALIA_PERTH);
  /** BTimezoneEnum constant for Asia_Taipei. */
  public static final BTimezoneEnum Asia_Taipei = new BTimezoneEnum(ASIA_TAIPEI);
  /** BTimezoneEnum constant for Asia_Tokyo. */
  public static final BTimezoneEnum Asia_Tokyo = new BTimezoneEnum(ASIA_TOKYO);
  /** BTimezoneEnum constant for Asia_Seoul. */
  public static final BTimezoneEnum Asia_Seoul = new BTimezoneEnum(ASIA_SEOUL);
  /** BTimezoneEnum constant for Asia_Yakutsk. */
  public static final BTimezoneEnum Asia_Yakutsk = new BTimezoneEnum(ASIA_YAKUTSK);
  /** BTimezoneEnum constant for Australia_Adelaide. */
  public static final BTimezoneEnum Australia_Adelaide = new BTimezoneEnum(AUSTRALIA_ADELAIDE);
  /** BTimezoneEnum constant for Australia_Darwin. */
  public static final BTimezoneEnum Australia_Darwin = new BTimezoneEnum(AUSTRALIA_DARWIN);
  /** BTimezoneEnum constant for Australia_Brisbane. */
  public static final BTimezoneEnum Australia_Brisbane = new BTimezoneEnum(AUSTRALIA_BRISBANE);
  /** BTimezoneEnum constant for Australia_Canberra. */
  public static final BTimezoneEnum Australia_Canberra = new BTimezoneEnum(AUSTRALIA_CANBERRA);
  /** BTimezoneEnum constant for Australia_Hobart. */
  public static final BTimezoneEnum Australia_Hobart = new BTimezoneEnum(AUSTRALIA_HOBART);
  /** BTimezoneEnum constant for Pacific_Guam. */
  public static final BTimezoneEnum Pacific_Guam = new BTimezoneEnum(PACIFIC_GUAM);
  /** BTimezoneEnum constant for Asia_Vladivostok. */
  public static final BTimezoneEnum Asia_Vladivostok = new BTimezoneEnum(ASIA_VLADIVOSTOK);
  /** BTimezoneEnum constant for Asia_Magadan. */
  public static final BTimezoneEnum Asia_Magadan = new BTimezoneEnum(ASIA_MAGADAN);
  /** BTimezoneEnum constant for Pacific_Auckland. */
  public static final BTimezoneEnum Pacific_Auckland = new BTimezoneEnum(PACIFIC_AUCKLAND);
  /** BTimezoneEnum constant for Pacific_Fiji. */
  public static final BTimezoneEnum Pacific_Fiji = new BTimezoneEnum(PACIFIC_FIJI);
  /** BTimezoneEnum constant for Pacific_Tongatapu. */
  public static final BTimezoneEnum Pacific_Tongatapu = new BTimezoneEnum(PACIFIC_TONGATAPU);
  
  /** Factory method with ordinal. */
  public static BTimezoneEnum make(int ordinal)
  {
    return (BTimezoneEnum)Etc_GMT.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BTimezoneEnum make(String tag)
  {
    return (BTimezoneEnum)Etc_GMT.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BTimezoneEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BTimezoneEnum DEFAULT = Etc_GMT;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTimezoneEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

 
}
