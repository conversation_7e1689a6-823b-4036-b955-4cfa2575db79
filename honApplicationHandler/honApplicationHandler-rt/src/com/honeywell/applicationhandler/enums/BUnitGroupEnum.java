/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.enums;

import javax.baja.data.BIDataValue;
import javax.baja.nre.annotations.NiagaraEnum;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.nre.annotations.Range;
import javax.baja.sys.BFrozenEnum;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since July 14, 2025
 */
@NiagaraEnum(
	    range = {
	    		@Range(ordinal=0, value="none"),
	    		@Range(ordinal=1, value="airflowUnit"),
	    		@Range(ordinal=2, value="measurementType")
	    }
	)

@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S1845",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public final class BUnitGroupEnum extends BFrozenEnum implements BIDataValue{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.enums.BUnitGroupEnum(1021565861)1.0$ @*/
/* Generated Tue Jul 15 19:05:01 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  /** Ordinal value for none. */
  public static final int NONE = 0;
  /** Ordinal value for airflowUnit. */
  public static final int AIRFLOW_UNIT = 1;
  /** Ordinal value for measurementType. */
  public static final int MEASUREMENT_TYPE = 2;
  
  /** BUnitGroupEnum constant for none. */
  public static final BUnitGroupEnum none = new BUnitGroupEnum(NONE);
  /** BUnitGroupEnum constant for airflowUnit. */
  public static final BUnitGroupEnum airflowUnit = new BUnitGroupEnum(AIRFLOW_UNIT);
  /** BUnitGroupEnum constant for measurementType. */
  public static final BUnitGroupEnum measurementType = new BUnitGroupEnum(MEASUREMENT_TYPE);
  
  /** Factory method with ordinal. */
  public static BUnitGroupEnum make(int ordinal)
  {
    return (BUnitGroupEnum)none.getRange().get(ordinal, false);
  }
  
  /** Factory method with tag. */
  public static BUnitGroupEnum make(String tag)
  {
    return (BUnitGroupEnum)none.getRange().get(tag);
  }
  
  /** Private constructor. */
  private BUnitGroupEnum(int ordinal)
  {
    super(ordinal);
  }
  
  public static final BUnitGroupEnum DEFAULT = none;

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BUnitGroupEnum.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
	public static String getValidUnitGroupTagForTagMappingFile(String currentTag) {
		switch (currentTag) {
		case "Airflow Unit":
			return "airflowUnit";
		case "Measurement Type":
			return "measurementType";
		case "None":
			return "none";
		default:
			return "";
		}
	}

}
