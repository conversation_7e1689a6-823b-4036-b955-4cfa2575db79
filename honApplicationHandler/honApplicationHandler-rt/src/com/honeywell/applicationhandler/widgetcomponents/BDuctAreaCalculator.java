/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.bacnet.enums.BBacnetEngineeringUnits;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDouble;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

import static com.honeywell.applicationhandler.common.Constants.DUCT_AREA_CALCULATOR_TYPE;

/**
 * <AUTHOR> Sun
 */
@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0", type = "double")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")

@NiagaraProperty (name = "unit", defaultValue = "", type = "String")
@NiagaraProperty (name = "options", defaultValue = "new BComponent()", type = "BComponent")
@NiagaraProperty (name = "belong", defaultValue = "", type = "String")
@NiagaraProperty (name = "role", defaultValue = "", type = "String")
@NiagaraProperty (name = "assocaitedSelector", defaultValue = "new BComponent()", type = "BComponent")
@NiagaraType
public class BDuctAreaCalculator extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator(3606313008)1.0$ @*/
/* Generated Wed Aug 27 13:55:53 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public double getValue() { return getDouble(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(double v) { setDouble(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "unit"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code unit} property.
   * @see #getUnit
   * @see #setUnit
   */
  public static final Property unit = newProperty(0, "", null);
  
  /**
   * Get the {@code unit} property.
   * @see #unit
   */
  public String getUnit() { return getString(unit); }
  
  /**
   * Set the {@code unit} property.
   * @see #unit
   */
  public void setUnit(String v) { setString(unit, v, null); }

////////////////////////////////////////////////////////////////
// Property "options"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code options} property.
   * @see #getOptions
   * @see #setOptions
   */
  public static final Property options = newProperty(0, new BComponent(), null);
  
  /**
   * Get the {@code options} property.
   * @see #options
   */
  public BComponent getOptions() { return (BComponent)get(options); }
  
  /**
   * Set the {@code options} property.
   * @see #options
   */
  public void setOptions(BComponent v) { set(options, v, null); }

////////////////////////////////////////////////////////////////
// Property "belong"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code belong} property.
   * @see #getBelong
   * @see #setBelong
   */
  public static final Property belong = newProperty(0, "", null);
  
  /**
   * Get the {@code belong} property.
   * @see #belong
   */
  public String getBelong() { return getString(belong); }
  
  /**
   * Set the {@code belong} property.
   * @see #belong
   */
  public void setBelong(String v) { setString(belong, v, null); }

////////////////////////////////////////////////////////////////
// Property "role"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code role} property.
   * @see #getRole
   * @see #setRole
   */
  public static final Property role = newProperty(0, "", null);
  
  /**
   * Get the {@code role} property.
   * @see #role
   */
  public String getRole() { return getString(role); }
  
  /**
   * Set the {@code role} property.
   * @see #role
   */
  public void setRole(String v) { setString(role, v, null); }

////////////////////////////////////////////////////////////////
// Property "assocaitedSelector"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code assocaitedSelector} property.
   * @see #getAssocaitedSelector
   * @see #setAssocaitedSelector
   */
  public static final Property assocaitedSelector = newProperty(0, new BComponent(), null);
  
  /**
   * Get the {@code assocaitedSelector} property.
   * @see #assocaitedSelector
   */
  public BComponent getAssocaitedSelector() { return (BComponent)get(assocaitedSelector); }
  
  /**
   * Set the {@code assocaitedSelector} property.
   * @see #assocaitedSelector
   */
  public void setAssocaitedSelector(BComponent v) { set(assocaitedSelector, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDuctAreaCalculator.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static List<String> ROLES = Arrays.asList("area", "width", "height", "radius", "ducttype", "dimensionunit");

    private static Map<String, DefaultValue> ROLE_DEFAULTVALUE_MAP = new HashMap<>();

    private static Map<String, BUnit> DIMENSION_UNIT_MAP = new HashMap<>();

    static {
        ROLE_DEFAULTVALUE_MAP.put("area", new DefaultValue("Duct Area", 0, 0, null, null));
        ROLE_DEFAULTVALUE_MAP.put("width", new DefaultValue("Width", 0, 0, null, null));
        ROLE_DEFAULTVALUE_MAP.put("height", new DefaultValue("Height", 0, 0, null, null));
        ROLE_DEFAULTVALUE_MAP.put("radius", new DefaultValue("Radius", 0, 0, null, null));
        ROLE_DEFAULTVALUE_MAP.put("ducttype", new DefaultValue("Duct Type", 1, 1,
                BEnumRange.make(new int[]{1, 2}, new String[]{"RECTANGLE", "ROUND"}),
                BEnumRange.make(new int[]{1, 2}, new String[]{SlotPath.escape("RectangleDuct.svg"), SlotPath.escape("RoundDuct.svg")})));
        ROLE_DEFAULTVALUE_MAP.put("dimensionunit", new DefaultValue("Dimension Unit", 1, 1,
                BEnumRange.make(new int[]{1, 2, 3, 4}, new String[]{
                        SlotPath.escape("Feet(ft)"),
                        SlotPath.escape("Meter(m)"),
                        SlotPath.escape("Inch(in)"),
                        SlotPath.escape("Centimeter(cm)")}),
                null));

        DIMENSION_UNIT_MAP.put("Feet", BBacnetEngineeringUnits.squareFeet.getNiagaraUnits());
        DIMENSION_UNIT_MAP.put("Inch", BBacnetEngineeringUnits.squareInches.getNiagaraUnits());
        DIMENSION_UNIT_MAP.put("Centimeter", BBacnetEngineeringUnits.squareCentimeters.getNiagaraUnits());
        DIMENSION_UNIT_MAP.put("Meter", BBacnetEngineeringUnits.squareMeters.getNiagaraUnits());
    }

    private static boolean validate(BDuctAreaCalculator ductAreaCalculator) {
        if(ductAreaCalculator.getRole().equals("ducttype")) {
            BComponent options = ductAreaCalculator.getOptions();
            List<BOption> optionList = Arrays.asList(options.getChildren(BOption.class));
            if(optionList.size() != 2) {
                logger.severe("Options count for BDuctAreaCalculator (" + ductAreaCalculator.getName() + ") must be 2.");
                return false;
            }

            for(BOption option : optionList) {
                if(option.getKey() == 1 && !option.getText().toLowerCase().contains("rectangle")) {
                    logger.severe("Option text with value (1) for BDuctAreaCalculator (" + ductAreaCalculator.getName() + ") should be RECTANGLE.");
                    return false;
                }
                if(option.getKey() == 2 && !option.getText().toLowerCase().contains("round")) {
                    logger.severe("Option text with value (2) for BDuctAreaCalculator (" + ductAreaCalculator.getName() + ") should be ROUND.");
                    return false;
                }
            }
        }
        else if(ductAreaCalculator.getRole().equals("dimensionunit")) {
            BComponent options = ductAreaCalculator.getOptions();
            List<BOption> optionList = Arrays.asList(options.getChildren(BOption.class));
            if(optionList.size() > 4) {
                logger.severe("Options count for BDuctAreaCalculator (" + ductAreaCalculator.getName() + ") must be equal or less than 4.");
                return false;
            }

            for(BOption option : optionList) {
                String optionText = option.getText();
                long count = DIMENSION_UNIT_MAP.keySet().stream().filter(key -> optionText.contains(key)).count();
                if(count != 1) {
                    logger.severe("Invalid dimension unit option for BDuctAreaCalculator (" + ductAreaCalculator.getName() + "): " + optionText);
                    return false;
                }
            }
        }
        return true;
    }

    public static String getAreaRole() {
        return ROLES.get(0);
    }

    public static boolean isDimensionUnit(String role) {
        return role.equals(ROLES.get(5));
    }

    public static BDuctAreaCalculator updateAreaUnitFromDimensionUnit(BDuctAreaCalculator dimensionUnit) {
        BUnit unit = getAreaUnitFromDimensionUnit(dimensionUnit);
        if(unit == null) {
            logger.severe("Invalid dimension unit for BDuctAreaCalculator (" + dimensionUnit.getLabel() + "): " + dimensionUnit.getValue());
        }
        else {
            BComponent parentComponent = (BComponent) dimensionUnit.getParent();
            Optional<BDuctAreaCalculator> areaOptional = Arrays.asList(parentComponent.getChildren(BDuctAreaCalculator.class)).stream().filter(ductAreaCalculator -> {
                return ductAreaCalculator.getBelong().equals(dimensionUnit.getBelong()) && ductAreaCalculator.getRole().equals("area");
            }).findFirst();
            if(areaOptional.isPresent()) {
                BDuctAreaCalculator area = areaOptional.get();
                area.setUnit(unit.getSymbol());
                area.setFacets(BDuctAreaCalculator.unit, BFacets.make(BFacets.UNITS, unit.getUnitName()));
                return area;
            }
        }

        return null;
    }

    private static BUnit getAreaUnitFromDimensionUnit(BDuctAreaCalculator dimensionUnit) {
        int value = (int)dimensionUnit.getValue();
        List<BOption> options = Arrays.asList(dimensionUnit.getOptions().getChildren(BOption.class));
        Optional<BOption> unitOptional = options.stream().filter(option -> option.getValue() == value).findFirst();
        if(unitOptional.isPresent()) {
            BOption unit = unitOptional.get();
            String unitText = unit.getText();
            for(Map.Entry<String, BUnit> entry : DIMENSION_UNIT_MAP.entrySet()) {
                if(unitText.contains(entry.getKey())) {
                    return entry.getValue();
                }
            }
        }
        return null;
    }

    public static BDuctAreaCalculator makeDefault(String role, String belong) {
        DefaultValue defaultValueByRole = ROLE_DEFAULTVALUE_MAP.get(role);
        return make(
                defaultValueByRole.value,
                defaultValueByRole.label,
                belong,
                role,
                null,
                defaultValueByRole.enumRange,
                defaultValueByRole.tagRange,
                null
        );
    }

    public static BDuctAreaCalculator make(BComponent srcComponent,
                                           String label,
                                           String belong,
                                           String role,
                                           BEnumRange tagRange,
                                           BBacnetObjectIdentifier bacnetObjectId
    ) {
        if(srcComponent instanceof BIHonWizardNumericPoint) {
            double value = ((BIHonWizardNumericPoint) srcComponent).getValue();
            BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();

            BDuctAreaCalculator result =  make(value, unit, label,  null, null, belong, role);

            if(result == null) {
                return null;
            }

            if(bacnetObjectId != null) {
                result.setBacnetObjectId(bacnetObjectId);
            }
            result.hideSlot("commonSlot");

            return result;
        } else if (srcComponent instanceof BIHonWizardEnumPoint) {
            int value = ((BIHonWizardEnumPoint) srcComponent).getValue().getOrdinal();
            int defaultValue = ((BIHonWizardEnumPoint) srcComponent).getDefault().getOrdinal();
            BEnumRange r = ((BIHonWizardEnumPoint) srcComponent).getEnumRange();

            BDuctAreaCalculator result = make(value,  null, label, r, tagRange, belong, role);

            if(result == null) {
                return null;
            }

            if(bacnetObjectId != null) {
                result.setBacnetObjectId(bacnetObjectId);
            }
            result.hideSlot("commonSlot");

            return result;
        } else {
            logger.severe("Invalid source component type for BDuctAreaCalculator");
            return null;
        }
    }

    public static BDuctAreaCalculator make(double value,
                                           String label,
                                           String belong,
                                           String role,
                                           BUnit unit,
                                           BEnumRange enumRange,
                                           BEnumRange tagRange,
                                           BOrd commonSlot
    ) {
        BDuctAreaCalculator result = make(value, unit, label, enumRange, tagRange, belong, role);

        if(result == null) {
            return null;
        }

        if(commonSlot != null) {
            result.setCommonSlot(commonSlot);
        }
        else {
            result.hideSlot("commonSlot");
        }
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BDuctAreaCalculator make(double value,
                                     BUnit unit,
                                     String label,
                                     BEnumRange enumRange,
                                     BEnumRange tagRange,
                                     String belong,
                                     String role
    ) {
        BDuctAreaCalculator ductAreaCalculator = new BDuctAreaCalculator();
        ductAreaCalculator.setLabel(label);
        ductAreaCalculator.setValue(value);
        ductAreaCalculator.setVisible(true);
        ductAreaCalculator.setRole(role);
        ductAreaCalculator.setBelong(belong);
        ductAreaCalculator.setUnit(unit == null || "null".equals(unit.getSymbol()) ? "" : unit.getSymbol());
        if(null != unit) {
            ductAreaCalculator.setFacets(BDuctAreaCalculator.unit, BFacets.make(BFacets.UNITS, unit.getUnitName()));
        }

        ductAreaCalculator.makeOptions(tagRange, enumRange);

        if(!validate(ductAreaCalculator)) {
            return null;
        }

        return ductAreaCalculator;
    }
    
    public BValue getDefaultValueBasedOnRole() {
    	DefaultValue defVal = ROLE_DEFAULTVALUE_MAP.get(getRole());
    	if(defVal.enumRange != null) {
    		return BDynamicEnum.make(defVal.enumRange);
    	} else {
    		return BDouble.make(defVal.value);
    	}
    }
    
    public BEnumRange getOptionDetails() {
    	DefaultValue defVal = ROLE_DEFAULTVALUE_MAP.get(getRole());
    	if(defVal.tagRange != null) {
    		return defVal.tagRange;
    	} else {
    		return null;
    	}
    }

    @Override
    public void makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        if(targetEnumRange != null) {
            BComponent newOptions = new BComponent();
            for (int i : targetEnumRange.getOrdinals()) {
                String tag = targetEnumRange.getDisplayTag(i, null);
                BOption option = new BOption();
                option.setKey(i);
                option.setText(tag);
                option.setValue(i);
                if(tagEnumRange != null && tagEnumRange.isOrdinal(i)) {
                    String image = SlotPath.unescape(tagEnumRange.getTag(i));
                    option.setImage(image);
                }
                newOptions.add("option?", option);
            }
            setOptions(newOptions);
        }
    }

    @Override
    public void setValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setValue(doubleValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BDuctAreaCalculator (" + getLabel() + "): " + value);
        }
    }



    @Override
    public BString getValueFromWizComp() {
        String currentRole = getRole();
        if(currentRole.equals("ducttype") || currentRole.equals("dimensionunit")) {
            return BString.make(Integer.toString((int)getValue()));
        }
        else {
            return BString.make(Double.toString(getValue()));
        }
    }

    public BEnumRange getEnumRange() {
    	DefaultValue defVal = ROLE_DEFAULTVALUE_MAP.get(getRole());
    	if(defVal.enumRange != null) {
    		return defVal.enumRange;
    	} else {
    		return null;
    	}
	}

    public BEnumRange getTagRange() {
        DefaultValue defVal = ROLE_DEFAULTVALUE_MAP.get(getRole());
        if(defVal.tagRange != null) {
            return defVal.tagRange;
        } else {
            return null;
        }
    }

    public String getDefaultItemName(String role) {
        return  DUCT_AREA_CALCULATOR_TYPE + "_" + role;
    }



	public static class DefaultValue {
        public DefaultValue(String label, double value, double defaultValue, BEnumRange enumRange, BEnumRange tagRange) {
            this.label = label;
            this.value = value;
            this.defaultValue = defaultValue;
            this.enumRange = enumRange;
            this.tagRange = tagRange;
        }
        public String label;
        public BEnumRange enumRange;
        public BEnumRange tagRange;
        public double value;
        public double defaultValue;
    }
}
