/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.schedule.BEnumSchedule;
import javax.baja.sys.BValue;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jan 29, 2025
 */
@NiagaraProperty(
        name = "bacnetObjectId",
        type = "BBacnetObjectIdentifier",
        defaultValue = "BBacnetObjectIdentifier.DEFAULT",
        flags = Flags.READONLY
)
@NiagaraProperty(
        name = "enumSchedule",
        type = "BEnumSchedule",
        defaultValue = "new BEnumSchedule()",
        flags = Flags.READONLY
)
@NiagaraType
public class BSchedulePageWidget extends BWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget(*********)1.0$ @*/
/* Generated Wed Jan 29 14:12:09 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "bacnetObjectId"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code bacnetObjectId} property.
   * @see #getBacnetObjectId
   * @see #setBacnetObjectId
   */
  public static final Property bacnetObjectId = newProperty(Flags.READONLY, BBacnetObjectIdentifier.DEFAULT, null);
  
  /**
   * Get the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public BBacnetObjectIdentifier getBacnetObjectId() { return (BBacnetObjectIdentifier)get(bacnetObjectId); }
  
  /**
   * Set the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public void setBacnetObjectId(BBacnetObjectIdentifier v) { set(bacnetObjectId, v, null); }

////////////////////////////////////////////////////////////////
// Property "enumSchedule"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code enumSchedule} property.
   * @see #getEnumSchedule
   * @see #setEnumSchedule
   */
  public static final Property enumSchedule = newProperty(Flags.READONLY, new BEnumSchedule(), null);
  
  /**
   * Get the {@code enumSchedule} property.
   * @see #enumSchedule
   */
  public BEnumSchedule getEnumSchedule() { return (BEnumSchedule)get(enumSchedule); }
  
  /**
   * Set the {@code enumSchedule} property.
   * @see #enumSchedule
   */
  public void setEnumSchedule(BEnumSchedule v) { set(enumSchedule, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSchedulePageWidget.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	@Override
	public BValue getValueFromWizComp() {
		return getEnumSchedule();
	}
  
}
