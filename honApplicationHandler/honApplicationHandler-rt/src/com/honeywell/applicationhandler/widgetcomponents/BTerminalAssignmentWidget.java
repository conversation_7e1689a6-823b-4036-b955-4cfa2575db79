/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BValue;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraType
public class BTerminalAssignmentWidget extends BWidgetComponentBase{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BTerminalAssignmentWidget(2979906276)1.0$ @*/
/* Generated Mon Feb 24 10:48:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }


    public static final Type TYPE = Sys.loadType(BTerminalAssignmentWidget.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    @Override
    public BValue getValueFromWizComp() {
        return null;
    }

}
