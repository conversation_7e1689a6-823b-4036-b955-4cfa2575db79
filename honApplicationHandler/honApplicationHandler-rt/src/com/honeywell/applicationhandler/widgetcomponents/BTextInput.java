/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

/**
 * <AUTHOR> <PERSON>
 */
@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "", type = "String")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraType
public class BTextInput extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BTextInput(3036896854)1.0$ @*/
/* Generated Wed Aug 27 13:18:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, "", null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public String getValue() { return getString(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(String v) { setString(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTextInput.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BTextInput make(String value,
                                    String label,
                                    BOrd commonSlot,
                                    BEnumRange enumRange
    ) {
        BTextInput result = make(value, label, enumRange);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");
        return result;
    }

    private static BTextInput make(String value, String label, BEnumRange enumRange) {
        BTextInput textInput = new BTextInput();
        textInput.setLabel(label);
        textInput.setValue(value);
        if(null != enumRange) {
        	BFacets range = BFacets.make(BFacets.RANGE, enumRange);
        	textInput.setFacets(BTextInput.value, range);
        }
        textInput.setVisible(true);
        return textInput;
    }
    @Override
    public void changed(Property property, Context context) {
        if (null != context && context.equals(CHANGE_CTX)) {
            return;
        }
        super.changed(property, context);
        if (property.equals(BTextInput.value) && null != this.getSlotFacets(getSlot(value.getName())).get(BFacets.RANGE)) {
            int val;
            try {
                val = Integer.parseInt(getValue());
            } catch (NumberFormatException e) {
                return;
            }
            BEnumRange range = (BEnumRange) this.getSlotFacets(getSlot(value.getName())).get(BFacets.RANGE);
            set(value, BString.make(range.get(val).getTag()), CHANGE_CTX);
        }
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(getValue());
	}

    private static final BasicContext CHANGE_CTX = new BasicContext();
}
