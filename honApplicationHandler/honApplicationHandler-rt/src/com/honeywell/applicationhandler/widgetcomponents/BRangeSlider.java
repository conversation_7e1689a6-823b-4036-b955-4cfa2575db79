/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import java.util.logging.Logger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0.0", type = "double")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "deadband", defaultValue = "0.0", type = "double")
@NiagaraProperty(name = "highPrecisionDeadband", defaultValue = "", type = "String")
@NiagaraProperty (name = "role", defaultValue = "", type = "String")
@NiagaraType
public class BRangeSlider extends BDynamicWidgetUnitSupportComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BRangeSlider(1517540472)1.0$ @*/
/* Generated Wed Aug 27 13:18:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public double getValue() { return getDouble(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(double v) { setDouble(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "deadband"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code deadband} property.
   * @see #getDeadband
   * @see #setDeadband
   */
  public static final Property deadband = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code deadband} property.
   * @see #deadband
   */
  public double getDeadband() { return getDouble(deadband); }
  
  /**
   * Set the {@code deadband} property.
   * @see #deadband
   */
  public void setDeadband(double v) { setDouble(deadband, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionDeadband"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionDeadband} property.
   * @see #getHighPrecisionDeadband
   * @see #setHighPrecisionDeadband
   */
  public static final Property highPrecisionDeadband = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionDeadband} property.
   * @see #highPrecisionDeadband
   */
  public String getHighPrecisionDeadband() { return getString(highPrecisionDeadband); }
  
  /**
   * Set the {@code highPrecisionDeadband} property.
   * @see #highPrecisionDeadband
   */
  public void setHighPrecisionDeadband(String v) { setString(highPrecisionDeadband, v, null); }

////////////////////////////////////////////////////////////////
// Property "role"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code role} property.
   * @see #getRole
   * @see #setRole
   */
  public static final Property role = newProperty(0, "", null);
  
  /**
   * Get the {@code role} property.
   * @see #role
   */
  public String getRole() { return getString(role); }
  
  /**
   * Set the {@code role} property.
   * @see #role
   */
  public void setRole(String v) { setString(role, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRangeSlider.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BRangeSlider make(BComponent srcComponent,
                                    String label,
                                    double minValue,
                                    double maxValue,
                                    double step,
                                    int precision,
                                    double deadband,
                                    BBacnetObjectIdentifier bacnetObjectId,
                                    String role
    ) {
        //TODO: value will always be double??
        double value = ((BIHonWizardNumericPoint) srcComponent).getValue();
        BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();
        BRangeSlider result = make(value, unit, step, precision, label, minValue, maxValue, deadband,
                 role, String.valueOf(value),String.valueOf(minValue), String.valueOf(maxValue), String.valueOf(deadband));
        if(result == null) {
            return null;
        }

        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");
        return result;
    }

    public static BRangeSlider make(double value,
                                    BUnit unit,
                                    double step,
                                    int precision,
                                    String label,
                                    double minValue,
                                    double maxValue,
                                    double deadband,
                                    String role,
                                    BOrd commonSlot,
                                    String highPrecisionValue,
                                    String highPrecisionMin,
                                    String highPrecisionMax,
                                    String highPrecisionDeadBand
    ) {
        BRangeSlider result = make(value,  unit, step, precision, label, minValue, maxValue, deadband, role,
                highPrecisionValue, highPrecisionMin, highPrecisionMax, highPrecisionDeadBand);
        if(result == null) {
            return null;
        }

        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");
        return result;
    }

    private static BRangeSlider make(double value,
                                    BUnit unit,
                                    double step,
                                    int precision,
                                    String label,
                                    double minValue,
                                    double maxValue,
                                    double deadband,
                                    String role,
                                    String highPrecisionValue,
                                    String highPrecisionMin,
                                    String highPrecisionMax,
                                    String highPrecisionDeadBand
    ) {
        if(role == null || role.isEmpty()) {
            logger.warning("RangeSlider creation issue: Belong or Role is empty for (" + label + ")");
            return null;
        }
        BRangeSlider rangeSlider = new BRangeSlider();
        rangeSlider.setLabel(label);
        rangeSlider.setValue(value);
        rangeSlider.setVisible(true);
        //How to get it? from tags??
        rangeSlider.setMin(minValue);
        //How to get it? from tags??
        rangeSlider.setMax(maxValue);
        rangeSlider.setDeadband(deadband);
        rangeSlider.setRole(role);
        rangeSlider.setUnit(unit == null || "null".equals(unit.getSymbol()) ? "" : unit.getSymbol());
        if(null != unit) {
        	rangeSlider.setFacets(BRangeSlider.unit, BFacets.make(BFacets.UNITS, unit));
        }
        rangeSlider.setHighPrecisionValue(highPrecisionValue);
        rangeSlider.setHighPrecisionMin(highPrecisionMin);
        rangeSlider.setHighPrecisionMax(highPrecisionMax);
        rangeSlider.setHighPrecisionDeadband(highPrecisionDeadBand);
        rangeSlider.setStep(step);
        rangeSlider.setPrecision(precision);
        return rangeSlider;
    }

    @Override
    public void setValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setValue(doubleValue);
            setHighPrecisionValue(value);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BRangeSlider (" + getLabel() + "): " + value);
        }
    }

    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(Double.toString(getValue()));
	}


}
