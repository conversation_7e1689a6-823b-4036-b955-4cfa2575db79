/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.widgetcomponents.BOption;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

import javax.baja.sys.BEnumRange;
import java.util.List;
/**
 * <AUTHOR>
 */
public abstract class DynamicWidgetWithOptionBase extends DynamicWidgetBase{
    private List<BOption> options;

    @SuppressWarnings({"java:S107"})
    public DynamicWidgetWithOptionBase(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);

    }
    public DynamicWidgetWithOptionBase() {
        super();
    }


    public List<BOption> getOptions() {
        return options;
    }

    public void setOptions(List<BOption> options) {
        this.options = options;
    }



    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        if(options == null || options.isEmpty()) return json;
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < options.size(); i++) {
            BOption option = options.get(i);
            JSONObject optionJson = option.toJson();
            jsonArray.put(optionJson);
        }
        json.put("options", jsonArray);
        return json;
    }

    public abstract List<BOption> makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange);
}
