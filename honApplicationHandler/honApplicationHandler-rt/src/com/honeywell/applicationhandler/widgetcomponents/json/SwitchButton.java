/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.tridium.json.JSONObject;
/**
 * <AUTHOR>
 */
public class SwitchButton extends DynamicWidgetBase{
    private String saveConvert = "";
    private String loadConvert = "";

    public SwitchButton(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        this.saveConvert = getSaveConvertFunc();
        this.loadConvert = getLoadConvertFunc();
    }

    public SwitchButton(){
        super();
    }
    public String getSaveConvert() {
        return saveConvert;
    }

    public void setSaveConvert(String saveConvert) {
        this.saveConvert = saveConvert;
    }

    public String getLoadConvert() {
        return loadConvert;
    }

    public void setLoadConvert(String loadConvert) {
        this.loadConvert = loadConvert;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        json.put("saveConvert", this.getSaveConvert());
        json.put("loadConvert", this.getLoadConvert());
        return json;
    }

    private static String getLoadConvertFunc() {
        return "function loadConvert(propertyValue) {\n" +
                "return propertyValue == 1 || propertyValue == true;\n" +
                "}";
    }

    private static String getSaveConvertFunc() {
        return "function saveConvert(value) {\n" +
                "return value == true || value == 1 ? 1 : 0;\n" +
                "}";
    }
}
