/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;

import javax.baja.sys.BValue;
import javax.baja.sys.Type;

/**
 * <AUTHOR>
 */
public final class PropertyTag {
    private Boolean isFromStaticJson;
    private Type type;
    private String propertyName;



    private String componentName;

    private BValue value;

    public BHonWizSelector getSelector() {
        return selector;
    }

    public void setSelector(BHonWizSelector selector) {
        this.selector = selector;
    }

    public BDynamicWidgetComponentBase getWidgetComponentBase() {
        return widgetComponentBase;
    }

    public void setWidgetComponentBase(BDynamicWidgetComponentBase widgetComponentBase) {
        this.widgetComponentBase = widgetComponentBase;
    }

    BHonWizSelector selector;

    BDynamicWidgetComponentBase widgetComponentBase;
    public Boolean getFromStaticJson() {
        return isFromStaticJson;
    }

    public void setFromStaticJson(Boolean fromStaticJson) {
        isFromStaticJson = fromStaticJson;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }


    public PropertyTag(String propertyName, Type type, Boolean isFromStaticJson){
        this.propertyName = propertyName;
        this.type = type;
        this.isFromStaticJson = isFromStaticJson;
    }
    public BValue getValue() {
        return value;
    }

    public void setValue(BValue value) {
        this.value = value;
    }
    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

}