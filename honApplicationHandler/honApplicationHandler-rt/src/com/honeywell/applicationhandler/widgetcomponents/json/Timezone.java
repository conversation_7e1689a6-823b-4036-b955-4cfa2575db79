/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.widgetcomponents.BOption;

import javax.baja.sys.BEnumRange;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 */
public class Timezone extends DynamicWidgetWithOptionBase{
    @SuppressWarnings({"java:S107"})
    public Timezone(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        this.setOptions(makeOptions(null, null));
    }
    public Timezone(){
        super();
    }

    @Override
    public List<BOption> makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        List<BOption> options = new ArrayList<>();
        BEnumRange enumRange = BTimezoneEnum.DEFAULT.getRange();

        for (int i : enumRange.getOrdinals()) {
            String tag = enumRange.getDisplayTag(i, null);
            BOption option = new BOption();
            option.setKey(i);
            option.setText(tag);
            option.setValue(i);
            options.add(option);
        }
        return options;
    }
}
