/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.tridium.json.JSONObject;

/**
 * <AUTHOR>
 */
public class RangeSlider extends DynamicWidgetUnitSupportBase{
    private String color = "";
    private String belong = "";


    @SuppressWarnings({"java:S107"})
    public RangeSlider(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible,  String unitGroup, String color, String belong, String role) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible, unitGroup);
        if(belong == null || belong.isEmpty()) {
            throw new IllegalArgumentException("RangeSlider creation issue: Belong is empty for " + itemName + "(" + label + ")");
        }
        this.color = color;
        this.belong = belong;
    }
    public RangeSlider(){
        super();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getBelong() {
        return belong;
    }

    public void setBelong(String belong) {
        this.belong = belong;
    }



    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        json.put("color", this.getColor());
        json.put("belong", this.getBelong());
        return json;
    }
}
