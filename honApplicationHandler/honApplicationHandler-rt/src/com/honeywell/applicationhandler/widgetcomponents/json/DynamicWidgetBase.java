/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.tridium.json.JSONObject;
/**
 * <AUTHOR>
 */

public abstract class DynamicWidgetBase {
    private String tabInPage = "";
    private String inline = "";
    private boolean readOnly = false;
    private String help = "";
    private String name = "";

    private String componentType = "";


    public String getTabInPage() {
        return tabInPage;
    }

    public void setTabInPage(String tabInPage) {
        this.tabInPage = tabInPage;
    }

    public String getInline() {
        return inline;
    }

    public void setInline(String inline) {
        this.inline = inline;
    }


    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getHelp() {
        return help;
    }

    public void setHelp(String help) {
        this.help = help;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public  JSONObject toJSON(){
        JSONObject json = new JSONObject();
        json.put("tabInPage", this.getTabInPage() == null?"":this.getTabInPage());
        json.put("inline", this.getInline() == null?"":this.getInline());
        json.put("readOnly", this.isReadOnly());
        json.put("help", this.getHelp() == null?"":this.getHelp());
        json.put("name", this.getName() == null?"":this.getName());
        json.put("componentType", this.getComponentType());
        return json;
    }
    @SuppressWarnings({"java:S107"})
    public DynamicWidgetBase(String tabInPage, String inline, boolean readOnly, String help, String name, String label, boolean visible) {
        this.setComponentType(this.getClass().getSimpleName());
        this.tabInPage = tabInPage;
        this.inline = inline;
        this.readOnly = readOnly;
        this.help = help;
        this.name = name;
    }
    public DynamicWidgetBase() {

    }

}
