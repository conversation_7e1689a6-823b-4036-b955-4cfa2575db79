/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.widgetcomponents.BOption;

import javax.baja.naming.SlotPath;
import javax.baja.sys.BEnumRange;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 */
public class SelectWidget extends DynamicWidgetWithOptionBase{
    @SuppressWarnings({"java:S107"})
    public SelectWidget(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, BEnumRange targetEnumRange, BEnumRange tagRange) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        this.setOptions(makeOptions(tagRange, targetEnumRange));
    }
    public SelectWidget(){
        super();
    }

    @Override
    public List<BOption> makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        List<BOption> newOptions = new ArrayList<>();
        if(targetEnumRange != null) {
            for (int i : targetEnumRange.getOrdinals()) {
                String tag = targetEnumRange.getDisplayTag(i, null);
                BOption option = new BOption();
                option.setKey(i);
                option.setText(tag);
                option.setValue(i);
                if(tagEnumRange != null && tagEnumRange.isOrdinal(i)) {
                    String image = SlotPath.unescape(tagEnumRange.getTag(i));
                    option.setImage(image);
                }
                newOptions.add(option);
            }
        }
        return newOptions;
    }
}
