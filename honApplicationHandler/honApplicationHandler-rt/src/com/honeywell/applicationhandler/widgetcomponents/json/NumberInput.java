/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;
/**
 * <AUTHOR>
 */
public class NumberInput extends DynamicWidgetUnitSupportBase{
    public NumberInput(){
        super();
    }
    @SuppressWarnings({"java:S107"})
    public NumberInput(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, String unitGroup) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible, unitGroup);
    }
}
