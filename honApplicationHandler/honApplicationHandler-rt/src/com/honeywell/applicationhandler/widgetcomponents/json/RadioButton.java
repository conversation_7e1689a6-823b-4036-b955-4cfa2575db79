/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;



import com.honeywell.applicationhandler.widgetcomponents.BOption;
import javax.baja.sys.BEnumRange;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 */
public class RadioButton extends DynamicWidgetWithOptionBase{

    @SuppressWarnings({"java:S107"})
    public RadioButton(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, BEnumRange enumRange) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        setOptions(makeOptions( null, enumRange));
    }
    public RadioButton(){
        super();
    }

    @Override
    public List<BOption> makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        List<BOption> options = new ArrayList<>();
        if(targetEnumRange != null) {
            for(int i=0;i<targetEnumRange.getOrdinals().length;i++) {
                int ordinal = targetEnumRange.getOrdinals()[i];
                String tag = targetEnumRange.getDisplayTag(ordinal, null);
                BOption option = new BOption();
                option.setKey(i);
                option.setText(tag);
                option.setValue(ordinal);
                options.add(option);
            }
        }
        return options;
    }
}
