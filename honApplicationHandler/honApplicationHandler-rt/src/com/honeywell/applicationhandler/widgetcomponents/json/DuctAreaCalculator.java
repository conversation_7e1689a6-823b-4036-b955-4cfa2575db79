/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;



import javax.baja.sys.BEnumRange;



/**
 * <AUTHOR>
 */
public class DuctAreaCalculator extends DynamicWidgetBase{
    public DuctAreaCalculator(){
        super();
    }

    @SuppressWarnings({"java:S107"})
    public DuctAreaCalculator(String tabInPage, String inline, boolean readOnly, String help, String itemName,
                              String label, boolean visible,  BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);

    }


}
