/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.tridium.json.JSONObject;
/**
 * <AUTHOR>
 */
public class DynamicWidgetUnitSupportBase extends DynamicWidgetBase{

    private BUnitGroupEnum unitGroup = BUnitGroupEnum.measurementType;

    @SuppressWarnings({"java:S107"})
    public DynamicWidgetUnitSupportBase(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, String unitGroup) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        BUnitGroupEnum unitGroupEnum = BUnitGroupEnum.none;
        if (null != unitGroup && !unitGroup.isEmpty()) {
            if (BUnitGroupEnum.measurementType.getDisplayTag(null).equals(unitGroup)) {
                unitGroupEnum = BUnitGroupEnum.measurementType;
            } else if(BUnitGroupEnum.airflowUnit.getDisplayTag(null).equals(unitGroup)) {
                unitGroupEnum = BUnitGroupEnum.airflowUnit;
            }
        }

        this.unitGroup = unitGroupEnum;
    }

    public DynamicWidgetUnitSupportBase() {
        super();
    }


    public BUnitGroupEnum getUnitGroup() {
        return unitGroup;
    }

    public void setUnitGroup(BUnitGroupEnum unitGroup) {
        this.unitGroup = unitGroup;
    }


    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        json.put("unitGroup", this.getUnitGroup().getOrdinal());
        return json;
    }


}
