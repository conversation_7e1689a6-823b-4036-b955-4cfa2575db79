/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.tridium.json.JSONObject;
/**
 * <AUTHOR>
 */
public class SingleSlider extends DynamicWidgetUnitSupportBase{
    private String color = "";

    public SingleSlider(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, String unitGroup, String color) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible, unitGroup);
        this.color = color;
    }
    public SingleSlider(){
        super();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }


    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        json.put("color", this.getColor());
        return json;
    }
}
