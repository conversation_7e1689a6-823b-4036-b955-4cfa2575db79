/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents.json;

import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.widgetcomponents.BOption;
import com.tridium.json.JSONObject;

import javax.baja.sys.BEnumRange;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Checkbox extends DynamicWidgetWithOptionBase {

    private String saveConvert = "";
    private String loadConvert = "";

    @SuppressWarnings({"java:S107"})
    public Checkbox(String tabInPage, String inline, boolean readOnly, String help, String itemName, String label, boolean visible, BEnumRange option) {
        super(tabInPage, inline, readOnly, help, itemName, label, visible);
        this.saveConvert = HoneywellConfigurableDeviceUtil.getTransferNumberListToBitwiseValueFuncString();
        this.loadConvert = HoneywellConfigurableDeviceUtil.getTransferBitwiseValueToNumberListFuncString();
        this.setOptions(makeOptions(option, null));

    }

    public Checkbox() {
        super();
    }


    public String getSaveConvert() {
        return saveConvert;
    }

    public void setSaveConvert(String saveConvert) {
        this.saveConvert = saveConvert;
    }

    public String getLoadConvert() {
        return loadConvert;
    }

    public void setLoadConvert(String loadConvert) {
        this.loadConvert = loadConvert;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = super.toJSON();
        json.put("saveConvert", this.getSaveConvert());
        json.put("loadConvert", this.getLoadConvert());
        return json;
    }

    @Override
    public List<BOption> makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        List<BOption> options = new ArrayList<>();
        if(tagEnumRange != null) {
            for(int i=0;i<tagEnumRange.getOrdinals().length;i++) {
                int ordinal = tagEnumRange.getOrdinals()[i];
                String tag = tagEnumRange.getDisplayTag(ordinal, null);
                BOption option = new BOption();
                option.setKey(i);
                option.setText(tag);
                option.setValue(ordinal);
                options.add(option);
            }
        }
        return options;
    }
}
