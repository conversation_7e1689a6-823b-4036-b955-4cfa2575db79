/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>hus<PERSON>
 * @since Jan 29, 2025
 */
@NiagaraProperty(
        name = "bacnetObjectId",
        type = "BBacnetObjectIdentifier",
        defaultValue = "BBacnetObjectIdentifier.DEFAULT",
        flags = Flags.READONLY
)

@NiagaraProperty (name = "commonSlot", defaultValue = "BOrd.DEFAULT", type = "BOrd")
@NiagaraType
public abstract class BDynamicWidgetComponentBase extends BWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase(*********)1.0$ @*/
/* Generated Wed Aug 27 13:18:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "bacnetObjectId"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code bacnetObjectId} property.
   * @see #getBacnetObjectId
   * @see #setBacnetObjectId
   */
  public static final Property bacnetObjectId = newProperty(Flags.READONLY, BBacnetObjectIdentifier.DEFAULT, null);
  
  /**
   * Get the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public BBacnetObjectIdentifier getBacnetObjectId() { return (BBacnetObjectIdentifier)get(bacnetObjectId); }
  
  /**
   * Set the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public void setBacnetObjectId(BBacnetObjectIdentifier v) { set(bacnetObjectId, v, null); }

////////////////////////////////////////////////////////////////
// Property "commonSlot"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code commonSlot} property.
   * @see #getCommonSlot
   * @see #setCommonSlot
   */
  public static final Property commonSlot = newProperty(0, BOrd.DEFAULT, null);
  
  /**
   * Get the {@code commonSlot} property.
   * @see #commonSlot
   */
  public BOrd getCommonSlot() { return (BOrd)get(commonSlot); }
  
  /**
   * Set the {@code commonSlot} property.
   * @see #commonSlot
   */
  public void setCommonSlot(BOrd v) { set(commonSlot, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDynamicWidgetComponentBase.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    public abstract void setValue(String value);


    public void makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {}

    protected void hideSlot(String slotName) {
        Slot slot = getSlot(slotName);
        if(slot != null) {
            setFlags(getSlot(slotName), Flags.HIDDEN);
        }
    }
}
