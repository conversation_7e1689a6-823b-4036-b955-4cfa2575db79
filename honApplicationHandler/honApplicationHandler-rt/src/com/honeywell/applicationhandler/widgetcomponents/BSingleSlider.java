/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import java.util.logging.Logger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0.0", type = "double")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraType
public class BSingleSlider extends BDynamicWidgetUnitSupportComponentBase{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BSingleSlider(1193416879)1.0$ @*/
/* Generated Wed Aug 27 13:18:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public double getValue() { return getDouble(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(double v) { setDouble(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSingleSlider.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BSingleSlider make(BComponent srcComponent,
                                     String label,
                                     double minValue,
                                     double maxValue,
                                     double step,
                                     int precision,
                                     BBacnetObjectIdentifier bacnetObjectId

    ) {
        //TODO: value will always be double??
        double value = ((BIHonWizardNumericPoint) srcComponent).getValue();
        BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();


		BSingleSlider result = make(value, unit, step, precision, label, minValue, maxValue, String.valueOf(value), String.valueOf(minValue), String.valueOf(maxValue));
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BSingleSlider make(double value,
                                      BUnit unit,
                                      double step,
                                      int precision,
                                      String label,
                                      double minValue,
                                      double maxValue,
                                      BOrd commonSlot,
                                      String highPrecisionValue,
                                      String highPrecisionMin,
                                      String highPrecisionMax

    ) {
		BSingleSlider result = make(value,  unit, step, precision, label, minValue, maxValue, highPrecisionValue, highPrecisionMin, highPrecisionMax);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BSingleSlider make(double value,
                                     BUnit unit,
                                     double step,
                                     int precision,
                                     String label,
                                     double minValue,
                                     double maxValue,
                                     String highPrecisionValue,
                                     String highPrecisionMin,
                                     String highPrecisionMax
    ) {
        BSingleSlider singleSlider = new BSingleSlider();
        singleSlider.setLabel(label);
        singleSlider.setValue(value);
        singleSlider.setVisible(true);
        //How to get it? from tags??
        singleSlider.setMin(minValue);
        //How to get it? from tags??
        singleSlider.setMax(maxValue);
        singleSlider.setUnit(unit == null || "null".equals(unit.getSymbol()) ? "" : unit.getSymbol());
        if(null != unit) {
        	singleSlider.setFacets(BSingleSlider.unit, BFacets.make(BFacets.UNITS, unit));
        }
        singleSlider.setHighPrecisionValue(highPrecisionValue);

        singleSlider.setHighPrecisionMin(highPrecisionMin);
        singleSlider.setHighPrecisionMax(highPrecisionMax);
        singleSlider.setStep(step);
        singleSlider.setPrecision(precision);
        return singleSlider;
    }

    @Override
    public void setValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setValue(doubleValue);
            setHighPrecisionValue(value);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BSingleSlider (" + getLabel() + "): " + value);
        }
    }



    @Override
	public BString getValueFromWizComp() {
		return BString.make(Double.toString(getValue()));
	}


}
