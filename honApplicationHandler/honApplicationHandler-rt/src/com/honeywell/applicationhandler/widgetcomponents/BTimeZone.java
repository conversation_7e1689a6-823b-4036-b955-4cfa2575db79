/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;


import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BString;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> - <PERSON>hya<PERSON><PERSON>har Madhusudhan
 * @since Jun 10, 2025
 */
@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "BTimezoneEnum.DEFAULT", type = "BTimezoneEnum")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraType
public class BTimeZone extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BTimeZone(303887941)1.0$ @*/
/* Generated Wed Aug 27 13:18:26 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, BTimezoneEnum.DEFAULT, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public BTimezoneEnum getValue() { return (BTimezoneEnum)get(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(BTimezoneEnum v) { set(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTimeZone.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BTimeZone make(BTimezoneEnum value,
                                 String label,
                                 BOrd commonSlot
    ) {
        BTimeZone timeZone = new BTimeZone();
        timeZone.setLabel(label);
        timeZone.setValue(value);
        timeZone.setVisible(true);
        timeZone.hideSlot("bacnetObjectId");
        timeZone.setCommonSlot(commonSlot);

        return timeZone;
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(String.valueOf(getValue().getOrdinal()));
	}

	@Override
	public void setValue(String value) {
		setValue(BTimezoneEnum.make(Integer.valueOf(value)));
		
	}

    
}