/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

@NiagaraProperty(name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0", type = "int")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraType
public class BAirflowUnit extends BDynamicWidgetComponentBase{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BAirflowUnit(4009050947)1.0$ @*/
/* Generated Wed Aug 27 12:03:58 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public int getValue() { return getInt(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(int v) { setInt(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAirflowUnit.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();
    public static BAirflowUnit make(BComponent srcComponent, String label, BBacnetObjectIdentifier bacnetObjectId) {
        int value = ((BIHonWizardEnumPoint) srcComponent).getValue().getOrdinal();
        BAirflowUnit result = make(value, label);
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");
        return result;
    }

    public static BAirflowUnit make(int value, String label, BOrd commonSlot) {
        BAirflowUnit result = make(value, label);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");
        return result;
    }

    private static BAirflowUnit make(int value, String label) {
        BAirflowUnit airflowUnit = new BAirflowUnit();
        airflowUnit.setLabel(label);
        airflowUnit.setValue(value);
        airflowUnit.setVisible(true);
        return airflowUnit;
    }

    @Override
    public void setValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BDropdown (" + getLabel() + "): " + value);
        }
    }



    @Override
    public BString getValueFromWizComp() {
        return BString.make(Integer.toString(getValue()));
    }


}
