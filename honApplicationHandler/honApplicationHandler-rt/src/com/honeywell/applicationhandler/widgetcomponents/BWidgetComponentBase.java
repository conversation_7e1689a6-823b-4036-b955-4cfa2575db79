/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BValue;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since Jan 29, 2025
 */
@NiagaraType
public abstract class BWidgetComponentBase extends BComponent {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase(2979906276)1.0$ @*/
/* Generated Wed Jan 29 08:38:53 IST 2025 by <PERSON><PERSON>-<PERSON>-<PERSON><PERSON> (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BWidgetComponentBase.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public abstract BValue getValueFromWizComp();
  
}
