/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tagmappings;

import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.exceptions.PointNotExistException;
import com.honeywell.applicationhandler.exceptions.PointNotSingleException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.generate.TagMappingInfo;

import javax.baja.sys.BComponent;
import java.util.ArrayList;
import java.util.List;

/**
 * Check naming conventions, there must be one and must be only one component or slot exit in device
 */

/**
 * <AUTHOR> Sun
 */
public class NamingConventionValidation extends TagMappingValidation {

    @Override
    protected void validate(TagMappingInfo tagMappingInfo, List<TagMappingInfo> tagMappingInfos, List<BIHonWizardPoint> points, BComponent device) throws ValidationException {
        if(tagMappingInfo.fullPointName == null && tagMappingInfo.namingConventions.isEmpty()) {
            return;
        }

        int matched = 0;
        List<String> slotPaths = new ArrayList<>();

        for(BIHonWizardPoint point : points) {
            if(tagMappingInfo.isMatched(point.getName())) {
                matched++;
                slotPaths.add(((BComponent)point).getSlotPath().toString());
            }
        }

        if(matched == 0) {
            throw new PointNotExistException(tagMappingInfo.getNamingConventionDisplayString(), null);
        }
        else if(matched > 1) {
            throw new PointNotSingleException(tagMappingInfo.getNamingConventionDisplayString(), null, slotPaths);
        }

    }
}
