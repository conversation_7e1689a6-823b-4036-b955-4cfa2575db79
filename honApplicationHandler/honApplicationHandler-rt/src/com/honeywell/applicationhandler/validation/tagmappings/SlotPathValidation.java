/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tagmappings;

import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.exceptions.PointNotExistException;
import com.honeywell.applicationhandler.exceptions.PointNotSingleException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.generate.TagMappingInfo;

import javax.baja.naming.BOrd;
import javax.baja.sys.BComponent;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Check the slot path, there must be one and must be only one component or slot exit in device
 */

/**
 * <AUTHOR> Sun
 */
public class SlotPathValidation extends TagMappingValidation {

    private static final String PATH_DELIMITER = "/";

    @Override
    protected void validate(TagMappingInfo tagMappingInfo, List<TagMappingInfo> tagMappingInfos, List<BIHonWizardPoint> points, BComponent device) throws ValidationException {
        if(tagMappingInfo.slotPath == null) {
            return;
        }

        String baseSlotPath = device.getSlotPath().toString();

        try {
            BOrd slotPath = tagMappingInfo.slotPath.startsWith("/")
                    ? BOrd.make(Const.STATION_ORD_BASE + baseSlotPath + tagMappingInfo.slotPath)
                    : BOrd.make(Const.STATION_ORD_BASE + baseSlotPath + PATH_DELIMITER + tagMappingInfo.slotPath);

            slotPath.resolve().get();
        }
        catch(Exception ex) {
            throw new PointNotExistException(null, tagMappingInfo.slotPath);
        }

        List<TagMappingInfo> tagMappingInfosWithSameSlotPath = tagMappingInfos.stream().filter(tagMapping -> tagMappingInfo.slotPath.equals(tagMapping.slotPath)).collect(Collectors.toList());

        if(tagMappingInfosWithSameSlotPath.size() > 1) {
            throw new PointNotSingleException(null, tagMappingInfo.slotPath, null);
        }

    }
}
