/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tagmappings;

import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.exceptions.InstanceDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.generate.TagMappingInfo;

import javax.baja.sys.BComponent;
import java.util.ArrayList;
import java.util.List;

import static com.honeywell.applicationhandler.common.Constants.AIRFLOW_UNIT;
import static com.honeywell.applicationhandler.common.Constants.MEASUREMENT_TYPE;
import static com.honeywell.applicationhandler.common.Constants.TIMEZONE_TYPE;
/**
 * Check widget type, at most there is a widget type for DuctAreaCalculator, measurement type, timezone ,schedule
 */

/**
 * <AUTHOR>
 */
public class WidgetTypeValidation extends TagMappingValidation{
    private static List<String> onlyOneInstanceWidgetTypes = new ArrayList<>();
    @Override
    protected void validate(TagMappingInfo tagMappingInfo, List<TagMappingInfo> tagMappingInfos, List<BIHonWizardPoint> points, BComponent device) throws ValidationException {
        if(tagMappingInfo.widgetType == null || tagMappingInfo.widgetType.isEmpty()) {
            return;
        }
        if(onlyOneInstanceWidgetTypes.contains(tagMappingInfo.widgetType)) {
            for(TagMappingInfo otherTagMappingInfo : tagMappingInfos) {
                if(otherTagMappingInfo.equals(tagMappingInfo)) {
                    continue;
                }
                if(otherTagMappingInfo.widgetType != null && otherTagMappingInfo.widgetType.equals(tagMappingInfo.widgetType)) {
                    List<String> identityList = getStrings(tagMappingInfo);
                    identityList.addAll(getStrings(otherTagMappingInfo));
                    identityList.sort(String::compareTo);
                    throw new InstanceDuplicatedException(tagMappingInfo.widgetType, identityList, lex.get("error.tagvalidation.reason.widgettype.duplicated"));
                }
            }
        }

    }

    private static List<String> getStrings(TagMappingInfo tagMappingInfo) {
        List<String> identityList = new ArrayList<>();
        if(null != tagMappingInfo.slotPath){
            identityList.add(tagMappingInfo.slotPath);
        }else if(null != tagMappingInfo.fullPointName) {
            identityList.add(tagMappingInfo.fullPointName);
        } else if( null != tagMappingInfo.getNamingConventionDisplayString()){
            identityList.add(tagMappingInfo.getNamingConventionDisplayString());
        }
        return identityList;
    }

    static {
        onlyOneInstanceWidgetTypes.add(TIMEZONE_TYPE);
        onlyOneInstanceWidgetTypes.add(MEASUREMENT_TYPE);
        onlyOneInstanceWidgetTypes.add(AIRFLOW_UNIT);
    }
}
