/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.validation.tagmappings;

import static com.honeywell.applicationhandler.ontology.Const.TEMPLATE_FILE_PATH;

import java.io.File;
import java.security.AccessController;
import java.security.PrivilegedActionException;
import java.security.PrivilegedExceptionAction;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import javax.baja.sys.BComponent;
import javax.baja.util.Lexicon;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.exceptions.DuplicateValidationException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.generate.TagMappingInfo;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.LexiconUtil;

/**
 * <AUTHOR> Sun
 */
public abstract class TagMappingValidation {
    protected static final Lexicon lex = LexiconUtil.getLexicon();
    protected static final List<TagMappingValidation> allValidations = new ArrayList<>();
    private static final List<String> duplicatedExceptionMessages = new ArrayList<>();

    protected abstract void validate(TagMappingInfo tagMappingInfo, List<TagMappingInfo> tagMappingInfos, List<BIHonWizardPoint> points, BComponent device) throws ValidationException;
    
    protected static synchronized void registerAll() {
        if(!allValidations.isEmpty()) {
            return;
        }

        allValidations.add(new NamingConventionValidation());
        allValidations.add(new SlotPathValidation());
        allValidations.add(new WidgetTypeValidation());
    }

    public static synchronized
    void validateAll(List<TagMappingInfo> tagMappingInfos, BHonWizardJob job) {
        String deviceName = job.getDevice().getDeviceName();
        List<BIHonWizardPoint> points = HoneywellConfigurableDeviceUtil.getAllHonWizardPoints((BComponent) job.getDevice());
        try {
            registerAll();
            duplicatedExceptionMessages.clear();

            for(TagMappingValidation tagValidation : allValidations) {
                for(TagMappingInfo tagMappingInfo : tagMappingInfos) {
                    validate(tagValidation, tagMappingInfo, tagMappingInfos, deviceName, points, job);
                }
            }
        }
        catch (Exception ex) {
            String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), ex.toString());
            HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage, ex);
            job.updateFailLog(logMessage, ex);
        }
    }

    /**
     * compare all columns in the tag mapping template file with the head names in the tag mapping file
     * @param headNames, all head names in the tag mapping file
     * @param job, job
     */
    public static void validateTemplate(Map<Integer, String> headNames, BHonWizardJob job){
        String deviceName = job.getDevice().getDeviceName();
        File tagMappingTemplateExcelFile = new File(TEMPLATE_FILE_PATH);
        if(!tagMappingTemplateExcelFile.exists()) {
            String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), lex.getText("error.tagvalidation.template.not.exist"));
            HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
            job.updateFailLog(logMessage);
            return;
        }
        if(tagMappingTemplateExcelFile.length() == 0){
            String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), lex.getText("error.tagvalidation.template.invalid"));
            HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
            job.updateFailLog(logMessage);
            return;
        }
		try {
			AccessController.doPrivileged((PrivilegedExceptionAction<Void>) () -> {
				try (XSSFWorkbook workbook = new XSSFWorkbook(tagMappingTemplateExcelFile)) {
					XSSFRow headerRow = workbook.getSheetAt(0).getRow(0);
					Iterator<Cell> cellIterator = headerRow.cellIterator();
					Map<Integer, String> templateHeaderNames = new HashMap<>();
					while (cellIterator.hasNext()) {
						Cell cell = cellIterator.next();
						if (cell.getCellType() == CellType.STRING) {
							String cellValue = cell.getStringCellValue();
							if (cellValue != null && !cellValue.isEmpty()) {
								templateHeaderNames.put(cell.getColumnIndex(), cellValue);
							}
						}
					}
					if (headNames.size() != templateHeaderNames.size()) {
						String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName),
								lex.getText("error.tagvalidation.template.header.size.mismatch", templateHeaderNames.size(), headNames.size()));
						HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
						job.updateFailLog(logMessage);
						return null;
					}
					for (Map.Entry<Integer, String> entry : headNames.entrySet()) {
						if (!templateHeaderNames.containsKey(entry.getKey())) {
							String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName),
									lex.getText("error.tagvalidation.template.header.index.mismatch", entry.getKey()));
							HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
							job.updateFailLog(logMessage);
							return null;
						}
						if (!templateHeaderNames.get(entry.getKey()).equals(entry.getValue())) {
							String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), lex.getText(
									"error.tagvalidation.template.header.value.mismatch", entry.getValue(), templateHeaderNames.get(entry.getKey())));
							HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
							job.updateFailLog(logMessage);
							return null;
						}
					}
				} catch (Exception e) {
					String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), e.getMessage());
					HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
					job.updateFailLog(logMessage);
				}
				return null;
			});
		} catch (PrivilegedActionException e) {
			Exception cause = e.getException();
			String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), cause.getMessage());
			HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage);
			job.updateFailLog(logMessage);
		}
    }

    private static void validate(TagMappingValidation tagMappingValidation, TagMappingInfo tagMappingInfo, List<TagMappingInfo> tagMappingInfos,
                                 String deviceName, List<BIHonWizardPoint> points, BHonWizardJob job) {
        try {
            tagMappingValidation.validate(tagMappingInfo, tagMappingInfos, points, (BComponent) job.getDevice());
        } catch (DuplicateValidationException e) {
            if(!duplicatedExceptionMessages.contains(e.getMessage())){
                logAndUpdateFail(deviceName, e, job);
                duplicatedExceptionMessages.add(e.getMessage());
            }

        } catch (ValidationException ex) {
            logAndUpdateFail(deviceName, ex, job);
        }
    }

    private static void logAndUpdateFail(String deviceName, Exception exception, BHonWizardJob job) {
        String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagmappingvalidation.failed", deviceName), exception);
        HoneywellDeviceWizardLogger.severe(logMessage);
        if (job != null) {
            job.updateFailLog(exception.getMessage());
        }
    }
}
