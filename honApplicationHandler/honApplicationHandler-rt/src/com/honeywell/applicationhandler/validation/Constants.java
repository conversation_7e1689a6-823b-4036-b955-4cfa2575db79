/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public final class Constants {
	public static final List<String> VALID_COLOR_NAMES = Arrays.asList("red", "blue", "black", "white", "gray", "green", "yellow", "orange", "teal");
	public static Map<String, String> COLOR_CODE_MAP = new HashMap<>();
	static {
	Map<String, String> colorCodeMap = new HashMap<>();
		colorCodeMap.put("red", "#EB4444");
		colorCodeMap.put("blue", "#13A5F6");
		colorCodeMap.put("black", "#000000");
		colorCodeMap.put("white", "#FFFFFF");
		colorCodeMap.put("gray", "#808080");
		colorCodeMap.put("green", "#279D2B");
		colorCodeMap.put("yellow", "#FFDB42");
		colorCodeMap.put("orange", "#E9730D");
		colorCodeMap.put("teal", "#12978C");
		COLOR_CODE_MAP = Collections.unmodifiableMap(colorCodeMap);
	}
	
	public static String getColorCodeForColor(String color) {
		if(color.startsWith("#")) {
			return color;
		}
		
		return COLOR_CODE_MAP.get(color.toLowerCase());
	}
	
	public static String getColorForColorCode(String color) {
		Iterator<Entry<String, String>> itr = COLOR_CODE_MAP.entrySet().iterator();
		while (itr.hasNext()) {
			Entry<String, String> colorEntry = itr.next();
			if(colorEntry.getValue().equalsIgnoreCase(color)) {
				return colorEntry.getKey();
			}
		}
		return color;
	}
	
}
