/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.numeric;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.OrderDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.common.Constants.*;

/**
 * Order tag value should be an integer number or a double number with non-zero decimal number.
 * Order tag values in one tab page should be unified.
 */

/**
 * <AUTHOR> Sun
 */
public class OrderValidation extends TagValidation {
    static List<String> noNeedCheckOrderDuplicateWidgetTypes = new ArrayList<>();

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_ORDER_TAG)) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        String orderString = tagValues.get(BHonWizardTag.HON_WIZ_ORDER_TAG).toString();

        if (!orderString.matches("^\\d*\\.?\\d+$")) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_ORDER_TAG,
                    orderString,
                    lex.getText("error.tagvalidation.reason.order.invalid")
            );
        }

        BIDataValue page = tagValues.get(BHonWizardTag.HON_WIZ_PAGE_TAG);
        BIDataValue tabInPage = tagValues.get(BHonWizardTag.HON_WIZ_TAB_IN_PAGE_TAG);
        BIDataValue widgetType = tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
        if(page == null || widgetType == null) {
            return;
        }

        Double orderNumber = Double.valueOf(orderString);
        for(BHonWizSelector otherSelector : honWizSelectors) {

            if(!shallCheckOrderDuplicate(widgetType, tabInPage, page, honWizSelector, otherSelector)) {
                continue;
            }

            String otherOrderString = otherSelector.get(SlotPath.escape(BHonWizardTag.WIZ_ORDER_TAG.getQName())).toString();
            Double otherOrderNumber = otherOrderString.matches("^\\d*\\.?\\d+$") ? Double.valueOf(otherOrderString) : null;

            if(orderNumber.equals(otherOrderNumber)) {
                List<String> slotPathList = Arrays.asList(slotPath, otherSelector.getSlotPath().toDisplayString());
                slotPathList.sort(String::compareTo);
                throw new OrderDuplicatedException(
                        orderString,
                        slotPathList,
                        lex.getText("error.tagvalidation.reason.order.duplicated")
                );
            }
        }
    }

    private boolean shallCheckOrderDuplicate(BIDataValue widgetType,
                                             BIDataValue tabInPage,
                                             BIDataValue page,
                                             BHonWizSelector honWizSelector,
                                             BHonWizSelector otherHonWizSelector) {

        BValue otherWidgetType = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.WGTTYPE_TAG.getQName()));
        BValue otherTabInPage = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.TAB_IN_PAGE_TAG.getQName()));
        BValue otherOrder = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.WIZ_ORDER_TAG.getQName()));
        BValue otherPage = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.PAGE_TAG.getQName()));

        if(otherHonWizSelector == honWizSelector) {
            return false;
        }

        if(otherOrder == null || otherPage == null || otherWidgetType == null) {
            return false;
        }

        if(!page.toString().equals(otherPage.toString())) {
            return false;
        }

        String tabInPageString = tabInPage == null ? page.toString() : tabInPage.toString();
        String otherTabInPageString = otherTabInPage == null ? otherPage.toString() : otherTabInPage.toString();

        if(!tabInPageString.equals(otherTabInPageString)) {
            return false;
        }

        if(!widgetType.toString().equals(otherWidgetType.toString())) {
            return true;
        }

        if(!noNeedCheckOrderDuplicateWidgetTypes.contains(widgetType.toString())) {
            return true;
        }

        BValue belong = honWizSelector.get(SlotPath.escape(BHonWizardTag.BELONG_TAG.getQName()));
        BValue otherBelong = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.BELONG_TAG.getQName()));

        if(belong == null || otherBelong == null) {
            return false;
        }

        return !belong.toString().equals(otherBelong.toString());
    }

    static {
        noNeedCheckOrderDuplicateWidgetTypes.add(RANGE_SLIDER);
        noNeedCheckOrderDuplicateWidgetTypes.add(DUCT_AREA_CALCULATOR_TYPE);
        noNeedCheckOrderDuplicateWidgetTypes.add(SCHEDULE);
    }
}
