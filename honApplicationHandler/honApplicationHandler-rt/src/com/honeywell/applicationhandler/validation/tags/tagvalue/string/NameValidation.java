/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.string;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.NameDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.common.Constants.*;
import static com.honeywell.applicationhandler.ontology.Const.SEPARATOR_LABEL_ROLE_CHARACTER;

/**
 * Name tag is string, and max length is {50}
 * Name tag values in one tab page should be unique.
 * Name tag values in one tab page should not contain the separator label role character.
 */

/**
 * <AUTHOR> Sun
 */
public class NameValidation extends StringTagValidation {

    private static final int MAX_NAME_LENGTH = 50;

    @Override
    protected int getMaxLength() {
        return MAX_NAME_LENGTH;
    }

    @Override
    protected String getTagName() {
        return BHonWizardTag.HON_WIZ_NAME_TAG;
    }

    static List<String> noNeedCheckOrderDuplicateWidgetTypes = new ArrayList<>();

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_NAME_TAG)) {
            return;
        }
        super.validate(honWizSelector, honWizSelectors, tagValues);

        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        String nameString = tagValues.get(BHonWizardTag.HON_WIZ_NAME_TAG).toString();
        if(nameString.contains(SEPARATOR_LABEL_ROLE_CHARACTER)){
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_NAME_TAG,
                    nameString,
                    lex.getText("error.tagvalidation.reason.name.invalid", SEPARATOR_LABEL_ROLE_CHARACTER)
            );
        }


        BIDataValue page = tagValues.get(BHonWizardTag.HON_WIZ_PAGE_TAG);
        BIDataValue widgetType = tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
        if(page == null || widgetType == null) {
            return;
        }

        for(BHonWizSelector otherSelector : honWizSelectors) {
            if(!shallCheckNameDuplicate(widgetType, page, honWizSelector, otherSelector)) {
                continue;
            }

            BValue otherName = otherSelector.get(SlotPath.escape(BHonWizardTag.NAME_TAG.getQName()));
            if (otherName == null){
                continue;
            }
            String otherNameString = otherName.toString();

            if(nameString.equals(otherNameString)) {
                List<String> slotPathList = Arrays.asList(slotPath, otherSelector.getSlotPath().toDisplayString());
                slotPathList.sort(String::compareTo);
                throw new NameDuplicatedException(
                        nameString,
                        slotPathList,
                        lex.getText("error.tagvalidation.reason.name.duplicated")
                );
            }
        }
    }


    private boolean shallCheckNameDuplicate(BIDataValue widgetType,
                                            BIDataValue page,
                                            BHonWizSelector honWizSelector,
                                            BHonWizSelector otherHonWizSelector) {

        BValue otherWidgetType = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.WGTTYPE_TAG.getQName()));
        BValue otherPage = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.PAGE_TAG.getQName()));

        if(otherHonWizSelector == honWizSelector) {
            return false;
        }

        if(otherPage == null || otherWidgetType == null) {
            return false;
        }
        if(!page.toString().equals(otherPage.toString())) {
            return false;
        }

        if(!noNeedCheckOrderDuplicateWidgetTypes.contains(widgetType.toString())) {
            return true;
        }
        return false;
    }

    static {
        noNeedCheckOrderDuplicateWidgetTypes.add(RANGE_SLIDER);
        noNeedCheckOrderDuplicateWidgetTypes.add(DUCT_AREA_CALCULATOR_TYPE);
        noNeedCheckOrderDuplicateWidgetTypes.add(SCHEDULE);
    }
}
