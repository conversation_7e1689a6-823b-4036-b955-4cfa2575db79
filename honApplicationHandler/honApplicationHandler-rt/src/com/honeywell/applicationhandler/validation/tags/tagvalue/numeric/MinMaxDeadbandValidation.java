/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.numeric;

import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.TagMissingException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BComponent;
import javax.baja.sys.BNumber;
import javax.baja.sys.BValue;
import java.util.List;
import java.util.Map;

/**
 * Min tag is double
 * Max tag is double
 * Deadband tag is double
 * Min < Max
 * Min <= current value <= Max
 * Deadband < (Max - Min)
 */

/**
 * <AUTHOR> Sun
 */
public class MinMaxDeadbandValidation extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        checkDouble(slotPath, tagValues, BHonWizardTag.HON_WIZ_MIN_TAG);
        checkDouble(slotPath, tagValues, BHonWizardTag.HON_WIZ_MAX_TAG);
        checkDouble(slotPath, tagValues, BHonWizardTag.HON_WIZ_DEADBAND_TAG);

        compareMinMax(slotPath, tagValues);
        checkDeadband(slotPath, tagValues);
        checkCurrentValueInRange(honWizSelector, tagValues);
    }

    private void checkDouble(String slotPath, Map<String, BIDataValue> tagValues, String tagName) throws ValidationException {
        BIDataValue tagValue = tagValues.get(tagName);
        if(tagValue == null) {
            return;
        }

        String value = tagValue.toString();
        if (!value.matches("^[-+]?\\d*\\.?\\d+$")) {
            throw new InvalidTagValueException(
                    slotPath,
                    tagName,
                    value,
                    lex.getText("error.tagvalidation.reason.numeric.invalid", tagName)
            );
        }
    }

    private void checkDeadband(String slotPath, Map<String, BIDataValue> tagValues) throws ValidationException {
        BIDataValue minTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MIN_TAG);
        BIDataValue maxTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MAX_TAG);
        BIDataValue deadbandTagValue = tagValues.get(BHonWizardTag.HON_WIZ_DEADBAND_TAG);

        if(deadbandTagValue == null || minTagValue == null || maxTagValue == null) {
            return;
        }

        Double min = Double.parseDouble(minTagValue.toString());
        Double max = Double.parseDouble(maxTagValue.toString());
        Double deadband = Double.parseDouble(deadbandTagValue.toString());

        if(deadband >= (max - min)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_DEADBAND_TAG,
                    deadbandTagValue.toString(),
                    lex.get("error.tagvalidation.reason.deadband.exceed.upper.limit")
            );
        }
    }

    private void compareMinMax(String slotPath, Map<String, BIDataValue> tagValues) throws ValidationException {
        BIDataValue minTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MIN_TAG);
        BIDataValue maxTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MAX_TAG);

        if((minTagValue == null && maxTagValue != null) || (minTagValue != null && maxTagValue == null)) {
            throw new TagMissingException(
                    slotPath,
                    minTagValue == null ? BHonWizardTag.HON_WIZ_MIN_TAG : BHonWizardTag.HON_WIZ_MAX_TAG,
                    lex.getText("error.tagvalidation.reason.minmax.inpair")
            );
        }

        if(minTagValue != null) {
            Double min = Double.parseDouble(minTagValue.toString());
            Double max = Double.parseDouble(maxTagValue.toString());

            if(min >= max) {
                throw new InvalidTagValueException(
                        slotPath,
                        BHonWizardTag.HON_WIZ_MIN_TAG + " / " + BHonWizardTag.HON_WIZ_MAX_TAG,
                        minTagValue + " / " + maxTagValue,
                        lex.getText("error.tagvalidation.reason.minmax.compare")
                );
            }
        }
    }

    private void checkCurrentValueInRange(BHonWizSelector honWizSelector, Map<String, BIDataValue> tagValues) throws ValidationException {
        Double currentValue = null;
        if(honWizSelector instanceof BHonWizPointSelector) {
            currentValue = getCurrentValue((BHonWizPointSelector) honWizSelector);
        }
        else if(honWizSelector instanceof BHonWizSlotSelector) {
            currentValue = getCurrentValue((BHonWizSlotSelector) honWizSelector);
        }

        if(currentValue == null) {
            return;
        }

        BIDataValue minTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MIN_TAG);
        BIDataValue maxTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MAX_TAG);

        if(minTagValue == null || maxTagValue == null) {
            return;
        }

        double min = Double.parseDouble(minTagValue.toString());
        double max = Double.parseDouble(maxTagValue.toString());

        if(currentValue < min || currentValue > max) {
            throw new InvalidTagValueException(
                    honWizSelector.getSlotPath().toDisplayString(),
                    BHonWizardTag.HON_WIZ_MIN_TAG + " / " + BHonWizardTag.HON_WIZ_MAX_TAG,
                    minTagValue + " / " + maxTagValue,
                    lex.getText("error.tagvalidation.reason.minmax.outofrange", currentValue, minTagValue, maxTagValue)
            );
        }
    }

    private Double getCurrentValue(BHonWizPointSelector honWizSelector) {
        BComponent parent = (BComponent) honWizSelector.getParent();
        if(!(parent instanceof BIHonWizardNumericPoint)) {
            return null;
        }

        return ((BIHonWizardNumericPoint) parent).getValue();
    }

    private Double getCurrentValue(BHonWizSlotSelector honWizSelector) {
        BComponent parent = (BComponent) honWizSelector.getParent();
        String sourcePropertyName = honWizSelector.getSourcePropertyNameString();
        BValue sourcePropertyValue = parent.get(sourcePropertyName);
        if(sourcePropertyValue == null) {
            return null;
        }

        if(!(sourcePropertyValue instanceof BNumber)) {
            return null;
        }

        return ((BNumber) sourcePropertyValue).getDouble();
    }
}
