/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.exceptions.TagNotSupportedException;
import com.honeywell.applicationhandler.exceptions.TagMissingException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Sun
 */
public abstract class WidgetTagsValidation extends TagValidation {

    protected abstract String getWidgetType();
    protected abstract List<String> getAdditionalRequiredTags();
    protected abstract List<String> getInapplicableTags();

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!checkWigetType(tagValues)) {
            return;
        }
        List<String> tagNames = Arrays.stream(tagValues.keySet().toArray(new String[0]))
                .collect(Collectors.toList());
        String slotPath = honWizSelector.getSlotPath().toDisplayString();

        checkAdditionalRequiredTags(tagNames, slotPath);
        checkInapplicableTags(tagNames, slotPath);
        validateWidget(honWizSelector, honWizSelectors, tagValues);
    }

    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        // there is no other validations required in default
        // override this method in subclass if needed
    }

    protected boolean checkWigetType(Map<String, BIDataValue> tagValues) {
        if(tagValues.containsKey(BHonWizardTag.HON_WIZ_WDTTYPE_TAG)) {
            BIDataValue widgetType = tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
            return widgetType.toString().equals(getWidgetType());
        }
        return false;
    }

    protected List<BHonWizSelector> getSelectorsWithSameBelongTag(List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) {
        List<BHonWizSelector> result = new ArrayList<>();
        String belong = tagValues.get(BHonWizardTag.HON_WIZ_BELONG_TAG).toString();
        for (BHonWizSelector honWizSelector : honWizSelectors) {
            BValue belongTagValue = honWizSelector.get(SlotPath.escape(BHonWizardTag.BELONG_TAG.getQName()));
            if(belongTagValue != null && belong.equals(belongTagValue.toString())) {
                result.add(honWizSelector);
            }
        }
        return result;
    }

    private void checkAdditionalRequiredTags(List<String> tags, String slotPath) throws ValidationException {
        List<String> missedTags = new ArrayList<>();
        List<String> additionalRequiredTags = getAdditionalRequiredTags();
        for(String additionalRequiredTag : additionalRequiredTags) {
            if(!tags.contains(additionalRequiredTag)) {
                missedTags.add(additionalRequiredTag);
            }
        }
        if(!missedTags.isEmpty()) {
            throw new TagMissingException(slotPath, missedTags.toString(), lex.getText("error.tagvalidation.reason.widget.requiredtags.missing", getWidgetType()));
        }
    }

    private void checkInapplicableTags(List<String> tags, String slotPath) throws ValidationException {
        List<String> attachedTags = new ArrayList<>();
        List<String> inapplicableTags = getInapplicableTags();
        for(String inapplicableTag : inapplicableTags) {
            if(tags.contains(inapplicableTag)) {
                attachedTags.add(inapplicableTag);
            }
        }
        if(!attachedTags.isEmpty()) {
            throw new TagNotSupportedException(slotPath, attachedTags, lex.getText("error.tagvalidation.reason.widget.tags.notsupported", getWidgetType()));
        }
    }
}
