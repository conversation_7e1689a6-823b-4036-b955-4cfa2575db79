/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue;

import java.util.List;
import java.util.Map;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BEnumRange;

import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 14, 2025
 */
public class UnitGroupValidation extends TagValidation {

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG)) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        BUnitGroupEnum unitGroup = (BUnitGroupEnum) tagValues.get(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);
        if(!isValidUnitGroup(unitGroup)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG,
                    unitGroup.toString(),
                    lex.getText("error.tagvalidation.reason.invalid.unitgroup")
            );
        }
    }

	private boolean isValidUnitGroup(BUnitGroupEnum unitGroup) {
		for (int ordinal : unitGroup.getRange().getOrdinals()) {
			String unitGroupTag = unitGroup.getRange().getTag(ordinal);
			String unitGroupTagWithoutSpaces = unitGroupTag.replaceAll("\\s", "");
			if (!unitGroupTagWithoutSpaces.equalsIgnoreCase("airflowunit") && !unitGroupTagWithoutSpaces.equalsIgnoreCase("measurementtype")
					&& !unitGroupTagWithoutSpaces.equalsIgnoreCase("none")) {
				return false;
			}
		}
		return true;
	}
}
