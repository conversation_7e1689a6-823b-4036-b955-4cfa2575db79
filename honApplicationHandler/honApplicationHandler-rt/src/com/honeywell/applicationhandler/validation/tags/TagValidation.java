/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BValue;
import javax.baja.sys.Sys;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.exceptions.DuplicateValidationException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.honeywell.applicationhandler.validation.tags.instancecheck.WidgetInstanceValidation;
import com.honeywell.applicationhandler.validation.tags.propertycheck.SourcePropertyExisting;
import com.honeywell.applicationhandler.validation.tags.selectorcheck.HonWizardSelectorValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.AirflowUnitWidgetValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.CheckboxGroupTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.CommonTagsRequired;
import com.honeywell.applicationhandler.validation.tags.tagcheck.DropdownTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.DuctAreaCalculatorTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.MeasurementTypeValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.NumberInputTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.RadioButtonGroupTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.RangeSliderTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.ScheduleTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.SelectButtonTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.SelectWidgetTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.SingleSliderTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.SwitchButtonValidation;
import com.honeywell.applicationhandler.validation.tags.tagcheck.TextInputTagsValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.ColorValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.OptionsValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.ReadOnlyValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.UnitGroupValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.WidgetTypeValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.numeric.MinMaxDeadbandValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.numeric.OrderValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.numeric.StepValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.BelongValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.GroupValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.HelpValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.NameValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.PageValidation;
import com.honeywell.applicationhandler.validation.tags.tagvalue.string.TabInPageValidation;

import static com.honeywell.applicationhandler.common.Constants.SCHEDULE;

/**
 * <AUTHOR> Sun
 */
public abstract class TagValidation {
    protected static final Lexicon lex = LexiconUtil.getLexicon();
    protected static final List<TagValidation> allValidations = new ArrayList<>();

    private static final List<String> duplicatedExceptionMessages = new ArrayList<>();
    protected abstract void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException;

    protected boolean isSchedule(Map<String, BIDataValue> tagValues) {
        if(tagValues.containsKey(BHonWizardTag.HON_WIZ_WDTTYPE_TAG)) {
            BIDataValue widgetType = tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
            return widgetType.toString().equals(SCHEDULE);
        }
        return false;
    }

    protected static synchronized void registerAll() {
        if(!allValidations.isEmpty() || !Sys.isStation()) {
            return;
        }

        allValidations.add(new HonWizardSelectorValidation());
        allValidations.add(new SourcePropertyExisting());
        allValidations.add(new CommonTagsRequired());
        allValidations.add(new WidgetTypeValidation());
        allValidations.add(new DropdownTagsValidation());
        allValidations.add(new SwitchButtonValidation());
        allValidations.add(new MeasurementTypeValidation());
        allValidations.add(new NumberInputTagsValidation());
        allValidations.add(new SingleSliderTagsValidation());
        allValidations.add(new RadioButtonGroupTagsValidation());
        allValidations.add(new SelectButtonTagsValidation());
        allValidations.add(new SelectWidgetTagsValidation());
        allValidations.add(new TextInputTagsValidation());
        allValidations.add(new CheckboxGroupTagsValidation());
        allValidations.add(new RangeSliderTagsValidation());
        allValidations.add(new DuctAreaCalculatorTagsValidation());
        allValidations.add(new ScheduleTagsValidation());
        allValidations.add(new PageValidation());
        allValidations.add(new GroupValidation());
        allValidations.add(new NameValidation());
        allValidations.add(new BelongValidation());
        allValidations.add(new TabInPageValidation());
        allValidations.add(new ColorValidation());
        allValidations.add(new ReadOnlyValidation());
        allValidations.add(new AirflowUnitWidgetValidation());
        allValidations.add(new MinMaxDeadbandValidation());
        allValidations.add(new OrderValidation());
        allValidations.add(new HelpValidation());
        allValidations.add(new StepValidation());
        allValidations.add(new OptionsValidation());
        allValidations.add(new UnitGroupValidation());
        allValidations.add(new WidgetInstanceValidation());
    }

    public static synchronized void validateAll(List<BHonWizSelector> honWizSelectors, BHonWizardJob job, int finalProgress) {
        String deviceName = job.getDevice().getDeviceName();
        int currentProgress = job.getProgress();
        try {
            registerAll();
            duplicatedExceptionMessages.clear();
            int halfProgress = (finalProgress - currentProgress) / 2;
            int selectorCount = honWizSelectors.size();
            float step = selectorCount == 0 ? halfProgress : ((float)halfProgress)/selectorCount;

            int index = 0;
            Map<String, Map<String, BIDataValue>> tagValuesMap = new LinkedHashMap<>();
            for(BHonWizSelector honWizSelector : honWizSelectors) {
                Map<String, BIDataValue> tagValues = HoneywellConfigurableDeviceUtil.getTagValues(honWizSelector, null);
                String slotPath = honWizSelector.getSlotPath().toDisplayString();
                tagValuesMap.put(slotPath, tagValues);
                job.progress((int)(currentProgress+step*(++index)));
            }

            currentProgress = job.getProgress();
            step = ((float)halfProgress)/allValidations.size();
            index = 0;
            for(TagValidation tagValidation : allValidations) {
                for(BHonWizSelector honWizSelector : honWizSelectors) {
                    Map<String, BIDataValue> tagValues = tagValuesMap.get(honWizSelector.getSlotPath().toDisplayString());
                    validate(tagValidation, deviceName, honWizSelector, honWizSelectors, tagValues, job);
                }
                job.progress((int)(currentProgress+step*(++index)));
            }
        }
        catch (Exception ex) {
            String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), ex.toString());
            HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage, ex);
            job.updateFailLog(logMessage, ex);
        }

        job.progress(finalProgress);
    }

    public static synchronized boolean validateAll(List<BHonWizSelector> honWizSelectors, String deviceName) {
        try {
            registerAll();
            duplicatedExceptionMessages.clear();
            Map<String, Map<String, BIDataValue>> tagValuesMap = new LinkedHashMap<>();
            for(BHonWizSelector honWizSelector : honWizSelectors) {
                Map<String, BIDataValue> tagValues = HoneywellConfigurableDeviceUtil.getTagValues(honWizSelector, null);
                String slotPath = honWizSelector.getSlotPath().toDisplayString();
                tagValuesMap.put(slotPath, tagValues);
            }

            for(TagValidation tagValidation : allValidations) {
                for(BHonWizSelector honWizSelector : honWizSelectors) {
                    Map<String, BIDataValue> tagValues = tagValuesMap.get(honWizSelector.getSlotPath().toDisplayString());
                    if(!validate(tagValidation, deviceName, honWizSelector, honWizSelectors, tagValues, null)) {
                        return false;
                    }
                }
            }
            return true;
        }
        catch (Exception ex) {
            String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), ex.toString());
            HoneywellDeviceWizardLogger.getLogger().log(Level.SEVERE, logMessage, ex);
            return false;
        }
    }

    private static synchronized boolean validate(TagValidation tagValidation, String deviceName, BHonWizSelector honWizSelector,
                                 List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues, BHonWizardJob job) {
        try {
            tagValues = HoneywellWidgetTagUtil.filterNoConfigOptionalTagValues(honWizSelector, tagValues);
            tagValidation.validate(honWizSelector, honWizSelectors, tagValues);
            return true;
        } catch (DuplicateValidationException e) {
            if(!duplicatedExceptionMessages.contains(e.getMessage())){
                logAndUpdateFail(deviceName, e, job);
                duplicatedExceptionMessages.add(e.getMessage());
            }
            return false;

        } catch (ValidationException ex) {
            logAndUpdateFail(deviceName, ex, job);
            return false;

        }
    }

    private static void logAndUpdateFail(String deviceName, Exception exception, BHonWizardJob job) {
        String logMessage = MessageFormat.format("{0}: {1}", lex.getText("error.tagvalidation.failed", deviceName), exception);
        HoneywellDeviceWizardLogger.severe(logMessage);
        if (job != null) {
            job.updateFailLog(exception.getMessage());
        }
    }
    protected BEnumRange getTargetEnumRange(BComponent parent) {
        if(parent instanceof BIHonWizardEnumPoint) {
            return ((BIHonWizardEnumPoint) parent).getEnumRange();
        }

        if(parent instanceof BIHonWizardBooleanPoint) {
            return ((BIHonWizardBooleanPoint) parent).getEnumRange();
        }

        return null;
    }

    protected BEnumRange getTargetEnumRange(BHonWizSlotSelector honWizSelector, BComponent parent) {
        String sourcePropertyName = honWizSelector.getSourcePropertyNameString();
        BValue sourcePropertyValue = parent.get(sourcePropertyName);
        if(sourcePropertyValue == null) {
            return null;
        }

        if(sourcePropertyValue instanceof BEnum) {
            if(!((BEnum) sourcePropertyValue).getRange().equals(BEnumRange.DEFAULT)) {
            	return ((BEnum) sourcePropertyValue).getRange();
            } else if(parent.getSlotFacets(parent.getSlot(sourcePropertyName)).get(BFacets.RANGE) instanceof BEnumRange) {
				return (BEnumRange) parent.getSlotFacets(parent.getSlot(sourcePropertyName)).get(BFacets.RANGE);
			}
        }

        return null;
    }
}
