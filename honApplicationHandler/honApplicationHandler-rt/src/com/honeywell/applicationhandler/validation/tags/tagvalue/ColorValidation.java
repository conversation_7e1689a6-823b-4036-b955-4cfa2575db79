/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.Constants;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import java.util.List;
import java.util.Map;

/**
 * Color tag is string, and the value can be a color name or a Hex color value like “#0f0f0f”
 */

/**
 * <AUTHOR> Sun
 */
public class ColorValidation extends TagValidation {

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_COLOR_TAG)) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        String colorValue = tagValues.get(BHonWizardTag.HON_WIZ_COLOR_TAG).toString();
        if(!isValidColorName(colorValue.toLowerCase())) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_COLOR_TAG,
                    colorValue,
                    lex.getText("error.tagvalidation.reason.invalid.color")
            );
        }


    }

    private boolean isValidColorName(String colorValue) {
        if(Constants.VALID_COLOR_NAMES.contains(colorValue.toLowerCase())) {
            return true;
        }
        if(!colorValue.startsWith("#")) {
            return false;
        }

        String hexValue = colorValue.substring(1);
        if(hexValue.length() != 6) {
            return false;
        }

        for(char c : hexValue.toCharArray()) {
            if(!Character.isDigit(c) && (c < 'a' || c > 'f')) {
                return false;
            }
        }
        return true;
    }
}
