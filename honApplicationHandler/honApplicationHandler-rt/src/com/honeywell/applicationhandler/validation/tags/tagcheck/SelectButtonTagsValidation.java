/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.ontology.BHonWizardTag;

import java.util.ArrayList;
import java.util.List;

import static com.honeywell.applicationhandler.common.Constants.SELECT_BUTTON;

/**
 * <AUTHOR> Sun
 */
public class SelectButtonTagsValidation extends WidgetTagsValidation {
    static List<String> inapplicableTags = new ArrayList<>();
    static List<String> additionalRequiredTags = new ArrayList<>();

    @Override
    protected String getWidgetType() {
        return SELECT_BUTTON;
    }

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return additionalRequiredTags;
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_DEADBAND_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_COLOR_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_STEP_TAG);
    }
}