/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.string;

import com.honeywell.applicationhandler.ontology.BHonWizardTag;
/**
 * Help tag is a string, and max length is {250}
 */

/**
 * <AUTHOR> zhang
 */
public class HelpValidation extends StringTagValidation {

    private static final int MAX_HELP_LENGTH = 250;

    @Override
    protected int getMaxLength() {
        return MAX_HELP_LENGTH;
    }

    @Override
    protected String getTagName() {
        return BHonWizardTag.HON_WIZ_HELP_TAG;
    }
}
