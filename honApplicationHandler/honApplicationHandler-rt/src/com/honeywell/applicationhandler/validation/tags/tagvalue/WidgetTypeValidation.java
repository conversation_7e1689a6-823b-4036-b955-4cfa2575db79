/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue;

import static com.honeywell.applicationhandler.common.Constants.*;
import static com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil.getWidgetTypeStringFromTagValues;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnum;
import javax.baja.sys.BInteger;
import javax.baja.sys.BLong;
import javax.baja.sys.BNumber;
import javax.baja.sys.BValue;

import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumSchedule;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

/**
 * If the point or slot type is numeric, WidgetType should be only CheckboxGroup, DuctAreaCalculator, NumberInput, SingleSlider, RangeSlider, TextInput (for only slot)
 * CheckboxGroup requires the slot value type should be BInteger
 * If the point or slot type is enum, WidgetType should be only RadioButtonGroup, Dropdown, DuctAreaCalculator, SelectButton, SelectWidget, MeasurementType (only for slot), TextInput (only for slot)
 * If the point or slot type is Boolean, WidgetType should be only SwitchButton, RadioButtonGroup, SelectButton, SelectWidget, Dropdown, TextInput (only for slot)
 */

/**
 * <AUTHOR> Sun
 */
public class WidgetTypeValidation extends TagValidation {
    static List<String> widgetTypesForNumeric = new ArrayList<>();
    static List<String> widgetTypesForBoolean = new ArrayList<>();
    static List<String> widgetTypesForEnum = new ArrayList<>();

    static final String LEXICON_KEY_VALIDVALUE = "error.tagvalidation.reason.widgettype.validvalue";
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_WDTTYPE_TAG)) {
            return;
        }

        BComponent parent = (BComponent) honWizSelector.getParent();

        String widgetType = getWidgetTypeStringFromTagValues(tagValues);
        if(honWizSelector instanceof BHonWizPointSelector) {
            checkPointSelector((BHonWizPointSelector) honWizSelector, parent, widgetType);
        }
        if(honWizSelector instanceof BHonWizSlotSelector) {
            checkSlotSelector((BHonWizSlotSelector) honWizSelector, parent, widgetType);
        }
    }

    private void checkPointSelector(BHonWizPointSelector pointSelector, BComponent parent, String widgetType) throws ValidationException {
        String slotPath = pointSelector.getSlotPath().toDisplayString();
        if(parent instanceof BIHonWizardEnumSchedule && !widgetType.equals(SCHEDULE)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                    widgetType,
                    lex.getText(LEXICON_KEY_VALIDVALUE, SCHEDULE)
            );
        }

        if(parent instanceof BIHonWizardNumericPoint && !widgetTypesForNumeric.contains(widgetType)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                    widgetType,
                    lex.getText(LEXICON_KEY_VALIDVALUE, widgetTypesForNumeric.toString())
            );
        }

        if(parent instanceof BIHonWizardEnumPoint && !widgetTypesForEnum.contains(widgetType)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                    widgetType,
                    lex.getText(LEXICON_KEY_VALIDVALUE, widgetTypesForEnum.toString())
            );
        }

        if(parent instanceof BIHonWizardBooleanPoint && !widgetTypesForBoolean.contains(widgetType)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                    widgetType,
                    lex.getText(LEXICON_KEY_VALIDVALUE, widgetTypesForBoolean.toString())
            );
        }
    }

    private void checkSlotSelector(BHonWizSlotSelector slotSelector, BComponent parent, String widgetType) throws ValidationException {
        String slotPath = slotSelector.getSlotPath().toDisplayString();
        String sourcePropertyName = slotSelector.getSourcePropertyNameString();
        BValue sourcePropertyValue = parent.get(sourcePropertyName);
        if(sourcePropertyValue == null) {
            return;
        }

        if(sourcePropertyValue instanceof BNumber) {
            if(sourcePropertyValue instanceof BLong || sourcePropertyValue instanceof BInteger) {
                if(!(widgetTypesForNumeric.contains(widgetType) || widgetType.equals(TEXT_INPUT))) {
                    List<String> validWidgetTypes = new ArrayList<>(widgetTypesForNumeric);
                    validWidgetTypes.add(TEXT_INPUT);
                    throw new InvalidTagValueException(
                            slotPath,
                            BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                            widgetType,
                            lex.getText(LEXICON_KEY_VALIDVALUE, validWidgetTypes.toString())
                    );
                }
            }
            else {
                if(!((widgetTypesForNumeric.contains(widgetType) && !widgetType.equals(CHECKBOX_GROUP))
                        || widgetType.equals(TEXT_INPUT))) {
                    List<String> validWidgetTypes = new ArrayList<>(widgetTypesForNumeric);
                    validWidgetTypes.remove(CHECKBOX_GROUP);
                    validWidgetTypes.add(TEXT_INPUT);
                    throw new InvalidTagValueException(
                            slotPath,
                            BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                            widgetType,
                            lex.getText(LEXICON_KEY_VALIDVALUE, validWidgetTypes.toString())
                    );
                }
            }
        }
        else if(sourcePropertyValue instanceof BBoolean) {
            if(!(widgetTypesForBoolean.contains(widgetType) || widgetType.equals(TEXT_INPUT))) {
                List<String> validWidgetTypes = new ArrayList<>(widgetTypesForBoolean);
                validWidgetTypes.add(TEXT_INPUT);
                throw new InvalidTagValueException(
                        slotPath,
                        BHonWizardTag.HON_WIZ_WDTTYPE_TAG,
                        widgetType,
                        lex.getText(LEXICON_KEY_VALIDVALUE, validWidgetTypes.toString())
                );
            }
        }
		else if (sourcePropertyValue instanceof BEnum) {
			if (!(widgetTypesForEnum.contains(widgetType) || widgetType.equals(TEXT_INPUT) || widgetType.equals(Constants.MEASUREMENT_TYPE)
					|| widgetType.equals(TIMEZONE_TYPE) || widgetType.equals(AIRFLOW_UNIT))) {
				List<String> validWidgetTypes = new ArrayList<>(widgetTypesForEnum);
				validWidgetTypes.add(TEXT_INPUT);
				validWidgetTypes.add(MEASUREMENT_TYPE);
				validWidgetTypes.add(AIRFLOW_UNIT);
				validWidgetTypes.add(TIMEZONE_TYPE);
				throw new InvalidTagValueException(slotPath, BHonWizardTag.HON_WIZ_WDTTYPE_TAG, widgetType,
						lex.getText(LEXICON_KEY_VALIDVALUE, validWidgetTypes.toString()));
			}
		}
    }

    static {
        widgetTypesForNumeric.add(CHECKBOX_GROUP);
        widgetTypesForNumeric.add(DUCT_AREA_CALCULATOR_TYPE);
        widgetTypesForNumeric.add(NUMBER_INPUT);
        widgetTypesForNumeric.add(SINGLE_SLIDER);
        widgetTypesForNumeric.add(RANGE_SLIDER);

        widgetTypesForBoolean.add(SWITCH_BUTTON);
        widgetTypesForBoolean.add(RADIO_BUTTON_GROUP);
        widgetTypesForBoolean.add(SELECT_BUTTON);
        widgetTypesForBoolean.add(SELECT_WIDGET);
        widgetTypesForBoolean.add(DROPDOWN);

        widgetTypesForEnum.add(RADIO_BUTTON_GROUP);
        widgetTypesForEnum.add(DROPDOWN);
        widgetTypesForEnum.add(DUCT_AREA_CALCULATOR_TYPE);
        widgetTypesForEnum.add(SELECT_BUTTON);
        widgetTypesForEnum.add(SELECT_WIDGET);
    }
}
