/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.device.generate.HonWizGlobalStoreGenerator;
import com.honeywell.applicationhandler.exceptions.BelongTagNumberIncorrectException;
import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.RoleDuplicatedException;
import com.honeywell.applicationhandler.exceptions.RoleMissingException;
import com.honeywell.applicationhandler.exceptions.TagValuesNotSyncException;
import com.honeywell.applicationhandler.exceptions.UnitNotConsistencyException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;
import javax.baja.units.BUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.honeywell.applicationhandler.common.Constants.RANGE_SLIDER;

/**
 * <AUTHOR> Sun
 */
public class RangeSliderTagsValidation extends WidgetTagsValidation {
    static List<String> inapplicableTags = new ArrayList<>();
    static List<String> additionalRequiredTags = new ArrayList<>();

    @Override
    protected String getWidgetType() {
        return RANGE_SLIDER;
    }

    /**
     * There should be 2 points or slots with same Belong tag value for one RangeSlider
     * The Role tag values for the 2 points or slots should be “min” and “max”
     * Other tag values except Role tag should be same between the 2 points or slots
     */
    @Override
    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        String role = tagValues.get(BHonWizardTag.HON_WIZ_ROLE_TAG).toString();
        if(!role.equals("min") && !role.equals("max")) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_ROLE_TAG,
                    role,
                    lex.get("error.tagvalidation.reason.widget.rangeslider.roletag.invalidvalue")
            );
        }

        List<BHonWizSelector> selectorsWithSameBelongTag = getSelectorsWithSameBelongTag(honWizSelectors, tagValues);
        List<String> selectorSlotPaths = selectorsWithSameBelongTag.stream().map(selector -> selector.getSlotPath().toDisplayString()).collect(Collectors.toList());
        String belong = tagValues.get(BHonWizardTag.HON_WIZ_BELONG_TAG).toString();
        if(selectorsWithSameBelongTag.size() < 2) {
            throw new RoleMissingException(
                    getWidgetType(),
                    Collections.singletonList(slotPath).toArray(new String[1]),
                    Collections.singletonList(role).toArray(new String[1]),
                    Collections.singletonList(role.equals("min") ? "max" : "min").toArray(new String[1]),
                    lex.get("error.tagvalidation.reason.widget.rangeslider.roletag.missing")
            );
        }
        if(selectorsWithSameBelongTag.size() > 2) {
            throw new BelongTagNumberIncorrectException(
                    belong,
                    selectorSlotPaths,
                    lex.get("error.tagvalidation.reason.widget.rangeslider.belongtag.number.incorrect")
            );
        }

        BValue role1 = selectorsWithSameBelongTag.get(0).get(SlotPath.escape(BHonWizardTag.ROLE_TAG.getQName()));
        BValue role2 = selectorsWithSameBelongTag.get(1).get(SlotPath.escape(BHonWizardTag.ROLE_TAG.getQName()));

        if(role1 != null && role2 != null && role1.toString().equals(role2.toString())) {
            throw new RoleDuplicatedException(
                    role,
                    selectorSlotPaths,
                    lex.get("error.tagvalidation.reason.widget.roletag.samevalue")
            );
        }

        Optional<BHonWizSelector> selector = selectorsWithSameBelongTag.stream().filter(s -> s != honWizSelector).findFirst();
        if(selector.isPresent()) {
            BHonWizSelector otherSelector = selector.get();
            Map<String, BIDataValue> otherSelectorTagValues = HoneywellConfigurableDeviceUtil.getNotNullTagValues(otherSelector, null);
            if(otherSelectorTagValues.size() != tagValues.size()) {
                throw new TagValuesNotSyncException(
                        selectorSlotPaths,
                        lex.get("error.tagvalidation.reason.widget.rangeslider.tagnotsync")
                );
            }
            for(Map.Entry<String, BIDataValue> tagEntry : tagValues.entrySet()) {
                if(!tagEntry.getKey().equals(BHonWizardTag.HON_WIZ_ROLE_TAG)) {
                    String value = tagEntry.getValue().toString();
                    BValue otherValue = otherSelector.get(SlotPath.escape(Const.HON_WIZARD_TAG_NAMESPACE+":"+tagEntry.getKey()));
                    if(otherValue == null || !otherValue.toString().equals(value)) {
                        throw new TagValuesNotSyncException(
                                selectorSlotPaths,
                                lex.get("error.tagvalidation.reason.widget.rangeslider.tagnotsync")
                        );
                    }
                }
            }
        }
        if(!checkSameUnit(selectorsWithSameBelongTag.get(0), selectorsWithSameBelongTag.get(1))){
            throw new UnitNotConsistencyException(
                    selectorSlotPaths,
                    lex.get("error.tagvalidation.reason.widget.rangeslider.unitnotconsistency")
            );
        }
    }

    /**
     * check if unit is same for 2 points or slots
     * @param minRangeSelector, the first element of range slider
     * @param maxRangeSelector, the second element of range slider
     * @return true or false
     */
    private boolean checkSameUnit(BHonWizSelector minRangeSelector, BHonWizSelector maxRangeSelector) {
        String srcPropertyNameMin = "";
        String srcPropertyNameMax = "";
        if(minRangeSelector instanceof BHonWizSlotSelector){
            srcPropertyNameMin = ((BHonWizSlotSelector) minRangeSelector).getSourcePropertyNameString();
        }
        if(maxRangeSelector instanceof BHonWizSlotSelector){
            srcPropertyNameMax = ((BHonWizSlotSelector) maxRangeSelector).getSourcePropertyNameString();
        }
        BUnit unitBySelectorMin = HoneywellConfigurableDeviceUtil.getUnitBySelector(minRangeSelector, srcPropertyNameMin);
        BUnit unitBySelectorMax = HoneywellConfigurableDeviceUtil.getUnitBySelector(maxRangeSelector, srcPropertyNameMax);
        if(unitBySelectorMin == null && unitBySelectorMax == null) {
            return true;
        }else if(unitBySelectorMin == null || unitBySelectorMax == null){
            return false;
        } else {
            if (!unitBySelectorMin.getUnitName().equals(unitBySelectorMax.getUnitName())) {
                return false;
            }
        }
        return true;
    }

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return additionalRequiredTags;
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_OPTIONS_TAG);

        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);
    }
}
