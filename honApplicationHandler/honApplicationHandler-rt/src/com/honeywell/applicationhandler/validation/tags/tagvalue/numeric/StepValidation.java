/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.numeric;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import java.util.List;
import java.util.Map;
/**
 * Step tag value should be an positive integer number or a double number with non-zero decimal number.
 * Step tag value should be less than (Max - Min).
 * Step tag value only have two decimal places.
 */

/**
 * <AUTHOR> Zhang
 */
public class StepValidation extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        checkDouble(slotPath, tagValues, BHonWizardTag.HON_WIZ_STEP_TAG);
        compareMinAndMax(slotPath, tagValues);
    }

    private void checkDouble(String slotPath, Map<String, BIDataValue> tagValues, String tagName) throws ValidationException {
        BIDataValue tagValue = tagValues.get(tagName);
        if(tagValue == null) {
            return;
        }
        String value = tagValue.toString();
        if (!value.matches("^([1-9]\\d*|0?\\.\\d{1,2}|\\d+\\.\\d{1,2})$")) {
            throw new InvalidTagValueException(
                    slotPath,
                    tagName,
                    value,
                    lex.getText("error.tagvalidation.reason.positivenumeric.invalid", tagName)
            );
        }
    }

    private void compareMinAndMax(String slotPath,  Map<String, BIDataValue> tagValues) throws ValidationException {
        BIDataValue minTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MIN_TAG);
        BIDataValue maxTagValue = tagValues.get(BHonWizardTag.HON_WIZ_MAX_TAG);
        BIDataValue stepTagValue = tagValues.get(BHonWizardTag.HON_WIZ_STEP_TAG);
        if(minTagValue == null || maxTagValue == null || stepTagValue == null) {
            return;
        }
        Double min = Double.parseDouble(minTagValue.toString());
        Double max = Double.parseDouble(maxTagValue.toString());
        Double step = Double.parseDouble(stepTagValue.toString());
        if (step > (max - min)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_STEP_TAG,
                    stepTagValue.toString(),
                    lex.getText("error.tagvalidation.reason.step.invalid")
            );
        }

    }
}
