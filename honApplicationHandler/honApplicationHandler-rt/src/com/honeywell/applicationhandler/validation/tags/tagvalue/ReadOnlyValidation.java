/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BBoolean;
import java.util.List;
import java.util.Map;

/**
 * ReadOnly tag is Boolean
 */

/**
 * <AUTHOR> Sun
 */
public class ReadOnlyValidation extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_READ_ONLY_TAG)) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();

        BIDataValue tagValue = tagValues.get(BHonWizardTag.HON_WIZ_READ_ONLY_TAG);
        if(!(tagValue instanceof BBoolean)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_READ_ONLY_TAG,
                    tagValue == null ? "" : tagValue.toString(),
                    lex.getText("error.tagvalidation.reason.readonly.validtype")
            );
        }
    }
}
