/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.string;

import com.honeywell.applicationhandler.ontology.BHonWizardTag;

/**
 * Group tag is string, and max length is {50}
 */

/**
 * <AUTHOR> Sun
 */
public class GroupValidation extends StringTagValidation {

    private static final int MAX_GROUP_LENGTH = 50;


    @Override
    protected int getMaxLength() {
        return MAX_GROUP_LENGTH;
    }

    @Override
    protected String getTagName() {
        return BHonWizardTag.HON_WIZ_GRP_TAG;
    }
}