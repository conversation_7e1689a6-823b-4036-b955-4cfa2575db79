/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.exceptions.TagMissingException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.util.Lexicon;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Below tags are required:
 * Page
 * Name
 * WidgetType
 * ReadOnly
 * Order
 */

/**
 * <AUTHOR> Sun
 */
public class CommonTagsRequired extends TagValidation {
    private static final Lexicon lex = LexiconUtil.getLexicon();

    static List<String> requiredTags = new ArrayList<>();

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        List<String> tagNames = Arrays.stream(tagValues.keySet().toArray(new String[0]))
                .collect(Collectors.toList());
        if(isSchedule(tagValues)) {
            if(tagNames.contains(BHonWizardTag.HON_WIZ_PAGE_TAG)) {
                return;
            } else {
                throw new TagMissingException(
                        honWizSelector.getSlotPath().toDisplayString(), BHonWizardTag.HON_WIZ_PAGE_TAG,
                        lex.get("error.tagvalidation.reason.commontag.required")
                );
            }
        }

        checkRequiredTags(tagNames, honWizSelector.getSlotPath().toDisplayString(), lex.get("error.tagvalidation.reason.commontag.required"));
    }

    private void checkRequiredTags(List<String> tags, String slotPath, String reason) throws ValidationException {
        List<String> missedTags = new ArrayList<>();
        for(String requiredTag : requiredTags) {
            if(!tags.contains(requiredTag)) {
                missedTags.add(requiredTag);
            }
        }
        if(!missedTags.isEmpty()) {
            throw new TagMissingException(
                    slotPath, missedTags.toString(), reason
            );
        }

    }

    static {
        requiredTags.add(BHonWizardTag.HON_WIZ_PAGE_TAG);
        requiredTags.add(BHonWizardTag.HON_WIZ_NAME_TAG);
        requiredTags.add(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
        requiredTags.add(BHonWizardTag.HON_WIZ_READ_ONLY_TAG);
        requiredTags.add(BHonWizardTag.HON_WIZ_ORDER_TAG);
    }
}
