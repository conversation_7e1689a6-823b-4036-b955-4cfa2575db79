/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import java.util.List;
import java.util.Map;

/**
 * Options tag is enumRange, and the key value in the enumRange should be same as the enumRange of the point or slot if the point or slot type is enum or Boolean
 */

/**
 * <AUTHOR> Sun
 */
public class OptionsValidation extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_OPTIONS_TAG)) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        BIDataValue options = tagValues.get(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
        if(!(options instanceof BEnumRange)) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_OPTIONS_TAG,
                    options == null ? "" : options.toString(),
                    lex.getText("error.tagvalidation.reason.enumrange.type", BHonWizardTag.HON_WIZ_OPTIONS_TAG)
            );
        }

        BComponent parent = (BComponent) honWizSelector.getParent();

        BEnumRange targetEnumRange = null;
        if(honWizSelector instanceof BHonWizPointSelector) {
            targetEnumRange = getTargetEnumRange(parent);
        }
        else if(honWizSelector instanceof BHonWizSlotSelector) {
            targetEnumRange = getTargetEnumRange((BHonWizSlotSelector)honWizSelector, parent);
        }

        if(targetEnumRange != null) {
            checkEnumRange(targetEnumRange, (BEnumRange) options, slotPath);
        }
    }

    private void checkEnumRange(BEnumRange targetEnumRange, BEnumRange options, String slotPath) throws ValidationException {
        int[] targetOrdinals = targetEnumRange.getOrdinals();
        int[] optionOrdinals = options.getOrdinals();

        boolean failed = false;
        if(targetOrdinals.length != optionOrdinals.length) {
            failed = true;
        }
        else {
            for (int optionOrdinal : optionOrdinals) {
                if (!targetEnumRange.isOrdinal(optionOrdinal)) {
                    failed = true;
                    break;
                }
            }
        }

        if(failed) {
            throw new InvalidTagValueException(
                    slotPath,
                    BHonWizardTag.HON_WIZ_OPTIONS_TAG,
                    options.toString(),
                    lex.getText("error.tagvalidation.reason.options.ordinals")
            );
        }
    }
}
