/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.device.BHonWizardMetaData;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.exceptions.BelongTagNumberIncorrectException;
import com.honeywell.applicationhandler.exceptions.InvalidPointTypeException;
import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.RoleDuplicatedException;
import com.honeywell.applicationhandler.exceptions.RoleMissingException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.tridium.nre.util.tuple.Pair;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BNumber;
import javax.baja.sys.BValue;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.honeywell.applicationhandler.common.Constants.DUCT_AREA_CALCULATOR_TYPE;

/**
 * <AUTHOR> Sun
 */
public class DuctAreaCalculatorTagsValidation extends WidgetTagsValidation {

    private static final String INVALID_POINT_TYPE_KEY = "error.tagvalidation.reason.widget.ductareacalculator.pointtype.invalid";
    private static final String INVALID_DUCTTYPE_ENUM_TEXT_KEY = "error.tagvalidation.reason.widget.ductareacalculator.ducttype.enumtext.invalid";
    private static final String AREA = "area";
    private static final String WIDTH = "width";
    private static final String HEIGHT = "height";
    private static final String RADIUS = "radius";
    private static final String DUCTTYPE = "ducttype";
    private static final String DIMENSIONUNIT = "dimensionunit";
    private static final String RECTANGLE = "rectangle";
    private static final String ROUND = "round";

    static List<String> inapplicableTags = new ArrayList<>();
    static List<String> additionalRequiredTags = new ArrayList<>();
    static List<String> validRoles = new ArrayList<>();
    static List<String> numericRoles = new ArrayList<>();
    static List<String> enumRoles = new ArrayList<>();
    static List<String> dimensionUnits = new ArrayList<>();
    @Override
    protected String getWidgetType() {
        return DUCT_AREA_CALCULATOR_TYPE;
    }

    /**
     * Role tag value should be from below strings:
     *      area/width/height/radius/ducttype/dimensionunit
     * In DuctAreaCalculators with same Belong tag:
     *      The number should not be larger than 6
     *      should not contains more than 2 DuctAreaCalculators with same Role tag
     *      must have one with Role tag value “area”.
     * For DuctAreaCalculator with Role tag “area”/“width”/“height”/“radius”, the source point or slot value type should be numeric
     * For DuctAreaCalculator with Role tag “ducttype”/“dimensionunit”, the source point or slot value type should be enum
     * If some point is attached the Role tag and value is “ducttype”:
     *      Length of enum range should be 2
     *      The text of enum with key value 1 should contain “rectangle” (ignore case sensitive)
     *      The text of enum with key value 2 should contain “round” (ignore case sensitive)
     * If some point is attached the Role tag and value is “dimensionunit”:
     *      Length of enum range should be no more than 4
     *      The text of enum should contain one of below strings (case sensitive):
     *          Feet/Inch/Centimeter/Meter
     */
    @Override
    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        String slotPath = honWizSelector.getSlotPath().toDisplayString();
        checkRoleTags(slotPath, tagValues);
        checkWithSameBelongTag(honWizSelectors, tagValues);
        checkPointOrSlotType(honWizSelector, tagValues);
        if(!(honWizSelector.getParent() instanceof BHonWizardMetaData)) {
        	// Perform validation only if the selectors are not present in metadata component
	        checkDuctType(honWizSelector, tagValues);
	        checkDimensionUnit(honWizSelector, tagValues);
        }	
    }

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return additionalRequiredTags;
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }

    private void checkRoleTags(String slotPath, Map<String, BIDataValue> tagValues) throws ValidationException {
        BIDataValue roleValue = tagValues.get(BHonWizardTag.HON_WIZ_ROLE_TAG);
        if(roleValue != null) {
            String roleString = roleValue.toString();
            if(!validRoles.contains(roleString)) {
                throw new InvalidTagValueException(
                        slotPath,
                        BHonWizardTag.HON_WIZ_ROLE_TAG,
                        roleString,
                        lex.get("error.tagvalidation.reason.widget.ductareacalculator.roletag.invalidvalue")
                );
            }
        }
    }

    private void checkWithSameBelongTag(List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        List<BHonWizSelector> selectorsWithSameBelongTag = getSelectorsWithSameBelongTag(honWizSelectors, tagValues);
        String belong = tagValues.get(BHonWizardTag.HON_WIZ_BELONG_TAG).toString();
        List<String> selectorSlotPaths = selectorsWithSameBelongTag.stream()
                .map(selector -> selector.getSlotPath().toDisplayString()).collect(Collectors.toList());
        if(selectorsWithSameBelongTag.size() > validRoles.size()) {
            throw new BelongTagNumberIncorrectException(
                    belong,
                    selectorSlotPaths,
                    lex.get("error.tagvalidation.reason.widget.ductareacalculator.belongtag.number.incorrect")
            );
        }

        List<Pair<String, String>> roleSlotPaths = selectorsWithSameBelongTag.stream()
                .filter(selector -> selector.get(SlotPath.escape(BHonWizardTag.ROLE_TAG.getQName())) != null)
                .map(selector -> {
                    String role = selector.get(SlotPath.escape(BHonWizardTag.ROLE_TAG.getQName())).toString();
                    String selectorPath = selector.getSlotPath().toDisplayString();
                    return new Pair<>(role, selectorPath);
                })
                .collect(Collectors.toList());

        for(String validRole : validRoles) {
            List<Pair<String, String>> rolePathsWithSameRole = roleSlotPaths.stream()
                    .filter(roleSlotPath -> validRole.equals(roleSlotPath.getFirst())).collect(Collectors.toList());
            if(rolePathsWithSameRole.size() > 1) {
                List<String> slotPathsWithSameRole = rolePathsWithSameRole.stream()
                        .map(Pair::getSecond).collect(Collectors.toList());
                throw new RoleDuplicatedException(
                        validRole,
                        slotPathsWithSameRole,
                        lex.get("error.tagvalidation.reason.widget.roletag.samevalue")
                );
            }
        }

        if(roleSlotPaths.stream().noneMatch(roleSlotPath -> AREA.equals(roleSlotPath.getFirst()))) {
            List<String> bindingRoles = roleSlotPaths.stream().map(Pair::getFirst).collect(Collectors.toList());
            List<String> bindingSlotPaths = roleSlotPaths.stream().map(Pair::getSecond).collect(Collectors.toList());
            throw new RoleMissingException(
                    getWidgetType(),
                    bindingSlotPaths.toArray(new String[1]),
                    bindingRoles.toArray(new String[1]),
                    new String[]{AREA},
                    lex.getText("error.tagvalidation.reason.widget.rangeslider.roletag.missing", AREA)
            );
        }
    }

    private void checkPointOrSlotType(BHonWizSelector honWizSelector, Map<String, BIDataValue> tagValues) throws ValidationException {
        BComponent parent = (BComponent) honWizSelector.getParent();
        String role = tagValues.get(BHonWizardTag.HON_WIZ_ROLE_TAG).toString();

        if(numericRoles.contains(role)) {
            if((honWizSelector instanceof BHonWizPointSelector) && !(parent instanceof BIHonWizardNumericPoint)) {
                throw new InvalidPointTypeException(
                        parent.getSlotPath().toDisplayString(),
                        lex.getText(INVALID_POINT_TYPE_KEY, role, "numeric")
                );
            }
            if(honWizSelector instanceof BHonWizSlotSelector) {
                String sourcePropertyName = ((BHonWizSlotSelector)honWizSelector).getSourcePropertyNameString();
                if(parent instanceof BHonWizardMetaData) {
                	// If the selectors are present in MetaData comp, then it's added via backend code. SO skip validation.
                	return;
                }
                BValue sourcePropertyValue = parent.get(sourcePropertyName);
                if((sourcePropertyValue != null) && !(sourcePropertyValue instanceof BNumber)) {
                    throw new InvalidPointTypeException(
                            parent.getSlotPath().toDisplayString() + "/" + sourcePropertyName,
                            lex.getText(INVALID_POINT_TYPE_KEY, role, "numeric")
                    );
                }
            }
        }

        if(enumRoles.contains(role)) {
            if((honWizSelector instanceof BHonWizPointSelector) && !(parent instanceof BIHonWizardEnumPoint)) {
                throw new InvalidPointTypeException(
                        parent.getSlotPath().toDisplayString(),
                        lex.getText(INVALID_POINT_TYPE_KEY, role, "enum")
                );
            }
            if(honWizSelector instanceof BHonWizSlotSelector) {
                String sourcePropertyName = ((BHonWizSlotSelector)honWizSelector).getSourcePropertyNameString();
                if(parent instanceof BHonWizardMetaData) {
                	// If the selectors are present in MetaData comp, then it's added via backend code. SO skip validation.
                	return;
                }
                BValue sourcePropertyValue = parent.get(sourcePropertyName);
                if((sourcePropertyValue != null) && !(sourcePropertyValue instanceof BEnum)) {
                    throw new InvalidPointTypeException(
                            parent.getSlotPath().toDisplayString() + "/" + sourcePropertyName,
                            lex.getText(INVALID_POINT_TYPE_KEY, role, "enum")
                    );
                }
            }
        }
    }

    private void checkDuctType(BHonWizSelector honWizSelector, Map<String, BIDataValue> tagValues) throws ValidationException {
        String role = tagValues.get(BHonWizardTag.HON_WIZ_ROLE_TAG).toString();
        if(!role.equals(DUCTTYPE)) {
            return;
        }

        BComponent parent = (BComponent) honWizSelector.getParent();
        BEnumRange enumRange = getParentEnumRange(honWizSelector, parent);

        if(enumRange == null) {
            return;
        }

        int[] ordinals = enumRange.getOrdinals();
        if(ordinals.length != 2 || ordinals[0] < 1 || ordinals[0] > 2 || ordinals[1] < 1 || ordinals[1] > 2) {
            throw new InvalidPointTypeException(
                    parent.getSlotPath().toDisplayString(),
                    lex.get("error.tagvalidation.reason.widget.ductareacalculator.ducttype.ordinals.invalid") + "\n  " +
                        lex.getText(INVALID_DUCTTYPE_ENUM_TEXT_KEY, 1, RECTANGLE) + "\n  " +
                        lex.getText(INVALID_DUCTTYPE_ENUM_TEXT_KEY, 2, ROUND) + "\n"
            );
        }

        if(!enumRange.getTag(1).equals(RECTANGLE) || !enumRange.getTag(2).equals(ROUND)) {
            throw new InvalidPointTypeException(
                    parent.getSlotPath().toDisplayString(),
                    "\n  " +
                        lex.getText(INVALID_DUCTTYPE_ENUM_TEXT_KEY, 1, RECTANGLE) + "\n  " +
                        lex.getText(INVALID_DUCTTYPE_ENUM_TEXT_KEY, 2, ROUND) + "\n"
            );
        }
    }

    private void checkDimensionUnit(BHonWizSelector honWizSelector, Map<String, BIDataValue> tagValues) throws ValidationException {
        String role = tagValues.get(BHonWizardTag.HON_WIZ_ROLE_TAG).toString();
        if(!role.equals(DIMENSIONUNIT)) {
            return;
        }

        BComponent parent = (BComponent) honWizSelector.getParent();
        BEnumRange enumRange = getParentEnumRange(honWizSelector, parent);

        if(enumRange == null) {
            return;
        }

        int[] ordinals = enumRange.getOrdinals();
        if(ordinals.length > 4) {
            throw new InvalidPointTypeException(
                    parent.getSlotPath().toDisplayString(),
                    lex.get("error.tagvalidation.reason.widget.ductareacalculator.dimensionunit.ordinals.invalid")
            );
        }

        for(int ordinal : ordinals) {
            String text = enumRange.getTag(ordinal);
            boolean noMatch = dimensionUnits.stream().noneMatch(text::contains);
            if(noMatch) {
                throw new InvalidPointTypeException(
                        parent.getSlotPath().toDisplayString(),
                        lex.get("error.tagvalidation.reason.widget.ductareacalculator.dimensionunit.enumtext.invalid")
                );
            }
        }
    }

    private BEnumRange getParentEnumRange(BHonWizSelector honWizSelector, BComponent parent) {
        BEnumRange enumRange = null;
        if(honWizSelector instanceof BHonWizPointSelector) {
            enumRange = ((BIHonWizardEnumPoint)parent).getEnumRange();
        }
        
        if(honWizSelector instanceof BHonWizSlotSelector) {
            String sourcePropertyName = ((BHonWizSlotSelector)honWizSelector).getSourcePropertyNameString();
            BEnum sourcePropertyValue = (BEnum)parent.get(sourcePropertyName);
            if(!sourcePropertyValue.getRange().equals(BEnumRange.DEFAULT)) {
            	enumRange = sourcePropertyValue.getRange();
            } else if(parent.getSlotFacets(parent.getSlot(sourcePropertyName)).get(BFacets.RANGE) instanceof BEnumRange) {
            	enumRange = (BEnumRange) parent.getSlotFacets(parent.getSlot(sourcePropertyName)).get(BFacets.RANGE);
			}
        }

        return enumRange;
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_COLOR_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_DEADBAND_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_STEP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);

        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);

        validRoles.add(AREA);
        validRoles.add(WIDTH);
        validRoles.add(HEIGHT);
        validRoles.add(RADIUS);
        validRoles.add(DUCTTYPE);
        validRoles.add(DIMENSIONUNIT);

        numericRoles.add(AREA);
        numericRoles.add(WIDTH);
        numericRoles.add(HEIGHT);
        numericRoles.add(RADIUS);

        enumRoles.add(DUCTTYPE);
        enumRoles.add(DIMENSIONUNIT);

        dimensionUnits.add("Feet");
        dimensionUnits.add("Inch");
        dimensionUnits.add("Centimeter");
        dimensionUnits.add("Meter");
    }
}
