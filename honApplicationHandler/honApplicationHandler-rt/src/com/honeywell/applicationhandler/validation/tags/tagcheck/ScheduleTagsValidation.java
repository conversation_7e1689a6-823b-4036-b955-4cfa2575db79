/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.exceptions.PageNameDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.common.Constants.SCHEDULE;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 4, 2025
 */
public class ScheduleTagsValidation extends WidgetTagsValidation{
    static List<String> inapplicableTags = new ArrayList<>();
    static List<String> additionalRequiredTags = new ArrayList<>();

    @Override
    protected String getWidgetType() {
        return SCHEDULE;
    }

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return additionalRequiredTags;
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }
    
    @Override
    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues)
    		throws ValidationException {
        String slotPath = honWizSelector.getSlotPath().toDisplayString();

        BIDataValue page = tagValues.get(BHonWizardTag.HON_WIZ_PAGE_TAG);
        if(page == null) {
            return;
        }

        String pageName = page.toString();
        for(BHonWizSelector otherSelector : honWizSelectors) {
        	if(honWizSelector.equals(otherSelector)) {
        		continue;
        	}
            String otherPageName = otherSelector.get(SlotPath.escape(BHonWizardTag.PAGE_TAG.getQName())).toString();

			if (pageName.equals(otherPageName)) {
				throw new PageNameDuplicatedException(pageName, Arrays.asList(slotPath, otherSelector.getSlotPath().toDisplayString()),
						lex.getText("error.tagvalidation.reason.schedule.duplicated"));
            }
        }
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_DEADBAND_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_COLOR_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_NAME_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_GRP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_TAB_IN_PAGE_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_ORDER_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_HELP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_STEP_TAG);
    }
}
