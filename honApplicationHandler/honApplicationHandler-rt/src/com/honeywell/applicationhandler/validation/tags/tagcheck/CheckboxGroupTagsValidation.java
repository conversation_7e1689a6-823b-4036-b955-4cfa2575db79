/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BEnumRange;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.common.Constants.CHECKBOX_GROUP;

/**
 * <AUTHOR> Sun
 */
public class CheckboxGroupTagsValidation extends WidgetTagsValidation {
    static List<String> inapplicableTags = new ArrayList<>();
    static List<String> additionalRequiredTags = new ArrayList<>();
    @Override
    protected String getWidgetType() {
        return CHECKBOX_GROUP;
    }

    /**
     * Value of option should be bitwise like 1, 2, 4, 8, 16, 32, 64 and so on.
     */
    @Override
    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(BHonWizardTag.HON_WIZ_OPTIONS_TAG)) {
            return;
        }

        BIDataValue options = tagValues.get(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
        if(!(options instanceof BEnumRange)) {
            return;
        }

        BEnumRange enumRange = (BEnumRange) options;
        int[] ordinals = enumRange.getOrdinals();
        if(ordinals.length == 0) {
            throw new InvalidTagValueException(
                    honWizSelector.getSlotPath().toDisplayString(),
                    BHonWizardTag.HON_WIZ_OPTIONS_TAG,
                    enumRange.toString(),
                    lex.getText("error.tagvalidation.reason.widget.checkboxgroup.options")
            );
        }
        for(int ordinal : ordinals) {
            if((ordinal & (ordinal - 1)) != 0) {
                throw new InvalidTagValueException(
                        honWizSelector.getSlotPath().toDisplayString(),
                        BHonWizardTag.HON_WIZ_OPTIONS_TAG,
                        enumRange.toString(),
                        lex.getText("error.tagvalidation.reason.widget.checkboxgroup.options")
                );
            }
        }
    }

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return additionalRequiredTags;
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_DEADBAND_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_COLOR_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_STEP_TAG);

        additionalRequiredTags.add(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
    }
}
