/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagcheck;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.OptionalInt;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;

import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.exceptions.InvalidPointTypeException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;


/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhus<PERSON>han
 * @since Jul 14, 2025
 */
public class AirflowUnitWidgetValidation extends WidgetTagsValidation {
    static List<String> inapplicableTags = new ArrayList<>();

    @Override
    protected List<String> getAdditionalRequiredTags() {
        return new ArrayList<>();
    }

    @Override
    protected List<String> getInapplicableTags() {
        return inapplicableTags;
    }

    @Override
    protected String getWidgetType() {
        return Constants.AIRFLOW_UNIT;
    }

    /**
     * The text of enum should contain one of below strings (ignore case):
     *      CubicFeetPerMinute/CFM
     *      CubicMeterPerHour/CMH
     *      LiterPerSecond/LPS
     */
    @Override
    protected void validateWidget(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        BComponent parent = (BComponent) honWizSelector.getParent();
        BEnumRange targetEnumRange;
        if(honWizSelector instanceof BHonWizPointSelector) {
            targetEnumRange = getTargetEnumRange(parent);
        }
        else if(honWizSelector instanceof BHonWizSlotSelector) {
            targetEnumRange = getTargetEnumRange((BHonWizSlotSelector)honWizSelector, parent);
        } else {
            targetEnumRange = null;
        }

        if(targetEnumRange == null) {
            return;
        }

        int[] ordinals = targetEnumRange.getOrdinals();
        OptionalInt cfmOrdinal = Arrays.stream(ordinals).filter(ordinal -> {
            String text = targetEnumRange.getTag(ordinal).toLowerCase();
            return text.contains("cubicfeetperminute") || text.contains("CFM");
        }).findFirst();
        OptionalInt cmhOrdinal = Arrays.stream(ordinals).filter(ordinal -> {
            String text = targetEnumRange.getTag(ordinal).toLowerCase();
            return text.contains("cubicfeetperminute") || text.contains("CMH");
        }).findFirst();
        OptionalInt lpsOrdinal = Arrays.stream(ordinals).filter(ordinal -> {
            String text = targetEnumRange.getTag(ordinal).toLowerCase();
            return text.contains("literpersecond") || text.contains("LPS");
        }).findFirst();

        if(!cfmOrdinal.isPresent() || !cmhOrdinal.isPresent() || !lpsOrdinal.isPresent()) {
            throw new InvalidPointTypeException(
                    parent.getSlotPath().toDisplayString(),
                    lex.get("error.tagvalidation.reason.widget.airflowunitwidget.enumtext.invalid")
            );
        }
    }

    static {
        inapplicableTags.add(BHonWizardTag.HON_WIZ_ROLE_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_BELONG_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_DEADBAND_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_OPTIONS_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MIN_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_MAX_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_COLOR_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_STEP_TAG);
        inapplicableTags.add(BHonWizardTag.HON_WIZ_UNIT_GROUP_TAG);
    }
}