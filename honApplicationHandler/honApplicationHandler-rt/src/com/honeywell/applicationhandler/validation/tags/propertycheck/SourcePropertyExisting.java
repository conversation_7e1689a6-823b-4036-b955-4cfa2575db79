/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.validation.tags.propertycheck;

import com.honeywell.applicationhandler.exceptions.HonWizSelectorDuplicatedException;
import com.honeywell.applicationhandler.exceptions.PropertyNotExistException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BComponent;
import java.util.List;
import java.util.Map;

/**
 * For common slot selector, the sourcePropertyname should be existed
 * Only one slot selector can have the same source property name
 */

/**
 * <AUTHOR> Sun
 */
public class SourcePropertyExisting extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!(honWizSelector instanceof BHonWizSlotSelector)) {
            return;
        }

        BComponent parent = (BComponent) honWizSelector.getParent();

        String slotPath = parent.getSlotPath().toDisplayString();
        String sourcePropertyName = ((BHonWizSlotSelector)honWizSelector).getSourcePropertyNameString();
        if (parent.getProperty(sourcePropertyName) == null) {
            throw new PropertyNotExistException(slotPath, sourcePropertyName);
        }

        BHonWizSlotSelector[] slotSelectors = parent.getChildren(BHonWizSlotSelector.class);
        for(BHonWizSlotSelector slotSelector : slotSelectors) {
            if(slotSelector == honWizSelector) {
                continue;
            }
            if(slotSelector.getSourcePropertyNameString().equals(sourcePropertyName)) {
                throw new HonWizSelectorDuplicatedException(slotPath,
                        lex.get("error.tagvalidation.slotselector.duplicated"),
                        lex.getText("error.tagvalidation.reason.slotselector.duplicated", sourcePropertyName));
            }
        }
    }
}
