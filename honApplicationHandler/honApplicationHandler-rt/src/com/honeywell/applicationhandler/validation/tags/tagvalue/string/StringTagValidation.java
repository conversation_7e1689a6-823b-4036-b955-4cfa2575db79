/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.tagvalue.string;

import com.honeywell.applicationhandler.exceptions.InvalidTagValueException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BString;
import java.util.List;
import java.util.Map;

public abstract class StringTagValidation extends TagValidation {

    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!tagValues.containsKey(getTagName())) {
            return;
        }

        String slotPath = honWizSelector.getSlotPath().toDisplayString();

        BIDataValue tagValue = tagValues.get(getTagName());
        if(!(tagValue instanceof BString)) {
            throw new InvalidTagValueException(
                    slotPath,
                    getTagName(),
                    tagValue.toString(),
                    lex.getText("error.tagvalidation.reason.string.type", getTagName())
            );
        }

        String tagString = tagValue.toString();
        if(tagString.length() > getMaxLength()) {
            throw new InvalidTagValueException(
                    slotPath,
                    getTagName(),
                    tagString,
                    lex.getText("error.tagvalidation.reason.string.length", getTagName(), getMaxLength())
            );
        }
    }

    protected abstract int getMaxLength();
    protected abstract String getTagName();
}
