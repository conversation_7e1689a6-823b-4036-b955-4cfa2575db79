/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.validation.tags.selectorcheck;

import com.honeywell.applicationhandler.exceptions.HonWizSelectorDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.sys.BComponent;
import java.util.List;
import java.util.Map;

/**
 * For one point, there should be only one point selector
 */

/**
 * <AUTHOR> Sun
 */
public class HonWizardSelectorValidation extends TagValidation {
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        if(!(honWizSelector instanceof BHonWizPointSelector)) {
            return;
        }

        BComponent parent = (BComponent) honWizSelector.getParent();

        BHonWizPointSelector[] allPointSelectors = parent.getChildren(BHonWizPointSelector.class);
        if(allPointSelectors.length > 1) {
            throw new HonWizSelectorDuplicatedException(parent.getSlotPath().toDisplayString(),
                    lex.get("error.tagvalidation.pointselector.duplicated"),
                    lex.get("error.tagvalidation.reason.pointselector.duplicated")
            );
        }
    }
}
