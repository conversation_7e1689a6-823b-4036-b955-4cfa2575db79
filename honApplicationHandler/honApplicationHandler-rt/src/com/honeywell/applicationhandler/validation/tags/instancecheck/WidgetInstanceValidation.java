/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.validation.tags.instancecheck;

import com.honeywell.applicationhandler.exceptions.InstanceDuplicatedException;
import com.honeywell.applicationhandler.exceptions.ValidationException;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.data.BIDataValue;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.honeywell.applicationhandler.common.Constants.MEASUREMENT_TYPE;
import static com.honeywell.applicationhandler.common.Constants.AIRFLOW_UNIT;
import static com.honeywell.applicationhandler.common.Constants.TIMEZONE_TYPE;

/**
 * For some widgets, it only is allowed to have one instance in wizard
 */

/**
 * <AUTHOR> Zhang
 */
public class WidgetInstanceValidation extends TagValidation {
    private static List<String> onlyOneInstanceWidgetTypes = new ArrayList<>();
    @Override
    protected void validate(BHonWizSelector honWizSelector, List<BHonWizSelector> honWizSelectors, Map<String, BIDataValue> tagValues) throws ValidationException {
        BIDataValue page = tagValues.get(BHonWizardTag.HON_WIZ_PAGE_TAG);
        BIDataValue widgetType = tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
        if(page == null || widgetType == null) {
            return;
        }
        for(BHonWizSelector otherSelector : honWizSelectors) {
            if(!shallCheckWidgetInstance(widgetType, honWizSelector, otherSelector)){
                continue;
            }
            String otherWidgetType = otherSelector.get(SlotPath.escape(BHonWizardTag.WGTTYPE_TAG.getQName())).toString();
            if(widgetType.toString().equals(otherWidgetType)) {
                String slotPath = honWizSelector.getSlotPath().toDisplayString();
                List<String> slotPathList = Arrays.asList(slotPath, otherSelector.getSlotPath().toDisplayString());
                slotPathList.sort(String::compareTo);
                throw new InstanceDuplicatedException(widgetType.toString(), slotPathList, lex.get("error.tagvalidation.reason.widgettype.duplicated"));
            }

        }

    }
    private boolean shallCheckWidgetInstance(BIDataValue widgetType,  BHonWizSelector honWizSelector, BHonWizSelector otherHonWizSelector) {
        BValue otherWidgetType = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.WGTTYPE_TAG.getQName()));
        BValue otherPage = otherHonWizSelector.get(SlotPath.escape(BHonWizardTag.PAGE_TAG.getQName()));
        if(otherHonWizSelector == honWizSelector) {
            return false;
        }
        if(otherPage == null || otherWidgetType == null) {
            return false;
        }
        return onlyOneInstanceWidgetTypes.contains(widgetType.toString());
    }
    static {
        onlyOneInstanceWidgetTypes.add(TIMEZONE_TYPE);
        onlyOneInstanceWidgetTypes.add(MEASUREMENT_TYPE);
        onlyOneInstanceWidgetTypes.add(AIRFLOW_UNIT);
    }
}
