/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import com.honeywell.applicationhandler.utils.LexiconUtil;

import javax.baja.util.Lexicon;

/**
 * <AUTHOR> <PERSON>
 */
public abstract class ValidationException extends Exception {
    private static final long serialVersionUID = 1;
    static final Lexicon lex = LexiconUtil.getLexicon();

    public ValidationException(String message) {
        super(message);
    }
}
