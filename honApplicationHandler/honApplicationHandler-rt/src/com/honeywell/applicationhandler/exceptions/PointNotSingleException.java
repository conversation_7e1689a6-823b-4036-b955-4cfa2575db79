/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.List;

/**
 * <AUTHOR> Sun
 */
public class PointNotSingleException extends DuplicateValidationException{
    private static final long serialVersionUID = 1;

    public PointNotSingleException(String namingConvention, String slotPath, List<String> slotPaths) {
        super(
            namingConvention != null ? (lex.getText("error.namingconvention.validation.point.notsingle", namingConvention) + "\n" +
            lex.getText("error.namingconvention.validation.point.found") + "\n  " + String.join("\n  ", slotPaths)) :
            (lex.getText("error.slotpath.validation.duplicated", slotPath) + "\n")
        );
    }
}
