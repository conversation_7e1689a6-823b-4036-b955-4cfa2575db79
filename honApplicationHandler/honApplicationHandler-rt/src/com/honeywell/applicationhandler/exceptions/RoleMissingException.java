/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.Arrays;

/**
 * <AUTHOR> Sun
 */
public class RoleMissingException extends ValidationException {
    private static final long serialVersionUID = 1;
    public RoleMissingException(String widgetType, String[] bindingSlotPaths, String[] bindingRoles, String[] missingRoles, String reason) {
        super(lex.get("error.tagvalidation.point.missing") + widgetType + "\n" +
                lex.get("error.tagvalidation.binding.point") + Arrays.toString(bindingSlotPaths) + "\n" +
                lex.get("error.tagvalidation.binding.role") + Arrays.toString(bindingRoles) + "\n" +
                lex.get("error.tagvalidation.missing.role") + Arrays.toString(missingRoles) + "\n" +
                lex.get("error.tagvalidation.reason") + reason + "\n");
    }
}
