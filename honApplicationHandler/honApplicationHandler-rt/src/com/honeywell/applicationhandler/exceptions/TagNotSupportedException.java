/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TagNotSupportedException extends ValidationException {
    private static final long serialVersionUID = 1;

    public TagNotSupportedException(String slotPath, List<String> tagNames, String reason) {
        super(lex.get("error.tagvalidation.tag.notsupported") + slotPath + "\n" +
                lex.get("error.tagvalidation.notsupported.tag") + tagNames + "\n" +
                lex.get("error.tagvalidation.reason") + reason);
    }
}
