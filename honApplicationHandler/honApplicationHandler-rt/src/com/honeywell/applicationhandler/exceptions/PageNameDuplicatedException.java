/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.List;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jul 4, 2025
 */
public class PageNameDuplicatedException extends ValidationException{
    private static final long serialVersionUID = 1;
    public PageNameDuplicatedException(String duplaicatedPageName, List<String> slotPaths, String reason) {
        super(lex.get("error.tagvalidation.pagename.duplicated") + "\n" +
                lex.getText("error.tagvalidation.duplicated.pagename", duplaicatedPageName) + "\n" +
                lex.getText("error.tagvalidation.duplicated.pagename.pointselectors") + "\n  " +
                String.join("\n  ", slotPaths) + "\n" +
                lex.get("error.tagvalidation.reason") + reason + "\n"
        );
    }
}
