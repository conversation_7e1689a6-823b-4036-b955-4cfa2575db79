/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.List;

/**
 * <AUTHOR> Sun
 */
public class OrderDuplicatedException extends DuplicateValidationException{
    private static final long serialVersionUID = 1;
    public OrderDuplicatedException(String duplicatedOrderValue, List<String> slotPaths, String reason) {
        super(lex.get("error.tagvalidation.order.duplicated") + "\n" +
                lex.getText("error.tagvalidation.duplicated.order", duplicatedOrderValue) + "\n" +
                lex.getText("error.tagvalidation.duplicated.order.pointselectors") + "\n  " +
                String.join("\n  ", slotPaths) + "\n" +
                lex.get("error.tagvalidation.reason") + reason + "\n"
        );
    }
}
