/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

import java.util.List;

/**
 * <AUTHOR>
 */
public class BelongTagNumberIncorrectException extends DuplicateValidationException {
    private static final long serialVersionUID = 1;

    public BelongTagNumberIncorrectException(String belong, List<String> slotPaths, String reason) {
        super(lex.get("error.tagvalidation.belong.tag.number.notcorrect") + "\n" +
                lex.getText("error.tagvalidation.belong.tagvalue", belong) + "\n" +
                lex.get("error.tagvalidation.belong.samevalue.pointselectors") + "\n  " +
                String.join("\n  ", slotPaths) + "\n" +
                lex.get("error.tagvalidation.reason") + reason + "\n");
    }
}
