/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

/**
 * <AUTHOR> Sun
 */
public class InvalidTagValueException extends ValidationException {
    private static final long serialVersionUID = 1;

    public InvalidTagValueException(String slotPath, String tagName, String currentValue, String reason) {
        super(lex.get("error.tagvalidation.tagvalue.invalid") + slotPath + "\n" +
                lex.get("error.tagvalidation.tagname") + tagName + "\n" +
                lex.get("error.tagvalidation.currentvalue") + currentValue + "\n" +
                lex.get("error.tagvalidation.reason") + reason + "\n");
    }
}
