/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

/**
 * <AUTHOR>
 */
public class PointNotExistException extends ValidationException {
    private static final long serialVersionUID = 1;

    public PointNotExistException(String namingConvention, String slotPath) {
        super(namingConvention != null ? lex.getText("error.namingconvention.validation.point.notfound", namingConvention) :
                lex.getText("error.slotpath.validation.notfound", slotPath));
    }
}
