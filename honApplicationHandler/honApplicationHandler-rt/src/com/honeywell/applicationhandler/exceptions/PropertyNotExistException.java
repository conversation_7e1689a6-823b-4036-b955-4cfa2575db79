/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.exceptions;

/**
 * <AUTHOR>
 */
public class PropertyNotExistException extends ValidationException{
    private static final long serialVersionUID = 1;
    public PropertyNotExistException(String componentSlotPath, String propertyName) {
        super(lex.get("error.tagvalidation.property.notexist") + componentSlotPath + "/" + propertyName + "\n");
    }
}
