/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.common;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.baja.job.BJob;
import javax.baja.job.BJobState;
import javax.baja.job.JobLogItem;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.Context;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>hus<PERSON>han
 * @since Jan 26, 2025
 */

@NiagaraProperty(
    name = "nameOfJob",
    type = "String",
    defaultValue = ""
)

@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S2160",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardJob extends BJob
{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.irmnano.manager.BIrmOperationsMonitorJob(742466967)1.0$ @*/
/* Generated Fri Feb 22 15:56:04 CET 2019 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "nameOfJob"
////////////////////////////////////////////////////////////////


/**
   * Slot for the {@code nameOfJob} property.
   * @see #getNameOfJob
   * @see #setNameOfJob
   */
  public static final Property nameOfJob = newProperty(0, "", null);

  /**
   * Get the {@code nameOfJob} property.
   * @see #nameOfJob
   */
  public String getNameOfJob() { return getString(nameOfJob); }

  /**
   * Set the {@code nameOfJob} property.
   * @see #nameOfJob
   */
  public void setNameOfJob(String v) { setString(nameOfJob, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

	private static final Logger LOGGER = Logger.getLogger("HonWizardJobs.common");
	private static final Lexicon lex = Lexicon.make(BHonWizardJob.class);
	private static final int SLEEP_TIME = 500;
	private boolean jobCanceled;
	private boolean hasFailItemsInLog;
	BIHoneywellConfigurableDevice device = null;
	private long startMillis;
	
	public BHonWizardJob(BIHoneywellConfigurableDevice device) {
		jobCanceled = false;
		this.device = device;
	}
	
	public BHonWizardJob() {
		jobCanceled = false;
	}

	@Override
	public void doRun(Context cx) {
		try {
			startMillis = System.currentTimeMillis();
			setJobState(BJobState.running);
			BHonWizardJob.this.log().message(lex.getText("WIZARD_JOB_STARTED", getNameOfJob(), device.getDeviceName()));

		} catch (Exception ex) {
			BHonWizardJob.this.failed(ex);
		}
	}
	
	@Override
	public void doCancel(Context cx) {
		if (getJobState().isRunning()) {
			setJobState(BJobState.canceling);
			jobCanceled = true;
			// Handle cancel in individual jobs
		}
	}
	
	@Override
	public void success(){
		if(getJobState() == BJobState.canceling || getJobState() == BJobState.canceled){
			complete(BJobState.canceled);
			return;
		}
		if(getJobState() == BJobState.failed){
			complete(BJobState.failed);
			return;
		}
		super.success();
	}
	
	public void exitOperationThread(int retry) {
		if (null == Thread.currentThread())
			return;
		int retryCount = retry;
		while (Thread.currentThread().isAlive() && retryCount > 0) {
			LOGGER.log(Level.FINEST, "Attempting to cancel the operation job with retryCount - {0}", new Object[] { retryCount });
			try {
				Thread.sleep(SLEEP_TIME);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
			retryCount--;
		}
		if (Thread.currentThread().isAlive()) {
			LOGGER.log(Level.FINEST, "Job got canceled and interrupting the operation thread after 15min wait");
			Thread.currentThread().interrupt();
		}
		updateCancelLog();
	}
	
	public void updateEndFailureLog(String message, String details) {
		String detailMsg = getJobDuration();
		log().add(new JobLogItem(JobLogItem.FAILED, BAbsTime.make(), message, detailMsg + " "  + details));
		complete(BJobState.failed);
	}
	
	public void updateFailLog(String message, String details) {
		log().add(new JobLogItem(JobLogItem.FAILED, BAbsTime.make(), message, details));
		this.setHasFailItemsInLog(true);
	}

	public void updateFailLog(String message, Exception details) {
		log().add(new JobLogItem(JobLogItem.FAILED, message, details));
		this.setHasFailItemsInLog(true);
	}
	
	public void updateFailLog(String message) {
		log().add(new JobLogItem(JobLogItem.FAILED, message));
		this.setHasFailItemsInLog(true);
	}
	
	public void updateMessageLog(String message) {
		log().add(new JobLogItem(JobLogItem.MESSAGE, message));
	}
	
	public void updateSuccessLog(String message, String details) {
		String detailMsg = getJobDuration();
		log().add(new JobLogItem(JobLogItem.SUCCESS, BAbsTime.make(), message, detailMsg + " "  + details));
		complete(BJobState.success);
	}
	
	public void updateCancelLog() {
		String jobCancelMsg = lex.getText("honWizardJob.cancelled", getNameOfJob(), device.getDeviceName());
		String detailMsg = jobCancelMsg + getJobDuration();
		log().add(new JobLogItem(JobLogItem.CANCELED, BAbsTime.make(), jobCancelMsg, detailMsg));
		complete(BJobState.canceled);
	}

	private String getJobDuration() {
		double duration = (System.currentTimeMillis() - startMillis) / 1000.0;
		return " [Job Duration: " + Double.toString(duration) + " s]";
	}
	
	@Override
	public String toString(Context cx) {
		String jobName = getNameOfJob();
		if (jobName.isEmpty()) {
			return super.toString(cx);
		} else {
			return jobName;
		}
	}

	public boolean isJobCanceled() {
		return jobCanceled;
	}

	public void setJobCanceled(boolean jobCanceled) {
		this.jobCanceled = jobCanceled;
	}

	public BIHoneywellConfigurableDevice getDevice() {
		return device;
	}

	public boolean isHasFailItemsInLog() {
		return hasFailItemsInLog;
	}

	public void setHasFailItemsInLog(boolean hasFailItemsInLog) {
		this.hasFailItemsInLog = hasFailItemsInLog;
	}
	
}