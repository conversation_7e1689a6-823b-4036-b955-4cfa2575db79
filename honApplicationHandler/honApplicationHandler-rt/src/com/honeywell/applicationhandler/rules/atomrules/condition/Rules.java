/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */

package com.honeywell.applicationhandler.rules.atomrules.condition;

import com.honeywell.applicationhandler.rules.enums.RuleOperator;
import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class Rules implements IConditionRule {

    RuleOperator ruleOperator;

    List<IConditionRule> rulesList = new ArrayList<>();

    @Override
    public RuleType getRuleType() {
        return RuleType.RULES;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        json.put("operator", this.getRuleOperator().getKey());

        JSONArray rulesArray = new JSONArray();
        for(IConditionRule rule : this.getRules()) {
            rulesArray.put(rule.toJSON());
        }
        json.put("rules", rulesArray);

        return json;
    }

    public RuleOperator getRuleOperator() {
        return ruleOperator;
    }

    public void setRuleOperator(RuleOperator ruleOperator) {
        this.ruleOperator = ruleOperator;
    }

    public void addRule(IConditionRule rule) {
        this.rulesList.add(rule);
    }

    public List<IConditionRule> getRules() {
        return rulesList;
    }
}
