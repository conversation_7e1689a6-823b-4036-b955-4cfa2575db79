/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.atomrules.value;

import java.util.ArrayList;
import java.util.List;

import com.honeywell.applicationhandler.rules.enums.CalculationOperator;
import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

public class Calculation extends ValueBaseRule {

    CalculationOperator operator;

    List<ValueBaseRule> values = new ArrayList<>();

    @Override
    public RuleType getRuleType() {
        return RuleType.CALCULATION;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        json.put("operator", this.getCalculationOperator().getKey());

        JSONArray valuesArray = new JSONArray();
        for(ValueBaseRule valueRule : this.getValueRules()) {
            valuesArray.put(valueRule.toJSON());
        }
        json.put("values", valuesArray);

        return json;
    }

    public CalculationOperator getCalculationOperator() {
        return operator;
    }

    public void setCalculationOperator(CalculationOperator operator) {
        this.operator = operator;
    }

    public void addValueRule(ValueBaseRule rule) {
        this.values.add(rule);
    }

    public List<ValueBaseRule> getValueRules() {
        return values;
    }
}
