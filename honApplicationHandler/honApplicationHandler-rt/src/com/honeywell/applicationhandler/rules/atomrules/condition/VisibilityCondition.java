/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.rules.atomrules.condition;

import com.tridium.json.JSONObject;

public abstract class VisibilityCondition implements IConditionRule {
    String targetStore;
    String targetItem;

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        if(this.getTargetStore() != null && !this.getTargetStore().isEmpty()) {
            json.put("targetStore", this.getTargetStore());
        }
        json.put("targetItem", this.getTargetItem());
        return json;
    }

    public String getTargetStore() {
        return targetStore;
    }

    public void setTargetStore(String targetStore) {
        this.targetStore = targetStore;
    }

    public String getTargetItem() {
        return targetItem;
    }

    public void setTargetItem(String targetItem) {
        this.targetItem = targetItem;
    }
}
