package com.honeywell.applicationhandler.rules.atomrules.value;

/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */

import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.tridium.json.JSONObject;

public class TargetValue extends ValueBaseRule {

    String targetStore;
    String targetItem;

    @Override
    public RuleType getRuleType() {
        return RuleType.TARGET_VALUE;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        if(this.getTargetStore() != null && !this.getTargetStore().isEmpty()) {
            json.put("targetStore", this.getTargetStore());
        }
        json.put("targetItem", this.getTargetItem());
        return json;
    }

    public String getTargetStore() {
        return targetStore;
    }

    public void setTargetStore(String targetStore) {
        this.targetStore = targetStore;
    }

    public String getTargetItem() {
        return targetItem;
    }

    public void setTargetItem(String targetItem) {
        this.targetItem = targetItem;
    }
}
