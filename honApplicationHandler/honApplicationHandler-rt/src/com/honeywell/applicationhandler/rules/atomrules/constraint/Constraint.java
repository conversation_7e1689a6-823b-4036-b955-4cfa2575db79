/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.atomrules.constraint;

import com.honeywell.applicationhandler.rules.atomrules.IAtomRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public abstract class Constraint implements IAtomRule {
    List<Object> items;
    Object deadband;
    Object enableRule;




    List<Object> withDeadbands;

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        JSONArray itemsArray = new JSONArray();
        for(Object item : this.getItems()) {
            itemsArray.put(item);
        }
        json.put("items", itemsArray);
        if(this.getDeadband() != null) {
            json.put("deadband", this.getDeadband());
        }
        if(this.getEnableRule() != null) {
            json.put("enableRule", this.getEnableRule());
        }
        if(this.getWithDeadbands() != null) {
            JSONArray withDeadbands = new JSONArray();
            for(Object item: this.getWithDeadbands()){
                withDeadbands.put(item);
            }
            json.put("withDeadbands", withDeadbands);
        }
        return json;
    }

    public Object getDeadband() {
        return deadband;
    }

    public void setDeadband(String targetStore, String targetItem) {
        JSONObject deadband = new JSONObject();
        if(targetStore != null && !targetStore.isEmpty()) {
            deadband.put("targetStore", targetStore);
        }
        deadband.put("targetItem", targetItem);

        this.deadband = deadband;
    }

    public List<Object> getWithDeadbands() {
        return withDeadbands;
    }

    public void setWithDeadbands(List<Object> withDeadbands) {
        this.withDeadbands = withDeadbands;
    }

    public void setDeadband(String targetItem) {
        setDeadband(null, targetItem);
    }

    public void setDeadband(float deadband) {
        this.deadband = deadband;
    }

    public Object getEnableRule() {
        return enableRule;
    }

    public void setEnableRule(boolean enableRule) {
        this.enableRule = enableRule;
    }

    public void setEnableRule(IConditionRule enableRule) {
        this.enableRule = enableRule.toJSON();
    }

    public List<Object> getItems() {
        return items;
    }

    public void addItem(String targetItem) {
        if(this.items == null) {
            this.items = new ArrayList<>();
        }
        this.items.add(targetItem);
    }

    public void addWithDeadband(boolean withDeadband) {
        if (this.withDeadbands == null) {
            this.withDeadbands = new ArrayList<>();
        }
        this.withDeadbands.add(withDeadband);
    }

    public void addItem(String targetStore, String targetItem) {
        JSONObject item = new JSONObject();
        if(targetStore != null && !targetStore.isEmpty()) {
            item.put("targetStore", targetStore);
        }
        item.put("targetItem", targetItem);
        if(this.items == null) {
            this.items = new ArrayList<>();
        }
        this.items.add(item);
    }
}
