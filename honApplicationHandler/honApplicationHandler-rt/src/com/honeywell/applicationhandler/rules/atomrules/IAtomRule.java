/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.atomrules;

import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.enums.RuleType;

public interface IAtomRule extends IRule {
    RuleType getRuleType();
}
