/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.atomrules.value;

import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.tridium.json.JSONObject;

public class PureValue extends ValueBaseRule {

    Object targetValue;

    @Override
    public RuleType getRuleType() {
        return RuleType.VALUE;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        json.put("targetValue", this.getTargetValue());
        return json;
    }

    public Object getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(String targetValue) {
        this.targetValue = targetValue;
    }

    public void setTargetValue(int targetValue) {
        this.targetValue = targetValue;
    }

    public void setTargetValue(float targetValue) {
        this.targetValue = targetValue;
    }

    public void setTargetValue(boolean targetValue) {
        this.targetValue = targetValue;
    }
}
