/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.rules.atomrules.value;

import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.tridium.json.JSONObject;

/**
 *
 * <AUTHOR> zhang
 * @since March 25, 2025
 */
public class TerminalAssignmentTargetValue extends ValueBaseRule{
    private String targetStore;

    private String targetValue;
    @Override
    public RuleType getRuleType() {
        return RuleType.TERMINAL_ASSIGNMENT_TARGET_VALUE;
    }

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("type", this.getRuleType().getKey());
        if(this.getTargetStore() != null && !this.getTargetStore().isEmpty()) {
            json.put("targetStore", this.getTargetStore());
        }
        json.put("targetValue", this.getTargetValue());
        return json;
    }
    public String getTargetStore() {
        return targetStore;
    }

    public void setTargetStore(String targetStore) {
        this.targetStore = targetStore;
    }
    public String getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(String targetValue) {
        this.targetValue = targetValue;
    }
}
