/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules;

import java.util.ArrayList;
import java.util.List;

import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

public class VisibilityRule implements IRule {

    IConditionRule visibilityRule;
    List<String> trueVisibleItems = new ArrayList<>();
    List<String> falseVisibleItems = new ArrayList<>();
    List<String> trueInvisibleItems = new ArrayList<>();
    List<String> falseInvisibleItems = new ArrayList<>();

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        try {
			json.put("visibilityRule", this.getVisibilityRule().toJSON());
			JSONArray trueVisibleItms = new JSONArray();
			for(String item : this.getTrueVisibleItems()) {
			    trueVisibleItms.put(item);
			}
			JSONArray falseVisibleItms = new JSONArray();
			for(String item : this.getFalseVisibleItems()) {
			    falseVisibleItms.put(item);
			}
            JSONArray trueInvisibleItms = new JSONArray();
            for(String item : this.getTrueInvisibleItems()) {
                trueInvisibleItms.put(item);
            }
            JSONArray falseInvisibleItms = new JSONArray();
            for(String item : this.getFalseInvisibleItems()) {
                falseInvisibleItms.put(item);
            }
			json.put("trueVisibleItems", trueVisibleItms);
			json.put("falseVisibleItems", falseVisibleItms);
            json.put("trueInvisibleItems", trueInvisibleItms);
            json.put("falseInvisibleItems", falseInvisibleItms);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return json;
    }

    public IConditionRule getVisibilityRule() {
        return visibilityRule;
    }

    public void setVisibilityRule(IConditionRule visibilityRule) {
        this.visibilityRule = visibilityRule;
    }

    public List<String> getTrueVisibleItems() {
        return trueVisibleItems;
    }

    public void addTrueVisibleItem(String trueVisibleItem) {
        this.trueVisibleItems.add(trueVisibleItem);
    }

    public List<String> getFalseVisibleItems() {
        return falseVisibleItems;
    }

    public void addFalseVisibleItem(String falseVisibleItem) {
        this.falseVisibleItems.add(falseVisibleItem);
    }

    public List<String> getTrueInvisibleItems() {
        return trueInvisibleItems;
    }

    public void addTrueInvisibleItem(String trueInvisibleItem) {
        this.trueInvisibleItems.add(trueInvisibleItem);
    }

    public List<String> getFalseInvisibleItems() {
        return falseInvisibleItems;
    }

    public void addFalseInvisibleItem(String falseInvisibleItem) {
        this.falseInvisibleItems.add(falseInvisibleItem);
    }

	public void setTrueVisibleItems(List<String> trueVisibleItems) {
		this.trueVisibleItems = trueVisibleItems;
		
	}

	public void setFalseVisibleItems(List<String> falseVisibleItems) {
		this.falseVisibleItems = falseVisibleItems;
	}
}
