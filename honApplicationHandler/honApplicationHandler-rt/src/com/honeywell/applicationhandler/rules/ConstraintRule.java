/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules;

import java.util.ArrayList;
import java.util.List;

import com.honeywell.applicationhandler.rules.atomrules.constraint.Constraint;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

public class ConstraintRule implements IRule {

    List<Constraint> constraints = new ArrayList<>();

    String defaultPageStore;

    @Override
    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("items", this.getItemsArray());
        json.put("defaultPageStore", this.defaultPageStore);

        JSONArray constraintRules = new JSONArray();
        for(Constraint constraint : this.getConstraints()) {
            constraintRules.put(constraint.toJSON());
        }
        json.put("constraintRules", constraintRules);
        return json;
    }

    private JSONArray getItemsArray() {
        JSONArray itemsArray = new JSONArray();
        List<Object> items = new ArrayList<>();
        for(Constraint constraint : this.getConstraints()) {
            constraint.getItems().forEach(constraintItem -> {
                if(items.stream().noneMatch(item -> item.toString().equals(constraintItem.toString()))) {
                    items.add(constraintItem);
                    itemsArray.put(constraintItem);
                }
            });
        }
        return itemsArray;
    }

    public List<Constraint> getConstraints() {
        return constraints;
    }

    public void addConstraint(Constraint constraint) {
        this.constraints.add(constraint);
    }

    public void addConstraints(List<Constraint> constraints) {
        this.constraints.addAll(constraints);
    }
    public void setDefaultPageStore(String defaultPageStore) {
        this.defaultPageStore = defaultPageStore;
    }
}
