/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */

package com.honeywell.applicationhandler.rules.parser;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.baja.job.JobLogItem;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Property;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.Const;


import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.ValueRule;
import com.honeywell.applicationhandler.rules.VisibilityRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.CompareRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.Equals;
import com.honeywell.applicationhandler.rules.atomrules.condition.GreaterThan;
import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.IsHidden;
import com.honeywell.applicationhandler.rules.atomrules.condition.IsVisible;
import com.honeywell.applicationhandler.rules.atomrules.condition.LessThan;
import com.honeywell.applicationhandler.rules.atomrules.condition.NotEquals;
import com.honeywell.applicationhandler.rules.atomrules.condition.Rules;
import com.honeywell.applicationhandler.rules.atomrules.value.Calculation;
import com.honeywell.applicationhandler.rules.atomrules.value.PureValue;
import com.honeywell.applicationhandler.rules.atomrules.value.TargetValue;
import com.honeywell.applicationhandler.rules.atomrules.value.ValueBaseRule;
import com.honeywell.applicationhandler.rules.enums.CalculationOperator;
import com.honeywell.applicationhandler.rules.enums.RuleOperator;
import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.honeywell.applicationhandler.utils.StringUtils;
import com.honeywell.applicationhandler.widgetcomponents.BRangeSlider;
import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;
import com.tridium.json.JSONObject;
import com.tridium.nre.util.tuple.Pair;

import static com.honeywell.applicationhandler.ontology.Const.SEPARATOR_LABEL_ROLE_CHARACTER;

/**
 * The RuleParser class is responsible for parsing rule queries and converting them into 
 * corresponding rule objects (IRule). The class supports parsing of value rules and visibility rules.
 * 
 * The expected query format is:
 * 
 * RuleType: Tab: IF Condition THEN Action1, Action2 ELSE Action3, Action4
 * 
 * - RuleType: Specifies the type of the rule (e.g., VALUE, VISIBILITY).
 * - CurrentTab: Specifies the tab context for the rule.
 * - Condition: Specifies the condition(s) that need to be met for the rule to be applied.
 * - Action1, Action2: Specifies the actions to be taken if the condition is met.
 * - Action3, Action4: Specifies the actions to be taken if the condition is not met.
 * 
 * Example queries:
 * 
 * ADD VALUE RULE TO: Tab1: IF item1 EQUALS "value1" THEN SET item2 TO "value2" ELSE SET item3 TO "value3"
 * ADD VISIBILITY RULE TO: Tab2: IF item4 GREATER_THAN "10" THEN SHOW item5 ELSE HIDE item6
 * 
 * The class provides methods to parse conditions, actions, and visibility actions, and to generate 
 * JSON representations of the parsed rules.
 * 
 * Methods:
 * - parse(String query): Parses the given query string and returns an IRule object.
 * - parseConditions(String condition, String currentTab): Parses the condition part of the query.
 * - parseAndConditions(String condition, String currentTab): Parses AND conditions.
 * - parseCondition(String condition, String currentTab): Parses individual conditions.
 * - parseSetAction(String action, String currentTab): Parses SET actions.
 * - parseVisibilityAction(String action, List<String> trueVisibleItems, List<String> falseVisibleItems, String currentTab): Parses visibility actions.
 * - createValueBaseRule(Calculation calculation, String value1, String value2, String value1Tab, String value2Tab): Creates value base rules for calculations.
 * - generateJson(IRule rule): Generates a JSON representation of the given rule.
 * 
 * Constants:
 * - STATIC_VALUE_CONST: Regular expression for static values.
 * - CONDITION_NOT_NULL: Warning message for null or empty conditions.
 * 
 * Logging:
 * The class uses a Logger to log various stages of parsing and any warnings or errors encountered.
 * 
 * Note:
 * The class assumes that the input query string is well-formed and follows the expected format.
 * 
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jan 10, 2025
 */
public class RuleParser {

    private static final Logger LOGGER = Logger.getLogger(RuleParser.class.getName());
    protected List<ErrorReport> errorReports = new ArrayList<>();
    private static final String STATIC_VALUE_CONST = "\"[^\"]+\"";
    private static final String CONDITION_NOT_NULL = "error.rulevalidation.conditional.not.null";
    protected static final String ACTION_NOT_NULL = "error.rulevalidation.action.not.null";
    private static final String VISIBILITY_ACTION_NOT_NULL = "error.rulevalidation.visibility.action.not.null";
    private static final String QUERY_NOT_NULL = "error.rulevalidation.query.not.null";
    
    private static final String QUERY_EMPTY_SUGGESTION = "error.rulevalidation.visibility.query.empty.suggestion";
    protected static final String ACTION_NULL_SUGGESTION = "error.rulevalidation.visibility.action.null.suggestion";
    private static final String CONDITION_NULL_SUGGESTION = "error.rulevalidation.visibility.condition.null.suggestion";
    private static final String VISIB_ACTION_NULL_SUGGESTION = "error.rulevalidation.visibility.condition.visib.action.null.suggestion";
    private static final String CONDITION_FORMAT_SUGGESTION = "error.rulevalidation.visibility.condition.condition.format.suggestion";
    private static final String CONDITION_OPERATOR_SUGGESTION = "error.rulevalidation.condition.operator.suggestion";
    private static final String ACTION_FORMAT_SUGGESTION = "error.rulevalidation.action.format.suggestion";


    private static final String VISIBILITY_ACTION_FORMAT_SUGGESTION = "error.rulevalidation.visibility.action.format.suggestion";
    private static final String VISIBILITY_ACTION_TYPE_SUGGESTION = "error.rulevalidation.visibility.action.type.suggestion";
    private static final String RULE_FORMAT_SUGGESTION = "error.rulevalidation.rule.format.suggestion";
    protected static final String ERROR_PAGE_SUGGESTION = "error.rulevalidation.error.page.suggestion";

	protected static final String CONSTRAINT_RULE_FORMAT_SUGGESTION = "error.rulevalidation.constraintrule.format.suggestion";


    private static final String ERROR_TARGET_ITEM_SUGGESTION = "error.rulevalidation.error.target.item.suggestion";


    private static final String RULE_TYPE_FORMAT_SUGGESTION = "error.rulevalidation.rule.type.format.suggestion";
    
    private static final String JOB_LOG_TEXT = "error.rulevalidation.job.log.text";

    protected static final String RULE_DATA = "error.rulevalidation.rule.data";
    protected static final String RULE_FORMAT = "error.rulevalidation.rule.format";


    protected static final Lexicon lex = LexiconUtil.getLexicon();
    protected BIHoneywellConfigurableDevice device;

    protected Map<String, Map<String,  List<Pair<String, String>>>> globalStoreCache;

	protected Map<String, Map<String, List<String>>> terminalStoreCache;
    
    public static Logger getLogger() {
		return LOGGER;
	}
    
    public static void finest(String msg) {
    	if(LOGGER.isLoggable(Level.FINEST)) {
    		LOGGER.log(Level.FINEST, msg);
    	}
    }
    
    public static void finer(String msg) {
    	if(LOGGER.isLoggable(Level.FINER)) {
    		LOGGER.log(Level.FINER, msg);
    	}
    }
    
    public static void fine(String msg) {
    	if(LOGGER.isLoggable(Level.FINE)) {
    		LOGGER.log(Level.FINE, msg);
    	}
    }
    
    public static void info(String msg) {
    	if(LOGGER.isLoggable(Level.INFO)) {
    		LOGGER.log(Level.INFO, msg);
    	}
    }
    
    public static void severe(String msg) {
    	if(LOGGER.isLoggable(Level.SEVERE)) {
    		LOGGER.log(Level.SEVERE, msg);
    	}
    }
    
    public static void severe(String msg, Exception ex) {
    	if(LOGGER.isLoggable(Level.SEVERE)) {
    		LOGGER.log(Level.SEVERE, msg, ex);
    	}
    }
    
    public static void warning(String msg) {
    	if(LOGGER.isLoggable(Level.WARNING)) {
    		LOGGER.log(Level.WARNING, msg);
    	}
    }

    public RuleParser(BIHoneywellConfigurableDevice device, boolean buildGlobalStoreCache) {
        this.device = device;
		if(buildGlobalStoreCache) {
			terminalStoreCache = this.buildTerminalStoreCache();
			globalStoreCache = this.buildWizardStoreCache();
		}
    }



	/**
     * Parses a query string and returns an IRule object based on the parsed data.
     * 
     * @param query the query string to be parsed. The query should be in the format:
     *              "ruleType:currentTab:IF condition THEN action1, action2 ELSE action3, action4".
     *              - ruleType: The type of the rule (e.g., VALUE, VISIBILITY).
     *              - currentTab: The current tab context.
     *              - condition: The condition part of the rule.
     *              - action1, action2: Actions to be performed if the condition is true.
     *              - action3, action4: Actions to be performed if the condition is false.
     * @return an IRule object representing the parsed rule, or null if the query is invalid.
     * @throws IllegalArgumentException if the query is null or empty.
     *
     * The method performs the following steps:
     * 1. Validates the input query string.
     * 2. Logs the parsing process at various stages.
     * 3. Splits the query string into parts and extracts the rule type, current tab, and condition.
     * 4. Determines the rule type and initializes the appropriate rule object (ValueRule or VisibilityRule).
     * 5. Parses the condition and actions (THEN and ELSE parts).
     * 6. Sets the parsed conditions and actions to the rule object.
     */
	public IRule parse(String query, String ruleName) {
		if (query == null || query.trim().isEmpty()) {
			String errorMsg = lex.getText(QUERY_NOT_NULL);
			warning(errorMsg);
			errorReports.add(new ErrorReport(query, "Query", errorMsg, lex.getText(QUERY_EMPTY_SUGGESTION)));
			return null;
		}
		
		query = StringUtils.normalizeSpace(query);
		
		if(ruleName == null || query.trim().isEmpty()) {
			ruleName = query;
		}

		info(MessageFormat.format("Parsing query: {0}", query));

		IRule rule;
		List<IConditionRule> conditions = new ArrayList<>();
		List<ValueRule.ValueResult> thenActions = new ArrayList<>();
		List<ValueRule.ValueResult> elseActions = new ArrayList<>();
		List<String> trueVisibleItems = new ArrayList<>();
		List<String> falseVisibleItems = new ArrayList<>();
		List<String> trueInvisibleItems = new ArrayList<>();
		List<String> falseInvisibleItems = new ArrayList<>();

		String[] parts = query.split(":");
		if (parts.length < 3) {
			String errorMsg = MessageFormat.format("Invalid query format: {0}", query);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, "Rule Format", errorMsg, lex.getText(RULE_FORMAT_SUGGESTION)));
			return null;
		}

		String ruleTypePart = parts[0].trim();
		String currentTab = parts[1].trim();
        if (!validateTab(ruleName, query, currentTab)) {
            return null;
        }
		String conditionPart = parts[2].substring(parts[2].indexOf("IF") + 2).trim();
		String[] conditionTokens = conditionPart.split("THEN");

		info(MessageFormat.format("Rule type part: {0}", ruleTypePart));
		info(MessageFormat.format("Current tab: {0}", currentTab));
		info(MessageFormat.format("Condition part: {0}", conditionPart));

        if (ruleTypePart.contains(RuleType.TERMINAL_ASSIGNMENT_VALUE.getKey())) {
            return new TerminalAssignmentRuleParser(device, errorReports, terminalStoreCache, globalStoreCache).parseRule(parts, query, ruleName);
        } else if (ruleTypePart.contains(RuleType.VALUE.getKey())) {
            rule = new ValueRule();
        } else if (ruleTypePart.contains(RuleType.VISIBILITY.getKey())) {
            rule = new VisibilityRule();
        } else if (ruleTypePart.contains(RuleType.CONSTRAINT.getKey())) {
			return new ConstraintRuleParser(device, errorReports, globalStoreCache).parseRule(parts, query, ruleName);
		} else {
            String errorMsg = MessageFormat.format("Unknown rule type: {0}", ruleTypePart);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, "Invalid Rule", errorMsg, lex.getText(RULE_TYPE_FORMAT_SUGGESTION)));
            return null;
        }

		conditions.add(parseConditions(conditionTokens[0].trim(), currentTab, ruleName));

		extractThenElseActions(rule, thenActions, elseActions, trueVisibleItems, falseVisibleItems,trueInvisibleItems, falseInvisibleItems, currentTab,
				conditionTokens, ruleName);

		initializeRuleData(rule, conditions, thenActions, elseActions, trueVisibleItems, falseVisibleItems, trueInvisibleItems, falseInvisibleItems);

		return rule;
	}

	/**
	 * Initializes the rule data based on the type of rule provided.
	 *
	 * @param rule The rule to be initialized. It can be an instance of either ValueRule or VisibilityRule.
	 * @param conditions A list of condition rules. The first condition in the list is used to set the condition for the rule.
	 * @param thenActions A list of ValueRule.ValueResult objects representing the actions to be taken if the condition is true.
	 * @param elseActions A list of ValueRule.ValueResult objects representing the actions to be taken if the condition is false.
	 * @param trueVisibleItems A list of strings representing the items that should be visible if the condition is true.
	 * @param falseVisibleItems A list of strings representing the items that should be visible if the condition is false.
	 * @param trueInvisibleItems A list of strings representing the items that should be hidden if the condition is true.
	 * @param falseInvisibleItems A list of strings representing the items that should be hidden if the condition is false.
	 *
	 * If the rule is an instance of ValueRule, the method sets the condition using the first element of the conditions list,
	 * and adds the thenActions and elseActions to the rule.
	 *
	 * If the rule is an instance of VisibilityRule, the method sets the visibility condition using the first element of the conditions list,
	 * and adds the trueVisibleItems and falseVisibleItems to the rule.
	 */
	private void initializeRuleData(IRule rule, List<IConditionRule> conditions, List<ValueRule.ValueResult> thenActions,
			List<ValueRule.ValueResult> elseActions, List<String> trueVisibleItems, List<String> falseVisibleItems,
			List<String> trueInvisibleItems, List<String> falseInvisibleItems) {
		if (rule instanceof ValueRule) {
			((ValueRule) rule).setCheckIf(conditions.get(0));
			for (ValueRule.ValueResult action : thenActions) {
				((ValueRule) rule).addResultThen(action);
			}
			for (ValueRule.ValueResult action : elseActions) {
				((ValueRule) rule).addResultElse(action);
			}
		} else if (rule instanceof VisibilityRule) {
			((VisibilityRule) rule).setVisibilityRule(conditions.get(0));
			for (String item : trueVisibleItems) {
				((VisibilityRule) rule).addTrueVisibleItem(item);
			}
			for (String item : falseVisibleItems) {
				((VisibilityRule) rule).addFalseVisibleItem(item);
			}
			for (String item : trueInvisibleItems) {
				((VisibilityRule) rule).addTrueInvisibleItem(item);
			}
			for (String item : falseInvisibleItems) {
				((VisibilityRule) rule).addFalseInvisibleItem(item);
			}
		}
	}

	/**
	 * Extracts and processes the actions specified in the condition tokens for both "then" and "else" scenarios.
	 * 
	 * @param thenActions       A list to store the parsed actions that should be executed if the condition is true.
	 * @param elseActions       A list to store the parsed actions that should be executed if the condition is false.
	 * @param trueVisibleItems  A list to store the items that should be visible if the condition is true.
	 * @param falseVisibleItems A list to store the items that should be visible if the condition is false.
	 * @param trueInvisibleItems  A list to store the items that should be hidden if the condition is true.
	 * @param falseInvisibleItems A list to store the items that should be hidden if the condition is false.
	 * @param currentTab      The current tab context in which the actions are being parsed.
	 * @param conditionTokens   An array of strings representing the condition and its associated actions.
	 * 
	 * The method splits the condition tokens into "then" and "else" parts based on the "ELSE" keyword.
	 * It then processes each part separately:
	 * - For the "then" part, it splits the actions by commas and adds them to the thenActions list or updates visibility lists.
	 * - For the "else" part, if present, it splits the actions by commas and adds them to the elseActions list or updates visibility lists.
	 *
	 * Actions starting with "SET" are parsed and added to the respective actions list.
	 * Other actions are considered visibility actions and are processed to update the visibility lists.
	 */
	private void extractThenElseActions(IRule rule, List<ValueRule.ValueResult> thenActions, List<ValueRule.ValueResult> elseActions,
			List<String> trueVisibleItems, List<String> falseVisibleItems, List<String> trueInvisibleItems, List<String> falseInvisibleItems,
			String currentTab, String[] conditionTokens, String ruleName) {
		String[] actionParts = conditionTokens[1].split("ELSE");
		String[] thenActionTokens = actionParts[0].split(",");
		for (String actionToken : thenActionTokens) {
			if (actionToken.trim().startsWith("SET")) {
				parseSetActionByRule(rule, thenActions, actionToken.trim(), currentTab, ruleName);
			} else {
				parseVisibilityAction(actionToken.trim(), trueVisibleItems, trueInvisibleItems, currentTab, ruleName);
			}
		}

		if (actionParts.length > 1) {
			String[] elseActionTokens = actionParts[1].split(",");
			for (String actionToken : elseActionTokens) {
				if (actionToken.trim().startsWith("SET")) {
					parseSetActionByRule(rule, elseActions, actionToken.trim(), currentTab, ruleName);
				} else {
					parseVisibilityAction(actionToken.trim(), falseVisibleItems, falseInvisibleItems, currentTab, ruleName);
				}
			}
		}
	}

    private void parseSetActionByRule(IRule rule, List<ValueRule.ValueResult> thenActions, String action, String currentTab, String ruleName) {
       if (rule instanceof ValueRule) {
            ValueRule.ValueResult valueResult = parseSetAction(action, currentTab, ruleName);
            if (null != valueResult) {
                thenActions.add(parseSetAction(action, currentTab, ruleName));
            }
        }
    }

    /**
     * Parses the given condition string and returns an IConditionRule object representing the parsed conditions.
     * The method supports parsing conditions with "OR" and "AND" operators.
     * 
     * If the condition string contains "OR" operators, it splits the condition into individual parts and 
     * parses each part separately using the parseAndConditions method. The parsed conditions are then combined 
     * into a single Rules object with the RuleOperator set to OR.
     * 
     * If the condition string does not contain "OR" operators, it directly parses the condition using the 
     * parseAndConditions method.
     * 
     * If the condition string is null or empty, the method logs a warning and returns null.
     * 
     * @param condition the condition string to be parsed. It can contain "OR" and "AND" operators.
     * @param currentTab the current tab context used for parsing the conditions.
     * @return an IConditionRule object representing the parsed conditions, or null if the condition string is null or empty.
     * 
     * @throws IllegalArgumentException if the condition string is invalid or cannot be parsed.
     * 
     * Example usage:
     * 
     * {@code
     * IConditionRule rule = parseConditions("condition1 AND condition2 OR condition3", "tab1");
     * }
     * 
     * 
     * Logging:
     * 
     *   Logs a warning if the condition string is null or empty.
     *   Logs an info message with the condition string being parsed.
     * 
     */
	protected IConditionRule parseConditions(String condition, String currentTab, String query) {
		if (condition == null || condition.trim().isEmpty()) {
			warning(lex.getText(CONDITION_NOT_NULL));
			errorReports.add(new ErrorReport(query, condition, lex.getText(CONDITION_NOT_NULL), lex.getText(CONDITION_NULL_SUGGESTION)));
			return null;
		}

		info(MessageFormat.format("Parsing conditions: {0}", condition));

		String[] orConditions = condition.split(" OR ");
		if (orConditions.length > 1) {
			Rules orRule = new Rules();
			orRule.setRuleOperator(RuleOperator.OR);
			for (String orConditionPart : orConditions) {
				IConditionRule parsedCondition = parseAndConditions(orConditionPart.trim(), currentTab, query);
				if (parsedCondition != null) {
					orRule.addRule(parsedCondition);
				}
			}
			return orRule;
		} else {
			return parseAndConditions(condition, currentTab, query);
		}
	}

    /**
     * Parses a given condition string that contains multiple conditions combined with the "AND" operator.
     * This method handles the parsing of complex conditions by breaking them down into individual conditions
     * and combining them using the "AND" logical operator.
     *
     * @param condition The condition string to be parsed. This string may contain multiple conditions separated by "AND".
     *                  For example, "condition1 AND condition2 AND condition3".
     *                  If the condition string is null or empty, a warning is logged and the method returns null.
     * @param currentTab The current tab context in which the condition is being parsed. This parameter is used
     *                     to provide context-specific parsing of the condition.
     * @return An instance of {@link IConditionRule} representing the parsed condition(s). If the condition string
     *         contains multiple conditions separated by "AND", a {@link Rules} object with the "AND" operator is returned,
     *         containing each parsed condition as a rule. If the condition string contains only a single condition,
     *         the parsed condition is returned directly. If the condition string is null or empty, null is returned.
     *
     * The method performs the following steps:
     * 1. Checks if the condition string is null or empty. If so, logs a warning and returns null.
     * 2. Logs an informational message indicating the start of parsing the "AND" conditions.
     * 3. Splits the condition string by the "AND" delimiter to separate individual conditions.
     * 4. If multiple conditions are found:
     *    a. Creates a new {@link Rules} object and sets its operator to {@link RuleOperator#AND}.
     *    b. Iterates over each separated condition part, trims it, and parses it using the {@link #parseCondition(String, String)} method.
     *    c. Adds each successfully parsed condition to the {@link Rules} object.
     *    d. Returns the {@link Rules} object containing all parsed conditions combined with the "AND" operator.
     * 5. If only a single condition is found, parses it directly using the {@link #parseCondition(String, String)} method and returns the result.
     */
	private IConditionRule parseAndConditions(String condition, String currentTab, String ruleName) {
		if (condition == null || condition.trim().isEmpty()) {
			warning(lex.getText(CONDITION_NOT_NULL));
			errorReports.add(new ErrorReport(ruleName, condition, lex.getText(CONDITION_NOT_NULL), lex.getText(CONDITION_NULL_SUGGESTION)));
			return null;
		}

		info(MessageFormat.format("Parsing AND conditions: {0}", condition));

		String[] andConditions = condition.split(" AND ");
		if (andConditions.length > 1) {
			Rules andRule = new Rules();
			andRule.setRuleOperator(RuleOperator.AND);
			for (String andConditionPart : andConditions) {
				IConditionRule parsedCondition = parseCondition(andConditionPart.trim(), currentTab, ruleName);
				if (parsedCondition != null) {
					andRule.addRule(parsedCondition);
				}
			}
			return andRule;
		} else {
			return parseCondition(condition, currentTab, ruleName);
		}
	}

    /**
     * Parses a condition string and returns an IConditionRule object representing the parsed condition.
     *
     * The condition string is expected to follow a specific format:
     * 
     *     targetItem [IN tab] operator ["expectedValue"] [secondItem [IN secondTab]]
     * 
     * where:
     * - targetItem: The primary item to which the condition applies.
     * - tab: (Optional) The tab in which the target item resides. If not provided, currentTab is used.
     * - operator: The comparison operator, which can be one of the following:
     *   - EQUALS
     *   - NOT_EQUALS
     *   - GREATER_THAN
     *   - LESS_THAN
     *   - IS_VISIBLE
     *   - IS_HIDDEN
     * - expectedValue: (Optional) The value to compare against.
     * - secondItem: (Optional) A secondary item involved in the condition.
     * - secondTab: (Optional) The tab in which the second item resides. If not provided, currentTab is used.
     *
     * The method performs the following steps:
     * 1. Checks if the condition string is null or empty. If so, logs a warning and returns null.
     * 2. Removes any semicolons from the condition string.
     * 3. Logs the condition string being parsed.
     * 4. Uses a regular expression to match the condition string against the expected format.
     * 5. If the condition string does not match the expected format, logs a warning and returns null.
     * 6. Extracts the components of the condition from the matched groups of the regular expression.
     * 7. Logs the parsed components of the condition.
     * 8. Calls the parseCondtion method with the extracted components to create and return an IConditionRule object.
     *
     * @param condition The condition string to be parsed.
     * @param currentTab The default tab to be used if the tab is not specified in the condition string.
     * @return An IConditionRule object representing the parsed condition, or null if the condition string is invalid.
     */
	private IConditionRule parseCondition(String condition, String currentTab, String ruleName) {
		if (condition == null || condition.trim().isEmpty()) {
			warning(lex.getText(CONDITION_NOT_NULL));
			errorReports.add(new ErrorReport(ruleName, condition, lex.getText(CONDITION_NOT_NULL), lex.getText(CONDITION_NULL_SUGGESTION)));
			return null;
		}

		condition = condition.replace(";", "");
		info(MessageFormat.format("Parsing condition: {0}", condition));

	    // Check if the condition contains any of the specified keywords
	    Pattern keywordPattern = Pattern.compile("\\b(EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\b");
	    Matcher keywordMatcher = keywordPattern.matcher(condition);
	    if (!keywordMatcher.find()) {
	        String errorMsg = MessageFormat.format("No valid operator present in the condition: {0}", condition);
	        warning(errorMsg);
	        errorReports.add(new ErrorReport(ruleName, condition, errorMsg, lex.getText(CONDITION_OPERATOR_SUGGESTION)));
	        return null;
	    }
		
		Pattern pattern = Pattern.compile(
//					"^(\\w+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\w+)*)(?:\\s+IN\\s+(\\w+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\w+)*))?\\s+(EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\s+(\\w+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\w+)*|\"[^\"]+\")(?:\\s+IN\\s+(\\w+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\w+)*))?$");
					"^([\\w\\-_$\\\\/]+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)[\\w\\-_$\\\\/]+)*))?\\s+(EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)[\\w\\-_$\\\\/]+)*|\"[^\"]+\")(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|EQUALS|NOT_EQUALS|GREATER_THAN|LESS_THAN|IS_VISIBLE|IS_HIDDEN)[\\w\\-_$\\\\/]+)*))?$");
		Matcher matcher = pattern.matcher(condition);
		if (!matcher.matches()) {
			String errorMsg = MessageFormat.format("Invalid condition format: {0}", condition);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, condition, errorMsg, lex.getText(CONDITION_FORMAT_SUGGESTION)));
			return null;
		}

		String targetItem = matcher.group(1);
		String tab = matcher.group(2) != null ? matcher.group(2) : currentTab;
		if(targetItem != null && targetItem.contains(" IN ")) {
			String[] targetDetails = targetItem.split(" IN ");
			targetItem = targetDetails[0].trim();
			tab = targetDetails[1].trim();
		}
		targetItem = getTargetNameInGlobalStore(ruleName, tab, condition, targetItem);
        if (null == targetItem) {
            return null;
        }

		String operator = matcher.group(3);
		String expectedValue = matcher.group(4);
		String secondItem = null;
		String secondTab = null;
		if(!expectedValue.matches("\\d+") && !expectedValue.matches(STATIC_VALUE_CONST)) {
			secondItem = expectedValue;
			expectedValue = null;
			secondTab = (matcher.group(5) != null) ? matcher.group(5) : null;
		}
		if(secondItem != null && secondItem.contains(" IN ")) {
			String[] targetDetails = secondItem.split(" IN ");
			secondItem = targetDetails[0].trim();
			secondTab = targetDetails[1].trim();
			secondItem = getTargetNameInGlobalStore(ruleName, secondTab, condition, secondItem);
			if(null == secondItem){
				return null;
			}
		}
		
		info(MessageFormat.format(
				"Parsed condition - Target item: {0}, Tab: {1}, Operator: {2}, Expected value: {3}, Second item: {4}, Second tab: {5}", targetItem,
				tab, operator, expectedValue, secondItem, secondTab));

		return parseCondition(targetItem, tab, operator, expectedValue, secondItem, secondTab, condition, ruleName);
	}

    /**
     * Parses the given parameters to create an appropriate IConditionRule object based on the specified operator.
     *
     * @param targetItem   The target item for the condition rule.
     * @param tab        The tab associated with the target item.
     * @param operator     The operator that defines the type of condition rule to be created. 
     *                     Supported operators are: "EQUALS", "NOT_EQUALS", "GREATER_THAN", "LESS_THAN", "IS_VISIBLE", "IS_HIDDEN".
     * @param expectedValue The expected value for the condition rule. If this is null, the secondItem and secondTab parameters are used.
     * @param secondItem   The second item to be used if expectedValue is null. This can be a dynamic value or a static value.
     * @param secondTab  The tab associated with the second item, used if expectedValue is null and secondItem is not a static value.
     * 
     * @return An instance of IConditionRule corresponding to the specified operator, or null if the operator is unknown.
     * 
     * The method performs the following steps:
     * 1. Determines the type of condition rule to create based on the operator parameter.
     * 2. Creates an instance of the appropriate condition rule class (Equals, NotEquals, GreaterThan, LessThan, IsVisible).
     * 3. Sets the target item and tab for the condition rule.
     * 4. If the expectedValue parameter is not null, it sets the expected value for the condition rule.
     * 5. If the expectedValue parameter is null, it sets the expected value to the secondItem parameter.
     * 6. If the secondItem is not a numeric value or a static value, it sets the expected tab to the secondTab parameter.
     * 7. Returns the created condition rule instance.
     * 
     * If the operator is "IS_VISIBLE" or "IS_HIDDEN", the method creates an IsVisible instance and sets the target item and tab.
     * If the operator is unknown, the method logs a warning message and returns null.
     */
	private IConditionRule parseCondition(String targetItem, String tab, String operator, String expectedValue, String secondItem,
										  String secondTab, String condition, String ruleName) {
		switch (RuleType.fromKey(operator)) {
		case EQUALS:
			Equals equals = new Equals();
			equals.setTargetItem(getValidNiagaraName(targetItem));
			if (expectedValue != null) {
				updateExpectedValueBasedOnDataType(expectedValue, equals);
			} else {
				equals.setExpectedValue(getValidNiagaraName(secondItem));
				if (!secondItem.matches("\\d+") && !secondItem.matches(STATIC_VALUE_CONST)) {
					equals.setExpectedStore(getValidNiagaraName(secondTab));
				}
			}
			equals.setTargetStore(getValidNiagaraName(tab));
			return equals;
		case NOT_EQUALS:
			NotEquals notEquals = new NotEquals();
			notEquals.setTargetItem(getValidNiagaraName(targetItem));
			if (expectedValue != null) {
				updateExpectedValueBasedOnDataType(expectedValue, notEquals);
			} else {
				notEquals.setExpectedValue(getValidNiagaraName(secondItem));
				if (!secondItem.matches("\\d+") && !secondItem.matches(STATIC_VALUE_CONST)) {
					notEquals.setExpectedStore(getValidNiagaraName(secondTab));
				}
			}
			notEquals.setTargetStore(getValidNiagaraName(tab));
			return notEquals;
		case GREATER_THAN:
			GreaterThan greaterThan = new GreaterThan();
			greaterThan.setTargetItem(getValidNiagaraName(targetItem));
			if (expectedValue != null) {
				updateExpectedValueBasedOnDataType(expectedValue, greaterThan);
			} else {
				greaterThan.setExpectedValue(getValidNiagaraName(secondItem));
				if (!secondItem.matches("\\d+") && !secondItem.matches(STATIC_VALUE_CONST)) {
					greaterThan.setExpectedStore(getValidNiagaraName(secondTab));
				}
			}
			greaterThan.setTargetStore(getValidNiagaraName(tab));
			return greaterThan;
		case LESS_THAN:
			LessThan lessThan = new LessThan();
			lessThan.setTargetItem(getValidNiagaraName(targetItem));
			if (expectedValue != null) {
				updateExpectedValueBasedOnDataType(expectedValue, lessThan);
			} else {
				lessThan.setExpectedValue(getValidNiagaraName(secondItem));
				if (!secondItem.matches("\\d+") && !secondItem.matches(STATIC_VALUE_CONST)) {
					lessThan.setExpectedStore(getValidNiagaraName(secondTab));
				}
			}
			lessThan.setTargetStore(getValidNiagaraName(tab));
			return lessThan;
		case IS_VISIBLE:
			IsVisible isVisible = new IsVisible();
			isVisible.setTargetItem(getValidNiagaraName(targetItem));
			isVisible.setTargetStore(getValidNiagaraName(tab));
			return isVisible;
		case IS_HIDDEN:
			IsHidden isHidden = new IsHidden();
			isHidden.setTargetItem(getValidNiagaraName(targetItem));
			isHidden.setTargetStore(getValidNiagaraName(tab));
			return isHidden;
		default:
			String errorMsg = MessageFormat.format("Unknown condition operator: {0}", operator);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, condition, errorMsg, lex.getText(CONDITION_OPERATOR_SUGGESTION)));
			return null;
		}
	}

	private void updateExpectedValueBasedOnDataType(String expectedValue, CompareRule equals) {
		if (expectedValue.matches("\\d+") ) {
			equals.setExpectedValue(Integer.parseInt(expectedValue));
		} else if (expectedValue.matches(STATIC_VALUE_CONST)) {
			equals.setExpectedValue(expectedValue.replace("\"", ""));
		}
	}    
    
    
    /**
     * Parses a SET action string and returns a ValueRule.ValueResult object representing the parsed action.
     *
     * The SET action string is expected to follow a specific format:
     * 
     * 
     * SET <targetItem> [IN <targetTab>] TO <value> [IN <valueTab>] [ADD|SUBTRACT <secondValue> [IN <secondValueTab>]]
     * 
     * 
     * Examples of valid SET action strings:
     * 
     *   SET item1 TO 100
     *   SET item1 IN tab1 TO 100 IN tab2
     *   SET item1 TO "value"
     *   SET item1 TO item2 ADD 50
     *   SET item1 TO item2 SUBTRACT item3 IN tab3
     * 
     *
     * @param action the SET action string to be parsed. Must not be null or empty.
     * @param currentTab the default tab to be used if the tab is not specified in the action string.
     * @return a ValueRule.ValueResult object representing the parsed action, or null if the action string is invalid.
     * 
     * The returned ValueRule.ValueResult object contains:
     * 
     *   item: the target item to be set
     *   itemTab: the tab where the target item is located
     *   resultRule: the rule representing the value to be set, which can be one of the following:
     *     
     *       PureValue: if the value is a static integer or string
     *       TargetValue: if the value is another item
     *       Calculation: if the value involves an arithmetic operation (ADD or SUBTRACT)
     *     
     *   
     * 
     * 
     * The method performs the following steps:
     * 
     *   Validates that the action string is not null or empty.
     *   Removes any trailing semicolons from the action string.
     *   Logs the action string being parsed.
     *   Uses a regular expression to match the action string against the expected format.
     *   If the action string does not match the expected format, logs a warning and returns null.
     *   Extracts the components of the action string (target item, target tab, value, value tab, operator, second value, second value tab) using the regular expression groups.
     *   Logs the parsed components of the action string.
     *   Creates a ValueRule.ValueResult object and sets the target item and target tab.
     *   If an operator is present (ADD or SUBTRACT), creates a Calculation object, sets the operator, and creates the value base rule using the value and second value.
     *   If no operator is present, determines the type of the value (integer, static string, or target item) and creates the appropriate rule (PureValue or TargetValue).
     *   Sets the result rule in the ValueRule.ValueResult object.
     *   Returns the ValueRule.ValueResult object.
     * 
     */
	private ValueRule.ValueResult parseSetAction(String action, String currentTab, String ruleName) {
		if (action == null || action.trim().isEmpty()) {
			String errorMsg = lex.getText(ACTION_NOT_NULL);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(ACTION_NULL_SUGGESTION)));
			return null;
		}

		action = action.replace(";", "");
		info(MessageFormat.format("Parsing SET action: {0}", action));
		Pattern pattern = Pattern
				.compile("^(SET)\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*))?\\s+TO\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*))?(?:\\s+(ADD|SUBTRACT)\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$\\\\/]+)*))?)?$");
		Matcher matcher = pattern.matcher(action);
		if (!matcher.matches()) {
			String errorMsg = lex.getText("error.invalid.set.action", action);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(ACTION_FORMAT_SUGGESTION)));
			return null;
		}

		String targetItem = matcher.group(2);
		String targetTab = matcher.group(3) != null ? matcher.group(3) : currentTab;
		if(targetItem != null && targetItem.contains(" IN ")) {
			String[] targetDetails = targetItem.split(" IN ");
			targetItem = targetDetails[0].trim();
			targetTab = targetDetails[1].trim();
		}
		targetItem = getTargetNameInGlobalStore(ruleName, targetTab, action, targetItem);
        if (null == targetItem) {
            return null;
        }
		String value = matcher.group(4);
		String valueTab = matcher.group(5) != null ? matcher.group(5) : currentTab;
		if(value != null && value.contains(" IN ")) {
			String[] targetDetails = value.split(" IN ");
			value = targetDetails[0].trim();
			valueTab = targetDetails[1].trim();
			value = getTargetNameInGlobalStore(ruleName, valueTab, action, value);
            if (null == value) {
                return null;
            }
		}
		String operator = matcher.group(6);
		String secondValue = matcher.group(7);
		String secondValueTab = matcher.group(8) != null ? matcher.group(8) : currentTab;
		if(secondValue != null && secondValue.contains(" IN ")) {
			String[] targetDetails = secondValue.split(" IN ");
			secondValue = targetDetails[0].trim();
			secondValueTab = targetDetails[1].trim();
			secondValue = getTargetNameInGlobalStore(ruleName, secondValueTab, action, secondValue);
            if (null == secondValue) {
                return null;
            }
		}
		
		info(MessageFormat.format(
				"Parsed SET action - Target item: {0}, Target tab: {1}, Value: {2}, Value tab: {3}, Operator: {4}, Second value: {5}, Second value tab: {6}",
				targetItem, targetTab, value, valueTab, operator, secondValue, secondValueTab));

		ValueRule.ValueResult valueResult = new ValueRule.ValueResult();
		valueResult.setItem(getValidNiagaraName(targetItem));
		valueResult.setItemStore(getValidNiagaraName(targetTab));

		if (operator != null) {
			Calculation calculation = new Calculation();
			calculation.setCalculationOperator(CalculationOperator.fromKey(operator));
			createValueBaseRule(calculation, value, secondValue, valueTab, secondValueTab, action, ruleName);
			valueResult.setResultRule(calculation);
		} else {
			if (null != value && value.matches("\\d+")) {
				PureValue pureValue = new PureValue();
				pureValue.setTargetValue(Integer.parseInt(value));
				valueResult.setResultRule(pureValue);
			} else if (null != value && value.matches(STATIC_VALUE_CONST)) {
				PureValue pureValue = new PureValue();
				pureValue.setTargetValue(value.replace("\"", ""));
				valueResult.setResultRule(pureValue);
			} else {
				TargetValue targetValue = new TargetValue();
				targetValue.setTargetItem(getValidNiagaraName(value));
				targetValue.setTargetStore(getValidNiagaraName(valueTab));
				valueResult.setResultRule(targetValue);
			}
		}

		return valueResult;
	}





    /**
     * Parses a visibility action string and updates the provided lists of visible and hidden items accordingly.
     *
     * This method processes a visibility action string in the format "SHOW item IN tab" or "HIDE item IN tab".
     * The action string can also omit the "IN tab" part, in which case the current tab is used. The method
     * updates the provided lists of visibleItems and invisibleItems based on the parsed action.
     *
     * The action string must follow the format:
     * 
     *   SHOW item [IN tab]
     *   HIDE item [IN tab]
     * 
     * If the action string does not match this format, a warning is logged and the method returns without making any changes.
     *
     * @param action The visibility action string to be parsed. It should be in the format "SHOW item IN tab" or "HIDE item IN tab".
     *               The "IN tab" part is optional. If the action is null or empty, a warning is logged and the method returns.
     * @param visibleItems The list to be updated with items that should be shown. If the action is a "SHOW" action,
     *                         the item is added to this list.
     * @param invisibleItems The list to be updated with items that should be hidden. If the action is a "HIDE" action,
     *                          the item is added to this list.
     * @param currentTab The current tab to be used if the action string does not specify a tab. This parameter is used
     *                     as the default tab when the "IN tab" part is omitted from the action string.
     *
     * Example usage:
     * 
     * {@code
     * List<String> visibleItems = new ArrayList<>();
     * List<String> invisibleItems = new ArrayList<>();
     * String currentTab = "defaultTab";
     *
     * parseVisibilityAction("SHOW item1 IN tab1", visibleItems, invisibleItems, currentTab);
     * parseVisibilityAction("HIDE item2", visibleItems, invisibleItems, currentTab);
     * }
     * 
     *
     * Logging:
     * 
     *   Logs a warning if the action is null or empty.
     *   Logs the parsed action string at the info level.
     *   Logs a warning if the action string does not match the expected format.
     *   Logs the parsed action type, target item, and target tab at the info level.
     *   Logs a warning if the action type is unknown (not "SHOW" or "HIDE").
     * 
     */
	private void parseVisibilityAction(String action, List<String> visibleItems, List<String> invisibleItems, String currentTab,
			String ruleName) {
		if (action == null || action.trim().isEmpty()) {
			String errorMsg = lex.getText(VISIBILITY_ACTION_NOT_NULL);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(VISIB_ACTION_NULL_SUGGESTION)));
			return;
		}

		action = action.replace(";", "");
		info(MessageFormat.format("Parsing visibility action: {0}", action));
		
//		Pattern pattern = Pattern.compile("^(SHOW|HIDE)\\s+(\\w+(?:\\s+(?!IN)\\w+)*)(?:\\s+IN\\s+(\\w+(?:\\s+(?!IN)\\w+)*))?$");
		Pattern pattern = Pattern.compile("^(SHOW|HIDE)\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!IN)[\\w\\-_$\\\\/]+)*))?$");
		Matcher matcher = pattern.matcher(action);
		if (!matcher.matches()) {
			String errorMsg = MessageFormat.format("Invalid visibility action format: {0}", action);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(VISIBILITY_ACTION_FORMAT_SUGGESTION)));
			return;
		}

		String actionType = matcher.group(1);
		String targetItem = matcher.group(2);
		String targetTab = matcher.group(3) != null ? matcher.group(3) : currentTab;
		if (targetItem != null && !targetItem.contains(" IN ")) {
			targetItem = getTargetNameInGlobalStore(ruleName, targetTab, action, targetItem);
			if(null == targetItem) {
				return;
			}
			targetItem = getValidNiagaraName(targetItem) + " IN " + getValidNiagaraName(targetTab);
		}

		info(MessageFormat.format("Parsed visibility action - Action type: {0}, Target item: {1}, Target tab: {2}", actionType,
				targetItem, targetTab));

		if (actionType.equals("SHOW")) {
			visibleItems.add(targetItem);
		} else if (actionType.equals("HIDE")) {
			invisibleItems.add(targetItem);
		} else {
			String errorMsg = MessageFormat.format("Unknown visibility action: {0}", actionType);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(VISIBILITY_ACTION_TYPE_SUGGESTION)));
		}
	}
    
    /**
     * Creates and adds value-based rules to the given calculation based on the provided values and their respective tabs.
     *
     * This method performs the following steps:
     * 
     *   Validates that the first value (value1) is not null or empty. If it is, logs a warning and returns immediately.
     *   Logs an informational message indicating the values and tabs being used to create the rules.
     *   Creates a ValueBaseRule for value1 based on its content:
     *     
     *       If value1 is a numeric string, creates a PureValue rule with the integer value of value1.
     *       If value1 matches a static value constant, creates a PureValue rule with the string value of value1 (stripped of quotes).
     *       Otherwise, creates a TargetValue rule with value1 and its corresponding tab (value1Tab).
     *     
     *   
     *   Creates a ValueBaseRule for value2 based on its content, if value2 is not null:
     *     
     *       If value2 is a numeric string, creates a PureValue rule with the integer value of value2.
     *       If value2 matches a static value constant, creates a PureValue rule with the string value of value2 (stripped of quotes).
     *       Otherwise, creates a TargetValue rule with value2 and its corresponding tab (value2Tab).
     *     
     *   
     *   Adds the created ValueBaseRule for value1 to the calculation.
     *   If a ValueBaseRule for value2 was created, adds it to the calculation as well.
     * 
     *
     * @param calculation The Calculation object to which the value-based rules will be added.
     * @param value1 The first value used to create a ValueBaseRule. Must not be null or empty.
     * @param value2 The second value used to create a ValueBaseRule. Can be null.
     * @param value1Tab The tab associated with the first value.
     * @param value2Tab The tab associated with the second value.
     */
	private void createValueBaseRule(Calculation calculation, String value1, String value2, String value1Tab, String value2Tab, String action,
			String ruleName) {
		if (value1 == null || value1.trim().isEmpty() || value2 == null || value2.trim().isEmpty()) {
			String errorMsg = "Value cannot be null or empty";
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(ACTION_FORMAT_SUGGESTION)));
			return;
		}

		info(MessageFormat.format("Creating value base rule - Value1: {0}, Value2: {1}, Value1 tab: {2}, Value2 tab: {3}", value1,
				value2, value1Tab, value2Tab));

		ValueBaseRule valueBaseRule1;
		ValueBaseRule valueBaseRule2;

		if (value1.matches("\\d+")) {
			PureValue pureValue1 = new PureValue();
			pureValue1.setTargetValue(Integer.parseInt(value1));
			valueBaseRule1 = pureValue1;
		} else if (value1.matches(STATIC_VALUE_CONST)) {
			PureValue pureValue1 = new PureValue();
			pureValue1.setTargetValue(value1.replace("\"", ""));
			valueBaseRule1 = pureValue1;
		} else {
			TargetValue targetValue1 = new TargetValue();
			targetValue1.setTargetItem(getValidNiagaraName(value1));
			targetValue1.setTargetStore(getValidNiagaraName(value1Tab));
			valueBaseRule1 = targetValue1;
		}

		if (value2.matches("\\d+")) {
			PureValue pureValue2 = new PureValue();
			pureValue2.setTargetValue(Integer.parseInt(value2));
			valueBaseRule2 = pureValue2;
		} else if (value2.matches(STATIC_VALUE_CONST)) {
			PureValue pureValue2 = new PureValue();
			pureValue2.setTargetValue(value2.replace("\"", ""));
			valueBaseRule2 = pureValue2;
		} else {
			TargetValue targetValue2 = new TargetValue();
			targetValue2.setTargetItem(getValidNiagaraName(value2));
			targetValue2.setTargetStore(getValidNiagaraName(value2Tab));
			valueBaseRule2 = targetValue2;
		}

		calculation.addValueRule(valueBaseRule1);
		calculation.addValueRule(valueBaseRule2);
	}
    
    public JSONObject generateJson(IRule rule) {
        if (rule == null) {
            warning("Rule cannot be null");
            return null;
        }
        
        return rule.toJSON();
    }

    public void generateErrorReport(BHonWizardRuleParserJob job) {
        int reportCount = errorReports.size();
        int errorReportPercent = 10;
        int progress = job.getProgress();
        if (reportCount == 0) {
            progress += 10;
            job.progress(progress);
            return;
        }
        job.setHasFailItemsInLog(true);
        int progressIncPerRep = errorReportPercent / reportCount;
        for (ErrorReport errorReport : errorReports) {
            severe(errorReport.toString());
            job.log()
                    .add(new JobLogItem(JobLogItem.FAILED, BAbsTime.make(),
                            lex.getText("honWizard.RuleParse.Error", errorReport.getRuleNameOrQuery(), job.getDevice().getDeviceName()),
                            lex.getText(JOB_LOG_TEXT) + errorReport.toString()));
            progress += progressIncPerRep;
            job.progress(progress);
        }
    }


	/**
	 * build terminal store cache, cache data structure is as below:
	 * Map<tabName, Map<terminalPrefix, [fbList]>>, like <terminalassignment, <SSR, [ReheatStage1, ReheatStage2,ReheatStage3]>
	 * @return terminal store cache
	 */
	private Map<String, Map<String, List<String>>> buildTerminalStoreCache() {
		Map<String, Map<String, List<String>>> terminalStoreCache = new HashMap<>();
		BHonWizardGlobalStore dynamicStoreComponent = device.getGlobalStore();
		if (null == device || null == dynamicStoreComponent) {
			return terminalStoreCache;
		}
		BComponent tabPageStoreComponent = (BComponent) device.getGlobalStore().get(SlotPath.escape(Const.TERMINAL_ASSIGNMENT_PAGE));
		Map<String, List<String>> pageCache = new HashMap<>();
		if(null == tabPageStoreComponent) {
			return terminalStoreCache;
		}
		String pageName = SlotPath.unescape(tabPageStoreComponent.getName());
		terminalStoreCache.put(pageName, pageCache);

		for (Property configItemProperty : tabPageStoreComponent.getDynamicPropertiesArray()) {
			BComponent configItemComponent = (BComponent) tabPageStoreComponent.get(configItemProperty);
			String name = SlotPath.unescape(configItemComponent.getName());
			String terminalName = name.split("~")[0];
			Pattern pattern = Pattern.compile("([^\\d-])+");
			Matcher matcher = pattern.matcher(terminalName);
			String terminalPrefix = terminalName;
			if (matcher.find()) {
				terminalPrefix = matcher.group(0);
			}
			if (!pageCache.containsKey(terminalPrefix)) {
				List<String> fbList = new ArrayList<>();
				for (Property subItemProperty : configItemComponent.getDynamicPropertiesArray()) {
					fbList.add(SlotPath.unescape(subItemProperty.getName()));
				}
				pageCache.put(terminalPrefix, fbList);
			}
		}
		return terminalStoreCache;
	}

	/**
	 * build global store cache to improve the performance of rule validation
	 * returns a map with the following structure:
	 * for range slider widget:
	 * Map<tabName, Map<label, [Pair<slotName, roleName>]>, the list length should be equal to role count
	 * for non-range slider widget:
	 * Map<tabName, Map<label, [Pair<slotName, null>]>, the list length should be 1
	 * @return global store cache
	 */
	private Map<String, Map<String,  List<Pair<String, String>>>> buildWizardStoreCache() {
		Map<String, Map<String, List<Pair<String, String>>>> globalStoreCache = new HashMap<>();
		BHonWizardGlobalStore dynamicStoreComponent = device.getGlobalStore();
		if (null == device || null == dynamicStoreComponent) {
			return globalStoreCache;
		}
		for (Property tabPageStoreProperty : device.getGlobalStore().getDynamicPropertiesArray()) {
			BValue propertyValue = dynamicStoreComponent.get(tabPageStoreProperty);
			if (!(propertyValue instanceof BComponent)) {
				continue;
			}
			if(propertyValue instanceof BSchedulePageWidget){
				continue;
			}
			Map<String, List<Pair<String, String>>> pageCache = new HashMap<>();
			BComponent tabPageStoreComponent = (BComponent) propertyValue;
			String pageName = SlotPath.unescape(tabPageStoreComponent.getName());
			globalStoreCache.put(pageName, pageCache);

			for (Property configItemProperty : tabPageStoreComponent.getDynamicPropertiesArray()) {
				BComponent configItemComponent = (BComponent) tabPageStoreComponent.get(configItemProperty);
				BString labelValue = (BString) configItemComponent.get("label");
				if(null == labelValue){
					continue;
				}
				if (pageCache.containsKey(labelValue.getString())) {
					if (configItemComponent instanceof BRangeSlider) {
						Pair<String, String> roleAndNamePair = new Pair<>(SlotPath.unescape(configItemComponent.getName()),
								((BRangeSlider) configItemComponent).getRole());
						pageCache.get(labelValue.getString()).add(roleAndNamePair);
					} else {
						severe(lex.getText("error.rulevalidation.label.duplicate.error", labelValue.getString(), pageName));
					}
				} else {
					if (configItemComponent instanceof BRangeSlider) {
						List<Pair<String, String>> roleAndNamePairList = new ArrayList<>();
						Pair<String, String> roleAndNamePair = new Pair<>(SlotPath.unescape(configItemComponent.getName()),
								((BRangeSlider) configItemComponent).getRole());
						roleAndNamePairList.add(roleAndNamePair);
						pageCache.put(labelValue.getString(), roleAndNamePairList);
					} else {
						List<Pair<String, String>> roleAndNamePairList = new ArrayList<>();
						Pair<String, String> roleAndNamePair = new Pair<>(SlotPath.unescape(configItemComponent.getName()), null);
						roleAndNamePairList.add(roleAndNamePair);
						pageCache.put(labelValue.getString(), roleAndNamePairList);
					}
				}
			}
		}
		return globalStoreCache;

	}

    protected boolean validateTab(String ruleName, String ruleData, String tabName) {
        if (!globalStoreCache.containsKey(SlotPath.unescape(tabName))) {
            String errorMsg = lex.getText("error.rulevalidation.page.notexist", tabName);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_PAGE_SUGGESTION)));
            return false;
        }
        return true;
    }

	/**
	 * get target item name in global store cache, item name is composed of label and role, role is optional
	 * @param ruleName, rule name
	 * @param tabName, tab name
	 * @param ruleData, rule data
	 * @param itemName, item name in rule query
	 * @return slot name in global store
	 */
	protected String getTargetNameInGlobalStore(String ruleName, String tabName, String ruleData, String itemName) {
		tabName = SlotPath.unescape(tabName);
		if (!globalStoreCache.containsKey(tabName)) {
			String errorMsg = lex.getText("error.rulevalidation.page.notexist", tabName);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_PAGE_SUGGESTION)));
			return null;
		}
		Pair<String, String> labelAndRolePair = parseLabelAndRoleFromItem(itemName);
		if (!globalStoreCache.get(tabName).containsKey(labelAndRolePair.getFirst())) {
			String errorMsg = lex.getText("error.rulevalidation.targetitem.notexist", labelAndRolePair.getFirst(), tabName);
			warning(errorMsg);
			errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_TARGET_ITEM_SUGGESTION)));
			return null;
		}
		if (null != labelAndRolePair.getSecond()) {
			Optional<Pair<String, String>> foundItem = globalStoreCache.get(tabName).get(labelAndRolePair.getFirst()).stream()
					.filter(pair -> pair.getSecond() != null && pair.getSecond().equals(labelAndRolePair.getSecond())).findFirst();
			if (!foundItem.isPresent()) {
				String errorMsg = lex.getText("error.rulevalidation.targetitem.role.notexist", labelAndRolePair.getSecond(), labelAndRolePair.getFirst(), tabName);
				warning(errorMsg);
				errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText("error.rulevalidation.error.target.role.suggestion")));
				return null;
			} else {
				return foundItem.get().getFirst();
			}
		} else {
			Optional<Pair<String, String>> foundItem = globalStoreCache.get(tabName).get(labelAndRolePair.getFirst()).stream().findFirst();
			if (foundItem.isPresent()) {
				return foundItem.get().getFirst();
			}
		}
		return null;
	}

	/**
	 * get valid niagara name
	 * @param name, slot name
	 * @return valid slot name
	 */
	protected String getValidNiagaraName(String name){
		return HoneywellConfigurableDeviceUtil.getValidNiagaraName(name);
	}



	/**
	 * item can be slot or point name
	 * for range slider,item name is compose of label and role name, separated by a hyphen
	 * for other widgets, item name is just label
	 * @param itemName, item namme
	 * @return label and role pair
	 */
	private Pair<String, String> parseLabelAndRoleFromItem(String itemName) {
		int lastSeparatorIndex = itemName.lastIndexOf(SEPARATOR_LABEL_ROLE_CHARACTER);
		if (lastSeparatorIndex == -1) {
			return new Pair<>(itemName.trim(), null);
		}
		String label = itemName.substring(0, lastSeparatorIndex).trim();
		String role = itemName.substring(lastSeparatorIndex + 1).trim();
		return new Pair<>(label, role);
	}

	/**
	 * report rule format error
	 * @param ruleString
	 * @param ruleName
	 * @param errorMsgLexicon
	 */
	protected void reportRuleFormatError(String ruleString, String ruleName, String errorMsgLexicon, String suggestionLexicon) {
		String errorMsg = lex.getText(errorMsgLexicon, ruleString);
		warning(errorMsg);
		errorReports.add(new ErrorReport(ruleName, "Rule Format", errorMsg, lex.getText(suggestionLexicon)));
	}

	public List<ErrorReport> getErrorReports() {
		return errorReports;
	}
	
}