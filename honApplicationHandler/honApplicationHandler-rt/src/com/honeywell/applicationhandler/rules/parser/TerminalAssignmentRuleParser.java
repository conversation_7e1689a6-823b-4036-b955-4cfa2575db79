/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.rules.parser;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.TerminalAssignmentValueRule;
import com.honeywell.applicationhandler.rules.ValueRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.honeywell.applicationhandler.rules.atomrules.value.TerminalAssignmentTargetValue;
import com.tridium.nre.util.tuple.Pair;

import javax.baja.naming.SlotPath;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.honeywell.applicationhandler.ontology.Const.UNASSIGNED;

/**
 *
 * <AUTHOR> zhang
 */
public class TerminalAssignmentRuleParser extends RuleParser {
    private static final String TERMINAL_ASSIGNMENT_ACTION_FORMAT_SUGGESTION = "error.rulevalidation.terminal.assignment.action.format.suggestion";

    private static final String ERROR_TERMINAL_SUGGESTION = "error.rulevalidation.error.terminal.suggestion";
    public TerminalAssignmentRuleParser(BIHoneywellConfigurableDevice device, List<ErrorReport> errorReports,
                                        Map<String, Map<String, List<String>>> terminalStoreCache,
                                        Map<String, Map<String,  List<Pair<String, String>>>> globalStoreCache
    ) {
        super(device, false);
        this.errorReports = errorReports;
        this.terminalStoreCache = terminalStoreCache;
        this.globalStoreCache = globalStoreCache;
    }
    public IRule parseRule(String[] parts, String query, String ruleName) {
        TerminalAssignmentValueRule rule = new TerminalAssignmentValueRule();
        List<IConditionRule> conditions = new ArrayList<>();
        List<ValueRule.ValueResult> thenActions = new ArrayList<>();
        List<ValueRule.ValueResult> elseActions = new ArrayList<>();
        String currentTab = parts[1].trim();
        String conditionPart = parts[2].substring(parts[2].indexOf("IF") + 2).trim();
        String[] conditionTokens = conditionPart.split("THEN");
        conditions.add(parseConditions(conditionTokens[0].trim(), currentTab, ruleName));
        extractThenElseActions(rule, thenActions, elseActions,  currentTab, conditionTokens, ruleName);
        initializeRuleData(rule, conditions, thenActions);
        return rule;
    }


    /**
     * Extracts and processes the actions specified in the condition tokens for both "then" and "else" scenarios.
     *
     * @param thenActions       A list to store the parsed actions that should be executed if the condition is true.
     * @param elseActions       A list to store the parsed actions that should be executed if the condition is false.
     * @param currentTab      The current tab context in which the actions are being parsed.
     * @param conditionTokens   An array of strings representing the condition and its associated actions.
     *
     * The method splits the condition tokens into "then" and "else" parts based on the "ELSE" keyword.
     * It then processes each part separately:
     * - For the "then" part, it splits the actions by commas and adds them to the thenActions list or updates visibility lists.
     * - For the "else" part, if present, it splits the actions by commas and adds them to the elseActions list or updates visibility lists.
     *
     * Actions starting with "SET" are parsed and added to the respective actions list.
     * Other actions are considered visibility actions and are processed to update the visibility lists.
     */
    private void extractThenElseActions(IRule rule, List<ValueRule.ValueResult> thenActions, List<ValueRule.ValueResult> elseActions, String currentTab, String[] conditionTokens, String ruleName) {
        String[] actionParts = conditionTokens[1].split("ELSE");
        String[] thenActionTokens = actionParts[0].split(",");
        for (String actionToken : thenActionTokens) {
            if (actionToken.trim().startsWith("SET")) {
                parseSetActionByRule(rule, thenActions, actionToken.trim(), currentTab, ruleName);
            }
        }

        if (actionParts.length > 1) {
            String[] elseActionTokens = actionParts[1].split(",");
            for (String actionToken : elseActionTokens) {
                if (actionToken.trim().startsWith("SET")) {
                    parseSetActionByRule(rule, elseActions, actionToken.trim(), currentTab, ruleName);
                }
            }
        }
    }

    private void parseSetActionByRule(IRule rule, List<ValueRule.ValueResult> thenActions, String action, String currentTab, String ruleName) {
        if (rule instanceof TerminalAssignmentValueRule) {
            List<ValueRule.ValueResult> valueResults = parseTerminalAssignmentAction(action, currentTab, ruleName);
            if (null != valueResults) {
                thenActions.addAll(valueResults);
            }
        }
    }

    /**
     * Parses a terminal assignment SET action string and returns a ValueRule.ValueResult object representing the parsed action.
     * action format: SET <targetItem> TO <value>
     *
     * @param actionWithOrs,     action with or connection string
     * @param currentTab, current tab
     * @param ruleName,   rule name
     * @return value result
     */
    private List<ValueRule.ValueResult> parseTerminalAssignmentAction(String actionWithOrs, String currentTab, String ruleName) {
        List<ValueRule.ValueResult> valueResultList = new ArrayList<>();
        if (actionWithOrs == null || actionWithOrs.trim().isEmpty()) {
            String errorMsg = lex.getText(ACTION_NOT_NULL);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, actionWithOrs, errorMsg, lex.getText(ACTION_NULL_SUGGESTION)));
            return null;
        }
        String[] actionList = actionWithOrs.split(" OR ");
        List<String> valueList = new ArrayList<>();
        String targetItem = null;
        for (String action : actionList) {
            action = action.replace(";", "").trim();
            info(lex.getText("info.terminal_set_action", action));
            Pattern pattern = Pattern
                    .compile("^(SET)\\s+([\\w\\-_$]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$]+)*)\\s+TO\\s+([\\w\\-_$]+(?:\\s+(?!IN|TO|ADD|SUBTRACT)[\\w\\-_$]+)*)$");
            Matcher matcher = pattern.matcher(action);
            if (!matcher.matches()) {
                String errorMsg = lex.getText("error.invalid.set.action", action);
                warning(errorMsg);
                errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(TERMINAL_ASSIGNMENT_ACTION_FORMAT_SUGGESTION)));
                return null;
            }
            if(targetItem != null && !matcher.group(2).equals(targetItem)){
                String errorMsg = lex.getText("error.terminal.set.action.target.item.not.same", action);
                warning(errorMsg);
                errorReports.add(new ErrorReport(ruleName, action, errorMsg, lex.getText(TERMINAL_ASSIGNMENT_ACTION_FORMAT_SUGGESTION)));
                return null;
            }

            targetItem = matcher.group(2);
            String value = matcher.group(3);
            valueList.add(value);
            if (!validateTerminal(ruleName, targetItem, action, value)) {
                return null;
            }
            info(lex.getText("info.terminal_parse_set_action", targetItem, value));

            ValueRule.ValueResult valueResult = new ValueRule.ValueResult();
            valueResult.setItem(getValidNiagaraName(targetItem));
            valueResult.setItemStore(getValidNiagaraName(currentTab));
            TerminalAssignmentTargetValue targetValue = new TerminalAssignmentTargetValue();
            targetValue.setTargetValue(value);
            targetValue.setTargetStore(currentTab);
            valueResult.setResultRule(targetValue);
            valueResultList.add(valueResult);
        }
        if(!validateAllPossibleTerminals(ruleName, targetItem, actionWithOrs, valueList)){
            return null;
        }
        return valueResultList;
    }

    /**
     * check if function block name and terminal name exists in the terminal assignment page
     * @param fbName, function block name
     * @param ruleData, rule data
     * @param terminalName, terminal name
     * @return true/false
     */
    private boolean validateTerminal(String ruleName, String fbName, String ruleData, String terminalName) {
        fbName = SlotPath.unescape(fbName);
        terminalName = SlotPath.unescape(terminalName);
        if(UNASSIGNED.equalsIgnoreCase(terminalName)){
            return true;
        }
        if (!terminalStoreCache.containsKey(Const.TERMINAL_ASSIGNMENT_PAGE)) {
            String errorMsg = lex.getText("error.rulevalidation.page.notexist", Const.TERMINAL_ASSIGNMENT_PAGE);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_PAGE_SUGGESTION)));
            return false;
        }
        Map<String, List<String>> terminalListMap = terminalStoreCache.get(Const.TERMINAL_ASSIGNMENT_PAGE);
        if (!terminalListMap.containsKey(terminalName)) {
            String errorMsg = lex.getText("error.rulevalidation.terminal.notexist", terminalName);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_TERMINAL_SUGGESTION)));
            return false;
        }
        if (fbName == null || fbName.isEmpty()) {
            String errorMsg = lex.getText("error.rulevalidation.functionblock.notexist", fbName);
            warning(errorMsg);
            errorReports.add(new ErrorReport(lex.getText(RULE_DATA), ruleData, errorMsg, lex.getText(ERROR_TERMINAL_SUGGESTION)));
            return false;
        }
        boolean fbNameFound = false;
        for (List<String> fbList : terminalListMap.values()) {
            if (fbList.contains(fbName)) {
                fbNameFound = true;
                break;
            }
        }
        if (!fbNameFound) {
            String errorMsg = lex.getText("error.rulevalidation.functionblock.notexist", fbName);
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_TERMINAL_SUGGESTION)));
            return false;
        }
        return true;

    }

    /**
     * check if function block can be assigned to given terminals
     * if all given terminals don't support the function block, validation failed.
     * if terminal name is Unassigned, validation succeed
     * @param fbName, function block name
     * @param ruleData, rule data
     * @param terminals, set terminals in rule data
     * @return true/false
     */
    private boolean validateAllPossibleTerminals(String ruleName, String fbName, String ruleData, List<String> terminals){
        Map<String, List<String>> terminalListMap = terminalStoreCache.get(Const.TERMINAL_ASSIGNMENT_PAGE);
        // Check if terminals include the same element and output the duplicate elements
        Set<String> uniqueTerminals = new HashSet<>();
        Set<String> duplicateTerminals = new HashSet<>();
        for (String terminal : terminals) {
            if (!uniqueTerminals.add(terminal)) {
                duplicateTerminals.add(terminal);
            }
        }
        if (!duplicateTerminals.isEmpty()) {
            String errorMsg = lex.getText("error.rulevalidation.terminal.duplicate", duplicateTerminals.toString());
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_TERMINAL_SUGGESTION)));
            return false;
        }

        boolean found = false;
        for (String terminal : terminals) {
            if(terminal.equalsIgnoreCase(UNASSIGNED) || terminalListMap.get(terminal).contains(fbName)){
                found = true;
                break;
            }
        }
        if(!found){
            String errorMsg = lex.getText("error.rulevalidation.functionblock.notbeassigned", fbName, terminals.toString());
            warning(errorMsg);
            errorReports.add(new ErrorReport(ruleName, ruleData, errorMsg, lex.getText(ERROR_TERMINAL_SUGGESTION)));
            return false;
        }
        return true;
    }



    /**
     * Initializes the rule data based on the type of rule provided.
     *
     * @param rule The rule to be initialized. It can be an instance of TerminalAssignmentRule.
     * @param conditions A list of condition rules. The first condition in the list is used to set the condition for the rule.
     * @param thenActions A list of ValueRule.ValueResult objects representing the actions to be taken if the condition is true.
     *
     * If the rule is an instance of ValueRule, the method sets the condition using the first element of the conditions list,
     * and adds the thenActions to the rule.
     *
     */
    private void initializeRuleData(IRule rule, List<IConditionRule> conditions, List<ValueRule.ValueResult> thenActions) {
        if (rule instanceof TerminalAssignmentValueRule) {
            ((ValueRule) rule).setCheckIf(conditions.get(0));
            for (ValueRule.ValueResult action : thenActions) {
                ((ValueRule) rule).addResultThen(action);
            }
        }
    }

}
