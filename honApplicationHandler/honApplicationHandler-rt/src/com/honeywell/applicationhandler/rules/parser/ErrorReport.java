/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.parser;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since Jan 16, 2025
 */
public class ErrorReport {
    private String ruleNameOrQuery;
    private String errorPortion;
    private String errorDetail;
    private String suggestion;

    public ErrorReport(String ruleNameOrQuery, String errorPortion, String errorDetail, String suggestion) {
        this.ruleNameOrQuery = ruleNameOrQuery;
        this.errorPortion = errorPortion;
        this.errorDetail = errorDetail;
        this.suggestion = suggestion;
    }

    public String getRuleNameOrQuery() {
        return ruleNameOrQuery;
    }

    public String getErrorPortion() {
        return errorPortion;
    }

    public String getErrorDetail() {
        return errorDetail;
    }

    public String getSuggestion() {
        return suggestion;
    }

    @Override
    public String toString() {
        return "Error in Rule: " + ruleNameOrQuery + "\n" +
               "Error portion: " + errorPortion + "\n" +
               "Error detail: " + errorDetail + "\n" +
               "Suggestion: " + suggestion;
    }
}