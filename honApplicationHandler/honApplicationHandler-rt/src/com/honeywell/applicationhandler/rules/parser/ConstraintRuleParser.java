/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */

package com.honeywell.applicationhandler.rules.parser;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.rules.ConstraintRule;
import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.honeywell.applicationhandler.rules.atomrules.condition.IsVisible;
import com.honeywell.applicationhandler.rules.atomrules.condition.Rules;
import com.honeywell.applicationhandler.rules.atomrules.constraint.Constraint;
import com.honeywell.applicationhandler.rules.atomrules.constraint.ConstraintEquals;
import com.honeywell.applicationhandler.rules.atomrules.constraint.ConstraintGreaterThan;
import com.honeywell.applicationhandler.rules.atomrules.constraint.ConstraintGreaterThanOrEquals;
import com.honeywell.applicationhandler.rules.atomrules.constraint.ConstraintLessThan;
import com.honeywell.applicationhandler.rules.atomrules.constraint.ConstraintLessThanOrEquals;
import com.honeywell.applicationhandler.rules.enums.RuleOperator;
import com.honeywell.applicationhandler.rules.enums.RuleType;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetUnitSupportComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase;
import com.tridium.nre.util.tuple.Pair;

import javax.baja.sys.BFacets;
import javax.baja.sys.Slot;
import javax.baja.units.BUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

 /**
 *
 * <AUTHOR> zhang
 */
public class ConstraintRuleParser extends RuleParser {

    public ConstraintRuleParser(BIHoneywellConfigurableDevice device, List<ErrorReport> errorReports, Map<String, Map<String,  List<Pair<String, String>>>> globalStoreCache) {
        super(device, false);
        this.errorReports = errorReports;
        this.globalStoreCache = globalStoreCache;
    }


    /**
     *  without deadband, rules should be in the form of "ADD CONSTRAINT RULE TO: <TAB>: <RULE STRING>", length should be 3
     *  with deadband, rules should be in the form of "ADD CONSTRAINT RULE TO: <TAB>: WITH DEADBAND AS: <DEADBAND> : <RULE STRING>", length should be 5
     * @param parts, parts
     * @param query, rule string
     * @param ruleName, rule name
     * @return rule
     */
    public IRule parseRule(String[] parts, String query, String ruleName) {
        ConstraintRule rule = new ConstraintRule();
        String ruleString = "";
        Pair<String, String> constraintDeadBand = null;
        if (parts.length == 3) {
            ruleString = parts[2];
        } else if (parts.length == 5) {
            ruleString = parts[4];
            constraintDeadBand = parseDeadBand(parts, query, ruleName);
            if(null == constraintDeadBand){
                return null;
            }
        } else{
            reportRuleFormatError(query, ruleName, "error.rulevalidation.constraintrule.invalid.format", CONSTRAINT_RULE_FORMAT_SUGGESTION);
            return null;
        }
        String currentTab = parts[1].trim();
        rule.setDefaultPageStore(getValidNiagaraName(currentTab));
        List<Constraint> constraintList = parseConstraints(ruleString, currentTab, ruleName, constraintDeadBand);
        if(null != constraintList) {
            rule.addConstraints(constraintList);
        }

        return rule;
    }

    /**
     * parse deadband
     * dead band should be in the form of "WITH DEADBAND AS: <DEADBAND> IN <TAB>" or float value
     * @param parts
     * @param query
     * @param ruleName
     * @return
     */
    private Pair<String, String> parseDeadBand(String[] parts, String query, String ruleName) {
        String deadBandPrefix = parts[2].trim();
        String deadBandValue = parts[3].trim();
        String currentTab = parts[1].trim();
        String deadBandTargetStore = "";
        String deadBandTargetItem = "";
        if (!deadBandPrefix.equalsIgnoreCase(WITH_BAND_AS)) {
            reportRuleFormatError(query, ruleName, "error.rulevalidation.constraintrule.deadband.prefix.error", CONSTRAINT_RULE_FORMAT_SUGGESTION);
            return null;
        }
        if (deadBandValue.contains(" IN ")) {
            String[] targetDetails = deadBandValue.split(" IN ");
            deadBandTargetItem = targetDetails[0].trim();
            deadBandTargetStore = targetDetails[1].trim();
        } else {
            if (!deadBandValue.matches(REGEX_NUMERIC)) {
                //dead band is not numeric, then it will be regarded as target item with current tab
                deadBandTargetItem = deadBandValue;
                deadBandTargetStore = currentTab;
            }
        }
        if (!deadBandTargetStore.isEmpty() && !deadBandTargetItem.isEmpty()) {
            deadBandTargetItem = getTargetNameInGlobalStore(ruleName, deadBandTargetStore, query, deadBandTargetItem);
            if (null == deadBandTargetItem) {
                return null;
            }
            return new Pair<>(deadBandTargetItem, deadBandTargetStore);
        } else {
            return new Pair<>(deadBandValue, null);
        }
    }




    /**
     * parse  rules
     * @param ruleString, like "IF <CONDITION> THEN <LIMIT> <OBJECT> IN <TAB> WITH DEAD BAND LESS_THAN <OBJECT> IN <TAB>"
     * @param currentTab current tab
     * @param ruleName, rule name
     * @param deadBand, dead band pair
     * @return constraint list
     */
    private List<Constraint> parseConstraints(String ruleString, String currentTab, String ruleName, Pair<String, String> deadBand) {
        List<Constraint> constraintList = new ArrayList<>();
        int startIf = ruleString.indexOf("IF");
        int endThen = ruleString.indexOf("THEN");
        if(startIf == -1){
            //no condition statement in rule
            List<Constraint> constraints = parseLimits(ruleString, null, currentTab, ruleName, deadBand);
            if(null == constraints) {
                return null;
            }
            constraintList.addAll(constraints);

        }else{
            //parse conditions and limits
            while (startIf != -1) {
                if(endThen == -1){
                    reportRuleFormatError(ruleString, ruleName, "error.rulevalidation.constraintrule.missing.then", CONSTRAINT_RULE_FORMAT_SUGGESTION);
                    return null;
                }
                String conditionPart = ruleString.substring(startIf + 2, endThen).trim();
                IConditionRule conditionRule = parseConditions(conditionPart, currentTab, ruleName);
                if(null == conditionRule){
                    return null;
                }
                int nextIf = ruleString.indexOf(endThen + 4);
                if(nextIf != -1) {
                    int nextThen = ruleString.indexOf("THEN", nextIf);
                    if(nextThen == -1){
                        reportRuleFormatError(ruleString, ruleName,"error.rulevalidation.constraintrule.missing.then", CONSTRAINT_RULE_FORMAT_SUGGESTION);
                        return null;
                    }
                    String limitString = ruleString.substring(endThen + 4, nextIf).trim();
                    List<Constraint> constraints = parseLimits(limitString, conditionRule, currentTab, ruleName, deadBand);
                    if(null == constraints) {
                        return null;
                    }
                    constraintList.addAll(constraints);
                    startIf = ruleString.indexOf("IF", nextThen);
                    endThen = ruleString.indexOf("THEN", nextThen);
                }else{
                    String limitString = ruleString.substring(endThen + 4).trim();
                    List<Constraint> constraints = parseLimits(limitString, conditionRule, currentTab, ruleName, deadBand);
                    if(null == constraints){
                        return null;
                    }
                    constraintList.addAll(constraints);
                    break;
                }
            }
        }
        return  constraintList;
    }

    /**
     * parse limits
     * @param limits, like "LIMIT <OBJECT> IN <TAB> WITH DEAD BAND LESS_THAN <OBJECT> IN <TAB>"
     * @param conditionRule, condition rule
     * @param currentTab, current tab
     * @param ruleName, rule name
     * @param deadBand, dead band
     * @return constraint list
     */
    private List<Constraint> parseLimits(String limits, IConditionRule conditionRule, String currentTab, String ruleName, Pair<String, String> deadBand) {
        String[] limitList = limits.split(",");
        List<List<RuleElement>> ruleElementsList = new ArrayList<>();
        for (int i = 0; i < limitList.length; i++) {
            String limit = limitList[i].trim();
            List<RuleElement> ruleElements = new ArrayList<>();
            Pattern pattern = Pattern.compile(REGEX_FOR_LIMIT_RULE);
            Matcher matcher = pattern.matcher(limit);
            if (matcher.find()) {
                String element = matcher.group(1);
                String tab = matcher.group(2) == null ? currentTab: matcher.group(2);
                element = getTargetNameInGlobalStore(ruleName, tab, limit, element);
                if(null == element){
                    return null;
                }
                boolean hasDeadband = matcher.group(3) != null;
                String otherLimits = matcher.group(4);

                // Process subsequent matches iteratively
                Pattern subPattern = Pattern.compile(REGEX_FOR_LIMIT_LOOP_RULE);
                Matcher subMatcher = subPattern.matcher(otherLimits);
                String operator = null;
                while (subMatcher.find()) {
                    operator = subMatcher.group(1);
                    ruleElements.add(new RuleElement(element, tab, operator, hasDeadband));
                    element = subMatcher.group(2);
                    tab = subMatcher.group(3) == null ? currentTab : subMatcher.group(3);
                    element = getTargetNameInGlobalStore(ruleName, tab, limit, element);
                    if(null == element){
                        return null;
                    }
                    hasDeadband = subMatcher.group(4) != null;
                }
                ruleElements.add(new RuleElement(element, tab, operator, hasDeadband));
            }
            ruleElementsList.add(ruleElements);
            if(!isValidOperators(ruleElements, ruleName, limit)){
                return null;
            }
            if(!isValidateConstraintRuleUnits(ruleElements, ruleName, limit, deadBand)) {
                return null;
            }

        }
        return buildConstraints(ruleElementsList, deadBand, conditionRule, currentTab, limits, ruleName);

    }

     /**
      * build constraints from rule elements
      * @param ruleElementsList, rule element list
      * @param deadBand, dead band
      * @param conditionRule, condition rule
      * @param currentTab, current tab
      * @param limits, limits
      * @param ruleName, rule name
      * @return
      */
    private List<Constraint> buildConstraints(List<List<RuleElement>> ruleElementsList, Pair<String,String> deadBand, IConditionRule conditionRule, String currentTab, String limits, String ruleName) {
        List<Constraint> constraintList = new ArrayList<>();
        for (List<RuleElement> ruleElements : ruleElementsList) {
            Constraint constraint = buildConstraint(ruleElements);
            setDeadBand(constraint, deadBand);
            Rules rules = new Rules();
            rules.setRuleOperator(RuleOperator.AND);
            if(null != conditionRule) {
                rules.addRule(conditionRule);
            }
            for (RuleElement ruleElement : ruleElements) {
                String element = ruleElement.element;
                String tab = ruleElement.tab;
                if (tab == null || tab.isEmpty()) {
                    tab = currentTab;
                } else {
                    tab = currentTab;
                }
                constraint.addWithDeadband(ruleElement.hasDeadband);
                constraint.addItem(getValidNiagaraName(tab), getValidNiagaraName(element));
                IsVisible visibleRule = new IsVisible();
                visibleRule.setTargetItem(getValidNiagaraName(ruleElement.element));
                visibleRule.setTargetStore(getValidNiagaraName(ruleElement.tab));
                rules.addRule(visibleRule);
            }
            constraint.setEnableRule(rules);
            constraintList.add(constraint);
        }
        return constraintList;
    }

    /**
     * set deadband if dead band is required
     * @param constraint, constraint
     * @param deadBand, dead band
     */
    private void setDeadBand(Constraint constraint, Pair<String, String> deadBand){
        if (null != deadBand.getFirst() && null != deadBand.getSecond()) {
            constraint.setDeadband(getValidNiagaraName(deadBand.getSecond()), getValidNiagaraName(deadBand.getFirst()));
        } else if (null != deadBand.getFirst()) {
            constraint.setDeadband(Float.parseFloat(deadBand.getFirst()));
        }
    }

    /**
     * build constraint according to operator
     * @param ruleElements, rule elements
     * @return constraint
     */
    private Constraint buildConstraint(List<RuleElement> ruleElements) {
        RuleElement ruleElement = ruleElements.get(0);
        if(ruleElement.operator.equals(RuleType.LESS_THAN.getKey())) {
            return new ConstraintLessThan();
        }else if(ruleElement.operator.equals(RuleType.LESS_THAN_OR_EQUALS.getKey())) {
            return new ConstraintLessThanOrEquals();
        }else if(ruleElement.operator.equals(RuleType.GREATER_THAN.getKey())) {
            return new ConstraintGreaterThan();
        }else if(ruleElement.operator.equals(RuleType.GREATER_THAN_OR_EQUALS.getKey())) {
            return new ConstraintGreaterThanOrEquals();
        } else {
            return new ConstraintEquals();
        }


    }

    /**
     * validate operators, operators should be in the same category
     * LESS_THAN, LESS_EQUAL_THAN is in  LESS category, GREAT_THAN, GREAT_EQUAL_THAN is in  GREAT category
     * @param ruleElements, rule elements
     * @param ruleName, rule name
     * @param ruleString, rule string
     * @return true if valid, false if invalid
     */
    private boolean isValidOperators(List<RuleElement> ruleElements, String ruleName, String ruleString) {
        boolean isValid = true;
        String operatorCategory = null;

        for (RuleElement ruleElement : ruleElements) {
            String operator = ruleElement.operator;
            if (operator != null) {
                if (operatorCategory == null) {
                    // Determine the category of the first non-null operator
                    if (operator.equals(RuleType.LESS_THAN.getKey()) || operator.equals(RuleType.LESS_THAN_OR_EQUALS.getKey())) {
                        operatorCategory = "LESS";
                    } else if (operator.equals(RuleType.GREATER_THAN.getKey()) || operator.equals(RuleType.GREATER_THAN_OR_EQUALS.getKey())) {
                        operatorCategory = "GREAT";
                    } else if (operator.equals(RuleType.EQUALS.getKey())) {
                        operatorCategory = "EQUALS";
                    }
                } else {
                    // Validate that the operator belongs to the same category
                    if ((operatorCategory.equals("LESS") && !(operator.equals(RuleType.LESS_THAN.getKey()) || operator.equals(RuleType.LESS_THAN_OR_EQUALS.getKey()))) ||
                            (operatorCategory.equals("GREAT") && !(operator.equals(RuleType.GREATER_THAN.getKey()) || operator.equals(RuleType.GREATER_THAN_OR_EQUALS.getKey()))) ||
                            (operatorCategory.equals("EQUALS") && !operator.equals(RuleType.EQUALS.getKey()))) {
                        isValid = false;
                        break;
                    }
                }
            }
        }
        if(!isValid){
            reportRuleFormatError(ruleString, ruleName, "error.rulevalidation.constraintrule.operator.error", "error.rulevalidation.constraintrule.operator.suggestion");
        }
        return isValid;
    }



     /**
      * If excludeUnitConversion tag is set only for some points in a given constraint rule, then it can be considered as failure.
      * if deadband exists in constraint rule, but point does not unit, then it can be considered as failure.
      * The successful validation scenarios are as follows.
      * deadband is fixed value, we won't consider validation for this case.
      * deadband has not unit, we will ignore deadband unit validation, only validate point units
      * All the points to be considered in the LIMIT rule have exclude conversion unit tag set. Deadband shall have unit conversion tag set.
      * All the points to be considered in the LIMIT rule have exclude conversion unit tag not set. deadband shall also have the excludeConversionTag not set.
      * @param ruleElements, all rule points
      * @param ruleName , rule name
      * @param ruleString, rule string
      * @param deadband, deadband
      * @return true or false
      */
    private boolean isValidateConstraintRuleUnits(List<RuleElement> ruleElements, String ruleName, String ruleString, Pair<String, String> deadband){
        BDynamicWidgetUnitSupportComponentBase deadBandComp = null;
        List<BDynamicWidgetUnitSupportComponentBase> limitPoints = new ArrayList<>();
        if(deadband.getSecond() == null){
            //deadband is fixed value
            return true;
        }
        deadBandComp = getComponentWithUnit(deadband.getFirst(), deadband.getSecond());
        for (RuleElement ruleElement : ruleElements) {
            BDynamicWidgetUnitSupportComponentBase componentWithUnit = getComponentWithUnit(ruleElement.element, ruleElement.tab);
            if(null == componentWithUnit){
                reportRuleFormatError(ruleString + ", " + ruleElement.element + " in " + ruleElement.tab, ruleName,
                        "error.rulevalidation.constraintrule.point.unit.error", "error.rulevalidation.constraintrule.point.unit.suggestion");
                return false;
            }
            limitPoints.add(componentWithUnit);
        }
        return checkDeadbandUnitWithPointUnit(deadBandComp, limitPoints, ruleName, ruleString);

    }

     /**
      * when dead band exist in constraint rule and points all includes unit, exception cases includes:
      * - excludeUnitConversion tag is set only for some points in a given constraint rule, then it can be considered as failure.
      * - excludeUnitConversion tag of point is not same with deadband excludeUnitConversion tag, then it can be considered as failure.
      * - units of point are not same, then it can be considered as failure.
      * - can not get unit from deadband or point, then it can be considered as failure.
      * - deadband unit or differential unit is not same with point unit, then it can be considered as failure.
      * @param deadBandComp, dead band widget
      * @param limitPoints, all points in constraint rule
      * @param ruleName, rule name
      * @param ruleString, rule string
      * @return validation succeed/failed
      */
    private boolean checkDeadbandUnitWithPointUnit(BDynamicWidgetUnitSupportComponentBase deadBandComp, List<BDynamicWidgetUnitSupportComponentBase> limitPoints,
                                                   String ruleName, String ruleString) {
        String unit = null;
        List<String> units = new ArrayList<>();
        for (BDynamicWidgetUnitSupportComponentBase limitPoint : limitPoints) {
            if(null == unit){
                unit = limitPoint.getUnit();
            }
            if(null != unit && !unit.equals(limitPoint.getUnit())){
                units.add(limitPoint.getUnit());
            }
        }
        if(units.size() > 1) {
            reportRuleFormatError(ruleString + ", units are " + units, ruleName,
                    "error.rulevalidation.constraintrule.unit.error", "error.rulevalidation.constraintrule.unit.suggestion");
            return false;
        }
        if(null != deadBandComp && null != deadBandComp.getUnit()){
            Slot unitSlot = deadBandComp.getSlot("unit");
            BFacets deadbandUnitFacets = deadBandComp.getSlotFacets(unitSlot);
            BUnit deadBandUnit = null;
            if(null != deadbandUnitFacets.getFacet(BFacets.UNITS)){
                deadBandUnit = (BUnit) deadbandUnitFacets.getFacet(BFacets.UNITS);
            }
            BUnit pointUnit = null;
            Slot pointUnitSlot = limitPoints.get(0).getSlot("unit");
            BFacets pointUnitFacets = limitPoints.get(0).getSlotFacets(pointUnitSlot);
            if(null != pointUnitFacets.getFacet(BFacets.UNITS)){
                pointUnit = (BUnit) pointUnitFacets.getFacet(BFacets.UNITS);
            }
            if(null == deadBandUnit || null == pointUnit){
                reportRuleFormatError(ruleString, ruleName,
                        "error.rulevalidation.constraintrule.point.unit.error", "error.rulevalidation.constraintrule.point.unit.suggestion");
                return false;
            }
            if (!pointUnit.getDifferentialUnit().getSymbol().equals(deadBandUnit.getSymbol()) && !pointUnit.getSymbol().equals(deadBandUnit.getSymbol())){
                reportRuleFormatError(ruleString + ", deadband unit is " + deadBandComp.getUnit() + ", point unit is " + unit, ruleName,
                        "error.rulevalidation.constraintrule.unit.deadband.error", "error.rulevalidation.constraintrule.unit.deadband.suggestion");
                return false;
            }
        }
        return true;
    }

     /**
      * get component according to component name and tab
      * @param compName, component name
      * @param pageStore, tab
      * @return widget component
      */
    private BDynamicWidgetUnitSupportComponentBase getComponentWithUnit(String compName, String pageStore){
        BWidgetComponentBase widgetComponent = device.getGlobalStore().getWidgetComponent(pageStore, compName);
        if(widgetComponent instanceof BDynamicWidgetUnitSupportComponentBase){
            return (BDynamicWidgetUnitSupportComponentBase) widgetComponent;
        }
        return null;
    }


     /**
      * the class is used to store point information in constraint rule
      * in constraint rule, rule will be parsed into the class
      */
    class RuleElement {
         /**
          * point name
          */
        private final String element;
         /**
          * point belong to page tab
          */
        private final String tab;
         /**
          * operator, like LESS_THAN, GREATER_THAN, EQUALS
          */
        private final String operator;
         /**
          * if WITH_DEADBAND is after the point
          */
        private final boolean hasDeadband;

        public RuleElement(String element, String tab, String operator, boolean hasDeadband) {
            this.element = element;
            this.tab = tab;
            this.operator = operator;
            this.hasDeadband = hasDeadband;
        }
    }

    private static final String REGEX_FOR_LIMIT_RULE = "LIMIT\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!WITH_DEADBAND|IN|LESS_THAN|GREATER_THAN|LESS_THAN_OR_EQUALS|GREATER_THAN_OR_EQUALS|EQUALS)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!WITH_DEADBAND|IN|LESS_THAN|GREATER_THAN|LESS_THAN_OR_EQUALS|GREATER_THAN_OR_EQUALS|EQUALS)[\\w\\-_$\\\\/]+)*))?(?:\\s+(WITH_DEADBAND))?\\s+(.*)";
    private static final String REGEX_FOR_LIMIT_LOOP_RULE = "(LESS_THAN|GREATER_THAN|LESS_THAN_OR_EQUALS|GREATER_THAN_OR_EQUALS|EQUALS)\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!WITH_DEADBAND|IN|LESS_THAN|GREATER_THAN|LESS_THAN_OR_EQUALS|GREATER_THAN_OR_EQUALS|EQUALS)[\\w\\-_$\\\\/]+)*)(?:\\s+IN\\s+([\\w\\-_$\\\\/]+(?:\\s+(?!WITH_DEADBAND|IN|LESS_THAN|GREATER_THAN|LESS_THAN_OR_EQUALS|GREATER_THAN_OR_EQUALS|EQUALS)[\\w\\-_$\\\\/]+)*))?(?:\\s+(WITH_DEADBAND))?";

    private static final String REGEX_NUMERIC = "^(-?[1-9]\\d*(\\.\\d+)?|0(\\.\\d+)?)$";
    private static final String WITH_BAND_AS = "WITH DEADBAND AS";
}
