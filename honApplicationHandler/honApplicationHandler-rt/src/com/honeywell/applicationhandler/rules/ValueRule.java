/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules;

import java.util.ArrayList;
import java.util.List;

import com.honeywell.applicationhandler.rules.atomrules.condition.IConditionRule;
import com.honeywell.applicationhandler.rules.atomrules.value.ValueBaseRule;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

public class ValueRule implements IRule{

    IConditionRule checkIf;
    List<ValueResult> resultThen = new ArrayList<>();
    List<ValueResult> resultElse = new ArrayList<>();


    @Override
    public JSONObject toJSON() {
    	JSONObject finalJson = new JSONObject();
        JSONObject json = new JSONObject();
        
        try {
			json.put("if", this.getCheckIf().toJSON());

			JSONArray resultThenArray = new JSONArray();
			for(ValueResult valueResult : this.getResultThen()) {
			    resultThenArray.put(valueResult.toJSON());
			}

			JSONArray resultElseArray = new JSONArray();
			for(ValueResult valueResult : this.getResultElse()) {
			    resultElseArray.put(valueResult.toJSON());
			}

			json.put("then", resultThenArray);
			json.put("else", resultElseArray);
			
			finalJson.put(getRuleName(), json);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return finalJson;
    }
    protected String  getRuleName(){
        return "valueRules";
    }

    public IConditionRule getCheckIf() {
        return checkIf;
    }

    public void setCheckIf(IConditionRule checkIf) {
        this.checkIf = checkIf;
    }

    public List<ValueResult> getResultThen() {
        return resultThen;
    }

    public void addResultThen(ValueResult resultThen) {
        this.resultThen.add(resultThen);
    }

    public List<ValueResult> getResultElse() {
        return resultElse;
    }

    public void addResultElse(ValueResult resultElse) {
        this.resultElse.add(resultElse);
    }


    public static class ValueResult implements IRule {
        String item;
        String itemStore;
        ValueBaseRule resultRule;

        @Override
        public JSONObject toJSON() {
            JSONObject json = new JSONObject();
            json.put("item", this.getItem());
            json.put("itemStore", this.getItemStore());
            json.put("resultRule", this.getResultRule().toJSON());
            return json;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getItemStore() {
			return itemStore;
		}

		public void setItemStore(String itemStore) {
			this.itemStore = itemStore;
		}

		public ValueBaseRule getResultRule() {
            return resultRule;
        }

        public void setResultRule(ValueBaseRule resultRule) {
            this.resultRule = resultRule;
        }
    }
}
