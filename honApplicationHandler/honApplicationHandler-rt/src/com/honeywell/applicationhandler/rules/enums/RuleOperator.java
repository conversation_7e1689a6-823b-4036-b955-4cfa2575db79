/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.enums;

public enum RuleOperator {
    AND("AND"),
    OR("OR"),
    NOT("NOT");

    private String key;
    RuleOperator(String key) {
        this.key = key;
    }

    public static RuleOperator fromKey(String key) {
        for (RuleOperator ruleOperator : RuleOperator.values()) {
            if (ruleOperator.key.equals(key)) {
                return ruleOperator;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
}
