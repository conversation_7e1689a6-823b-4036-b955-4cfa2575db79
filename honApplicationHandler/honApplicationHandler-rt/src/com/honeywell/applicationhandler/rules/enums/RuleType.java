/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.enums;

public enum RuleType {

    EQUALS("EQUALS"),
    NOT_EQUALS("NOT_EQUALS"),
    GREATER_THAN("GREATER_THAN"),
    LESS_THAN("LESS_THAN"),
    GREATER_THAN_OR_EQUALS("GREATER_THAN_OR_EQUALS"),
    LESS_THAN_OR_EQUALS("LESS_THAN_OR_EQUALS"),
    IS_VISIBLE("IS_VISIBLE"),
    IS_HIDDEN("IS_HIDDEN"),
    RULES("RULES"),
    VALUE("VALUE"),
    VISIBILITY("VISIBILITY"),
    TARGET_VALUE("TARGETVALUE"),
    CALCULATION("CALCULATION"),
    TERMINAL_ASSIGNMENT_VALUE("TERMINAL_ASSIGNMENT_VALUE"),
    TERMINAL_ASSIGNMENT_TARGET_VALUE("TERMINAL_ASSIGNMENT_TARGET_VALUE"),
    CONSTRAINT("CONSTRAINT"),

    MULTIPLE("MULTIPLE");

    private String key;
    RuleType(String key) {
        this.key = key;
    }

    public static RuleType fromKey(String key) {
        for (RuleType ruleType : RuleType.values()) {
            if (ruleType.key.equals(key)) {
                return ruleType;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
}
