/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.rules.enums;

public enum CalculationOperator {
    ADD("ADD"),
    SUBTRACT("SUBTRACT");

    private String key;
    CalculationOperator(String key) {
        this.key = key;
    }

    public static CalculationOperator fromKey(String key) {
        for (CalculationOperator calculationOperator : CalculationOperator.values()) {
            if (calculationOperator.key.equals(key)) {
                return calculationOperator;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
}
