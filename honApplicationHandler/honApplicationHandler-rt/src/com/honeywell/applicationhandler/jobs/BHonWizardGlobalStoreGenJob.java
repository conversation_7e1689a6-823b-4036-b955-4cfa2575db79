/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.jobs;

import java.util.Arrays;
import java.util.List;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.generate.HonWizGlobalStoreGenerator;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

/**
 * <AUTHOR> Sun
 */
@NiagaraType
@SuppressWarnings({
        "squid:MaximumInheritanceDepth",
        "squid:S2160",
        "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardGlobalStoreGenJob extends BHonWizardJob {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardGlobalStoreGenJob(2979906276)1.0$ @*/
/* Generated Mon Mar 10 17:26:57 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardGlobalStoreGenJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final Lexicon lex = Lexicon.make(BHonWizardGlobalStoreGenJob.class);
    private static final int MAX_RETRY_ON_CANCEL = 10;
    private static final String GENERIC_FAILURE= "BHonWizardGlobalStoreGenJob.fail";

    public BHonWizardGlobalStoreGenJob() {
        super();
    }

    public BHonWizardGlobalStoreGenJob(BIHoneywellConfigurableDevice device) {
        super(device);
        setNameOfJob(lex.getText("honWizardJob.globalStoreGenJob"));
    }

    @Override
	public void doRun(Context cx) {
		super.doRun(cx);
		progress(5);
		getDevice().stopDynamicUpdatesOfGlobalStore("");
		try {
			if (!BHonWizardGlobalStoreGenJob.validateAllSelectors(this, 50)) {
				return;
			}

			progress(50);

			BHonWizardGlobalStoreGenJob.generateWizardConfigurations(this);
			progress(99);
		} catch (Exception e) {
			updateEndFailureLog(lex.getText(GENERIC_FAILURE, getDevice().getDeviceName()), Arrays.toString(e.getStackTrace()));
		} finally {
			getDevice().resumeDynamicUpdatesOfGlobalStore(Const.FORCE_RESUME_DYNAMIC_UPDATE);
		}
	}


	public static void generateWizardConfigurations(BHonWizardJob wizardGenerationJob) {
		HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(wizardGenerationJob.getDevice());
		BasicContext context = new BasicContext();
		boolean hasFailed = false;
		try {
			// reserve context to extend it in the future
			generator.generate(context, true);
		} catch (Exception e) {
			hasFailed = true;
			wizardGenerationJob.updateEndFailureLog(lex.getText(GENERIC_FAILURE, wizardGenerationJob.getDevice().getDeviceName()),
					Arrays.toString(e.getStackTrace()));
		}

		if (wizardGenerationJob.isHasFailItemsInLog() || hasFailed) {
			wizardGenerationJob.getDevice().setIsWizardInSyncWithConfiguration(false);
			wizardGenerationJob.updateEndFailureLog(lex.getText(GENERIC_FAILURE, wizardGenerationJob.getDevice().getDeviceName()),
					"");
		} else {
			wizardGenerationJob.getDevice().setIsWizardInSyncWithConfiguration(true);
			wizardGenerationJob.updateSuccessLog(lex.getText("BHonWizardGlobalStoreGenJob.success", wizardGenerationJob.getDevice().getDeviceName()),
					"");
		}
	}

	public static boolean validateAllSelectors(BHonWizardJob wizardGenerationJob, int finaProgress) {
		wizardGenerationJob.updateMessageLog(lex.getText("BHonWizardGlobalStoreGenJob.validation.start"));
		List<BHonWizSelector> selectors = HoneywellConfigurableDeviceUtil
				.getAllHonWizardSelectors((BComponent) wizardGenerationJob.getDevice());

		wizardGenerationJob.getDevice().stopDynamicUpdatesOfGlobalStore("validateSelectors");
		TagValidation.validateAll(selectors, wizardGenerationJob, finaProgress);

		if (wizardGenerationJob.isHasFailItemsInLog()) {
			wizardGenerationJob.getDevice().setIsWizardInSyncWithConfiguration(false);
			wizardGenerationJob.updateEndFailureLog(
					lex.getText(GENERIC_FAILURE, wizardGenerationJob.getDevice().getDeviceName()), "");
			wizardGenerationJob.getDevice().resumeDynamicUpdatesOfGlobalStore("validateSelectors");
			return false;
		}
		return true;
	}

    @Override
    public void doCancel(Context cx) {
        super.doCancel(cx);
        exitOperationThread(MAX_RETRY_ON_CANCEL);
    }
}
