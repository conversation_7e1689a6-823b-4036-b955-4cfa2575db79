/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.jobs;

import static com.honeywell.applicationhandler.common.Constants.FB_NAME;
import static com.honeywell.applicationhandler.common.Constants.PIN_NAME;
import static com.honeywell.applicationhandler.common.Constants.TERMINAL_ASSIGNMENT;
import static com.honeywell.applicationhandler.ontology.Const.DEAD_BAND;
import static com.honeywell.applicationhandler.ontology.Const.DEFAULT_VALUE;
import static com.honeywell.applicationhandler.ontology.Const.HIGH_PRECISION_DEAD_BAND;
import static com.honeywell.applicationhandler.ontology.Const.HIGH_PRECISION_DEFAULT_VALUE;
import static com.honeywell.applicationhandler.ontology.Const.HIGH_PRECISION_MAX;
import static com.honeywell.applicationhandler.ontology.Const.HIGH_PRECISION_MIN;
import static com.honeywell.applicationhandler.ontology.Const.HIGH_PRECISION_VALUE;
import static com.honeywell.applicationhandler.ontology.Const.MAX;
import static com.honeywell.applicationhandler.ontology.Const.MIN;
import static com.honeywell.applicationhandler.ontology.Const.PRECISION;
import static com.honeywell.applicationhandler.ontology.Const.STEP;
import static com.honeywell.applicationhandler.ontology.Const.UNIT;
import static com.honeywell.applicationhandler.ontology.Const.UNIT_NAME;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.filegenerator.DateRangeHolidayDetails;
import com.honeywell.applicationhandler.filegenerator.DayScheduleEvent;
import com.honeywell.applicationhandler.filegenerator.HolidayDetails;
import com.honeywell.applicationhandler.filegenerator.RecurringHolidayDetails;
import com.honeywell.applicationhandler.filegenerator.ScheduleEventStartEndStatus;
import com.honeywell.applicationhandler.filegenerator.SpecificDateHolidayDetails;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetUnitSupportComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BRangeSlider;
import com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONException;
import com.tridium.json.JSONObject;
/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jan 27, 2025
 */
@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S2160",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardValueSaveJob extends BHonWizardJob {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob(2979906276)1.0$ @*/
/* Generated Mon Jan 27 13:17:30 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardValueSaveJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  private static final Lexicon lex = Lexicon.make(BHonWizardValueSaveJob.class);
  private static final int TOTAL_AVAILABLE_PROG= 90;
  private int globalStoreSaveProgress;
  private int valueUpdateToWiresheetProgress;
  private int valueUpdateToControllerProgress;
  private static final int MAX_RETRY_ON_CANCEL = 10;
  private JSONObject valueObject;
  private JSONArray pageOrderArray;
  private BHonWizardGlobalStore globalStore;
  private int wizardOptionSave;
  private int savedPropertyCount = 0;
  private static final String RECURRING_DAYS = "recurringDaysEveryYear";
  private static final String SPECIFIC_DATE = "specificDateEveryYear";
  private static final String DATE_RANGE = "dateRangeEveryYear";
  
  
	public BHonWizardValueSaveJob(BIHoneywellConfigurableDevice device, JSONObject valueObject,
			JSONArray pageOrderArray, int wizardOptionSave, BHonWizardGlobalStore globalStore) {
		super(device);
		this.valueObject = valueObject;
		this.pageOrderArray = pageOrderArray;
		this.wizardOptionSave=wizardOptionSave;
		this.globalStore = globalStore;
		setNameOfJob(lex.getText("honWizardJob.saveJob"));
	}
	
	public BHonWizardValueSaveJob() {
		super();
	}

	@Override
	public void doRun(Context cx) {
		super.doRun(cx);
		int progress = 5;
		progress(progress);

		updatePageOrderInGlobalStore(pageOrderArray);
		
		if (valueObject != null && valueObject.length() > 0) {

			List<BOrd> changedComponentOrdList = new ArrayList<>();
			JSONArray storeArray = valueObject.names();

			int propertyCount = storeArray.length();
			if (propertyCount == 0) {
				progress(100);
				return;
			}

			updateProgressDetails();
			int progressIncPerProp = globalStoreSaveProgress / propertyCount;

			progress = updateGlobalStoreWithNewValues(progress, changedComponentOrdList, storeArray, progressIncPerProp);

			int expectedProgAftRuleParse = globalStoreSaveProgress + 5;
			if (progress < expectedProgAftRuleParse) {
				progress = expectedProgAftRuleParse;
			}
			progress(progress);

			HoneywellDeviceWizardLogger.info(MessageFormat.format("Below point values are saved: {0}", valueObject));

			if (!changedComponentOrdList.isEmpty()) {
				getDevice().syncValueChangeFromHonConfigurableDevice(changedComponentOrdList, this, wizardOptionSave);
			}

			HoneywellDeviceWizardLogger.info("All points are synced to device module.");
		}
        progress(99);
        
		if(isHasFailItemsInLog()) {
			updateEndFailureLog(lex.getText("BHonWizardValueSaveJob.fail", getDevice().getDeviceName()), "");
		} else {
			updateSuccessLog(lex.getText("BHonWizardValueSaveJob.success", getDevice().getDeviceName()), "");
		}
	}

	

	private int updateGlobalStoreWithNewValues(int progress,
			List<BOrd> changedComponentOrdList, JSONArray storeArray, int progressIncPerProp) {
		
		for (int i = 0; i < storeArray.length(); i++) {
			String storeName = storeArray.getString(i);

			if (storeName.equals("Scheduling")) {
				// Handle separately for Schedules
				JSONArray scheduleObjects = valueObject.getJSONArray("Scheduling");
				for (int j = 0; j < scheduleObjects.length(); j++) {
					if (!handleScheduleUpdateFromWizard(scheduleObjects.getJSONObject(j), globalStore, changedComponentOrdList)) {
						savedPropertyCount++;
					}
				}
			} else if (storeName.equals(TERMINAL_ASSIGNMENT)) {
				if (handleTerminalAssignmentsUpdateFromWizard(changedComponentOrdList)) {
					savedPropertyCount++;
				}
			} else {
				handleValueUpdatesForPointsAndSlots(changedComponentOrdList, storeName);
			}
			
			progress += progressIncPerProp;
			progress(progress);
		}
		
		return progress;
	}

	public void handleValueUpdatesForPointsAndSlots(List<BOrd> changedComponentOrdList, String storeName) {
		JSONArray pointArray = valueObject.getJSONArray(storeName);

		for (int j = 0; j < pointArray.length(); j++) {
			JSONObject setPointObj = pointArray.getJSONObject(j);
			String pointValue = String.valueOf(setPointObj.get("value"));
			String propertyName = (String) setPointObj.get("propertyName");
			BWidgetComponentBase widgetComponent = globalStore.getWidgetComponent(storeName, propertyName);
			if (globalStore.saveComponentValue(widgetComponent, pointValue)) {
				if(((BDynamicWidgetComponentBase)widgetComponent).getBacnetObjectId().isValid() ||
						!((BDynamicWidgetComponentBase)widgetComponent).getCommonSlot().isNull()) {
					changedComponentOrdList.add(widgetComponent.getSlotPathOrd());
				}
				savedPropertyCount++;

				handleValueUpdatesForDuctAreaCalculator(changedComponentOrdList, widgetComponent);
			}
			if (widgetComponent instanceof BDynamicWidgetUnitSupportComponentBase) {
				updateUnitRelatedValues(setPointObj, widgetComponent);
			}
			if (widgetComponent instanceof BRangeSlider) {
				updateDeadBandValue(setPointObj, widgetComponent);
			}
		}
	}

	public void handleValueUpdatesForDuctAreaCalculator(List<BOrd> changedComponentOrdList, BWidgetComponentBase widgetComponent) {
		if (widgetComponent instanceof BDuctAreaCalculator && BDuctAreaCalculator
				.isDimensionUnit(((BDuctAreaCalculator) widgetComponent).getRole())) {
			// Update the area unit in the global store
			BDuctAreaCalculator area = BDuctAreaCalculator
					.updateAreaUnitFromDimensionUnit((BDuctAreaCalculator) widgetComponent);
			if(area != null) {
				if(area.getBacnetObjectId().isValid() || !area.getCommonSlot().isNull()) {
					changedComponentOrdList.add(area.getSlotPathOrd());
				}
				savedPropertyCount++;
			}
		}
	}

	public void updatePageOrderInGlobalStore(JSONArray pageOrderArray) {
		if(null != pageOrderArray && pageOrderArray.length() > 0) {
			// Update the page order in global store
			globalStore.clearAllPageOrders();
			for(int i = 0; i < pageOrderArray.length(); i++) {
				String pageName = pageOrderArray.getString(i);
				if(pageName.contains("Scheduling_")) {
					pageName = pageName.replace("Scheduling_", "");
				}
				if(SlotPath.isValidName(pageName)) {
					pageName = SlotPath.unescape(pageName);
				}
				globalStore.addPageOrderForNewPage(pageName);
			}
		}
	}

	/**
	 * update unit related values if the component is unit supported
	 * @param setPointObj, JSON object
	 * @param widgetComponent, widget component
	 */
	private void updateUnitRelatedValues(JSONObject setPointObj, BWidgetComponentBase widgetComponent){
		String unit = (String) setPointObj.get(UNIT);
		String unitName=null;
		if (!setPointObj.isNull(UNIT_NAME)) {
			 unitName = (String) setPointObj.get(UNIT_NAME);
		}
		String highPrecisionValue = (String) setPointObj.get(HIGH_PRECISION_VALUE);
		String highPrecisionMin = (String) setPointObj.get(HIGH_PRECISION_MIN);
		String highPrecisionMax = (String) setPointObj.get(HIGH_PRECISION_MAX);
		double minValue = setPointObj.getDouble(MIN);
		double maxValue = setPointObj.getDouble(MAX);
		double step = setPointObj.getDouble(STEP);
		int precision = setPointObj.getInt(PRECISION);
		((BDynamicWidgetUnitSupportComponentBase) widgetComponent).updateUnitRelatedValues( unitName, highPrecisionValue, highPrecisionMin,
				highPrecisionMax, minValue, maxValue, step, precision);

	}

	/**
	 * set dead band for range slider widget
	 * @param setPointObj, JSON object
	 * @param widgetComponent, widget component
	 */
	private void updateDeadBandValue(JSONObject setPointObj, BWidgetComponentBase widgetComponent){
		String highDeadBandValue = (String) setPointObj.get(HIGH_PRECISION_DEAD_BAND);
		double deadband = setPointObj.getDouble(DEAD_BAND);
		((BRangeSlider)widgetComponent).setHighPrecisionDeadband(highDeadBandValue);
		((BRangeSlider)widgetComponent).setDeadband(deadband);
	}
	
	private static boolean handleScheduleUpdateFromWizard(JSONObject scheduleObjects, BHonWizardGlobalStore globalStore,
			List<BOrd> changedComponentOrdList) {
		JSONArray eventObjects = scheduleObjects.getJSONArray("events");
		String componentName = scheduleObjects.getString("label");
		int defaultLocalScheduleType = scheduleObjects.getInt("defaultLocalScheduleType");
		JSONObject holidayObjects = scheduleObjects.getJSONObject("Holidays");
		
		Map<String, HolidayDetails> holidayDetails = handleHolidayScheduleUpdateFromWizard(holidayObjects);
		
		Map<String, DayScheduleEvent> eventsDetails = new HashMap<>();
		try {
			for (int j = 0; j < eventObjects.length(); j++) {
				JSONObject event = eventObjects.getJSONObject(j);
				String day = event.get("day").toString();
				DayScheduleEvent dayEvent;
				if (null == eventsDetails.get(day)) {
					dayEvent = new DayScheduleEvent();
					dayEvent.setDayOfEvent(day);
				} else {
					dayEvent = eventsDetails.get(day);
				}
				int eventId = event.getInt("eventId");
				String start = event.get("start").toString();
				String end = event.get("end").toString();
				int status = event.getInt("status");
				ScheduleEventStartEndStatus startEndEvent = new ScheduleEventStartEndStatus(eventId, start, end, status);
				dayEvent.getListOfEventsForDay().add(startEndEvent);
				eventsDetails.put(day, dayEvent);
			}
			BWidgetComponentBase widgetComponent = globalStore.getWidgetComponent(componentName, componentName);
			if (globalStore.saveScheduleDetails(widgetComponent, eventsDetails, defaultLocalScheduleType, holidayDetails)) {
				changedComponentOrdList.add(widgetComponent.getSlotPathOrd());
			}
		} catch (JSONException e) {
			HoneywellDeviceWizardLogger.warning(MessageFormat.format("Error occurred while processing schedule update: {0}", e));
			return false;
		}
		return true;
	}

	/**
	 * Save terminal assignments from wizard
	 * 1. save terminal assignments in global store
	 * 2. sync terminal assignments to IO components
	 * 3. sync terminal assignments to controller
	 *
	 * @return succeed or failed
	 */
	private boolean handleTerminalAssignmentsUpdateFromWizard(List<BOrd> changedOrdList) {
		if (!getDevice().isSupportTerminalAssignment()) {
			return true;
		}
		JSONArray terminalObjects = valueObject.getJSONArray(TERMINAL_ASSIGNMENT);
		try {
			BComponent comp = new BComponent();
			for (int i = 0; i < terminalObjects.length(); i++) {
				JSONObject terminalObj = terminalObjects.getJSONObject(i);
				String pinName = terminalObj.getString(PIN_NAME);
				String fbName = terminalObj.getString(FB_NAME);
				comp.add(SlotPath.escape(pinName), BString.make(SlotPath.unescape(fbName)));
			}
			globalStore.updateTerminalAssignments(TERMINAL_ASSIGNMENT, comp, changedOrdList);
		} catch (Exception e) {
			HoneywellDeviceWizardLogger.severe(lex.getText("BHonWizardValueSaveJob.terminalAssignmentError", getDevice().getDeviceName()), e);
			return false;
		}
		return true;
	}
	
	private static Map<String, HolidayDetails> handleHolidayScheduleUpdateFromWizard(JSONObject holidayObject) {
		JSONArray recurringDay;
		try {
			recurringDay = holidayObject.getJSONArray(RECURRING_DAYS);
		} catch (JSONException e) {
			recurringDay = new JSONArray();
		}
		JSONArray specificDate;
		try {
			specificDate = holidayObject.getJSONArray(SPECIFIC_DATE);
		} catch (JSONException e) {
			specificDate = new JSONArray();
		}
		JSONArray dateRange;
		try {
			dateRange = holidayObject.getJSONArray(DATE_RANGE);
		} catch (Exception e) {
			dateRange = new JSONArray();
		}
		Map<String, HolidayDetails> holidayDetails = new HashMap<>();
		for(int i = 0; i < recurringDay.length(); i++) {
			JSONObject recurringDayObject = recurringDay.getJSONObject(i);
			RecurringHolidayDetails recurringHolidays = new RecurringHolidayDetails(recurringDayObject);
			holidayDetails.put(recurringHolidays.getName(), recurringHolidays);
		}
		for(int i = 0; i < specificDate.length(); i++) {
			JSONObject specificDateObject = specificDate.getJSONObject(i);
			SpecificDateHolidayDetails specificDateHolidays = new SpecificDateHolidayDetails(specificDateObject);
			holidayDetails.put(specificDateHolidays.getName(), specificDateHolidays);
		}
		for(int i = 0; i < dateRange.length(); i++) {
			JSONObject dateRangeObject = dateRange.getJSONObject(i);
			DateRangeHolidayDetails dateRangeHolidays = new DateRangeHolidayDetails(dateRangeObject);
			holidayDetails.put(dateRangeHolidays.getName(), dateRangeHolidays);
		}
		
		return holidayDetails;
	}
	
	private void updateProgressDetails() {
		boolean isSaveToWiresheet = getDevice().canSaveToDatabase();
		boolean isUpdateToController = getDevice().canUpdateValuesToController();
		if (isSaveToWiresheet && isUpdateToController) {
			globalStoreSaveProgress = valueUpdateToWiresheetProgress = valueUpdateToControllerProgress = TOTAL_AVAILABLE_PROG / 3;
		} else if (isSaveToWiresheet) {
			valueUpdateToControllerProgress = 0;
			globalStoreSaveProgress = valueUpdateToWiresheetProgress = TOTAL_AVAILABLE_PROG / 2;
		} else if (isUpdateToController) {
			valueUpdateToWiresheetProgress = 0;
			globalStoreSaveProgress = valueUpdateToControllerProgress = TOTAL_AVAILABLE_PROG / 2;
		} else {
			valueUpdateToWiresheetProgress = 0;
			valueUpdateToControllerProgress = 0;
			globalStoreSaveProgress = TOTAL_AVAILABLE_PROG;
		}
	}

	@Override
	public void doCancel(Context cx) {
		super.doCancel(cx);
		exitOperationThread(MAX_RETRY_ON_CANCEL);
	}
	
	public int getSavedPropertyCount() {
		return savedPropertyCount;
	}

	public int getGlobalStoreSaveProgress() {
		return globalStoreSaveProgress;
	}

	public int getValueUpdateToWiresheetProgress() {
		return valueUpdateToWiresheetProgress;
	}

	public int getValueUpdateToControllerProgress() {
		return valueUpdateToControllerProgress;
	}
	
}
