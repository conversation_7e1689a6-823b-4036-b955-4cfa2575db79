/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.jobs;

import java.util.List;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Tag;
import javax.baja.tag.Taggable;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.generate.TagGenerator;
import com.honeywell.applicationhandler.ontology.generate.TagMappingInfo;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.validation.tagmappings.TagMappingValidation;

/**
 * <AUTHOR> Sun
 */
@NiagaraType
@SuppressWarnings({
        "squid:MaximumInheritanceDepth",
        "squid:S2160",
        "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardTagGenJob extends BHonWizardJob {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardTagGenJob(2979906276)1.0$ @*/
/* Generated Wed Apr 02 11:19:32 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardTagGenJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
    private static final Lexicon lex = Lexicon.make(BHonWizardGlobalStoreGenJob.class);
    private static final int MAX_RETRY_ON_CANCEL = 10;

    private String fileData;

    public BHonWizardTagGenJob() {
        super();
    }

    public BHonWizardTagGenJob(BIHoneywellConfigurableDevice device, String fileData) {
        super(device);
        this.fileData = fileData;
        setNameOfJob(lex.getText("honWizardJob.tagGenJob"));
    }

    @Override
    public void doRun(Context cx) {
        super.doRun(cx);
        progress(5);

        updateMessageLog(lex.getText("BHonWizardTagGenJob.loadingTagMapping"));
        TagGenerator tagGenerator = new TagGenerator();
        tagGenerator.loadTagMappingInfoFromFileData(fileData);

        updateMessageLog(lex.getText("BHonWizardTagGenJob.validation.start"));
        BComponent device = (BComponent) getDevice();
        List<BIHonWizardPoint> points = HoneywellConfigurableDeviceUtil.getAllHonWizardPoints(device);
        List<TagMappingInfo> allTagMappingInfos = tagGenerator.getPointTagMappingInfos();
        allTagMappingInfos.addAll(tagGenerator.getSlotTagMappingInfos());
        getDevice().stopDynamicUpdatesOfGlobalStore("TagGenerationJob");
        TagMappingValidation.validateTemplate(tagGenerator.getHeaderNames(), this);
        TagMappingValidation.validateAll(allTagMappingInfos, this);

        if(isHasFailItemsInLog()) {
            updateEndFailureLog(lex.getText("BHonWizardTagGenJob.fail", getDevice().getDeviceName()), "");
            getDevice().resumeDynamicUpdatesOfGlobalStore(Const.FORCE_RESUME_DYNAMIC_UPDATE);
            return;
        }

        progress(30);

        getDevice().setIsWizardInSyncWithConfiguration(false);
        
        try {
			// Clean up existing slots
			HoneywellConfigurableDeviceUtil.cleanUpExistingHonPointSelectorTags(device);
			HoneywellConfigurableDeviceUtil.cleanUpExistingHonSlotSelectorTags(device);
			for(BIHonWizardPoint point : points) {
			    if(!(point instanceof Taggable)) {
			        continue;
			    }
			    BHonWizPointSelector[] existingTags = ((BComponent) point).getChildren(BHonWizPointSelector.class);
			    for(BHonWizPointSelector existingTag : existingTags) {
			        ((BComponent) point).remove(existingTag);
			    }
			    List<Tag> tags = tagGenerator.generateTagsForPoint(point, getDevice());

			    if(tags != null) {
			        tagGenerator.renewPointSelector((BComponent) point, tags);
			    }
			}

			String baseSlotPath = device.getSlotPath().toString();
			tagGenerator.renewSlotSelector(baseSlotPath, this);
		} catch (Exception e) {
			updateEndFailureLog(lex.getText("BHonWizardTagGenJob.fail", getDevice().getDeviceName()), e.getStackTrace().toString());
		}

        progress(60);

        if(isHasFailItemsInLog()) {
            updateEndFailureLog(lex.getText("BHonWizardTagGenJob.fail", getDevice().getDeviceName()), "");
        }
        else {
            updateMessageLog(lex.getText("BHonWizardTagGenJob.success", getDevice().getDeviceName()));
            BHonWizardGlobalStoreGenJob.validateAllSelectors(this, 80);
            progress(80);
            BHonWizardGlobalStoreGenJob.generateWizardConfigurations(this);
        }
        
        getDevice().resumeDynamicUpdatesOfGlobalStore(Const.FORCE_RESUME_DYNAMIC_UPDATE);
    }

    @Override
    public void doCancel(Context cx) {
        super.doCancel(cx);
        exitOperationThread(MAX_RETRY_ON_CANCEL);
    }
}
