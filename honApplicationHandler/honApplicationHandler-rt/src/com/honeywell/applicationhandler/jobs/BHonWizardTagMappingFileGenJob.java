/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.jobs;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.generate.TagMappingFileGenerator;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Feb 19, 2025
 */
@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S2160",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardTagMappingFileGenJob extends BHonWizardJob {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob(2979906276)1.0$ @*/
/* Generated Mon Jan 27 13:17:30 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardTagMappingFileGenJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  private static final Lexicon lex = Lexicon.make(BHonWizardTagMappingFileGenJob.class);
  private static final int MAX_RETRY_ON_CANCEL = 10;
  
  private int totalAvailableProgress= 90;
  
	public BHonWizardTagMappingFileGenJob(BIHoneywellConfigurableDevice device) {
		super(device);
		setNameOfJob(lex.getText("honWizardJob.tagFileGenJob"));
	}
	
	public BHonWizardTagMappingFileGenJob() {
		super();
	}

	@Override
	public void doRun(Context cx) {
		super.doRun(cx);
		int progress = 5;
		progress(progress);
		
		String fileName = Sys.getStationHome().toPath() + "/userConfigData/TagMapping_" + BAbsTime.now().getMillis() + ".xlsx";
		
		TagMappingFileGenerator generator = new TagMappingFileGenerator(getDevice(), this, fileName);
    	generator.generateTagMappingExcel();
		
        progress(99);
        
		if(isHasFailItemsInLog()) {
			updateEndFailureLog(lex.getText("BHonWizardTagMappingFileGenJob.fail", getDevice().getDeviceName()), "");
		} else {
			updateSuccessLog(lex.getText("BHonWizardTagMappingFileGenJob.success", getDevice().getDeviceName(), fileName), "");
		}
	}

	@Override
	public void doCancel(Context cx) {
		super.doCancel(cx);
		exitOperationThread(MAX_RETRY_ON_CANCEL);
	}

	public int getTotalAvailableProgress() {
		return totalAvailableProgress;
	}
	
}
