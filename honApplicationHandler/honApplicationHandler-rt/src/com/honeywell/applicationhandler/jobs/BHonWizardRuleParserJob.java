/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.jobs;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.rules.IRule;
import com.honeywell.applicationhandler.rules.parser.RuleParser;
import com.honeywell.applicationhandler.utils.HoneywellWidgetStaticDataUtil;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

import javax.baja.driver.loadable.BLoadableNetwork;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jan 27, 2025
 */
@NiagaraType
@SuppressWarnings({
    "squid:MaximumInheritanceDepth",
    "squid:S2160",
    "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizardRuleParserJob extends BHonWizardJob {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob(2979906276)1.0$ @*/
/* Generated Mon Jan 27 13:17:30 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardRuleParserJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  private static final Lexicon lex = Lexicon.make(BHonWizardRuleParserJob.class);
  private static final int RULE_PARSE_PERCENT = 80; 
  private static final String UNDERSCORE = "_";
  private BHonWizardRuleStore ruleStore;
  private static final int MAX_RETRY_ON_CANCEL = 10;
  private boolean onlyValidate;
  
	public BHonWizardRuleParserJob(BIHoneywellConfigurableDevice device, BHonWizardRuleStore ruleStore, boolean onlyValidate) {
		super(device);
		this.ruleStore = ruleStore;
		this.onlyValidate = onlyValidate;
		if(onlyValidate) {
			setNameOfJob(lex.getText("honWizardJob.validateRule"));
		} else {
			setNameOfJob(lex.getText("honWizardJob.ruleParser"));
		}
	}
	
	public BHonWizardRuleParserJob() {
		super();
	}

	@Override
	public void doRun(Context cx) {
		super.doRun(cx);
		int progress = 5;
		progress(progress);
		RuleParser parser = new RuleParser(getDevice(), true);
		JSONArray jsonArray = new JSONArray();
		int propertyCount = ruleStore.getDynamicPropertiesArray().length;
		if(propertyCount == 0) {
			progress(100);
			updateSuccessLog(lex.getText("BHonWizardRuleParserJob.norules", getDevice().getDeviceName()), "");
			return;
		}
		
		if(isJobCanceled()) {
			exitOperationThread(MAX_RETRY_ON_CANCEL);
		}
		
		validateAndGenerateWizardRulesJSON(this.getDevice(), ruleStore, parser, jsonArray);
		progress(progress + RULE_PARSE_PERCENT);
		
		if(isJobCanceled()) {
			exitOperationThread(MAX_RETRY_ON_CANCEL);
		}
	
		int expectedProgAftRuleParse = 85;
		if(progress < expectedProgAftRuleParse) {
			progress = expectedProgAftRuleParse;
		}
		if (!onlyValidate) {
			// If only validation is required, we don't need to save the rules
			File userConfigData = new File(Sys.getStationHome(), "userConfigData");
			userConfigData.mkdir();
			String path = getFolderPath((BComponent) getDevice());
			File devicePath = new File(userConfigData, path);
			devicePath.mkdir();
			File userConfig = new File(devicePath, "wizardRules.hfg");

			try (PrintWriter writer = new PrintWriter(new FileWriter(userConfig.getAbsolutePath()))) {
				writer.print(jsonArray.toString());
			} catch (IOException e) {
				updateEndFailureLog(lex.getText("BHonWizardRuleParserJob.writeFailed", getDevice().getDeviceName(), e.getMessage()),
						Arrays.toString(e.getStackTrace()));
			}
		}

		progress += 5;
		progress(progress);
		
		if(RuleParser.getLogger().isLoggable(Level.INFO)) {
			RuleParser.getLogger().info(jsonArray.toString());
		}	
		
		parser.generateErrorReport(this);
		if(isHasFailItemsInLog()) {
			updateEndFailureLog(lex.getText("BHonWizardRuleParserJob.fail", getDevice().getDeviceName()), "");
		} else {
			updateSuccessLog(lex.getText("BHonWizardRuleParserJob.success", getDevice().getDeviceName()), "");
		}
	}

	public static void validateAndGenerateWizardRulesJSON(BIHoneywellConfigurableDevice device, BHonWizardRuleStore store, RuleParser parser, JSONArray jsonArray) {
		if(device == null) {
			return;
		}
		Map<String, String> rules = new HashMap<>();
		if(store.getSwappingOut()){
			JSONObject wizardRuleStoreJson = HoneywellWidgetStaticDataUtil.loadRuleStoreFromJson(device.getGlobalStoreUuid(false));
			if(null != wizardRuleStoreJson) {
				for (String s : wizardRuleStoreJson.keySet()) {
					rules.put(s, wizardRuleStoreJson.getString(s));
				}
			}
		} else {
			Property[] dynamicRuleArray = store.getDynamicPropertiesArray();
			for (Property ruleProp : dynamicRuleArray) {
				if (store.get(ruleProp) instanceof BString) {
					rules.put(ruleProp.getDefaultDisplayName(null), (store.get(ruleProp)).toString());
				}
			}
		}
		rules.forEach((ruleName, ruleQuery) -> {
			if(ruleQuery == null || ruleQuery.isEmpty()) {
				return;
			}
			IRule rule = parser.parse(ruleQuery, ruleName);
			JSONObject json = parser.generateJson(rule);
			jsonArray.put(json);
		});
	}
	
	// Helper method to build the folder path recursively with underscores
    public static String getFolderPath(BComponent component) {
    	String deviceName = component.getName();
    	String newPath = "";
    	while(component.getParent() != null && !(component instanceof BLoadableNetwork)) {
    		String compName = "";
    		if(deviceName.equals(component.getName())) {
    			compName = component.getName();
    		} else {
    			compName = component.getName() + UNDERSCORE;
    		}
    		newPath = compName.concat(newPath);
       		component = (BComponent) component.getParent();
       	}
       	return newPath;
    }
	
	@Override
	public void doCancel(Context cx) {
		super.doCancel(cx);
		exitOperationThread(MAX_RETRY_ON_CANCEL);
	}
  
}
