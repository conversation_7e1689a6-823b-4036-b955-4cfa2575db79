/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.jobs;

import com.honeywell.applicationhandler.common.BHonWizardJob;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.validation.tags.TagValidation;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;
import java.util.List;

/**
 * <AUTHOR> Sun
 */
@NiagaraType
public class BHonWizardTagValidationJob extends BHonWizardJob {
/*+ ------------ <PERSON><PERSON><PERSON> BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.jobs.BHonWizardTagValidationJob(2979906276)1.0$ @*/
/* Generated Thu Apr 17 16:23:27 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardTagValidationJob.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final Lexicon lex = Lexicon.make(BHonWizardGlobalStoreGenJob.class);
    private static final int MAX_RETRY_ON_CANCEL = 10;

    public BHonWizardTagValidationJob() {
        super();
    }

    public BHonWizardTagValidationJob(BIHoneywellConfigurableDevice device) {
        super(device);
        setNameOfJob(lex.getText("honWizardJob.tagValidationJob"));
    }

    @Override
    public void doRun(Context cx) {
        super.doRun(cx);
        progress(10);

        updateMessageLog(lex.getText("BHonWizardTagValidationJob.validation.start"));

        List<BHonWizSelector> selectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) getDevice());

        TagValidation.validateAll(selectors, this, 99);

        progress(99);

        if(isHasFailItemsInLog()) {
            updateEndFailureLog(lex.getText("BHonWizardTagValidationJob.fail", getDevice().getDeviceName()), "");
        }
        else {
            updateSuccessLog(lex.getText("BHonWizardTagValidationJob.success", getDevice().getDeviceName()), "");
        }
    }

    @Override
    public void doCancel(Context cx) {
        super.doCancel(cx);
        exitOperationThread(MAX_RETRY_ON_CANCEL);
    }
}
