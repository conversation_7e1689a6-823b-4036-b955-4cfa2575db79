/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.menu;

import java.util.logging.Logger;

import javax.baja.file.BIFile;
import javax.baja.file.IExtFileFilter;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraSingleton;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Action;
import javax.baja.sys.BComponent;
import javax.baja.sys.BSingleton;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BMenu;
import javax.baja.ui.BMenuItem;
import javax.baja.ui.BWidget;
import javax.baja.ui.Command;
import javax.baja.ui.CommandArtifact;
import javax.baja.ui.CommandEvent;
import javax.baja.ui.file.BFileChooser;

import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.ontology.generate.TagGenerator;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.tridium.workbench.nav.BComponentMenuAgent;
import com.tridium.workbench.nav.BIComponentMenuDecorator;

/**
 * <AUTHOR> Sun
 */
@NiagaraType(
        agent = {@AgentOn(
                types = {"workbench:ComponentMenuAgent"}
        )}
)
@NiagaraSingleton
@SuppressWarnings({
        "squid:S6548"
})
public class BHonWizardMenuDecorator extends BSingleton implements BIComponentMenuDecorator {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.menu.BHonWizardMenuDecorator(3361691315)1.0$ @*/
/* Generated Mon Apr 14 11:33:03 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */
  
  public static final BHonWizardMenuDecorator INSTANCE = new BHonWizardMenuDecorator();

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardMenuDecorator.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    @Override
    public void decorateMenu(BComponentMenuAgent bComponentMenuAgent, BMenu bMenu, BWidget owner, BComponent target, Context context) {
        if(!(target instanceof BIHoneywellConfigurableDevice)) {
            return;
        }

        BMenuItem actions = (BMenuItem) bMenu.get(BComponentMenuAgent.ACTIONS);
        if(actions == null) {
            return;
        }
        BMenu[] actionMenu = actions.getChildren(BMenu.class);
        if(actionMenu == null || actionMenu.length == 0) {
            return;
        }

        BMenuItem[] actionMenuItems = actionMenu[0].getMenuItems();
        for (BMenuItem item : actionMenuItems) {
            String logMessage = "Menu item name: " + item.getText();
            logger.info(logMessage);
            if ("Attach Hon Wizard Tags".equals(item.getText())) {
                item.setCommand(new BHonWizardMenuDecorator.TagGenCommand(owner, "Attach Hon Wizard Tags", (BIHoneywellConfigurableDevice) target));
            }
        }
    }

    private static class TagGenCommand extends Command {

        BIHoneywellConfigurableDevice device;

        public TagGenCommand(BWidget owner, String label, BIHoneywellConfigurableDevice device) {
            super(owner, label);
            this.device = device;
        }

        @Override
        @SuppressWarnings({
                "squid:S3516",  // Methods returns should not be invariant
        })
        public CommandArtifact doInvoke(CommandEvent event) throws Exception {
            new Thread(this::handleFileSelection).start();;
            return null;
        }

		public void handleFileSelection() {
			BFileChooser fileChooser = BFileChooser.makeOpen(getOwner());
            fileChooser.addFilter(getTagMappingFileFilter());
            BOrd fileOrd = fileChooser.show();
            if(fileOrd == null) {
				return;
			}
            String fileData = TagGenerator.parseTagMappingInfo(fileOrd);

            if(fileData == null) {
                logger.severe("Failed to get the tag mapping file data.");
                return;
            }

            Action tagGenAction = device.getAttachHonWizardTagsActionWithFileContent();
            if(tagGenAction != null) {
                BComponent bDevice = (BComponent) device;
                bDevice.invoke(tagGenAction, BString.make(fileData), null);
            } else {
                logger.severe("Tag generation action is not found.");
            }
		}

        private IExtFileFilter getTagMappingFileFilter() {
            return new IExtFileFilter() {
                @Override
                public boolean accept(final BIFile file) {
                    return (null != file && null != file.getExtension() && file.getExtension().equals("xlsx"));
                }

                @Override
                public String getDescription(Context arg0) {
                    return "Excel files (*.xlsx)";
                }

                @Override
                public boolean acceptExtension(String var1) {
                    return (var1.equals("xlsx"));
                }

                @Override
                public String getDefaultExtension() {
                    return "xlsx";
                }
            };
        }
    }
}
