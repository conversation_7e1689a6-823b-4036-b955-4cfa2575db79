@echo on
REM ---------------------------------------------- INITIALIZE ------------------------------------------------------

set WD=%CD%
set USER=%1
set PASSWORD=%2
set NIAGARA_FULL_VERSION=%3
set ARTIFACTORY_PREFIX=%4
set REPO_NAME=%5
set NIAGARA_HOME=C:\Temp\Niagara\Niagara-%NIAGARA_FULL_VERSION%

REM ---------------------------------------------- CLEANUP ------------------------------------------------------

cd C:\Temp\
rmdir Niagara /S /Q
mkdir Niagara
cd C:\Temp\Niagara
mkdir %NIAGARA_HOME%\
cd %NIAGARA_HOME%\
del Niagara-%NIAGARA_FULL_VERSION%.*

REM ---------------------------------------------- DOWNLOAD NIAGARA ------------------------------------------------------

cd %WD%\
rmdir Niagara /S /Q
mkdir Niagara
cd Niagara
curl -u %USER%:%PASSWORD% "%ARTIFACTORY_PREFIX%/%REPO_NAME%/Niagara/Niagara-%NIAGARA_FULL_VERSION%.zip" --output %WD%/Niagara/Niagara-%NIAGARA_FULL_VERSION%.zip

REM ---------------------------------------------- UNPACKING AND INSTALLING ------------------------------------------------------

cd %WD%\Niagara\
copy Niagara-%NIAGARA_FULL_VERSION%.zip %NIAGARA_HOME%\
cd %NIAGARA_HOME%\
7z x -y Niagara-%NIAGARA_FULL_VERSION%.zip
DIR
del Niagara-%NIAGARA_FULL_VERSION%.zip
DIR

REM ---------------------------------------------- END ------------------------------------------------------