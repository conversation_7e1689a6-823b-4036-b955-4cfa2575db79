@echo on
REM ---------------------------------------------- INITIALIZE ------------------------------------------------------

set TOOL_VERSION=%1
set NIAGARA_FULL_VERSION=%2
set RELEASE_TYPE=%3
set USER=%4
set PASSWORD=%5
set PRODUCT_NAME=%6
set ARTIFACTORY_PREFIX=%7
set REPO_NAME=%8
set SIGNING_REPO_NAME=%9
shift
set SIGNING_REPO=%9
shift
set WORKFLOW_FILE=%9
shift
set PAT_TOKEN=%9
shift
set OBFUSCATE_MODULES=%9
set WD=%CD%
set NIAGARA_HOME=C:\Niagara\Niagara-%NIAGARA_FULL_VERSION%
set JAVA_HOME=C:\Niagara\Niagara-%NIAGARA_FULL_VERSION%\jre
set PACKAGE_NAME=%PRODUCT_NAME%_%TOOL_VERSION%
set OBFUSCATION_TOOL_LOCATION=C:\Temp\ObfuscationTool
set OBFUSCATION_SCRIPT_FILE=C:\Temp\ObfuscationTool\ObfuscationScript_Tool.txt
set OBFUSCATED_PATH=C:\Temp\Obfuscated

REM ---------------------------------------------- CLEANUP ------------------------------------------------------

del %NIAGARA_HOME%\modules\honApplicationHandler*.jar

dir %NIAGARA_HOME%\modules\*honApplicationHandler*.jar

del c:\temp\%PACKAGE_NAME%\signed\*.* /Q
del c:\temp\%PACKAGE_NAME%\unsigned\*.* /Q
del c:\temp\%PACKAGE_NAME%\*.* /Q
rmdir c:\temp\%PACKAGE_NAME%\signed
rmdir c:\temp\%PACKAGE_NAME%\unsigned
rmdir c:\temp\%PACKAGE_NAME%
mkdir c:\temp\%PACKAGE_NAME%
mkdir c:\temp\%PACKAGE_NAME%\signed
mkdir c:\temp\%PACKAGE_NAME%\unsigned
dir c:\temp\%PACKAGE_NAME%\signed
dir c:\temp\%PACKAGE_NAME%\unsigned
set SIGNED_PATH=c:\temp\%PACKAGE_NAME%\signed
set UNSIGNED_PATH=c:\temp\%PACKAGE_NAME%\unsigned

REM ---------------------------------------------- DOWNLOAD AND SETUP OBFUSCATION TOOL ------------------------------------------------------

if /I "%OBFUSCATE_MODULES%"=="TRUE" (
    cd C:\Temp\
    rmdir ObfuscationTool /S /Q
    mkdir ObfuscationTool

    rmdir Obfuscated /S /Q
    mkdir Obfuscated

    curl -u %USER%:%PASSWORD% "%ARTIFACTORY_PREFIX%/%REPO_NAME%/buildDependencies/ObfuscationTool/ObfuscationTool.zip" --output C:\Temp\ObfuscationTool\ObfuscationTool.zip
    if %ERRORLEVEL% neq 0 (
        echo Download failed!
        exit /b %ERRORLEVEL%
    )
    7z e C:\Temp\ObfuscationTool\ObfuscationTool.zip -oC:\Temp\ObfuscationTool
    del C:\Temp\ObfuscationTool\ObfuscationTool.zip /Q
    dir C:\Temp\ObfuscationTool
    dir C:\Temp\Obfuscated
)

REM ---------------------------------------------- BUILD ------------------------------------------------------

cd %WD%
call gradlew clean slotomatic build -D NIAGARA_HOME=%NIAGARA_HOME% -P TOOL_VERSION=%TOOL_VERSION% 

echo.
echo ======= Finding and copying JAR files to modules directory =======
echo Creating modules directory if it doesn't exist...
mkdir %NIAGARA_HOME%\modules 2>NUL

echo Looking for JAR files in build directories...
for /R %WD% %%G in (*honApplicationHandler*.jar) do echo Found JAR: %%G

echo Copying JAR files to modules directory...
for /R %WD% %%G in (*honApplicationHandler*.jar) do (
  echo Copying: %%G to %NIAGARA_HOME%\modules\
  copy "%%G" "%NIAGARA_HOME%\modules\"
  if %errorlevel% neq 0 (
    echo ERROR: Failed to copy %%G to modules directory
  ) else (
    echo SUCCESS: Copied %%G to modules directory
  )
)

echo Modules directory contents:

if %errorlevel% neq 0 exit /b %errorlevel%
echo ======= Copy complete =======
echo.

dir %NIAGARA_HOME%\modules\*honApplicationHandler*.jar
if "%RELEASE_TYPE%" == "DAILY" (
	echo Skipping the operation as the build type is set to 'DAILY'
	goto bailout
)

REM ----------------------------------------------  PACKAGING ------------------------------------------------------

copy %NIAGARA_HOME%\modules\honApplicationHandler-rt.jar %UNSIGNED_PATH%
if %errorlevel% neq 0 exit /b %errorlevel%
7z d %UNSIGNED_PATH%\honApplicationHandler-rt.jar NIAGARA1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-rt.jar NIAGARA1.SF -r
7z d %UNSIGNED_PATH%\honApplicationHandler-rt.jar CODESIG1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-rt.jar CODESIG1.SF -r

copy %NIAGARA_HOME%\modules\honApplicationHandler-ux.jar %UNSIGNED_PATH%
if %errorlevel% neq 0 exit /b %errorlevel%
7z d %UNSIGNED_PATH%\honApplicationHandler-ux.jar NIAGARA1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-ux.jar NIAGARA1.SF -r
7z d %UNSIGNED_PATH%\honApplicationHandler-ux.jar CODESIG1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-ux.jar CODESIG1.SF -r

copy %NIAGARA_HOME%\modules\honApplicationHandler-wb.jar %UNSIGNED_PATH%
if %errorlevel% neq 0 exit /b %errorlevel%
7z d %UNSIGNED_PATH%\honApplicationHandler-wb.jar NIAGARA1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-wb.jar NIAGARA1.SF -r
7z d %UNSIGNED_PATH%\honApplicationHandler-wb.jar CODESIG1.RSA -r
7z d %UNSIGNED_PATH%\honApplicationHandler-wb.jar CODESIG1.SF -r

cd %UNSIGNED_PATH%
7z a %NIAGARA_FULL_VERSION%.zip .
if %errorlevel% neq 0 exit /b %errorlevel%

if /I "%OBFUSCATE_MODULES%"=="TRUE" (
    REM ---------------------------------------------- UPDATE OBFUSCATION SCRIPT FILE ------------------------------------------------------

    call %WD%/buildScripts/updateObfuscationScript.bat %NIAGARA_HOME% %OBFUSCATION_SCRIPT_FILE% %OBFUSCATED_PATH% %UNSIGNED_PATH%
    if %errorlevel% neq 0 exit /b %errorlevel%

    @echo on
    REM ---------------------------------------------- OBFUSCATION ------------------------------------------------------

    cd %WD%
    java -Xmx1024m -jar %OBFUSCATION_TOOL_LOCATION%\ZKM.jar %OBFUSCATION_SCRIPT_FILE%

    waitfor SomethingThatIsNeverHappening /t 60 2>NUL

    dir %OBFUSCATED_PATH%
    7z a %OBFUSCATED_PATH%\%NIAGARA_FULL_VERSION%.zip %OBFUSCATED_PATH%\*.*
    if %ERRORLEVEL% neq 0 (
        echo Download failed!
        exit /b %ERRORLEVEL%
    )
)

REM ----------------------------------------------  MODULE SIGNING ------------------------------------------------------
@echo on
if /I "%OBFUSCATE_MODULES%"=="TRUE" (
    curl -X PUT -u %USER%:%PASSWORD% -T %OBFUSCATED_PATH%\%NIAGARA_FULL_VERSION%.zip "%ARTIFACTORY_PREFIX%/%SIGNING_REPO_NAME%/tools/%PRODUCT_NAME%/%PACKAGE_NAME%/%NIAGARA_FULL_VERSION%/qa/unsigned/%NIAGARA_FULL_VERSION%.zip"
) else (
    curl -X PUT -u %USER%:%PASSWORD% -T %UNSIGNED_PATH%\%NIAGARA_FULL_VERSION%.zip "%ARTIFACTORY_PREFIX%/%SIGNING_REPO_NAME%/tools/%PRODUCT_NAME%/%PACKAGE_NAME%/%NIAGARA_FULL_VERSION%/qa/unsigned/%NIAGARA_FULL_VERSION%.zip"
)

set artifactoryjarRepo=%ARTIFACTORY_PREFIX%/%SIGNING_REPO_NAME%/tools/%PRODUCT_NAME%/%PACKAGE_NAME%/%NIAGARA_FULL_VERSION%/qa/unsigned/%NIAGARA_FULL_VERSION%.zip
call curl -X POST ^
  -H "Accept: application/vnd.github.v3+json" ^
  -H "Authorization: token %PAT_TOKEN%" ^
  https://api.github.com/repos/%SIGNING_REPO%/actions/workflows/%WORKFLOW_FILE%/dispatches ^
  -d "{\"ref\":\"master\",\"inputs\":{\"artifactoryjarRepo\":\"%artifactoryjarRepo%\",\"Niagara_Version\":\"%NIAGARA_FULL_VERSION%\"}}"
call %WD%\BuildScripts\checkSigningStatus.bat %PAT_TOKEN%

call curl -u%USER%:%PASSWORD% %ARTIFACTORY_PREFIX%/%SIGNING_REPO_NAME%/tools/%PRODUCT_NAME%/%PACKAGE_NAME%/%NIAGARA_FULL_VERSION%/qa/signed/%NIAGARA_FULL_VERSION%.zip --output %SIGNED_PATH%\%NIAGARA_FULL_VERSION%.zip

dir %SIGNED_PATH%

REM ----------------------------------------------  UPLOAD BINARIES TO ARTIFACTORY ------------------------------------------------------
@echo on

curl -X PUT -u %USER%:%PASSWORD% -T %SIGNED_PATH%\%NIAGARA_FULL_VERSION%.zip "%ARTIFACTORY_PREFIX%/%REPO_NAME%/tools/%PRODUCT_NAME%/%PACKAGE_NAME%/%PACKAGE_NAME%.zip"
if %errorlevel% neq 0 exit /b %errorlevel%
	
if %RELEASE_TYPE% == RELEASE (
	curl -X PUT -u %USER%:%PASSWORD% -T %SIGNED_PATH%\%NIAGARA_FULL_VERSION%.zip "%ARTIFACTORY_PREFIX%/%REPO_NAME%/tools/%PRODUCT_NAME%/latest/%PRODUCT_NAME%.zip"
    if %errorlevel% neq 0 exit /b %errorlevel%
)

:bailout
REM ---------------------------------------------- CLEANUP ------------------------------------------------------

del %NIAGARA_HOME%\modules\honApplicationHandler*.jar

dir %NIAGARA_HOME%\modules\*honApplicationHandler*.jar

del c:\temp\%PACKAGE_NAME%\signed\*.* /Q
del c:\temp\%PACKAGE_NAME%\unsigned\*.* /Q
del c:\temp\%PACKAGE_NAME%\*.* /Q
rmdir c:\temp\%PACKAGE_NAME%\signed
rmdir c:\temp\%PACKAGE_NAME%\unsigned
rmdir c:\temp\%PACKAGE_NAME%

dir %NIAGARA_HOME%\

REM ---------------------------------------------- END ------------------------------------------------------
