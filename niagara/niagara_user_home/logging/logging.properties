# Default Log Configuration - Auto-generated by baja logging framework
handlers = java.util.logging.ConsoleHandler com.tridium.nre.syslog.SyslogLogHandler
.level = INFO

# FINEST is equivalent to Niagara AX Trace level
java.util.logging.ConsoleHandler.level = FINEST
java.util.logging.ConsoleHandler.formatter = com.tridium.nre.logging.NiagaraSimpleFormatter
java.util.logging.SimpleFormatter.format = %4$s [%1$tH:%1$tM:%1$tS %1$td-%1$tb-%1$ty %1$tZ][%3$s] %5$s%6$s%n

java.awt.level = SEVERE
sun.awt.level = SEVERE
java.util.prefs.level = SEVERE
web.jetty.level = SEVERE
org.bouncycastle.level = SEVERE
com.prosysopc.ua.level = WARNING
