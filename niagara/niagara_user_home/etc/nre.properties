########################################################
#
# File:  nre.properties
# 
#   This file contains properties used by the NRE 
#   launch executable to initialze the containing JRE.
#
#   Options are defined in key values pairs, where the
#   key determines which executable will use the options
#   specfied in the value.
#
#   The default memory values of these options are only 
#   suggested limitations. You may make modifications to 
#   this file to configure the NRE application as necessary
#   for the resources present in this environment, or remove 
#   the memory options entirely to allow the JRE to configure
#   its footprint according to its own heuristics.
#
#   To specify an argument for a long option (--), use --name=value.
#
#   Supported NRE binaries at this time:
#
#     station      The Niagara Station "station[.exe]"
#                  executable.
#
#     wb           The Niagara Workbench "wb[.exe]" 
#                  executable.
#
#     test         The Niagara Test Framework "test[.exe]" 
#                  executable.
#
#     nre          All other NRE excutables like "nre[.exe]"
#                  or "plat[.exe]".
#
#   Use the environment variable:
#
#     nre_debug=1
#
#   prior to launching the associated executable to debug 
#   the configuration of the JRE when started from the 
#   Niagara Console.
#
########################################################

# The list of options separated by spaces to pass thru to the VM 
station.java.options=-Dfile.encoding=UTF-8 -Xss512K -Xmx1024M
wb.java.options=-Dfile.encoding=UTF-8 -Xss512K -Xmx1024M 

# This property is used to toggle how the hostid is calculated
# for the current installation.  If true on a Window's box, then 
# the hostid will be prefixed with "WinSJ"
softjace=false