@echo off
REM ===============================================
REM fix_jacoco.bat
REM 一键修复 Windows 下 Niagara Jacoco agent jar 问题
REM 自动切换 UTF-8 控制台，解决中文乱码
REM ===============================================

:: 切换控制台编码为 UTF-8
chcp 65001 >nul

echo ==================================================
echo Step 1: 关闭所有 Niagara 测试进程
echo ==================================================
echo 请确保没有运行的 niagaraTest 或 NRE 进程
pause

echo ==================================================
echo Step 2: 删除 Gradle 缓存中的 Jacoco agent
echo ==================================================
set GRADLE_CACHE=%USERPROFILE%\.gradle\caches\modules-2\files-2.1\org.jacoco\org.jacoco.agent
if exist "%GRADLE_CACHE%" (
    rmdir /s /q "%GRADLE_CACHE%"
    echo 已删除 Gradle 缓存 Jacoco agent
) else (
    echo Gradle 缓存 Jacoco agent 不存在，跳过
)

echo ==================================================
echo Step 3: 删除项目临时解压目录
echo ==================================================
set PROJECT_BUILD_TMP=C:\Users\<USER>\workSpace\niagara\unit_test\HonApplicationHandler-feature-NIACPO-8901-unit-tests-for-widget-base-classes\honApplicationHandler\honApplicationHandler-rt\build\tmp\expandedArchives
if exist "%PROJECT_BUILD_TMP%" (
    rmdir /s /q "%PROJECT_BUILD_TMP%"
    echo 已删除临时解压目录
) else (
    echo 临时解压目录不存在，跳过
)

echo ==================================================
echo Step 4: 清理并刷新 Gradle 依赖
echo ==================================================
gradlew clean --refresh-dependencies
if %ERRORLEVEL% NEQ 0 (
    echo Gradle clean 或刷新依赖失败，请检查网络或 Gradle 配置
    pause
    exit /b 1
)
echo Gradle clean 和依赖刷新完成

echo ==================================================
echo Step 5: 运行 Niagara 测试
echo ==================================================
gradlew :honApplicationHandler-rt:niagaraTest
if %ERRORLEVEL% EQU 0 (
    echo niagaraTest 成功运行 ✅
) else (
    echo niagaraTest 运行失败 ❌
    pause
)

echo ==================================================
echo 修复完成
echo ==================================================
pause
