name: niagara_tools/honApplicationHandler
on:
  workflow_dispatch:
    inputs:
      NIAGARA_FULL_VERSION:
        required: true
        default: '4.15.1.16'
      RELEASE_TYPE:
        required: true
        default: 'DAILY'
      TOOL_VERSION:
        required: true
        default: '0.0.4'
      OBFUSCATE_MODULES:
        required: true
        default: 'TRUE'
env:
  ARTIFACTORY_PREFIX: https://artifactory-na.honeywell.com:443/artifactory
  PASSWORD: "${{ secrets.PASSWORD }}"
  PRODUCT_NAME: HonApplicationHandler
  REPO_NAME: btools-generic-stable-local
  SIGNING_REPO_NAME: bmsdevops-generic-stable-local
  USER: "${{ secrets.USERNAME }}"
  SIGNING_REPO: 'HON-BA/bmsdevops'
  WORKFLOW_FILE: 'signing_jars.yml'
  PAT_TOKEN: "${{ secrets.PAT_TOKEN }}"

jobs:
  Build-HonApplicationHandler:
    runs-on: niagara_tools
    steps:
    - name: Checkout HonApplicationHandler Repository
      uses: actions/checkout@v4.1.0
      env:
        REPO_NAME: "HON-BA/HonApplicationHandler"
      with:
        repository: "${{ env.REPO_NAME }}"
        ref: '${{github.ref_name}}'
        token: "${{secrets.PAT_TOKEN}}"
        clean: true

    # - name: Download and Setup Niagara
    #   run: ".\\buildScripts\\downloadNiagara.bat ${{ env.USER }} ${{ env.PASSWORD }} ${{ github.event.inputs.NIAGARA_FULL_VERSION }} ${{ env.ARTIFACTORY_PREFIX }} ${{ env.REPO_NAME }}"
    #   shell: cmd

    - uses: actions/setup-node@v4
      with:
        node-version: 18
    
    - name: Install Node
      run: |
        set WD=${{github.workspace}}
        cd %WD%\honApplicationHandler\honApplicationHandler-ux
        npm install babel-plugin-istanbul@4.1.3 --save-dev
        npm install --legacy-peer-deps
        npm --version
      shell: cmd

    - name: Install Grunt CLI
      run: |
        npm install -g grunt-cli
      shell: cmd

    - name: Run babel task
      run: |
        set WD=${{github.workspace}}
        cd %WD%\honApplicationHandler\honApplicationHandler-ux
        grunt babel
      env:
        NIAGARA_HOME: "C:\\Niagara\\Niagara-${{ github.event.inputs.NIAGARA_FULL_VERSION }}"
        NIAGARA_USER_HOME: "C:\\Niagara\\Niagara-${{ github.event.inputs.NIAGARA_FULL_VERSION }}"
      shell: cmd
      
    - name: Build and Upload Modules
      run: ".\\buildScripts\\build.bat ${{ github.event.inputs.TOOL_VERSION }} ${{ github.event.inputs.NIAGARA_FULL_VERSION }} ${{ github.event.inputs.RELEASE_TYPE }} ${{ env.USER }} ${{ env.PASSWORD }} ${{ env.PRODUCT_NAME }} ${{ env.ARTIFACTORY_PREFIX }} ${{ env.REPO_NAME }} ${{ env.SIGNING_REPO_NAME }} ${{ env.SIGNING_REPO }} ${{ env.WORKFLOW_FILE }} ${{ env.PAT_TOKEN }} ${{ github.event.inputs.OBFUSCATE_MODULES }}"
      shell: cmd

    #Generated by Copilot begin
    - name: Install SonarQube Scanner
      run: |
        Invoke-WebRequest -Uri https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-5.0.1.3006-windows.zip -OutFile sonar-scanner.zip
        # Fallback to the current URL if the original one fails
        if (-not (Test-Path -Path sonar-scanner.zip)) {
          Write-Host "Primary URL failed, trying alternate URL..."
          Invoke-WebRequest -Uri https://repo1.maven.org/maven2/org/sonarsource/scanner/cli/sonar-scanner-cli/5.0.1.3006/sonar-scanner-cli-5.0.1.3006-windows.zip -OutFile sonar-scanner.zip
        }
        tar -xvf sonar-scanner.zip -C C:\
        setx PATH "C:\sonar-scanner-5.0.1.3006-windows\bin;%PATH%"
      shell: powershell
    #Generated by Copilot end
    
    #Generated by Copilot begin
    - name: Run Jacoco Test Coverage
      run: |
        set WD=${{github.workspace}}
        cd %WD%
        gradlew :honApplicationHandler-rt:jacocoNiagaraTestReport :honApplicationHandler-ux:jacocoNiagaraTestReport :honApplicationHandler-wb:jacocoNiagaraTestReport
      env:
        NIAGARA_HOME: "C:\\Niagara\\Niagara-${{ github.event.inputs.NIAGARA_FULL_VERSION }}"
        NIAGARA_USER_HOME: "C:\\Niagara\\Niagara-${{ github.event.inputs.NIAGARA_FULL_VERSION }}"
        JAVA_HOME: "C:\\Niagara\\Niagara-${{ github.event.inputs.NIAGARA_FULL_VERSION }}\\jre"
      shell: cmd
    #Generated by Copilot end
    
    - name: Upload Jacoco Coverage Report to SonarQube
      env:
        JAVA_HOME: "C:\\Program Files\\Java\\jdk-11.0.23+9"
      run: |
        set WD=${{github.workspace}}
        C:\sonar-scanner-5.0.1.3006-windows\bin\sonar-scanner.bat ^
          -Dsonar.projectBaseDir=${{ github.workspace }} ^
          -Dsonar.projectKey=HAPPLH ^
          -Dsonar.projectName=HonApplicationHandler ^
          -Dsonar.branch.name=${{github.ref_name}} ^
          -Dsonar.java.binaries=**/build/classes/java/main ^
          -Dsonar.exclusions=**/src/doc/**,**/*.js.flow,**/*.flow,**/node_modules/**/*.js.flow,**/node_modules/**/*.flow,**/node_modules/**/* ^
          -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} ^
          -Dsonar.login=${{ secrets.SONAR_TOKEN }} ^
          -Dsonar.projectVersion=${{ github.event.inputs.TOOL_VERSION }} ^
          -Dsonar.sources=honApplicationHandler ^
          -Dsonar.coverage.jacoco.xmlReportPaths=%WD%\honApplicationHandler\honApplicationHandler-rt\build\reports\jacoco\niagaraTest\jacocoNiagaraTestReport.xml,%WD%\honApplicationHandler\honApplicationHandler-ux\build\reports\jacoco\niagaraTest\jacocoNiagaraTestReport.xml,%WD%\honApplicationHandler\honApplicationHandler-wb\build\reports\jacoco\niagaraTest\jacocoNiagaraTestReport.xml ^
          -Dsonar.javascript.lcov.reportPaths=%WD%\honApplicationHandler\honApplicationHandler-ux\coverage\lcov.info
      shell: cmd

    - name: Tag Repository
      if: github.event.inputs.RELEASE_TYPE != 'DAILY'
      env:
        TAG_NAME: ${{ env.PRODUCT_NAME }}_${{ github.event.inputs.RELEASE_TYPE }}_${{ github.event.inputs.TOOL_VERSION }}
      run: |
        git fetch --tags
        if (git tag -l $env:TAG_NAME) {
          Write-Error "Tag $env:TAG_NAME already exists. Failing the build."
          exit 1
        }
        git tag $env:TAG_NAME
        git push origin $env:TAG_NAME
      shell: powershell
