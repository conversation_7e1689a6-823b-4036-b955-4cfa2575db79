name: Fetch_CodeCoverage

on:
  push:
     
env:
  SONAR_PROJECTKEY: ${{ vars.SONARQUBE_PROJECT_KEY }} 
  SONAR_SERVERURL: ${{ vars.SONARQUBE_SERVER_URL }}  
  SONAR_TOKEN: ${{ secrets.SONARI_TOKEN }}
  COVERAGE_THRESHOLD: ${{ vars.COVERAGE_THRESHOLD }}
jobs:
  fetch-coverage:
    runs-on: niagara_tools
    steps:
    - name: Checkout code
      uses: actions/checkout@v4.1.0
    - name: Fetch Coverage from SonarQube And Check Against Threshold Value
      shell: powershell
      run: |
        $branch_name="${{ github.ref_name }}"
        $ErrorActionPreference = "Stop"
        $NEW_LINES_THRESHOLD = 10
        $branch = "${{ github.ref_name }}"
        $threshold = [double]${{ env.COVERAGE_THRESHOLD }}
        $url = "${{ env.SONAR_SERVERURL }}/api/measures/component?component=${{ env.SONAR_PROJECTKEY }}&branch=$branch&metricKeys=new_coverage,new_lines"
        $response = curl.exe -s -u "${{ env.SONAR_TOKEN }}:" "$url"
        if (-not $response) {
          Write-Host "Failed to fetch data from SonarQube API."
          exit 1
        }
        Write-Output "Fetching coverage from: $url"
        Write-Output "Response: $response"
        $jsonResponse = $response | ConvertFrom-Json
        if ($null -eq $jsonResponse.component.measures -or $jsonResponse.component.measures.Count -eq 0) {
          Write-Host "Coverage data hasn't yet published to SonarQube for this branch"
          exit 1
        }
                 
        $coverage = $jsonResponse.component.measures | Where-Object { $_.metric -eq "new_coverage" } | Select-Object -ExpandProperty periods | Select-Object -First 1 -ExpandProperty value
        $newLines = $jsonResponse.component.measures | Where-Object { $_.metric -eq "new_lines" } | Select-Object -ExpandProperty periods | Select-Object -First 1 -ExpandProperty value
           
        if ([string]::IsNullOrWhiteSpace($coverage) -or [string]::IsNullOrWhiteSpace($newLines)) {
          Write-Host "Coverage or new lines data is missing."
          exit 1
        }
        Write-Output "New lines of code: $newLines"
        Write-Output "Coverage on new code: $coverage"
        
        # Check if new lines exist
        if ([double]$newLines -gt 0) {
          # Convert coverage to double for comparison
          $coverageValue = [double]$coverage
          
          # Check if coverage is below the threshold or zero
          if ($coverageValue -lt $threshold) {
            Write-Host "Coverage ($coverageValue%) is below the minimum threshold of $threshold%"
            exit 1
          } elseif ($coverageValue -eq 0) {
            Write-Host "No coverage found for new code"
            exit 1
          } else {
            Write-Host "Coverage ($coverageValue%) meets or exceeds the minimum threshold of $threshold%"
          }
        } else {
          Write-Host "No new lines of code detected"
        }
