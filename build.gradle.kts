/*
 * Copyright 2025 Honeywell. All Rights Reserved.
 */

plugins {
    // Base Niagara plugins
    id("com.tridium.niagara")
    id("com.tridium.vendor")
    id("com.tridium.niagara-signing")
    id("com.tridium.convention.niagara-home-repositories")
    
    
    id("base")
}

vendor {
    // defaultVendor sets the "vendor" attribute on module and dist files
    defaultVendor("Honeywell")

    // defaultModuleVersion sets the "vendorVersion" attribute on all modules
    defaultModuleVersion("5.0.0")
}
if (project.hasProperty("TOOL_VERSION")) {
  version = project.property("TOOL_VERSION") as String
  vendor {
    defaultModuleVersion(version as String)
  }
}

// Clean task to remove root build directory
tasks.register<Delete>("customClean") {
    delete("${rootDir}/build")
}

tasks.named("clean") {
    dependsOn("customClean")
}

// Configure JaCoCo test coverage for subprojects
subprojects {
    repositories {
        mavenCentral()
    }
    
    // Apply test configuration to Java projects
    plugins.withId("java") {
        // Configure Niagara test tasks
        tasks.withType<com.tridium.gradle.plugins.niagara.task.RunNiagaraTestTask> {
            // Ensure JaCoCo report is generated after tests
            finalizedBy("jacocoNiagaraTestReport")
            
        }
        
        // Configure JaCoCo XML report for SonarQube integration
        tasks.withType<JacocoReport> {
            reports {
                xml.required.set(true)
                html.required.set(true)
            }
        }
    }
}


////////////////////////////////////////////////////////////////
// Dependencies and configurations... configuration
////////////////////////////////////////////////////////////////

subprojects {
    repositories {
        mavenCentral()
    }
}
