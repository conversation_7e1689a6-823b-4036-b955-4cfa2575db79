#Uncomment and set to the path of the Gradle plugins if they are not in niagara_home
gradlePluginHome=C:/work/tool/OptimizerSupervisor-N4.15.1.16/etc/m2/repository

#The path to the installation of Niagara you are building against
niagara_home=C:/work/tool/OptimizerSupervisor-N4.15.1.16

#The path to niagara_user_home for the version of Niagara you are building against
#niagara_user_home=

#Uncomment and set to the path of your node install if you are building JavaScript
#modules
nodeHome=C:/Users/<USER>/AppData/Roaming/nodejs
niagara.ignoreSignatureValidation=true
#NOTE: Niagara builds _require_ a full JDK. You can see what toolchains Gradle can
#find automatically by running
#
#  "gradlew -q javaToolchains -Porg.gradle.java.installations.auto-detect=true".
#
#If that list includes a valid Java 8 JDK, you can leave the following three
#properties commented out and let <PERSON><PERSON><PERSON> select a JDK automatically. If it does not,
#you can set the 'org.gradle.java.installations.paths' property to the full path to
#a Java 8 JDK
org.gradle.java.installations.auto-detect=false
org.gradle.java.installations.auto-download=false
org.gradle.java.installations.paths=C:/Users/<USER>/Java/jdk-1.8
systemProp.org.gradle.internal.publish.checksums.insecure=true
